import { createClient } from '@/lib/supabase/server'
import { getCurrentUser, getUserProfile } from '@/lib/supabase/server'
import { redirect } from 'next/navigation'
import { ProtectedLayout } from '@/components/layout'
import { RouteGuard } from '@/components/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Bell, 
  Check,
  Trash2,
  Filter,
  Search,
  MoreVertical,
  FileText,
  AlertCircle,
  CheckCircle,
  Info,
  Settings
} from 'lucide-react'

export default async function NotificacionesPage() {
  const { user, error } = await getCurrentUser()
  
  if (error || !user) {
    redirect('/auth/login')
  }

  const { data: profile } = await getUserProfile(user.id)
  
  if (!profile) {
    redirect('/auth/setup-profile')
  }

  // Get user's notifications
  const supabase = createClient()
  const { data: notifications } = await supabase
    .from('notifications')
    .select('*')
    .eq('user_id', user.id)
    .order('created_at', { ascending: false })

  // Get notification counts
  const { count: unreadCount } = await supabase
    .from('notifications')
    .select('*', { count: 'exact', head: true })
    .eq('user_id', user.id)
    .eq('is_read', false)

  const { count: totalCount } = await supabase
    .from('notifications')
    .select('*', { count: 'exact', head: true })
    .eq('user_id', user.id)

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'procedure_update':
        return <FileText className="h-4 w-4" />
      case 'system_alert':
        return <AlertCircle className="h-4 w-4" />
      case 'procedure_completed':
        return <CheckCircle className="h-4 w-4" />
      default:
        return <Info className="h-4 w-4" />
    }
  }

  const getNotificationColor = (type: string) => {
    switch (type) {
      case 'procedure_update':
        return 'text-blue-600 bg-blue-100'
      case 'system_alert':
        return 'text-red-600 bg-red-100'
      case 'procedure_completed':
        return 'text-green-600 bg-green-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  return (
    <ProtectedLayout requireAuth={true} allowedRoles={['ciudadano', 'admin', 'super_admin']}>
      <RouteGuard requiredRoles={['ciudadano', 'admin', 'super_admin']}>
        {/* Header */}
        <div className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-6">
              <div>
                <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                  <Bell className="h-6 w-6 mr-2 text-chia-blue-600" />
                  Notificaciones
                </h1>
                <p className="text-gray-600">
                  Mantente al día con las actualizaciones de tus trámites
                </p>
              </div>
              <div className="flex items-center space-x-2">
                <Button variant="outline" size="sm">
                  <Settings className="h-4 w-4 mr-2" />
                  Configurar
                </Button>
                <Button variant="outline" size="sm">
                  <Check className="h-4 w-4 mr-2" />
                  Marcar Todas como Leídas
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Notifications List */}
            <div className="lg:col-span-3">
              <Card>
                <CardHeader>
                  <div className="flex justify-between items-center">
                    <div>
                      <CardTitle>Todas las Notificaciones</CardTitle>
                      <CardDescription>
                        {totalCount || 0} notificaciones totales, {unreadCount || 0} sin leer
                      </CardDescription>
                    </div>
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm">
                        <Search className="h-4 w-4 mr-2" />
                        Buscar
                      </Button>
                      <Button variant="outline" size="sm">
                        <Filter className="h-4 w-4 mr-2" />
                        Filtrar
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  {notifications && notifications.length > 0 ? (
                    <div className="space-y-3">
                      {notifications.map((notification: any) => (
                        <div 
                          key={notification.id} 
                          className={`border rounded-lg p-4 ${
                            !notification.is_read ? 'bg-blue-50 border-blue-200' : 'bg-white'
                          }`}
                        >
                          <div className="flex items-start justify-between">
                            <div className="flex items-start space-x-3 flex-1">
                              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                                getNotificationColor(notification.type)
                              }`}>
                                {getNotificationIcon(notification.type)}
                              </div>
                              <div className="flex-1">
                                <div className="flex items-center space-x-2 mb-1">
                                  <h3 className="font-medium text-gray-900">
                                    {notification.title}
                                  </h3>
                                  {!notification.is_read && (
                                    <Badge variant="default" className="text-xs">
                                      Nuevo
                                    </Badge>
                                  )}
                                </div>
                                <p className="text-sm text-gray-600 mb-2">
                                  {notification.message}
                                </p>
                                <div className="flex items-center justify-between">
                                  <p className="text-xs text-gray-500">
                                    {new Date(notification.created_at).toLocaleString('es-CO')}
                                  </p>
                                  {notification.action_url && (
                                    <Button variant="link" size="sm" className="text-xs p-0 h-auto">
                                      Ver detalles
                                    </Button>
                                  )}
                                </div>
                              </div>
                            </div>
                            <div className="flex items-center space-x-1 ml-4">
                              {!notification.is_read && (
                                <Button variant="ghost" size="sm">
                                  <Check className="h-4 w-4" />
                                </Button>
                              )}
                              <Button variant="ghost" size="sm">
                                <MoreVertical className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <Bell className="mx-auto h-12 w-12 text-gray-300 mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">
                        No tienes notificaciones
                      </h3>
                      <p className="text-gray-500">
                        Las notificaciones sobre tus trámites aparecerán aquí
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Quick Stats */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Resumen</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Sin leer</span>
                    <Badge variant="destructive">{unreadCount || 0}</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Total</span>
                    <Badge variant="outline">{totalCount || 0}</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Esta semana</span>
                    <Badge variant="outline">
                      {notifications?.filter(n => {
                        const weekAgo = new Date()
                        weekAgo.setDate(weekAgo.getDate() - 7)
                        return new Date(n.created_at) > weekAgo
                      }).length || 0}
                    </Badge>
                  </div>
                </CardContent>
              </Card>

              {/* Notification Types */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Tipos de Notificación</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 bg-blue-100 rounded flex items-center justify-center">
                      <FileText className="h-3 w-3 text-blue-600" />
                    </div>
                    <span className="text-sm">Actualizaciones de Trámites</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 bg-green-100 rounded flex items-center justify-center">
                      <CheckCircle className="h-3 w-3 text-green-600" />
                    </div>
                    <span className="text-sm">Trámites Completados</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 bg-red-100 rounded flex items-center justify-center">
                      <AlertCircle className="h-3 w-3 text-red-600" />
                    </div>
                    <span className="text-sm">Alertas del Sistema</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 bg-gray-100 rounded flex items-center justify-center">
                      <Info className="h-3 w-3 text-gray-600" />
                    </div>
                    <span className="text-sm">Información General</span>
                  </div>
                </CardContent>
              </Card>

              {/* Settings */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Configuración</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button variant="outline" className="w-full justify-start text-sm">
                    <Settings className="h-4 w-4 mr-2" />
                    Preferencias de Notificación
                  </Button>
                  <Button variant="outline" className="w-full justify-start text-sm">
                    <Trash2 className="h-4 w-4 mr-2" />
                    Limpiar Notificaciones
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </RouteGuard>
    </ProtectedLayout>
  )
}
