'use client'

import { useState, useMemo } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { 
  Search, 
  Filter, 
  Grid, 
  List, 
  Clock, 
  DollarSign, 
  Building, 
  FileText,
  Star,
  Users,
  Phone,
  Mail,
  MapPin,
  ExternalLink,
  Plus,
  Eye,
  ChevronDown,
  ChevronUp,
  SortAsc,
  SortDesc
} from 'lucide-react'

interface Procedure {
  id: string
  name: string
  description?: string
  cost?: number
  response_time?: string
  category?: string
  difficulty_level?: number
  popularity_score?: number
  requirements?: string[]
  documents_required?: string[]
  process_steps?: string[]
  legal_framework?: string
  contact_info?: any
  online_available?: boolean
  tags?: string[]
  dependency?: {
    id: string
    name: string
    acronym?: string
    description?: string
    contact_email?: string
    contact_phone?: string
    address?: string
  }
}

interface ProcedureSearchInterfaceProps {
  procedures: Procedure[]
  dependencies: any[]
  categories: string[]
  userProcedures: any[]
  currentUserId: string
}

export function ProcedureSearchInterface({
  procedures,
  dependencies,
  categories,
  userProcedures,
  currentUserId
}: ProcedureSearchInterfaceProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('')
  const [selectedDependency, setSelectedDependency] = useState('')
  const [sortBy, setSortBy] = useState('name')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [showFilters, setShowFilters] = useState(false)
  const [selectedProcedure, setSelectedProcedure] = useState<Procedure | null>(null)

  // Filter and sort procedures
  const filteredProcedures = useMemo(() => {
    let filtered = procedures.filter(procedure => {
      const matchesSearch = !searchTerm || 
        procedure.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        procedure.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        procedure.dependency?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        procedure.tags?.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))

      const matchesCategory = !selectedCategory || procedure.category === selectedCategory
      const matchesDependency = !selectedDependency || procedure.dependency?.id === selectedDependency

      return matchesSearch && matchesCategory && matchesDependency
    })

    // Sort procedures
    filtered.sort((a, b) => {
      let aValue: any, bValue: any

      switch (sortBy) {
        case 'name':
          aValue = a.name
          bValue = b.name
          break
        case 'cost':
          aValue = a.cost || 0
          bValue = b.cost || 0
          break
        case 'popularity':
          aValue = a.popularity_score || 0
          bValue = b.popularity_score || 0
          break
        case 'difficulty':
          aValue = a.difficulty_level || 0
          bValue = b.difficulty_level || 0
          break
        default:
          aValue = a.name
          bValue = b.name
      }

      if (typeof aValue === 'string') {
        return sortOrder === 'asc' 
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue)
      } else {
        return sortOrder === 'asc' 
          ? aValue - bValue
          : bValue - aValue
      }
    })

    return filtered
  }, [procedures, searchTerm, selectedCategory, selectedDependency, sortBy, sortOrder])

  const clearFilters = () => {
    setSearchTerm('')
    setSelectedCategory('')
    setSelectedDependency('')
    setSortBy('name')
    setSortOrder('asc')
  }

  const getUserProcedureStatus = (procedureId: string) => {
    return userProcedures.find(up => up.procedure_id === procedureId)?.status
  }

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
            <div>
              <CardTitle className="flex items-center">
                <Search className="mr-2 h-5 w-5 text-blue-600" />
                Buscar Trámites
              </CardTitle>
              <CardDescription>
                Encuentra el trámite que necesitas con nuestro sistema de búsqueda avanzada
              </CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
              >
                {viewMode === 'grid' ? <List className="h-4 w-4" /> : <Grid className="h-4 w-4" />}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
              >
                <Filter className="h-4 w-4 mr-2" />
                Filtros
                {showFilters ? <ChevronUp className="h-4 w-4 ml-2" /> : <ChevronDown className="h-4 w-4 ml-2" />}
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Search Bar */}
          <div className="relative mb-4">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Buscar por nombre, descripción, dependencia o etiquetas..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Advanced Filters */}
          {showFilters && (
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 p-4 bg-gray-50 rounded-lg">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Categoría
                </label>
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md text-sm"
                >
                  <option value="">Todas las categorías</option>
                  {categories.map(category => (
                    <option key={category} value={category}>
                      {category}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Dependencia
                </label>
                <select
                  value={selectedDependency}
                  onChange={(e) => setSelectedDependency(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md text-sm"
                >
                  <option value="">Todas las dependencias</option>
                  {dependencies.map(dep => (
                    <option key={dep.id} value={dep.id}>
                      {dep.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Ordenar por
                </label>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md text-sm"
                >
                  <option value="name">Nombre</option>
                  <option value="cost">Costo</option>
                  <option value="popularity">Popularidad</option>
                  <option value="difficulty">Dificultad</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Orden
                </label>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                  className="w-full"
                >
                  {sortOrder === 'asc' ? (
                    <>
                      <SortAsc className="h-4 w-4 mr-2" />
                      Ascendente
                    </>
                  ) : (
                    <>
                      <SortDesc className="h-4 w-4 mr-2" />
                      Descendente
                    </>
                  )}
                </Button>
              </div>

              <div className="md:col-span-4 flex justify-end">
                <Button variant="ghost" size="sm" onClick={clearFilters}>
                  Limpiar filtros
                </Button>
              </div>
            </div>
          )}

          {/* Results Summary */}
          <div className="flex justify-between items-center mt-4 text-sm text-gray-600">
            <span>
              {filteredProcedures.length} de {procedures.length} trámites encontrados
            </span>
            {(searchTerm || selectedCategory || selectedDependency) && (
              <Button variant="ghost" size="sm" onClick={clearFilters}>
                Limpiar búsqueda
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Procedures Results */}
      {filteredProcedures.length > 0 ? (
        <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-4'}>
          {filteredProcedures.map((procedure) => {
            const userStatus = getUserProcedureStatus(procedure.id)

            return (
              <Card key={procedure.id} className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <CardTitle className="text-lg leading-tight">
                        {procedure.name}
                      </CardTitle>
                      <CardDescription className="mt-2">
                        {procedure.description?.substring(0, 100)}
                        {procedure.description && procedure.description.length > 100 && '...'}
                      </CardDescription>
                    </div>
                    {userStatus && (
                      <Badge variant="outline" className="ml-2">
                        {userStatus.display_name}
                      </Badge>
                    )}
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {/* Basic Info */}
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div className="flex items-center">
                        <Building className="h-4 w-4 text-gray-400 mr-2" />
                        <span className="text-gray-600 truncate">
                          {procedure.dependency?.name || 'N/A'}
                        </span>
                      </div>
                      <div className="flex items-center">
                        <DollarSign className="h-4 w-4 text-green-600 mr-2" />
                        <span className="font-medium">
                          {procedure.cost ? `$${procedure.cost.toLocaleString()}` : 'Gratuito'}
                        </span>
                      </div>
                      <div className="flex items-center">
                        <Clock className="h-4 w-4 text-blue-600 mr-2" />
                        <span className="text-gray-600">
                          {procedure.response_time || 'No especificado'}
                        </span>
                      </div>
                      <div className="flex items-center">
                        <Star className="h-4 w-4 text-yellow-500 mr-2" />
                        <span className="text-gray-600">
                          Nivel {procedure.difficulty_level || 1}
                        </span>
                      </div>
                    </div>

                    {/* Tags */}
                    {procedure.tags && procedure.tags.length > 0 && (
                      <div className="flex flex-wrap gap-1">
                        {procedure.tags.slice(0, 3).map((tag, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                        {procedure.tags.length > 3 && (
                          <Badge variant="outline" className="text-xs">
                            +{procedure.tags.length - 3} más
                          </Badge>
                        )}
                      </div>
                    )}

                    {/* Category and Online Status */}
                    <div className="flex justify-between items-center">
                      {procedure.category && (
                        <Badge variant="outline" className="text-xs">
                          {procedure.category}
                        </Badge>
                      )}
                      {procedure.online_available && (
                        <Badge className="text-xs bg-green-100 text-green-800">
                          Disponible en línea
                        </Badge>
                      )}
                    </div>

                    {/* Action Buttons */}
                    <div className="flex space-x-2 pt-2">
                      <Button
                        size="sm"
                        variant="outline"
                        className="flex-1"
                        onClick={() => setSelectedProcedure(procedure)}
                      >
                        <Eye className="h-4 w-4 mr-2" />
                        Ver Detalles
                      </Button>
                      {!userStatus && (
                        <Button size="sm" className="flex-1">
                          <Plus className="h-4 w-4 mr-2" />
                          Iniciar
                        </Button>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>
      ) : (
        <Card>
          <CardContent className="text-center py-12">
            <Search className="mx-auto h-12 w-12 text-gray-300 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No se encontraron trámites
            </h3>
            <p className="text-gray-500 mb-4">
              Intenta ajustar tus criterios de búsqueda o filtros
            </p>
            <Button variant="outline" onClick={clearFilters}>
              Limpiar filtros
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Procedure Detail Modal */}
      {selectedProcedure && (
        <ProcedureDetailModal
          procedure={selectedProcedure}
          onClose={() => setSelectedProcedure(null)}
          userStatus={getUserProcedureStatus(selectedProcedure.id)}
        />
      )}
    </div>
  )
}
