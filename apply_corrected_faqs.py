#!/usr/bin/env python3
"""
Apply corrected FAQ questions in manageable chunks
"""

import re
import os

def split_corrected_file():
    """Split the corrected FAQ file into smaller chunks"""
    
    # Read the corrected file
    with open('corrected_faq_insertion.sql', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Split by INSERT statements
    insert_statements = re.findall(r'INSERT INTO municipal_faqs.*?;', content, re.DOTALL)
    
    print(f"Found {len(insert_statements)} INSERT statements")
    
    # Create chunks of 15 statements each (smaller for reliability)
    chunk_size = 15
    chunk_num = 1
    
    for i in range(0, len(insert_statements), chunk_size):
        chunk = insert_statements[i:i + chunk_size]
        
        filename = f"corrected_faq_chunk_{chunk_num:02d}.sql"
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"-- Corrected FAQ Chunk {chunk_num}\n")
            f.write(f"-- Questions {i+1} to {min(i+chunk_size, len(insert_statements))}\n\n")
            f.write("\n".join(chunk))
            f.write("\n")
        
        print(f"Created {filename} with {len(chunk)} questions")
        chunk_num += 1
    
    print(f"\nTotal chunks created: {chunk_num-1}")
    print(f"Total questions: {len(insert_statements)}")
    
    return chunk_num - 1, len(insert_statements)

def list_chunk_files():
    """List all corrected chunk files"""
    chunk_files = []
    for i in range(1, 30):  # Max 30 chunks
        filename = f"corrected_faq_chunk_{i:02d}.sql"
        if os.path.exists(filename):
            chunk_files.append(filename)
        else:
            break
    
    return chunk_files

def main():
    """Main function"""
    print("Corrected FAQ Chunk Processor")
    print("=" * 35)
    
    try:
        total_chunks, total_questions = split_corrected_file()
        
        print(f"\n✅ Successfully created {total_chunks} chunk files")
        print(f"📊 Total questions: {total_questions}")
        print(f"📁 Files created: corrected_faq_chunk_01.sql to corrected_faq_chunk_{total_chunks:02d}.sql")
        
        # List the files for verification
        chunk_files = list_chunk_files()
        print(f"\n📋 Chunk files ready for application:")
        for i, filename in enumerate(chunk_files, 1):
            print(f"   {i:2d}. {filename}")
        
        return 0
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
