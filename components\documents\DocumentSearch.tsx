'use client'

import { useState } from 'react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { 
  Search,
  X,
  Filter
} from 'lucide-react'

interface DocumentSearchProps {
  searchQuery: string
  onSearchChange: (query: string) => void
}

export function DocumentSearch({ searchQuery, onSearchChange }: DocumentSearchProps) {
  const [localQuery, setLocalQuery] = useState(searchQuery)

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    onSearchChange(localQuery)
  }

  const handleClear = () => {
    setLocalQuery('')
    onSearchChange('')
  }

  return (
    <form onSubmit={handleSearch} className="flex items-center space-x-2">
      <div className="relative flex-1">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        <Input
          type="text"
          placeholder="Buscar documentos, trámites o referencias..."
          value={localQuery}
          onChange={(e) => setLocalQuery(e.target.value)}
          className="pl-10 pr-10"
        />
        {localQuery && (
          <button
            type="button"
            onClick={handleClear}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
          >
            <X className="h-4 w-4" />
          </button>
        )}
      </div>
      <Button type="submit" variant="outline">
        <Search className="h-4 w-4 mr-2" />
        Buscar
      </Button>
    </form>
  )
}
