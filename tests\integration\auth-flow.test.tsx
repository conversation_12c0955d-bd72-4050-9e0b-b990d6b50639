import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { LoginForm } from '@/components/auth/LoginForm'
import { RegisterForm } from '@/components/auth/RegisterForm'
import { ProtectedLayout } from '@/components/layout/ProtectedLayout'
import { createClient } from '@/lib/supabase/client'

// Mock Supabase client
jest.mock('@/lib/supabase/client')
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    refresh: jest.fn(),
  }),
  usePathname: () => '/dashboard',
}))

const mockSupabase = {
  auth: {
    signInWithPassword: jest.fn(),
    signUp: jest.fn(),
    signOut: jest.fn(),
    getUser: jest.fn(),
    onAuthStateChange: jest.fn(() => ({
      data: { subscription: { unsubscribe: jest.fn() } },
    })),
  },
  from: jest.fn(() => ({
    select: jest.fn(() => ({
      eq: jest.fn(() => ({
        single: jest.fn(),
      })),
    })),
    insert: jest.fn(),
    update: jest.fn(() => ({
      eq: jest.fn(),
    })),
  })),
}

describe('Authentication Flow Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    ;(createClient as jest.Mock).mockReturnValue(mockSupabase)
  })

  describe('Login Flow', () => {
    it('should complete successful login flow', async () => {
      const user = userEvent.setup()
      
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        email_confirmed_at: '2023-01-01T00:00:00Z',
      }

      mockSupabase.auth.signInWithPassword.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      })

      render(<LoginForm />)

      // Fill in login form
      const emailInput = screen.getByLabelText(/correo electrónico/i)
      const passwordInput = screen.getByLabelText(/contraseña/i)
      const submitButton = screen.getByRole('button', { name: /iniciar sesión/i })

      await user.type(emailInput, '<EMAIL>')
      await user.type(passwordInput, 'password123')
      await user.click(submitButton)

      await waitFor(() => {
        expect(mockSupabase.auth.signInWithPassword).toHaveBeenCalledWith({
          email: '<EMAIL>',
          password: 'password123',
        })
      })
    })

    it('should handle login errors appropriately', async () => {
      const user = userEvent.setup()
      
      mockSupabase.auth.signInWithPassword.mockResolvedValue({
        data: { user: null },
        error: { message: 'Invalid login credentials' },
      })

      render(<LoginForm />)

      const emailInput = screen.getByLabelText(/correo electrónico/i)
      const passwordInput = screen.getByLabelText(/contraseña/i)
      const submitButton = screen.getByRole('button', { name: /iniciar sesión/i })

      await user.type(emailInput, '<EMAIL>')
      await user.type(passwordInput, 'wrongpassword')
      await user.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText(/credenciales inválidas/i)).toBeInTheDocument()
      })
    })

    it('should validate form fields before submission', async () => {
      const user = userEvent.setup()
      
      render(<LoginForm />)

      const submitButton = screen.getByRole('button', { name: /iniciar sesión/i })
      await user.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText(/el correo electrónico es requerido/i)).toBeInTheDocument()
        expect(screen.getByText(/la contraseña es requerida/i)).toBeInTheDocument()
      })

      expect(mockSupabase.auth.signInWithPassword).not.toHaveBeenCalled()
    })
  })

  describe('Registration Flow', () => {
    it('should complete successful registration flow', async () => {
      const user = userEvent.setup()
      
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
      }

      mockSupabase.auth.signUp.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      })

      render(<RegisterForm />)

      // Fill in registration form
      const fullNameInput = screen.getByLabelText(/nombre completo/i)
      const emailInput = screen.getByLabelText(/correo electrónico/i)
      const documentInput = screen.getByLabelText(/documento de identidad/i)
      const passwordInput = screen.getByLabelText(/contraseña/i)
      const confirmPasswordInput = screen.getByLabelText(/confirmar contraseña/i)
      const submitButton = screen.getByRole('button', { name: /registrarse/i })

      await user.type(fullNameInput, 'New User')
      await user.type(emailInput, '<EMAIL>')
      await user.type(documentInput, '12345678')
      await user.type(passwordInput, 'password123')
      await user.type(confirmPasswordInput, 'password123')
      await user.click(submitButton)

      await waitFor(() => {
        expect(mockSupabase.auth.signUp).toHaveBeenCalledWith({
          email: '<EMAIL>',
          password: 'password123',
        })
      })
    })

    it('should validate password confirmation', async () => {
      const user = userEvent.setup()
      
      render(<RegisterForm />)

      const passwordInput = screen.getByLabelText(/contraseña/i)
      const confirmPasswordInput = screen.getByLabelText(/confirmar contraseña/i)
      const submitButton = screen.getByRole('button', { name: /registrarse/i })

      await user.type(passwordInput, 'password123')
      await user.type(confirmPasswordInput, 'differentpassword')
      await user.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText(/las contraseñas no coinciden/i)).toBeInTheDocument()
      })

      expect(mockSupabase.auth.signUp).not.toHaveBeenCalled()
    })

    it('should handle registration errors', async () => {
      const user = userEvent.setup()
      
      mockSupabase.auth.signUp.mockResolvedValue({
        data: { user: null },
        error: { message: 'User already registered' },
      })

      render(<RegisterForm />)

      const fullNameInput = screen.getByLabelText(/nombre completo/i)
      const emailInput = screen.getByLabelText(/correo electrónico/i)
      const documentInput = screen.getByLabelText(/documento de identidad/i)
      const passwordInput = screen.getByLabelText(/contraseña/i)
      const confirmPasswordInput = screen.getByLabelText(/confirmar contraseña/i)
      const submitButton = screen.getByRole('button', { name: /registrarse/i })

      await user.type(fullNameInput, 'Existing User')
      await user.type(emailInput, '<EMAIL>')
      await user.type(documentInput, '12345678')
      await user.type(passwordInput, 'password123')
      await user.type(confirmPasswordInput, 'password123')
      await user.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText(/usuario ya registrado/i)).toBeInTheDocument()
      })
    })
  })

  describe('Protected Route Access', () => {
    it('should allow access to protected content when authenticated', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
      }

      const mockProfile = {
        id: 'user-123',
        full_name: 'Test User',
        role: 'ciudadano',
      }

      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      })

      mockSupabase.from().select().eq().single.mockResolvedValue({
        data: mockProfile,
        error: null,
      })

      const TestProtectedComponent = () => (
        <ProtectedLayout requireAuth={true} allowedRoles={['ciudadano']}>
          <div>Protected Content</div>
        </ProtectedLayout>
      )

      render(<TestProtectedComponent />)

      await waitFor(() => {
        expect(screen.getByText('Protected Content')).toBeInTheDocument()
      })
    })

    it('should redirect to login when not authenticated', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: null,
      })

      const TestProtectedComponent = () => (
        <ProtectedLayout requireAuth={true} allowedRoles={['ciudadano']}>
          <div>Protected Content</div>
        </ProtectedLayout>
      )

      render(<TestProtectedComponent />)

      await waitFor(() => {
        expect(screen.queryByText('Protected Content')).not.toBeInTheDocument()
      })
    })

    it('should deny access when user lacks required role', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
      }

      const mockProfile = {
        id: 'user-123',
        full_name: 'Test User',
        role: 'ciudadano',
      }

      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      })

      mockSupabase.from().select().eq().single.mockResolvedValue({
        data: mockProfile,
        error: null,
      })

      const TestProtectedComponent = () => (
        <ProtectedLayout requireAuth={true} allowedRoles={['admin']}>
          <div>Admin Content</div>
        </ProtectedLayout>
      )

      render(<TestProtectedComponent />)

      await waitFor(() => {
        expect(screen.getByText(/acceso denegado/i)).toBeInTheDocument()
        expect(screen.queryByText('Admin Content')).not.toBeInTheDocument()
      })
    })
  })

  describe('Session Management', () => {
    it('should handle session expiration gracefully', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: { message: 'JWT expired' },
      })

      const TestComponent = () => (
        <ProtectedLayout requireAuth={true}>
          <div>Protected Content</div>
        </ProtectedLayout>
      )

      render(<TestComponent />)

      await waitFor(() => {
        expect(screen.queryByText('Protected Content')).not.toBeInTheDocument()
      })
    })

    it('should update UI when auth state changes', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
      }

      // Initially no user
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: null,
      })

      const TestComponent = () => (
        <ProtectedLayout requireAuth={true}>
          <div>Protected Content</div>
        </ProtectedLayout>
      )

      const { rerender } = render(<TestComponent />)

      // Simulate auth state change
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      })

      mockSupabase.from().select().eq().single.mockResolvedValue({
        data: { id: 'user-123', role: 'ciudadano' },
        error: null,
      })

      rerender(<TestComponent />)

      await waitFor(() => {
        expect(screen.getByText('Protected Content')).toBeInTheDocument()
      })
    })
  })
})
