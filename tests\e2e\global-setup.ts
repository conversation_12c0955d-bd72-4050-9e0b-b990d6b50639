import { chromium, FullConfig } from '@playwright/test'

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting global setup for E2E tests...')
  
  // Launch browser for setup
  const browser = await chromium.launch()
  const page = await browser.newPage()
  
  try {
    // Wait for the development server to be ready
    const baseURL = config.projects[0].use.baseURL || 'http://localhost:3000'
    console.log(`⏳ Waiting for server at ${baseURL}...`)
    
    let retries = 0
    const maxRetries = 30
    
    while (retries < maxRetries) {
      try {
        const response = await page.goto(baseURL, { timeout: 5000 })
        if (response && response.ok()) {
          console.log('✅ Server is ready!')
          break
        }
      } catch (error) {
        retries++
        if (retries === maxRetries) {
          throw new Error(`❌ Server not ready after ${maxRetries} attempts`)
        }
        console.log(`⏳ Attempt ${retries}/${maxRetries} - Server not ready, retrying...`)
        await page.waitForTimeout(2000)
      }
    }
    
    // Setup test data if needed
    await setupTestData(page, baseURL)
    
  } catch (error) {
    console.error('❌ Global setup failed:', error)
    throw error
  } finally {
    await browser.close()
  }
  
  console.log('✅ Global setup completed successfully!')
}

async function setupTestData(page: any, baseURL: string) {
  console.log('📝 Setting up test data...')
  
  // Here you could:
  // 1. Create test users in the database
  // 2. Set up test data
  // 3. Configure test environment
  
  // For now, we'll just verify the app is accessible
  try {
    await page.goto(baseURL)
    
    // Check if the app loads correctly
    const title = await page.title()
    console.log(`📄 App title: ${title}`)
    
    // Verify essential elements are present
    const hasNavigation = await page.locator('nav').count() > 0
    if (hasNavigation) {
      console.log('✅ Navigation found')
    }
    
    // Check if auth pages are accessible
    await page.goto(`${baseURL}/auth/login`)
    const loginForm = await page.locator('form').count() > 0
    if (loginForm) {
      console.log('✅ Login form found')
    }
    
  } catch (error) {
    console.warn('⚠️ Warning during test data setup:', error)
    // Don't fail the setup for non-critical issues
  }
}

export default globalSetup
