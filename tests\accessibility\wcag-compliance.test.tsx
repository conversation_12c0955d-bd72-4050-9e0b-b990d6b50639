import React from 'react'
import { render } from '@testing-library/react'
import { axe, toHaveNoViolations } from 'jest-axe'
import { mockUsers, setupMockAuthSuccess, cleanupMocks } from '@/tests/utils/test-helpers'

// Extend Jest matchers
expect.extend(toHaveNoViolations)

// Mock authentication
jest.mock('@/hooks/useAuth', () => ({
  useAuth: () => ({
    user: mockUsers.ciudadano,
    profile: mockUsers.ciudadano.profile,
    loading: false,
  }),
}))

jest.mock('@/hooks/useRole', () => ({
  useRole: () => ({
    role: 'ciudadano',
    isLoading: false,
    hasRole: jest.fn((role) => role === 'ciudadano'),
  }),
}))

// Mock Supabase
jest.mock('@/lib/supabase/client', () => ({
  supabase: {
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          order: jest.fn(() => ({
            limit: jest.fn(() => Promise.resolve({
              data: [],
              error: null,
            })),
          })),
        })),
      })),
    })),
    storage: {
      from: jest.fn(() => ({
        list: jest.fn(() => Promise.resolve({
          data: [],
          error: null,
        })),
      })),
    },
  },
}))

describe('WCAG 2.1 AA Compliance Tests', () => {
  beforeEach(() => {
    setupMockAuthSuccess('ciudadano')
  })

  afterEach(() => {
    cleanupMocks()
  })

  describe('Dashboard Components', () => {
    it('CitizenDashboard should have no accessibility violations', async () => {
      const { CitizenDashboard } = await import('@/components/dashboard/CitizenDashboard')
      const { container } = render(<CitizenDashboard />)
      
      const results = await axe(container, {
        rules: {
          // WCAG 2.1 AA rules
          'color-contrast': { enabled: true },
          'keyboard-navigation': { enabled: true },
          'focus-management': { enabled: true },
          'aria-labels': { enabled: true },
          'heading-order': { enabled: true },
          'landmark-roles': { enabled: true },
        },
      })
      
      expect(results).toHaveNoViolations()
    })

    it('DashboardStats should have proper ARIA labels', async () => {
      const { DashboardStats } = await import('@/components/dashboard/DashboardStats')
      const { container } = render(<DashboardStats />)
      
      // Check for proper ARIA labels on statistics
      const stats = container.querySelectorAll('[role="img"], [aria-label]')
      expect(stats.length).toBeGreaterThan(0)
      
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })

    it('QuickActions should be keyboard accessible', async () => {
      const { QuickActions } = await import('@/components/dashboard/QuickActions')
      const { container } = render(<QuickActions />)
      
      // Check that all buttons are focusable
      const buttons = container.querySelectorAll('button')
      buttons.forEach(button => {
        expect(button).toHaveAttribute('tabindex', '0')
        expect(
          button.textContent || 
          button.getAttribute('aria-label') || 
          button.getAttribute('title')
        ).toBeTruthy()
      })
      
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })
  })

  describe('Procedure Components', () => {
    it('ProcedureSearch should have accessible form controls', async () => {
      const { ProcedureSearch } = await import('@/components/procedures/ProcedureSearch')
      const { container } = render(<ProcedureSearch />)
      
      // Check form accessibility
      const inputs = container.querySelectorAll('input, select, textarea')
      inputs.forEach(input => {
        const id = input.getAttribute('id')
        if (id) {
          const label = container.querySelector(`label[for="${id}"]`)
          expect(
            label || 
            input.getAttribute('aria-label') || 
            input.getAttribute('aria-labelledby')
          ).toBeTruthy()
        }
      })
      
      const results = await axe(container, {
        rules: {
          'label': { enabled: true },
          'form-field-multiple-labels': { enabled: true },
        },
      })
      expect(results).toHaveNoViolations()
    })

    it('ProcedureCard should have proper semantic structure', async () => {
      const { ProcedureCard } = await import('@/components/procedures/ProcedureCard')
      const mockProcedure = {
        id: 1,
        name: 'Test Procedure',
        description: 'Test description',
        category: 'Test',
        cost: 10000,
        duration_days: 5,
      }
      
      const { container } = render(<ProcedureCard procedure={mockProcedure} />)
      
      // Check heading structure
      const headings = container.querySelectorAll('h1, h2, h3, h4, h5, h6')
      expect(headings.length).toBeGreaterThan(0)
      
      // Check for proper link/button semantics
      const interactiveElements = container.querySelectorAll('a, button')
      interactiveElements.forEach(element => {
        expect(
          element.textContent || 
          element.getAttribute('aria-label') || 
          element.getAttribute('title')
        ).toBeTruthy()
      })
      
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })
  })

  describe('Document Components', () => {
    it('DocumentPortal should have accessible file upload', async () => {
      const { DocumentPortal } = await import('@/components/documents/DocumentPortal')
      const { container } = render(<DocumentPortal />)
      
      // Check file input accessibility
      const fileInputs = container.querySelectorAll('input[type="file"]')
      fileInputs.forEach(input => {
        expect(
          input.getAttribute('aria-label') || 
          input.getAttribute('aria-labelledby') ||
          container.querySelector(`label[for="${input.id}"]`)
        ).toBeTruthy()
      })
      
      const results = await axe(container, {
        rules: {
          'label': { enabled: true },
          'button-name': { enabled: true },
        },
      })
      expect(results).toHaveNoViolations()
    })

    it('DocumentGrid should have proper grid semantics', async () => {
      const { DocumentGrid } = await import('@/components/documents/DocumentGrid')
      const mockDocuments = [
        {
          id: '1',
          name: 'test.pdf',
          type: 'application/pdf',
          size: 1024,
          uploaded_at: '2024-01-01T00:00:00Z',
        },
      ]
      
      const { container } = render(<DocumentGrid documents={mockDocuments} />)
      
      // Check grid accessibility
      const grid = container.querySelector('[role="grid"]')
      if (grid) {
        expect(grid).toHaveAttribute('aria-label')
        
        const gridCells = container.querySelectorAll('[role="gridcell"]')
        expect(gridCells.length).toBeGreaterThan(0)
      }
      
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })
  })

  describe('Navigation Components', () => {
    it('ProtectedNavigation should have proper navigation semantics', async () => {
      const { ProtectedNavigation } = await import('@/components/navigation/ProtectedNavigation')
      const { container } = render(<ProtectedNavigation />)
      
      // Check navigation structure
      const nav = container.querySelector('nav')
      expect(nav).toBeInTheDocument()
      expect(nav).toHaveAttribute('aria-label')
      
      // Check navigation links
      const links = container.querySelectorAll('a')
      links.forEach(link => {
        expect(
          link.textContent || 
          link.getAttribute('aria-label') || 
          link.getAttribute('title')
        ).toBeTruthy()
      })
      
      const results = await axe(container, {
        rules: {
          'link-name': { enabled: true },
          'navigation': { enabled: true },
        },
      })
      expect(results).toHaveNoViolations()
    })

    it('MobileMenu should be keyboard accessible', async () => {
      const { MobileMenu } = await import('@/components/navigation/MobileMenu')
      const { container } = render(<MobileMenu isOpen={true} onClose={jest.fn()} />)
      
      // Check focus management
      const focusableElements = container.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      )
      
      expect(focusableElements.length).toBeGreaterThan(0)
      
      // Check for proper ARIA attributes
      const menu = container.querySelector('[role="menu"]')
      if (menu) {
        expect(menu).toHaveAttribute('aria-label')
      }
      
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })
  })

  describe('Form Components', () => {
    it('SearchInput should have proper form accessibility', async () => {
      const { SearchInput } = await import('@/components/forms/SearchInput')
      const { container } = render(
        <SearchInput 
          placeholder="Search test" 
          onSearch={jest.fn()} 
          aria-label="Search procedures"
        />
      )
      
      // Check input accessibility
      const input = container.querySelector('input')
      expect(input).toHaveAttribute('aria-label')
      expect(input).toHaveAttribute('placeholder')
      
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })

    it('FilterPanel should have accessible filter controls', async () => {
      const { FilterPanel } = await import('@/components/forms/FilterPanel')
      const { container } = render(<FilterPanel onFilterChange={jest.fn()} />)
      
      // Check fieldset and legend usage
      const fieldsets = container.querySelectorAll('fieldset')
      fieldsets.forEach(fieldset => {
        const legend = fieldset.querySelector('legend')
        expect(legend).toBeInTheDocument()
      })
      
      // Check checkbox/radio accessibility
      const checkboxes = container.querySelectorAll('input[type="checkbox"], input[type="radio"]')
      checkboxes.forEach(checkbox => {
        const id = checkbox.getAttribute('id')
        if (id) {
          const label = container.querySelector(`label[for="${id}"]`)
          expect(label).toBeInTheDocument()
        }
      })
      
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })
  })

  describe('Modal Components', () => {
    it('Modal should have proper focus management', async () => {
      const { Modal } = await import('@/components/ui/Modal')
      const { container } = render(
        <Modal isOpen={true} onClose={jest.fn()} title="Test Modal">
          <p>Modal content</p>
        </Modal>
      )
      
      // Check modal accessibility
      const modal = container.querySelector('[role="dialog"]')
      expect(modal).toBeInTheDocument()
      expect(modal).toHaveAttribute('aria-labelledby')
      expect(modal).toHaveAttribute('aria-modal', 'true')
      
      // Check for close button
      const closeButton = container.querySelector('[aria-label*="close"], [aria-label*="cerrar"]')
      expect(closeButton).toBeInTheDocument()
      
      const results = await axe(container, {
        rules: {
          'dialog-name': { enabled: true },
          'focus-order-semantics': { enabled: true },
        },
      })
      expect(results).toHaveNoViolations()
    })
  })

  describe('Loading and Error States', () => {
    it('LoadingSpinner should have proper ARIA attributes', async () => {
      const { LoadingSpinner } = await import('@/components/ui/LoadingSpinner')
      const { container } = render(<LoadingSpinner />)
      
      // Check loading indicator accessibility
      const spinner = container.querySelector('[role="status"], [aria-live]')
      expect(spinner).toBeInTheDocument()
      
      if (spinner) {
        expect(
          spinner.getAttribute('aria-label') || 
          spinner.textContent
        ).toBeTruthy()
      }
      
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })

    it('ErrorBoundary should announce errors properly', async () => {
      const { ErrorBoundary } = await import('@/components/ui/ErrorBoundary')
      const ThrowError = () => {
        throw new Error('Test error')
      }
      
      const { container } = render(
        <ErrorBoundary>
          <ThrowError />
        </ErrorBoundary>
      )
      
      // Check error announcement
      const errorRegion = container.querySelector('[role="alert"], [aria-live="assertive"]')
      expect(errorRegion).toBeInTheDocument()
      
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })
  })

  describe('Color Contrast', () => {
    it('should meet WCAG AA color contrast requirements', async () => {
      const { CitizenDashboard } = await import('@/components/dashboard/CitizenDashboard')
      const { container } = render(<CitizenDashboard />)
      
      const results = await axe(container, {
        rules: {
          'color-contrast': { enabled: true },
        },
      })
      
      expect(results).toHaveNoViolations()
    })
  })

  describe('Keyboard Navigation', () => {
    it('should support proper tab order', async () => {
      const { CitizenDashboard } = await import('@/components/dashboard/CitizenDashboard')
      const { container } = render(<CitizenDashboard />)
      
      // Check that focusable elements have proper tab order
      const focusableElements = container.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      )
      
      focusableElements.forEach((element, index) => {
        const tabIndex = element.getAttribute('tabindex')
        if (tabIndex && tabIndex !== '0') {
          expect(parseInt(tabIndex)).toBeGreaterThan(0)
        }
      })
      
      const results = await axe(container, {
        rules: {
          'tabindex': { enabled: true },
          'focus-order-semantics': { enabled: true },
        },
      })
      
      expect(results).toHaveNoViolations()
    })
  })

  describe('Screen Reader Support', () => {
    it('should have proper heading hierarchy', async () => {
      const { CitizenDashboard } = await import('@/components/dashboard/CitizenDashboard')
      const { container } = render(<CitizenDashboard />)
      
      const headings = container.querySelectorAll('h1, h2, h3, h4, h5, h6')
      expect(headings.length).toBeGreaterThan(0)
      
      // Check heading order
      let previousLevel = 0
      headings.forEach(heading => {
        const level = parseInt(heading.tagName.charAt(1))
        expect(level).toBeGreaterThanOrEqual(1)
        expect(level).toBeLessThanOrEqual(6)
        
        if (previousLevel > 0) {
          expect(level - previousLevel).toBeLessThanOrEqual(1)
        }
        previousLevel = level
      })
      
      const results = await axe(container, {
        rules: {
          'heading-order': { enabled: true },
        },
      })
      
      expect(results).toHaveNoViolations()
    })

    it('should have proper landmark regions', async () => {
      const { CitizenDashboard } = await import('@/components/dashboard/CitizenDashboard')
      const { container } = render(<CitizenDashboard />)
      
      // Check for main landmark
      const main = container.querySelector('main')
      expect(main).toBeInTheDocument()
      
      const results = await axe(container, {
        rules: {
          'landmark-one-main': { enabled: true },
          'region': { enabled: true },
        },
      })
      
      expect(results).toHaveNoViolations()
    })
  })
})
