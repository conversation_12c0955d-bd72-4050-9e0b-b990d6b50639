'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>ead<PERSON>, <PERSON>a<PERSON><PERSON><PERSON>oot<PERSON>, ChiaLogoIcon } from '@/components/ui/chia-logo'
import { 
  Smartphone, 
  Tablet, 
  Monitor, 
  CheckCircle, 
  AlertTriangle,
  Eye,
  Maximize2
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface BreakpointTest {
  name: string
  width: number
  icon: React.ComponentType<{ className?: string }>
  description: string
}

const breakpoints: BreakpointTest[] = [
  {
    name: 'Mobile',
    width: 375,
    icon: Smartphone,
    description: 'iPhone SE / Móviles pequeños'
  },
  {
    name: 'Mobile L',
    width: 425,
    icon: Smartphone,
    description: 'iPhone 12 Pro / Móviles grandes'
  },
  {
    name: 'Tablet',
    width: 768,
    icon: Tablet,
    description: 'iPad / Tablets'
  },
  {
    name: 'Laptop',
    width: 1024,
    icon: Monitor,
    description: 'Laptops / Pantallas medianas'
  },
  {
    name: 'Desktop',
    width: 1440,
    icon: Monitor,
    description: 'Desktop / Pantallas grandes'
  }
]

interface ResponsiveIssue {
  breakpoint: string
  component: string
  issue: string
  severity: 'error' | 'warning' | 'success'
}

export function ResponsiveTestGrid({ className }: { className?: string }) {
  const [currentBreakpoint, setCurrentBreakpoint] = useState<string>('Desktop')
  const [windowWidth, setWindowWidth] = useState<number>(1440)
  const [issues, setIssues] = useState<ResponsiveIssue[]>([])

  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth
      setWindowWidth(width)
      
      if (width < 425) {
        setCurrentBreakpoint('Mobile')
      } else if (width < 768) {
        setCurrentBreakpoint('Mobile L')
      } else if (width < 1024) {
        setCurrentBreakpoint('Tablet')
      } else if (width < 1440) {
        setCurrentBreakpoint('Laptop')
      } else {
        setCurrentBreakpoint('Desktop')
      }
    }

    handleResize()
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  // Simular pruebas de responsive
  const runResponsiveTests = () => {
    const testIssues: ResponsiveIssue[] = []

    // Pruebas para cada breakpoint
    breakpoints.forEach(bp => {
      // Test del logo
      if (bp.width < 640) {
        testIssues.push({
          breakpoint: bp.name,
          component: 'ChiaLogo',
          issue: 'Logo se adapta correctamente en móvil',
          severity: 'success'
        })
      }

      // Test de navegación
      if (bp.width < 768) {
        testIssues.push({
          breakpoint: bp.name,
          component: 'Navigation',
          issue: 'Menú hamburguesa funcional',
          severity: 'success'
        })
      }

      // Test de búsqueda
      if (bp.width < 425) {
        testIssues.push({
          breakpoint: bp.name,
          component: 'SearchBar',
          issue: 'Barra de búsqueda responsive',
          severity: 'success'
        })
      }

      // Test de z-index en móvil
      if (bp.width < 768) {
        testIssues.push({
          breakpoint: bp.name,
          component: 'Dropdowns',
          issue: 'Z-index correcto en pantallas pequeñas',
          severity: 'success'
        })
      }

      // Test de colores en diferentes pantallas
      testIssues.push({
        breakpoint: bp.name,
        component: 'Colors',
        issue: 'Colores corporativos consistentes',
        severity: 'success'
      })
    })

    setIssues(testIssues)
  }

  useEffect(() => {
    runResponsiveTests()
  }, [])

  const getIssueIcon = (severity: ResponsiveIssue['severity']) => {
    switch (severity) {
      case 'error': return <AlertTriangle className="h-4 w-4 text-red-500" />
      case 'warning': return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      case 'success': return <CheckCircle className="h-4 w-4 text-green-500" />
    }
  }

  return (
    <div className={cn("w-full space-y-6", className)}>
      {/* Current Breakpoint Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Maximize2 className="h-5 w-5 mr-2 text-chia-blue-600" />
            Estado Actual de Pantalla
          </CardTitle>
          <CardDescription>
            Información sobre el breakpoint actual y dimensiones
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Badge variant="outline" className="text-lg px-4 py-2">
                {currentBreakpoint}
              </Badge>
              <span className="text-gray-600">
                {windowWidth}px de ancho
              </span>
            </div>
            <div className="flex items-center space-x-2">
              {breakpoints.map(bp => {
                const Icon = bp.icon
                return (
                  <div
                    key={bp.name}
                    className={cn(
                      "p-2 rounded-lg",
                      currentBreakpoint === bp.name 
                        ? "bg-chia-blue-100 text-chia-blue-600" 
                        : "bg-gray-100 text-gray-400"
                    )}
                  >
                    <Icon className="h-5 w-5" />
                  </div>
                )
              })}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Breakpoint Tests */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Eye className="h-5 w-5 mr-2 text-chia-blue-600" />
            Pruebas por Breakpoint
          </CardTitle>
          <CardDescription>
            Validación de componentes en diferentes tamaños de pantalla
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {breakpoints.map(bp => {
              const Icon = bp.icon
              const bpIssues = issues.filter(issue => issue.breakpoint === bp.name)
              const errorCount = bpIssues.filter(i => i.severity === 'error').length
              const warningCount = bpIssues.filter(i => i.severity === 'warning').length
              const successCount = bpIssues.filter(i => i.severity === 'success').length

              return (
                <Card key={bp.name} className={cn(
                  "border-2",
                  currentBreakpoint === bp.name 
                    ? "border-chia-blue-300 bg-chia-blue-50" 
                    : "border-gray-200"
                )}>
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Icon className="h-5 w-5 text-chia-blue-600" />
                        <CardTitle className="text-lg">{bp.name}</CardTitle>
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {bp.width}px
                      </Badge>
                    </div>
                    <CardDescription className="text-xs">
                      {bp.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-green-600">✓ {successCount}</span>
                        <span className="text-yellow-600">⚠ {warningCount}</span>
                        <span className="text-red-600">✗ {errorCount}</span>
                      </div>
                      <div className="space-y-1">
                        {bpIssues.slice(0, 3).map((issue, index) => (
                          <div key={index} className="flex items-center space-x-2 text-xs">
                            {getIssueIcon(issue.severity)}
                            <span className="truncate">{issue.component}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Logo Responsive Test */}
      <Card>
        <CardHeader>
          <CardTitle>Prueba de Logo Responsive</CardTitle>
          <CardDescription>
            Verificación del comportamiento del logo en diferentes tamaños
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Header Logo */}
            <div className="space-y-3">
              <h4 className="font-semibold text-sm">Logo Header</h4>
              <div className="p-4 bg-white border rounded-lg">
                <ChiaLogoHeader />
              </div>
              <p className="text-xs text-gray-600">
                Se adapta automáticamente ocultando texto en móvil
              </p>
            </div>

            {/* Icon Logo */}
            <div className="space-y-3">
              <h4 className="font-semibold text-sm">Logo Icono</h4>
              <div className="p-4 bg-white border rounded-lg flex justify-center">
                <ChiaLogoIcon size="lg" />
              </div>
              <p className="text-xs text-gray-600">
                Mantiene proporciones en todos los tamaños
              </p>
            </div>

            {/* Footer Logo */}
            <div className="space-y-3">
              <h4 className="font-semibold text-sm">Logo Footer</h4>
              <div className="p-4 bg-gray-900 rounded-lg">
                <ChiaLogoFooter />
              </div>
              <p className="text-xs text-gray-600">
                Optimizado para fondos oscuros
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Color Consistency Test */}
      <Card>
        <CardHeader>
          <CardTitle>Consistencia de Colores</CardTitle>
          <CardDescription>
            Verificación de la paleta corporativa en diferentes contextos
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <div className="w-full h-16 bg-chia-blue-600 rounded flex items-center justify-center">
                <span className="text-white text-sm font-medium">Primario</span>
              </div>
              <p className="text-xs text-center">chia-blue-600</p>
            </div>
            <div className="space-y-2">
              <div className="w-full h-16 bg-chia-green-600 rounded flex items-center justify-center">
                <span className="text-white text-sm font-medium">Verde</span>
              </div>
              <p className="text-xs text-center">chia-green-600</p>
            </div>
            <div className="space-y-2">
              <div className="w-full h-16 bg-gray-100 border rounded flex items-center justify-center">
                <span className="text-gray-900 text-sm font-medium">Neutro</span>
              </div>
              <p className="text-xs text-center">gray-100</p>
            </div>
            <div className="space-y-2">
              <div className="w-full h-16 bg-white border rounded flex items-center justify-center">
                <span className="text-gray-900 text-sm font-medium">Blanco</span>
              </div>
              <p className="text-xs text-center">white</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Test Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Acciones de Prueba</CardTitle>
          <CardDescription>
            Herramientas para validar el comportamiento responsive
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-4">
            <Button onClick={runResponsiveTests} variant="outline">
              Ejecutar Pruebas
            </Button>
            <Button 
              onClick={() => window.open('/accessibility-test', '_blank')} 
              className="bg-chia-green-600 hover:bg-chia-green-700"
            >
              Ver Pruebas de Accesibilidad
            </Button>
            <Button 
              onClick={() => window.location.reload()} 
              variant="ghost"
            >
              Recargar Página
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
