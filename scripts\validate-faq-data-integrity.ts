#!/usr/bin/env tsx

/**
 * Script de Validación de Integridad de Datos FAQ
 * 
 * Este script verifica que:
 * 1. Las tablas correctas estén siendo utilizadas
 * 2. Las relaciones de foreign key sean válidas
 * 3. Los conteos de datos sean consistentes
 * 4. No haya referencias a tablas obsoletas en el código
 */

import { supabase } from '../lib/supabase/client'
import { readFileSync, readdirSync, statSync } from 'fs'
import { join } from 'path'

interface ValidationResult {
  test: string
  status: 'PASS' | 'FAIL' | 'WARNING'
  message: string
  details?: any
}

class FAQDataValidator {
  private results: ValidationResult[] = []

  /**
   * Ejecutar todas las validaciones
   */
  async runAllValidations(): Promise<void> {
    console.log('🔍 Iniciando validación de integridad de datos FAQ...\n')

    await this.validateDatabaseStructure()
    await this.validateDataConsistency()
    await this.validateForeignKeyIntegrity()
    this.validateCodeReferences()
    
    this.printResults()
  }

  /**
   * Validar estructura de base de datos
   */
  private async validateDatabaseStructure(): Promise<void> {
    console.log('📊 Validando estructura de base de datos...')

    try {
      // Verificar que las tablas principales existan y tengan datos
      const { data: municipalFaqs, error: faqError } = await supabase
        .from('municipal_faqs')
        .select('id', { count: 'exact' })
        .limit(1)

      const { data: faqThemes, error: themeError } = await supabase
        .from('faq_themes')
        .select('id', { count: 'exact' })
        .limit(1)

      if (faqError) {
        this.addResult('DB_STRUCTURE', 'FAIL', `Error accediendo a municipal_faqs: ${faqError.message}`)
      } else {
        this.addResult('DB_STRUCTURE', 'PASS', `Tabla municipal_faqs accesible con ${municipalFaqs?.length || 0} registros`)
      }

      if (themeError) {
        this.addResult('DB_STRUCTURE', 'FAIL', `Error accediendo a faq_themes: ${themeError.message}`)
      } else {
        this.addResult('DB_STRUCTURE', 'PASS', `Tabla faq_themes accesible con ${faqThemes?.length || 0} registros`)
      }

    } catch (error) {
      this.addResult('DB_STRUCTURE', 'FAIL', `Error de conexión: ${error}`)
    }
  }

  /**
   * Validar consistencia de datos
   */
  private async validateDataConsistency(): Promise<void> {
    console.log('🔢 Validando consistencia de datos...')

    try {
      // Contar FAQs activas
      const { count: faqCount, error: faqCountError } = await supabase
        .from('municipal_faqs')
        .select('*', { count: 'exact', head: true })
        .eq('is_active', true)

      // Contar temas activos
      const { count: themeCount, error: themeCountError } = await supabase
        .from('faq_themes')
        .select('*', { count: 'exact', head: true })
        .eq('is_active', true)

      if (faqCountError || themeCountError) {
        this.addResult('DATA_CONSISTENCY', 'FAIL', 'Error obteniendo conteos de datos')
      } else {
        this.addResult('DATA_CONSISTENCY', 'PASS', `FAQs activas: ${faqCount}, Temas activos: ${themeCount}`)
        
        // Verificar que haya FAQs para la mayoría de temas
        if (faqCount && themeCount && faqCount < themeCount) {
          this.addResult('DATA_CONSISTENCY', 'WARNING', 'Hay más temas que FAQs, algunos temas pueden estar vacíos')
        }
      }

      // Verificar distribución de FAQs por tema
      const { data: themeDistribution, error: distError } = await supabase
        .from('municipal_faqs')
        .select('theme_id')
        .eq('is_active', true)

      if (!distError && themeDistribution) {
        const themeCounts = new Map<string, number>()
        themeDistribution.forEach(faq => {
          if (faq.theme_id) {
            themeCounts.set(faq.theme_id, (themeCounts.get(faq.theme_id) || 0) + 1)
          }
        })

        const emptyThemes = themeCount! - themeCounts.size
        if (emptyThemes > 0) {
          this.addResult('DATA_CONSISTENCY', 'WARNING', `${emptyThemes} temas sin FAQs asociadas`)
        } else {
          this.addResult('DATA_CONSISTENCY', 'PASS', 'Todos los temas tienen FAQs asociadas')
        }
      }

    } catch (error) {
      this.addResult('DATA_CONSISTENCY', 'FAIL', `Error validando consistencia: ${error}`)
    }
  }

  /**
   * Validar integridad de foreign keys
   */
  private async validateForeignKeyIntegrity(): Promise<void> {
    console.log('🔗 Validando integridad de foreign keys...')

    try {
      // Verificar FAQs con theme_id inválido
      const { data: orphanedFaqs, error: orphanError } = await supabase
        .from('municipal_faqs')
        .select('id, theme_id')
        .eq('is_active', true)
        .is('faq_themes.id', null)
        .limit(10)

      if (orphanError) {
        this.addResult('FK_INTEGRITY', 'FAIL', `Error verificando foreign keys: ${orphanError.message}`)
      } else if (orphanedFaqs && orphanedFaqs.length > 0) {
        this.addResult('FK_INTEGRITY', 'FAIL', `${orphanedFaqs.length} FAQs con theme_id inválido`, orphanedFaqs)
      } else {
        this.addResult('FK_INTEGRITY', 'PASS', 'Todas las FAQs tienen theme_id válido')
      }

    } catch (error) {
      this.addResult('FK_INTEGRITY', 'FAIL', `Error validando foreign keys: ${error}`)
    }
  }

  /**
   * Validar referencias en código
   */
  private validateCodeReferences(): void {
    console.log('💻 Validando referencias en código...')

    const obsoletePatterns = [
      'faq_categories',
      'from(\'faqs\')',
      'category_id'
    ]

    const filesToCheck = [
      'lib/services/faqService.ts',
      'components/faq/FAQSection.tsx',
      'scripts/test-faq-service.ts'
    ]

    let foundObsoleteReferences = false

    filesToCheck.forEach(filePath => {
      try {
        const content = readFileSync(filePath, 'utf-8')
        
        obsoletePatterns.forEach(pattern => {
          if (content.includes(pattern)) {
            this.addResult('CODE_REFERENCES', 'FAIL', `Referencia obsoleta "${pattern}" encontrada en ${filePath}`)
            foundObsoleteReferences = true
          }
        })
      } catch (error) {
        this.addResult('CODE_REFERENCES', 'WARNING', `No se pudo leer archivo ${filePath}`)
      }
    })

    if (!foundObsoleteReferences) {
      this.addResult('CODE_REFERENCES', 'PASS', 'No se encontraron referencias obsoletas en archivos principales')
    }
  }

  /**
   * Agregar resultado de validación
   */
  private addResult(test: string, status: 'PASS' | 'FAIL' | 'WARNING', message: string, details?: any): void {
    this.results.push({ test, status, message, details })
  }

  /**
   * Imprimir resultados
   */
  private printResults(): void {
    console.log('\n📋 RESULTADOS DE VALIDACIÓN\n')
    console.log('=' .repeat(60))

    const passed = this.results.filter(r => r.status === 'PASS').length
    const failed = this.results.filter(r => r.status === 'FAIL').length
    const warnings = this.results.filter(r => r.status === 'WARNING').length

    this.results.forEach(result => {
      const icon = result.status === 'PASS' ? '✅' : result.status === 'FAIL' ? '❌' : '⚠️'
      console.log(`${icon} [${result.test}] ${result.message}`)
      
      if (result.details) {
        console.log(`   Detalles: ${JSON.stringify(result.details, null, 2)}`)
      }
    })

    console.log('\n' + '=' .repeat(60))
    console.log(`📊 RESUMEN: ${passed} ✅ | ${failed} ❌ | ${warnings} ⚠️`)
    
    if (failed > 0) {
      console.log('\n🚨 ACCIÓN REQUERIDA: Se encontraron errores críticos que deben ser corregidos.')
      process.exit(1)
    } else if (warnings > 0) {
      console.log('\n⚠️  ADVERTENCIAS: Se encontraron problemas menores que deberían ser revisados.')
    } else {
      console.log('\n🎉 VALIDACIÓN EXITOSA: Todos los tests pasaron correctamente.')
    }
  }
}

// Ejecutar validación si el script se ejecuta directamente
if (require.main === module) {
  const validator = new FAQDataValidator()
  validator.runAllValidations().catch(error => {
    console.error('❌ Error ejecutando validación:', error)
    process.exit(1)
  })
}

export default FAQDataValidator
