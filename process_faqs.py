#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Procesador de FAQs para el sistema municipal de Chía
Convierte el contenido extraído de FAQ.docx en estructura JSON jerárquica
"""

import json
import re
from typing import List, Dict, Any

def clean_text(text: str) -> str:
    """Limpia y normaliza texto"""
    if not text:
        return ""
    return text.strip().replace('\n', ' ').replace('\r', '')

def extract_keywords(question: str, answer: str) -> List[str]:
    """Extrae palabras clave de pregunta y respuesta"""
    text = f"{question} {answer}".lower()
    
    # Palabras clave comunes en contexto municipal
    keywords = []
    
    # Términos municipales
    municipal_terms = [
        'tramite', 'procedimiento', 'requisito', 'documento', 'certificado',
        'inscripcion', 'registro', 'solicitud', 'costo', 'gratuito',
        'presencial', 'virtual', 'linea', 'horario', 'direccion',
        'programa', 'servicio', 'beneficio', 'apoyo', 'asistencia'
    ]
    
    for term in municipal_terms:
        if term in text:
            keywords.append(term)
    
    # Extraer palabras específicas importantes
    important_words = re.findall(r'\b[a-záéíóúñ]{4,}\b', text)
    keywords.extend([word for word in important_words[:5] if word not in keywords])
    
    return keywords[:8]  # Máximo 8 palabras clave

def parse_faq_content(file_path: str) -> Dict[str, Any]:
    """Parsea el contenido del FAQ y lo estructura jerárquicamente"""
    
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = [line.strip() for line in f.readlines() if line.strip()]
    
    faqs_structure = {"faqs": []}
    current_dependencia = None
    current_subdependencia = None
    current_tema = None
    current_questions = []
    
    i = 0
    while i < len(lines):
        line = lines[i]
        
        # Detectar nueva dependencia
        if line.startswith('Dependencia:'):
            # Guardar tema anterior si existe
            if current_tema and current_questions:
                save_current_tema(faqs_structure, current_dependencia, current_subdependencia, 
                                current_tema, current_questions)
                current_questions = []
            
            current_dependencia = clean_text(line.replace('Dependencia:', ''))
            current_subdependencia = None
            current_tema = None
            
        # Detectar subdependencia
        elif line.startswith('SUBDEPENDENCIA') or line.startswith('Subdependencia'):
            current_subdependencia = clean_text(line.split(':', 1)[1] if ':' in line else line)
            
        # Detectar tema
        elif line.startswith('TEMA:') or line.startswith('Tema:'):
            # Guardar tema anterior si existe
            if current_tema and current_questions:
                save_current_tema(faqs_structure, current_dependencia, current_subdependencia, 
                                current_tema, current_questions)
                current_questions = []
            
            current_tema = clean_text(line.replace('TEMA:', '').replace('Tema:', ''))
            
        # Detectar preguntas (líneas que empiezan con ¿)
        elif line.startswith('¿') and current_dependencia:
            question = clean_text(line)
            answer_parts = []
            
            # Recopilar respuesta (líneas siguientes hasta encontrar otra pregunta o sección)
            j = i + 1
            while j < len(lines):
                next_line = lines[j]
                if (next_line.startswith('¿') or 
                    next_line.startswith('Dependencia:') or
                    next_line.startswith('SUBDEPENDENCIA') or
                    next_line.startswith('Subdependencia') or
                    next_line.startswith('TEMA:') or
                    next_line.startswith('Tema:')):
                    break
                answer_parts.append(clean_text(next_line))
                j += 1
            
            answer = ' '.join(answer_parts).strip()
            
            if answer:  # Solo agregar si hay respuesta
                keywords = extract_keywords(question, answer)
                current_questions.append({
                    "pregunta": question,
                    "respuesta": answer,
                    "palabras_clave": keywords
                })
            
            i = j - 1  # Ajustar índice
            
        i += 1
    
    # Guardar último tema si existe
    if current_tema and current_questions:
        save_current_tema(faqs_structure, current_dependencia, current_subdependencia, 
                        current_tema, current_questions)
    
    return faqs_structure

def save_current_tema(faqs_structure: Dict, dependencia: str, subdependencia: str, 
                     tema: str, questions: List[Dict]):
    """Guarda el tema actual en la estructura de FAQs"""
    
    if not dependencia or not tema or not questions:
        return
    
    # Buscar dependencia existente
    dep_entry = None
    for faq in faqs_structure["faqs"]:
        if faq["dependencia"] == dependencia:
            dep_entry = faq
            break
    
    # Crear nueva entrada de dependencia si no existe
    if not dep_entry:
        dep_entry = {
            "dependencia": dependencia,
            "codigo_dependencia": generate_dependency_code(dependencia),
            "subdependencia": subdependencia or "",
            "codigo_subdependencia": generate_subdependency_code(subdependencia) if subdependencia else "",
            "temas": []
        }
        faqs_structure["faqs"].append(dep_entry)
    
    # Agregar tema
    tema_entry = {
        "tema": tema,
        "descripcion": generate_tema_description(tema),
        "preguntas_frecuentes": questions
    }
    
    dep_entry["temas"].append(tema_entry)

def generate_dependency_code(dependencia: str) -> str:
    """Genera código de dependencia basado en el nombre"""
    if not dependencia:
        return "DEP_000"
    
    # Mapeo de dependencias conocidas
    dependency_codes = {
        "Despacho del alcalde": "DEP_001",
        "SECRETARÍA DE GOBIERNO": "DEP_002", 
        "SECRETARIA DE HACIENDA": "DEP_003",
        "SECRETARIA DE DESARROLLO SOCIAL": "DEP_004",
        "SECRETARÍA DE EDUCACIÓN": "DEP_005",
        "SECRETARIA DE SALUD": "DEP_006",
        "SECRETARÍA PARA EL DESARROLLO ECONÓMICO": "DEP_007",
        "SECRETARÍA DE MOVILIDAD": "DEP_008",
        "SECRETARÍA DE PARTICIPACIÓN CIUDADANA Y ACCIÓN COMUNITARIA": "DEP_009"
    }
    
    return dependency_codes.get(dependencia, f"DEP_{hash(dependencia) % 1000:03d}")

def generate_subdependency_code(subdependencia: str) -> str:
    """Genera código de subdependencia basado en el nombre"""
    if not subdependencia:
        return ""
    
    # Generar código basado en hash del nombre
    return f"SUB_{hash(subdependencia) % 1000:03d}"

def generate_tema_description(tema: str) -> str:
    """Genera descripción breve del tema"""
    if not tema:
        return ""
    
    # Descripción basada en palabras clave del tema
    tema_lower = tema.lower()
    
    if 'programa' in tema_lower:
        return f"Información sobre el {tema}"
    elif 'tramite' in tema_lower or 'trámite' in tema_lower:
        return f"Procedimientos y requisitos para {tema}"
    elif 'servicio' in tema_lower:
        return f"Detalles del servicio de {tema}"
    else:
        return f"Preguntas frecuentes sobre {tema}"

def main():
    """Función principal"""
    print("Procesando FAQs del municipio de Chía...")
    
    try:
        # Procesar archivo
        faqs_data = parse_faq_content('FAQ_extracted.txt')
        
        # Guardar resultado
        output_file = 'faqs_chia_estructurado.json'
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(faqs_data, f, ensure_ascii=False, indent=2)
        
        # Estadísticas
        total_deps = len(faqs_data["faqs"])
        total_temas = sum(len(dep["temas"]) for dep in faqs_data["faqs"])
        total_preguntas = sum(
            len(tema["preguntas_frecuentes"]) 
            for dep in faqs_data["faqs"] 
            for tema in dep["temas"]
        )
        
        print(f"✅ Procesamiento completado:")
        print(f"   - Dependencias: {total_deps}")
        print(f"   - Temas: {total_temas}")
        print(f"   - Preguntas: {total_preguntas}")
        print(f"   - Archivo generado: {output_file}")
        
    except Exception as e:
        print(f"❌ Error durante el procesamiento: {e}")
        raise

if __name__ == "__main__":
    main()
