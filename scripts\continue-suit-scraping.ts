/**
 * Continue SUIT Scraping
 * Script para continuar el scraping de los procedimientos restantes
 */

import { createClient } from '@supabase/supabase-js'
import puppeteer from 'puppeteer'
import * as dotenv from 'dotenv'
import * as path from 'path'
import * as fs from 'fs'

// Cargar variables de entorno
dotenv.config({ path: path.join(__dirname, '..', '.env.local') })

const supabaseUrl = process.env['NEXT_PUBLIC_SUPABASE_URL']!
const supabaseKey = process.env['SUPABASE_SERVICE_ROLE_KEY']!
const supabase = createClient(supabaseUrl, supabaseKey)

interface SuitData {
  titulo: string
  descripcionDetallada?: string
  entidadResponsable?: string
  tiempoRespuesta?: string
  costoDetallado?: string
  modalidad?: string
  puntoAtencion?: string
  rawText: string
  rawHtml: string
}

interface ScrapingResult {
  fichaId: string
  procedureId: string
  success: boolean
  data?: SuitData
  error?: string
  processingTime: number
  scrapedAt: Date
}

class ContinueSuitScraper {
  private browser: puppeteer.Browser | null = null
  private page: puppeteer.Page | null = null
  private logFile: string
  private statsFile: string
  private batchSize: number = 20
  private rateLimit: number = 3000 // 3 segundos entre requests

  constructor() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    this.logFile = path.join(__dirname, '..', 'logs', `continue-suit-${timestamp}.log`)
    this.statsFile = path.join(__dirname, '..', 'logs', `continue-suit-stats-${timestamp}.json`)
  }

  private log(level: string, message: string, data?: any): void {
    const timestamp = new Date().toISOString()
    const logEntry = `[${timestamp}] ${level}: ${message}`
    
    console.log(logEntry)
    if (data) {
      console.log(JSON.stringify(data, null, 2))
    }

    // Escribir al archivo de log
    const logLine = data ? `${logEntry}\n${JSON.stringify(data, null, 2)}\n` : `${logEntry}\n`
    fs.appendFileSync(this.logFile, logLine)
  }

  async initialize(): Promise<void> {
    this.log('INFO', '🚀 Inicializando Continue SUIT Scraper')

    this.browser = await puppeteer.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-web-security'
      ]
    })

    this.page = await this.browser.newPage()
    await this.page.setViewport({ width: 1366, height: 768 })
    
    // User agent realista
    await this.page.setUserAgent(
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    )

    this.log('INFO', '✅ Navegador inicializado')
  }

  async scrapeSuitPage(fichaId: string, procedureId: string): Promise<ScrapingResult> {
    const startTime = Date.now()
    const url = `https://visorsuit.funcionpublica.gov.co/auth/visor?fi=${fichaId}`
    
    const result: ScrapingResult = {
      fichaId,
      procedureId,
      success: false,
      processingTime: 0,
      scrapedAt: new Date()
    }

    try {
      if (!this.page) {
        throw new Error('Navegador no inicializado')
      }

      // Navegar a la página
      const response = await this.page.goto(url, { 
        waitUntil: 'domcontentloaded',
        timeout: 60000 
      })

      if (!response || response.status() !== 200) {
        throw new Error(`HTTP ${response?.status()}: Error cargando la página`)
      }

      // Esperar carga completa
      await new Promise(resolve => setTimeout(resolve, 5000))

      // Extraer datos usando la estructura conocida del sitio
      const extractedData = await this.page.evaluate(() => {
        const data: SuitData = {
          titulo: '',
          rawText: document.body.innerText,
          rawHtml: document.documentElement.outerHTML
        }

        const lines = document.body.innerText.split('\n')
          .map(line => line.trim())
          .filter(line => line.length > 0)

        // Extraer título - aparece después de "Página web de la entidad"
        let titleFound = false
        for (let i = 0; i < lines.length; i++) {
          if (lines[i].includes('Página web de la entidad') && i + 1 < lines.length) {
            // Buscar el siguiente elemento significativo
            for (let j = i + 1; j < lines.length && j < i + 5; j++) {
              const candidate = lines[j]
              if (candidate && 
                  !candidate.includes('(También se conoce como:') && 
                  candidate !== '¿Cuándo se puede realizar?' && 
                  candidate.length > 5 &&
                  !candidate.includes('http')) {
                data.titulo = candidate
                titleFound = true
                break
              }
            }
            break
          }
        }

        // Extraer entidad responsable
        const entidadIndex = lines.findIndex(line => line === 'Informacion suministrada por')
        if (entidadIndex !== -1 && entidadIndex + 1 < lines.length) {
          data.entidadResponsable = lines[entidadIndex + 1]
        }

        // Extraer información de costo
        const costoIndex = lines.findIndex(line => line === '¿Requiere pago?')
        if (costoIndex !== -1 && costoIndex + 1 < lines.length) {
          data.costoDetallado = lines[costoIndex + 1]
        }

        // Extraer modalidad
        const modalidadIndex = lines.findIndex(line => line === '¿Es totalmente en línea?')
        if (modalidadIndex !== -1 && modalidadIndex + 1 < lines.length) {
          data.modalidad = lines[modalidadIndex + 1]
        }

        // Extraer punto de atención
        const atencionIndex = lines.findIndex(line => line === '¿A dónde ir?')
        if (atencionIndex !== -1 && atencionIndex + 1 < lines.length) {
          data.puntoAtencion = lines[atencionIndex + 1]
        }

        // Extraer tiempo de respuesta
        const tiempoIndex = lines.findIndex(line => line === '¿Cuándo se puede realizar?')
        if (tiempoIndex !== -1 && tiempoIndex + 1 < lines.length) {
          data.tiempoRespuesta = lines[tiempoIndex + 1]
        }

        // Extraer descripción - buscar sección "Descripción"
        const descIndex = lines.findIndex(line => line === 'Descripción')
        if (descIndex !== -1) {
          const descLines = []
          for (let i = descIndex + 1; i < lines.length; i++) {
            const line = lines[i]
            if (line.startsWith('¿') || line === 'Encuesta' || line === 'Requisitos') {
              break
            }
            if (line.length > 10) {
              descLines.push(line)
            }
          }
          if (descLines.length > 0) {
            data.descripcionDetallada = descLines.join(' ')
          }
        }

        return data
      })

      if (extractedData.titulo && extractedData.titulo.length > 5) {
        result.success = true
        result.data = extractedData
      } else {
        result.error = 'No se pudo extraer título válido'
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Error desconocido'
      result.error = errorMessage
    }

    result.processingTime = Date.now() - startTime
    return result
  }

  async saveResult(result: ScrapingResult): Promise<void> {
    try {
      if (result.success && result.data) {
        const { error } = await supabase
          .from('suit_scraped_data')
          .update({
            titulo: result.data.titulo,
            descripcion_detallada: result.data.descripcionDetallada,
            entidad_responsable: result.data.entidadResponsable,
            tiempo_respuesta: result.data.tiempoRespuesta,
            costo_detallado: result.data.costoDetallado,
            raw_html: result.data.rawHtml,
            raw_text: result.data.rawText,
            scraping_status: 'success',
            error_message: null,
            scraped_at: result.scrapedAt.toISOString()
          })
          .eq('ficha_id', result.fichaId)

        if (error) {
          this.log('ERROR', `Error guardando resultado exitoso para ficha ${result.fichaId}`, { error })
        }
      } else {
        const { error } = await supabase
          .from('suit_scraped_data')
          .update({
            scraping_status: 'failed',
            error_message: result.error,
            scraped_at: result.scrapedAt.toISOString()
          })
          .eq('ficha_id', result.fichaId)

        if (error) {
          this.log('ERROR', `Error guardando resultado fallido para ficha ${result.fichaId}`, { error })
        }
      }
    } catch (error) {
      this.log('ERROR', `Error guardando resultado para ficha ${result.fichaId}`, { error })
    }
  }

  async runContinuousScraping(): Promise<void> {
    try {
      await this.initialize()

      let totalProcessed = 0
      let totalSuccess = 0
      let totalFailed = 0

      while (true) {
        // Obtener siguiente lote de procedimientos pendientes
        const { data: procedures, error } = await supabase
          .rpc('get_procedures_needing_scraping')

        if (error) {
          this.log('ERROR', 'Error obteniendo procedimientos', { error })
          break
        }

        if (!procedures || procedures.length === 0) {
          this.log('INFO', '🎉 No hay más procedimientos pendientes')
          break
        }

        const batch = procedures.slice(0, this.batchSize)
        this.log('INFO', `📦 Procesando lote de ${batch.length} procedimientos`)

        for (let i = 0; i < batch.length; i++) {
          const procedure = batch[i]
          const fichaId = procedure.suit_link.match(/fi=(\d+)/)?.[1]
          
          if (!fichaId) {
            this.log('WARN', `No se pudo extraer ficha ID de: ${procedure.suit_link}`)
            continue
          }

          this.log('INFO', `📄 [${totalProcessed + 1}] ${procedure.name || 'Sin nombre'}`)
          
          const result = await this.scrapeSuitPage(fichaId, procedure.id)
          await this.saveResult(result)

          totalProcessed++
          if (result.success) {
            totalSuccess++
            this.log('INFO', `✅ Éxito: ${result.data?.titulo}`)
          } else {
            totalFailed++
            this.log('WARN', `❌ Fallo: ${result.error}`)
          }

          // Rate limiting
          if (i < batch.length - 1) {
            await new Promise(resolve => setTimeout(resolve, this.rateLimit))
          }
        }

        // Estadísticas del lote
        const successRate = (totalSuccess / totalProcessed * 100).toFixed(2)
        this.log('INFO', `📊 Progreso: ${totalProcessed} procesados, ${totalSuccess} exitosos (${successRate}%)`)

        // Pausa entre lotes
        await new Promise(resolve => setTimeout(resolve, 10000))
      }

      // Estadísticas finales
      const finalSuccessRate = (totalSuccess / totalProcessed * 100).toFixed(2)
      this.log('INFO', '🎯 ESTADÍSTICAS FINALES')
      this.log('INFO', `Total procesados: ${totalProcessed}`)
      this.log('INFO', `Exitosos: ${totalSuccess}`)
      this.log('INFO', `Fallidos: ${totalFailed}`)
      this.log('INFO', `Tasa de éxito: ${finalSuccessRate}%`)

    } catch (error) {
      this.log('ERROR', 'Error durante el scraping continuo', { error })
    } finally {
      await this.cleanup()
    }
  }

  async cleanup(): Promise<void> {
    if (this.browser) {
      await this.browser.close()
      this.log('INFO', '🧹 Navegador cerrado')
    }
  }
}

// Ejecutar si es el módulo principal
if (require.main === module) {
  console.log('🚀 INICIANDO SCRAPING CONTINUO DE SUIT')
  console.log('=' .repeat(50))
  
  const scraper = new ContinueSuitScraper()
  scraper.runContinuousScraping()
    .then(() => {
      console.log('🎉 Scraping continuo completado')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Error en el scraping continuo:', error)
      process.exit(1)
    })
}

export { ContinueSuitScraper }
