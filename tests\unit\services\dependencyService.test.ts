import DependencyService from '@/lib/services/dependencyService'

// Mock de los datos JSON
jest.mock('@/tramites_chia_optimo.json', () => [
  {
    "Nombre": "Test Trámite 1",
    "dependencia": "Secretaría de Hacienda",
    "codigo_dependencia": "040",
    "codigo_subdependencia": "041"
  },
  {
    "Nombre": "Test Trámite 2", 
    "dependencia": "Secretaría de Planeación",
    "codigo_dependencia": "010",
    "codigo_subdependencia": "011"
  },
  {
    "Nombre": "Test Trámite 3",
    "dependencia": "Secretaría de Hacienda", 
    "codigo_dependencia": "040",
    "codigo_subdependencia": "041"
  }
])

jest.mock('@/OPA-chia-optimo.json', () => ({
  dependencias: {
    "040": {
      "nombre": "Secretaría de Hacienda",
      "sigla": "SH",
      "subdependencias": {
        "040": {
          "nombre": "Directo",
          "sigla": "SH",
          "OPA": [
            {
              "codigo_OPA": "1",
              "OPA": "Test OPA 1"
            },
            {
              "codigo_OPA": "2", 
              "OPA": "Test OPA 2"
            }
          ]
        }
      }
    },
    "010": {
      "nombre": "Secretaría de Planeación",
      "sigla": "SP",
      "subdependencias": {
        "010": {
          "nombre": "Directo",
          "sigla": "SP",
          "OPA": [
            {
              "codigo_OPA": "1",
              "OPA": "Test OPA Planeación"
            }
          ]
        }
      }
    }
  }
}))

// Mock de Supabase
jest.mock('@/lib/supabase/client', () => ({
  supabase: {
    from: jest.fn(() => ({
      select: jest.fn(() => Promise.resolve({ data: [], error: null }))
    }))
  }
}))

describe('DependencyService', () => {
  beforeEach(() => {
    // Reset singleton instance for each test
    (DependencyService as any).instance = null
  })

  describe('getAllDependencies', () => {
    it('debería retornar todas las dependencias ordenadas por total de procedimientos', async () => {
      const dependencies = await DependencyService.getAllDependencies()
      
      expect(dependencies).toHaveLength(2)
      expect(dependencies[0].name).toBe('Secretaría de Hacienda')
      expect(dependencies[0].tramitesCount).toBe(2)
      expect(dependencies[0].opasCount).toBe(2)
      expect(dependencies[0].totalProcedures).toBe(4)
      
      expect(dependencies[1].name).toBe('Secretaría de Planeación')
      expect(dependencies[1].tramitesCount).toBe(1)
      expect(dependencies[1].opasCount).toBe(1)
      expect(dependencies[1].totalProcedures).toBe(2)
    })

    it('debería incluir información completa de cada dependencia', async () => {
      const dependencies = await DependencyService.getAllDependencies()
      const hacienda = dependencies.find(d => d.id === '040')
      
      expect(hacienda).toBeDefined()
      expect(hacienda?.name).toBe('Secretaría de Hacienda')
      expect(hacienda?.sigla).toBe('SH')
      expect(hacienda?.description).toContain('financiera')
      expect(hacienda?.icon).toBe('banknote')
      expect(hacienda?.color).toContain('bg-')
    })
  })

  describe('getDependencyById', () => {
    it('debería retornar una dependencia específica por ID', async () => {
      const dependency = await DependencyService.getDependencyById('040')
      
      expect(dependency).toBeDefined()
      expect(dependency?.name).toBe('Secretaría de Hacienda')
      expect(dependency?.id).toBe('040')
    })

    it('debería retornar null para ID inexistente', async () => {
      const dependency = await DependencyService.getDependencyById('999')
      expect(dependency).toBeNull()
    })
  })

  describe('getDependencyStats', () => {
    it('debería retornar estadísticas correctas', async () => {
      const stats = await DependencyService.getDependencyStats()
      
      expect(stats.totalDependencies).toBe(2)
      expect(stats.totalTramites).toBe(3)
      expect(stats.totalOPAs).toBe(3)
      expect(stats.totalProcedures).toBe(6)
      expect(stats.averageProceduresPerDependency).toBe(3)
    })
  })

  describe('searchDependencies', () => {
    it('debería buscar dependencias por nombre', async () => {
      const results = await DependencyService.searchDependencies('Hacienda')
      
      expect(results).toHaveLength(1)
      expect(results[0].name).toBe('Secretaría de Hacienda')
    })

    it('debería buscar dependencias por sigla', async () => {
      const results = await DependencyService.searchDependencies('SH')
      
      expect(results).toHaveLength(1)
      expect(results[0].sigla).toBe('SH')
    })

    it('debería retornar todas las dependencias para búsqueda vacía', async () => {
      const results = await DependencyService.searchDependencies('')
      expect(results).toHaveLength(2)
    })

    it('debería ser case-insensitive', async () => {
      const results = await DependencyService.searchDependencies('hacienda')
      expect(results).toHaveLength(1)
    })

    it('debería retornar array vacío para búsqueda sin coincidencias', async () => {
      const results = await DependencyService.searchDependencies('NoExiste')
      expect(results).toHaveLength(0)
    })
  })

  describe('getProceduresByDependency', () => {
    it('debería retornar procedimientos de una dependencia específica', async () => {
      const result = await DependencyService.getProceduresByDependency('040')
      
      expect(result).toBeDefined()
      expect(result?.dependency.name).toBe('Secretaría de Hacienda')
      expect(result?.tramites).toHaveLength(2)
      expect(result?.opas).toHaveLength(2)
    })

    it('debería retornar null para dependencia inexistente', async () => {
      const result = await DependencyService.getProceduresByDependency('999')
      expect(result).toBeNull()
    })
  })

  describe('getTopDependencies', () => {
    it('debería retornar las dependencias más populares', async () => {
      const topDeps = await DependencyService.getTopDependencies(1)
      
      expect(topDeps).toHaveLength(1)
      expect(topDeps[0].name).toBe('Secretaría de Hacienda')
      expect(topDeps[0].totalProcedures).toBe(4)
    })

    it('debería respetar el límite especificado', async () => {
      const topDeps = await DependencyService.getTopDependencies(2)
      expect(topDeps).toHaveLength(2)
    })

    it('debería usar límite por defecto de 6', async () => {
      const topDeps = await DependencyService.getTopDependencies()
      expect(topDeps.length).toBeLessThanOrEqual(6)
    })
  })

  describe('Métodos auxiliares', () => {
    it('debería extraer siglas correctamente', async () => {
      const dependencies = await DependencyService.getAllDependencies()
      const hacienda = dependencies.find(d => d.name === 'Secretaría de Hacienda')
      const planeacion = dependencies.find(d => d.name === 'Secretaría de Planeación')
      
      expect(hacienda?.sigla).toBe('SH')
      expect(planeacion?.sigla).toBe('SP')
    })

    it('debería asignar iconos apropiados', async () => {
      const dependencies = await DependencyService.getAllDependencies()
      const hacienda = dependencies.find(d => d.name === 'Secretaría de Hacienda')
      const planeacion = dependencies.find(d => d.name === 'Secretaría de Planeación')
      
      expect(hacienda?.icon).toBe('banknote')
      expect(planeacion?.icon).toBe('map')
    })

    it('debería asignar colores únicos', async () => {
      const dependencies = await DependencyService.getAllDependencies()
      
      dependencies.forEach(dep => {
        expect(dep.color).toContain('bg-')
        expect(dep.color).toContain('border-')
        expect(dep.color).toContain('hover:')
      })
    })

    it('debería generar descripciones apropiadas', async () => {
      const dependencies = await DependencyService.getAllDependencies()
      
      dependencies.forEach(dep => {
        expect(dep.description).toBeDefined()
        expect(dep.description!.length).toBeGreaterThan(0)
      })
    })
  })

  describe('Inicialización', () => {
    it('debería inicializar solo una vez (singleton)', async () => {
      const service1 = DependencyService
      const service2 = DependencyService
      
      expect(service1).toBe(service2)
    })

    it('debería manejar errores de inicialización', async () => {
      // Mock error en procesamiento
      const originalConsoleError = console.error
      console.error = jest.fn()
      
      // Simular error en datos
      jest.doMock('@/data/tramites_chia_optimo.json', () => {
        throw new Error('Error loading data')
      })
      
      try {
        await DependencyService.getAllDependencies()
      } catch (error) {
        expect(error).toBeDefined()
      }
      
      console.error = originalConsoleError
    })
  })
})
