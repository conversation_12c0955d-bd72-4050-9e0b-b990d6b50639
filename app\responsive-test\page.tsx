import { ResponsiveTestGrid } from '@/components/testing/ResponsiveTestGrid'
import { PublicLayout } from '@/components/layout/PublicLayout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { ChiaLogoHeader } from '@/components/ui/chia-logo'
import { 
  Smartphone, 
  Tablet, 
  Monitor, 
  CheckCircle,
  Maximize2
} from 'lucide-react'

export default function ResponsiveTestPage() {
  return (
    <PublicLayout>
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center space-x-3 mb-4">
              <ChiaLogoHeader />
            </div>
            <h1 className="text-3xl font-bold text-chia-blue-900 mb-2">
              Pruebas de Diseño Responsive
            </h1>
            <p className="text-lg text-gray-600">
              Validación del comportamiento del portal en diferentes dispositivos y tamaños de pantalla
            </p>
          </div>

          {/* Instructions */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Maximize2 className="h-5 w-5 mr-2 text-chia-blue-600" />
                Instrucciones de Prueba
              </CardTitle>
              <CardDescription>
                Cómo realizar las pruebas de responsive design
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="flex items-start space-x-3">
                  <div className="w-8 h-8 bg-chia-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-chia-blue-600 font-bold text-sm">1</span>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-1">Redimensionar Ventana</h4>
                    <p className="text-sm text-gray-600">
                      Cambia el tamaño de la ventana del navegador para simular diferentes dispositivos
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <div className="w-8 h-8 bg-chia-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-chia-blue-600 font-bold text-sm">2</span>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-1">Usar DevTools</h4>
                    <p className="text-sm text-gray-600">
                      Utiliza las herramientas de desarrollador (F12) para simular dispositivos específicos
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <div className="w-8 h-8 bg-chia-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-chia-blue-600 font-bold text-sm">3</span>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-1">Verificar Componentes</h4>
                    <p className="text-sm text-gray-600">
                      Observa cómo se adaptan los logos, menús, búsqueda y filtros
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Responsive Test Grid */}
          <ResponsiveTestGrid />

          {/* Breakpoint Reference */}
          <Card className="mt-8">
            <CardHeader>
              <CardTitle>Referencia de Breakpoints</CardTitle>
              <CardDescription>
                Puntos de quiebre utilizados en el diseño responsive
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left py-2">Dispositivo</th>
                      <th className="text-left py-2">Ancho Mínimo</th>
                      <th className="text-left py-2">Clase Tailwind</th>
                      <th className="text-left py-2">Características</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y">
                    <tr>
                      <td className="py-3 flex items-center">
                        <Smartphone className="h-4 w-4 mr-2 text-gray-500" />
                        Mobile
                      </td>
                      <td className="py-3">0px - 639px</td>
                      <td className="py-3">
                        <code className="bg-gray-100 px-2 py-1 rounded text-xs">base</code>
                      </td>
                      <td className="py-3 text-gray-600">Menú hamburguesa, logo compacto</td>
                    </tr>
                    <tr>
                      <td className="py-3 flex items-center">
                        <Smartphone className="h-4 w-4 mr-2 text-gray-500" />
                        Mobile L
                      </td>
                      <td className="py-3">640px+</td>
                      <td className="py-3">
                        <code className="bg-gray-100 px-2 py-1 rounded text-xs">sm:</code>
                      </td>
                      <td className="py-3 text-gray-600">Texto adicional visible</td>
                    </tr>
                    <tr>
                      <td className="py-3 flex items-center">
                        <Tablet className="h-4 w-4 mr-2 text-gray-500" />
                        Tablet
                      </td>
                      <td className="py-3">768px+</td>
                      <td className="py-3">
                        <code className="bg-gray-100 px-2 py-1 rounded text-xs">md:</code>
                      </td>
                      <td className="py-3 text-gray-600">Navegación expandida</td>
                    </tr>
                    <tr>
                      <td className="py-3 flex items-center">
                        <Monitor className="h-4 w-4 mr-2 text-gray-500" />
                        Laptop
                      </td>
                      <td className="py-3">1024px+</td>
                      <td className="py-3">
                        <code className="bg-gray-100 px-2 py-1 rounded text-xs">lg:</code>
                      </td>
                      <td className="py-3 text-gray-600">Layout completo</td>
                    </tr>
                    <tr>
                      <td className="py-3 flex items-center">
                        <Monitor className="h-4 w-4 mr-2 text-gray-500" />
                        Desktop
                      </td>
                      <td className="py-3">1280px+</td>
                      <td className="py-3">
                        <code className="bg-gray-100 px-2 py-1 rounded text-xs">xl:</code>
                      </td>
                      <td className="py-3 text-gray-600">Máximo ancho, espaciado óptimo</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>

          {/* Test Results Summary */}
          <Card className="mt-8">
            <CardHeader>
              <CardTitle className="flex items-center">
                <CheckCircle className="h-5 w-5 mr-2 text-green-600" />
                Resumen de Implementación
              </CardTitle>
              <CardDescription>
                Estado de las mejoras implementadas
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="p-4 bg-green-50 rounded-lg">
                  <h4 className="font-semibold text-green-900 mb-2">✅ Logo Oficial</h4>
                  <p className="text-sm text-green-700">
                    Implementado con SVG escalable y variantes responsive
                  </p>
                </div>
                
                <div className="p-4 bg-green-50 rounded-lg">
                  <h4 className="font-semibold text-green-900 mb-2">✅ Colores Corporativos</h4>
                  <p className="text-sm text-green-700">
                    Chia-green como primario, cumple WCAG 2.1 AA
                  </p>
                </div>
                
                <div className="p-4 bg-green-50 rounded-lg">
                  <h4 className="font-semibold text-green-900 mb-2">✅ Z-Index Corregido</h4>
                  <p className="text-sm text-green-700">
                    Jerarquía clara, sin superposiciones visuales
                  </p>
                </div>
                
                <div className="p-4 bg-green-50 rounded-lg">
                  <h4 className="font-semibold text-green-900 mb-2">✅ Responsive Design</h4>
                  <p className="text-sm text-green-700">
                    Adaptación completa a todos los dispositivos
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </PublicLayout>
  )
}
