import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { ProcedureSearch } from '@/components/procedures/ProcedureSearch'

// Mock data
const mockProcedures = [
  {
    id: 1,
    name: 'Certificado de Residencia',
    description: 'Certificado que acredita la residencia en el municipio',
    category: 'Certificados',
    dependency: 'Secretaría General',
    cost: 15000,
    duration_days: 5,
    requirements: ['Cédula de ciudadanía', 'Recibo de servicios públicos'],
    status: 'active',
  },
  {
    id: 2,
    name: 'Licencia de Construcción',
    description: 'Permiso para construcción de vivienda',
    category: 'Licencias',
    dependency: 'Planeación',
    cost: 500000,
    duration_days: 30,
    requirements: ['Planos arquitectónicos', 'Estudio de suelos'],
    status: 'active',
  },
  {
    id: 3,
    name: 'Paz y Salvo Predial',
    description: 'Certificado de estar al día con impuestos prediales',
    category: 'Certificados',
    dependency: 'Hacienda',
    cost: 25000,
    duration_days: 3,
    requirements: ['Cédula de ciudadanía', 'Escritura del predio'],
    status: 'active',
  },
]

// Mock the search hook
const mockSetSearchResults = jest.fn()
const mockSetLoading = jest.fn()

jest.mock('@/hooks/useProcedureSearch', () => ({
  useProcedureSearch: () => ({
    searchResults: mockProcedures,
    loading: false,
    error: null,
    searchProcedures: jest.fn(),
    setSearchResults: mockSetSearchResults,
    setLoading: mockSetLoading,
  }),
}))

describe('ProcedureSearch', () => {
  const user = userEvent.setup()

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders search interface correctly', () => {
    render(<ProcedureSearch />)

    // Check for search input
    expect(screen.getByPlaceholderText(/Buscar trámites/i)).toBeInTheDocument()
    
    // Check for filter options
    expect(screen.getByText('Filtros')).toBeInTheDocument()
    expect(screen.getByText('Categoría')).toBeInTheDocument()
    expect(screen.getByText('Dependencia')).toBeInTheDocument()
    expect(screen.getByText('Costo')).toBeInTheDocument()
  })

  it('performs text search correctly', async () => {
    const mockSearchProcedures = jest.fn()
    jest.doMock('@/hooks/useProcedureSearch', () => ({
      useProcedureSearch: () => ({
        searchResults: mockProcedures.filter(p => 
          p.name.toLowerCase().includes('certificado')
        ),
        loading: false,
        error: null,
        searchProcedures: mockSearchProcedures,
        setSearchResults: mockSetSearchResults,
        setLoading: mockSetLoading,
      }),
    }))

    render(<ProcedureSearch />)

    const searchInput = screen.getByPlaceholderText(/Buscar trámites/i)
    await user.type(searchInput, 'certificado')

    await waitFor(() => {
      expect(mockSearchProcedures).toHaveBeenCalledWith({
        query: 'certificado',
        filters: {},
      })
    })
  })

  it('displays search results correctly', async () => {
    render(<ProcedureSearch />)

    await waitFor(() => {
      // Check that all procedures are displayed
      expect(screen.getByText('Certificado de Residencia')).toBeInTheDocument()
      expect(screen.getByText('Licencia de Construcción')).toBeInTheDocument()
      expect(screen.getByText('Paz y Salvo Predial')).toBeInTheDocument()
    })

    // Check procedure details
    expect(screen.getByText('$15.000')).toBeInTheDocument()
    expect(screen.getByText('5 días')).toBeInTheDocument()
    expect(screen.getByText('Secretaría General')).toBeInTheDocument()
  })

  it('filters by category correctly', async () => {
    render(<ProcedureSearch />)

    // Open category filter
    const categoryFilter = screen.getByText('Categoría')
    await user.click(categoryFilter)

    // Select "Certificados" category
    const certificadosOption = screen.getByText('Certificados')
    await user.click(certificadosOption)

    await waitFor(() => {
      // Should show only certificate procedures
      expect(screen.getByText('Certificado de Residencia')).toBeInTheDocument()
      expect(screen.getByText('Paz y Salvo Predial')).toBeInTheDocument()
      expect(screen.queryByText('Licencia de Construcción')).not.toBeInTheDocument()
    })
  })

  it('filters by dependency correctly', async () => {
    render(<ProcedureSearch />)

    // Open dependency filter
    const dependencyFilter = screen.getByText('Dependencia')
    await user.click(dependencyFilter)

    // Select "Planeación" dependency
    const planeacionOption = screen.getByText('Planeación')
    await user.click(planeacionOption)

    await waitFor(() => {
      // Should show only Planeación procedures
      expect(screen.getByText('Licencia de Construcción')).toBeInTheDocument()
      expect(screen.queryByText('Certificado de Residencia')).not.toBeInTheDocument()
    })
  })

  it('filters by cost range correctly', async () => {
    render(<ProcedureSearch />)

    // Open cost filter
    const costFilter = screen.getByText('Costo')
    await user.click(costFilter)

    // Select cost range
    const lowCostOption = screen.getByText('Hasta $50.000')
    await user.click(lowCostOption)

    await waitFor(() => {
      // Should show only low-cost procedures
      expect(screen.getByText('Certificado de Residencia')).toBeInTheDocument()
      expect(screen.getByText('Paz y Salvo Predial')).toBeInTheDocument()
      expect(screen.queryByText('Licencia de Construcción')).not.toBeInTheDocument()
    })
  })

  it('combines multiple filters correctly', async () => {
    render(<ProcedureSearch />)

    // Apply category filter
    const categoryFilter = screen.getByText('Categoría')
    await user.click(categoryFilter)
    const certificadosOption = screen.getByText('Certificados')
    await user.click(certificadosOption)

    // Apply cost filter
    const costFilter = screen.getByText('Costo')
    await user.click(costFilter)
    const lowCostOption = screen.getByText('Hasta $50.000')
    await user.click(lowCostOption)

    await waitFor(() => {
      // Should show procedures that match both filters
      expect(screen.getByText('Certificado de Residencia')).toBeInTheDocument()
      expect(screen.getByText('Paz y Salvo Predial')).toBeInTheDocument()
      expect(screen.queryByText('Licencia de Construcción')).not.toBeInTheDocument()
    })
  })

  it('clears filters correctly', async () => {
    render(<ProcedureSearch />)

    // Apply a filter first
    const categoryFilter = screen.getByText('Categoría')
    await user.click(categoryFilter)
    const certificadosOption = screen.getByText('Certificados')
    await user.click(certificadosOption)

    // Clear filters
    const clearButton = screen.getByText('Limpiar Filtros')
    await user.click(clearButton)

    await waitFor(() => {
      // Should show all procedures again
      expect(screen.getByText('Certificado de Residencia')).toBeInTheDocument()
      expect(screen.getByText('Licencia de Construcción')).toBeInTheDocument()
      expect(screen.getByText('Paz y Salvo Predial')).toBeInTheDocument()
    })
  })

  it('displays loading state correctly', () => {
    jest.doMock('@/hooks/useProcedureSearch', () => ({
      useProcedureSearch: () => ({
        searchResults: [],
        loading: true,
        error: null,
        searchProcedures: jest.fn(),
        setSearchResults: mockSetSearchResults,
        setLoading: mockSetLoading,
      }),
    }))

    render(<ProcedureSearch />)

    expect(screen.getByTestId('search-loading')).toBeInTheDocument()
  })

  it('displays error state correctly', () => {
    jest.doMock('@/hooks/useProcedureSearch', () => ({
      useProcedureSearch: () => ({
        searchResults: [],
        loading: false,
        error: 'Error al cargar los trámites',
        searchProcedures: jest.fn(),
        setSearchResults: mockSetSearchResults,
        setLoading: mockSetLoading,
      }),
    }))

    render(<ProcedureSearch />)

    expect(screen.getByText('Error al cargar los trámites')).toBeInTheDocument()
  })

  it('displays empty state correctly', () => {
    jest.doMock('@/hooks/useProcedureSearch', () => ({
      useProcedureSearch: () => ({
        searchResults: [],
        loading: false,
        error: null,
        searchProcedures: jest.fn(),
        setSearchResults: mockSetSearchResults,
        setLoading: mockSetLoading,
      }),
    }))

    render(<ProcedureSearch />)

    expect(screen.getByText('No se encontraron trámites')).toBeInTheDocument()
  })

  it('handles procedure selection correctly', async () => {
    const mockOnSelect = jest.fn()
    render(<ProcedureSearch onProcedureSelect={mockOnSelect} />)

    const procedureCard = screen.getByText('Certificado de Residencia')
    await user.click(procedureCard)

    expect(mockOnSelect).toHaveBeenCalledWith(mockProcedures[0])
  })

  it('displays procedure requirements correctly', async () => {
    render(<ProcedureSearch />)

    // Click on a procedure to see details
    const procedureCard = screen.getByText('Certificado de Residencia')
    await user.click(procedureCard)

    await waitFor(() => {
      expect(screen.getByText('Requisitos:')).toBeInTheDocument()
      expect(screen.getByText('Cédula de ciudadanía')).toBeInTheDocument()
      expect(screen.getByText('Recibo de servicios públicos')).toBeInTheDocument()
    })
  })

  it('is accessible', () => {
    const { container } = render(<ProcedureSearch />)

    // Check for proper form labels
    const searchInput = screen.getByPlaceholderText(/Buscar trámites/i)
    expect(searchInput).toHaveAttribute('aria-label', 'Buscar trámites')

    // Check for proper button roles
    const buttons = screen.getAllByRole('button')
    expect(buttons.length).toBeGreaterThan(0)

    // Check for proper heading structure
    const headings = container.querySelectorAll('h1, h2, h3, h4, h5, h6')
    expect(headings.length).toBeGreaterThan(0)
  })

  it('supports keyboard navigation', async () => {
    render(<ProcedureSearch />)

    const searchInput = screen.getByPlaceholderText(/Buscar trámites/i)
    
    // Focus on search input
    searchInput.focus()
    expect(searchInput).toHaveFocus()

    // Tab to next element
    await user.tab()
    
    // Should focus on filter button
    const filterButton = screen.getByText('Filtros')
    expect(filterButton).toHaveFocus()
  })

  it('debounces search input correctly', async () => {
    const mockSearchProcedures = jest.fn()
    jest.doMock('@/hooks/useProcedureSearch', () => ({
      useProcedureSearch: () => ({
        searchResults: mockProcedures,
        loading: false,
        error: null,
        searchProcedures: mockSearchProcedures,
        setSearchResults: mockSetSearchResults,
        setLoading: mockSetLoading,
      }),
    }))

    render(<ProcedureSearch />)

    const searchInput = screen.getByPlaceholderText(/Buscar trámites/i)
    
    // Type quickly
    await user.type(searchInput, 'cert')
    
    // Should not call search immediately
    expect(mockSearchProcedures).not.toHaveBeenCalled()

    // Wait for debounce
    await waitFor(() => {
      expect(mockSearchProcedures).toHaveBeenCalledWith({
        query: 'cert',
        filters: {},
      })
    }, { timeout: 1000 })
  })
})
