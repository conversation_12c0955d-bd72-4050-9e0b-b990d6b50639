/**
 * @jest-environment jsdom
 */

import { render, screen } from '@testing-library/react'
import { ProcedureCardEnhanced } from '@/components/procedures/ProcedureCardEnhanced'
import { PublicProcedureDetailModal } from '@/components/procedures/PublicProcedureDetailModal'

// Mock data para pruebas de extracción de costos
const mockProcedureWithUVT = {
  id: '1',
  name: 'Certificado con costo UVT',
  description: 'Formulario: No. Tiempo de respuesta: 15 días hábiles. ¿Tiene pago?: 0,2 UVT',
  has_cost: true,
  cost_description: 'Costo en UVT - Consultar descripción completa',
  procedure_type: 'TRAMITE' as const,
  dependency: { id: 'dep1', name: 'Secretaría de Planeación' },
  subdependency: { id: 'sub1', name: 'Oficina de Licencias' }
}

const mockProcedureWithSMLDV = {
  id: '2',
  name: 'Trámite con costo SMLDV',
  description: 'Formulario: No. Tiempo de respuesta: 15 días hábiles. ¿Tiene pago?: 1.5 SMLDV',
  has_cost: true,
  cost_description: 'Costo en SMLDV - Consultar descripción completa',
  procedure_type: 'TRAMITE' as const,
  dependency: { id: 'dep1', name: 'Secretaría de Planeación' },
  subdependency: { id: 'sub1', name: 'Oficina de Licencias' }
}

const mockProcedureWithVariableTariff = {
  id: '3',
  name: 'Servicio con tarifa variable',
  description: 'Pago: Las tarifas están establecidas según estrato socioeconómico',
  has_cost: true,
  cost_description: 'Tarifa variable según estrato - Consultar descripción completa',
  procedure_type: 'TRAMITE' as const,
  dependency: { id: 'dep1', name: 'Secretaría de Planeación' },
  subdependency: { id: 'sub1', name: 'Oficina de Licencias' }
}

const mockProcedureFree = {
  id: '4',
  name: 'Certificado gratuito',
  description: 'Formulario: Solicitud certificado. Tiempo de respuesta: 15 días hábiles. ¿Tiene pago?: No',
  has_cost: false,
  cost_description: 'Gratuito',
  procedure_type: 'TRAMITE' as const,
  dependency: { id: 'dep1', name: 'Secretaría de Planeación' },
  subdependency: { id: 'sub1', name: 'Oficina de Licencias' }
}

const mockProcedureWithNumericCost = {
  id: '5',
  name: 'Trámite con costo numérico',
  description: 'Trámite con costo específico',
  cost: 50000,
  has_cost: true,
  cost_description: 'Gratuito', // Caso donde cost_description no refleja el costo real
  procedure_type: 'TRAMITE' as const,
  dependency: { id: 'dep1', name: 'Secretaría de Planeación' },
  subdependency: { id: 'sub1', name: 'Oficina de Licencias' }
}

const mockOnViewDetails = jest.fn()
const mockOnToggleFavorite = jest.fn()
const mockOnClose = jest.fn()

describe('Extracción de Información de Costos', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('ProcedureCardEnhanced - Formateo de Costos', () => {
    test('debe mostrar información de costo UVT correctamente', () => {
      render(
        <ProcedureCardEnhanced
          procedure={mockProcedureWithUVT}
          onViewDetails={mockOnViewDetails}
          onToggleFavorite={mockOnToggleFavorite}
        />
      )

      expect(screen.getByText('Costo en UVT - Consultar descripción completa')).toBeInTheDocument()
    })

    test('debe mostrar información de costo SMLDV correctamente', () => {
      render(
        <ProcedureCardEnhanced
          procedure={mockProcedureWithSMLDV}
          onViewDetails={mockOnViewDetails}
          onToggleFavorite={mockOnToggleFavorite}
        />
      )

      expect(screen.getByText('Costo en SMLDV - Consultar descripción completa')).toBeInTheDocument()
    })

    test('debe mostrar tarifa variable correctamente', () => {
      render(
        <ProcedureCardEnhanced
          procedure={mockProcedureWithVariableTariff}
          onViewDetails={mockOnViewDetails}
          onToggleFavorite={mockOnToggleFavorite}
        />
      )

      expect(screen.getByText('Tarifa variable según estrato - Consultar descripción completa')).toBeInTheDocument()
    })

    test('debe mostrar "Gratuito" para procedimientos sin costo', () => {
      render(
        <ProcedureCardEnhanced
          procedure={mockProcedureFree}
          onViewDetails={mockOnViewDetails}
          onToggleFavorite={mockOnToggleFavorite}
        />
      )

      expect(screen.getByText('Gratuito')).toBeInTheDocument()
    })

    test('debe priorizar costo numérico sobre cost_description cuando hay conflicto', () => {
      render(
        <ProcedureCardEnhanced
          procedure={mockProcedureWithNumericCost}
          onViewDetails={mockOnViewDetails}
          onToggleFavorite={mockOnToggleFavorite}
        />
      )

      // Debe mostrar el costo numérico formateado, no "Gratuito"
      expect(screen.getByText('$50.000')).toBeInTheDocument()
    })

    test('debe mostrar "Tiene costo - Consultar" cuando has_cost es true pero no hay información específica', () => {
      const procedureWithGenericCost = {
        ...mockProcedureFree,
        has_cost: true,
        cost_description: undefined,
        cost: undefined
      }

      render(
        <ProcedureCardEnhanced
          procedure={procedureWithGenericCost}
          onViewDetails={mockOnViewDetails}
          onToggleFavorite={mockOnToggleFavorite}
        />
      )

      expect(screen.getByText('Tiene costo - Consultar')).toBeInTheDocument()
    })
  })

  describe('PublicProcedureDetailModal - Formateo de Costos', () => {
    test('debe mostrar información de costo UVT en el modal', () => {
      render(
        <PublicProcedureDetailModal
          procedure={mockProcedureWithUVT}
          onClose={mockOnClose}
        />
      )

      expect(screen.getAllByText('Costo en UVT - Consultar descripción completa')).toHaveLength(2)
    })

    test('debe mostrar información de costo SMLDV en el modal', () => {
      render(
        <PublicProcedureDetailModal
          procedure={mockProcedureWithSMLDV}
          onClose={mockOnClose}
        />
      )

      expect(screen.getAllByText('Costo en SMLDV - Consultar descripción completa')).toHaveLength(2)
    })

    test('debe mostrar "Gratuito" en el modal para procedimientos sin costo', () => {
      render(
        <PublicProcedureDetailModal
          procedure={mockProcedureFree}
          onClose={mockOnClose}
        />
      )

      expect(screen.getAllByText('Gratuito')).toHaveLength(2)
    })

    test('debe mostrar costo numérico formateado en el modal', () => {
      render(
        <PublicProcedureDetailModal
          procedure={mockProcedureWithNumericCost}
          onClose={mockOnClose}
        />
      )

      expect(screen.getAllByText('$50.000')).toHaveLength(2)
    })
  })

  describe('Casos Edge de Extracción de Costos', () => {
    test('debe manejar procedimiento sin información de costo', () => {
      const procedureWithoutCostInfo = {
        id: '6',
        name: 'Procedimiento sin info de costo',
        description: 'Descripción sin información de costos',
        procedure_type: 'TRAMITE' as const,
        dependency: { id: 'dep1', name: 'Secretaría de Planeación' },
        subdependency: { id: 'sub1', name: 'Oficina de Licencias' }
      }

      render(
        <ProcedureCardEnhanced
          procedure={procedureWithoutCostInfo}
          onViewDetails={mockOnViewDetails}
          onToggleFavorite={mockOnToggleFavorite}
        />
      )

      expect(screen.getByText('Gratuito')).toBeInTheDocument()
    })

    test('debe manejar procedimiento con cost_description vacío', () => {
      const procedureWithEmptyCostDescription = {
        ...mockProcedureFree,
        cost_description: '',
        has_cost: false
      }

      render(
        <ProcedureCardEnhanced
          procedure={procedureWithEmptyCostDescription}
          onViewDetails={mockOnViewDetails}
          onToggleFavorite={mockOnToggleFavorite}
        />
      )

      expect(screen.getByText('Gratuito')).toBeInTheDocument()
    })

    test('debe manejar procedimiento con costo 0 pero has_cost true', () => {
      const procedureWithZeroCost = {
        ...mockProcedureFree,
        cost: 0,
        has_cost: true,
        cost_description: 'Costo específico'
      }

      render(
        <ProcedureCardEnhanced
          procedure={procedureWithZeroCost}
          onViewDetails={mockOnViewDetails}
          onToggleFavorite={mockOnToggleFavorite}
        />
      )

      // Debe mostrar cost_description cuando cost es 0 pero has_cost es true
      expect(screen.getByText('Costo específico')).toBeInTheDocument()
    })
  })

  describe('Integración con Vista Unificada', () => {
    test('debe manejar datos de la vista unificada correctamente', () => {
      const unifiedViewData = {
        id: '7',
        name: 'Procedimiento de vista unificada',
        has_cost: true,
        cost_description: 'Costo en UVT - Consultar descripción completa',
        procedure_type: 'TRAMITE' as const,
        dependency: { id: 'dep1', name: 'Secretaría de Planeación' },
        subdependency: { id: 'sub1', name: 'Oficina de Licencias' }
      }

      render(
        <ProcedureCardEnhanced
          procedure={unifiedViewData}
          onViewDetails={mockOnViewDetails}
          onToggleFavorite={mockOnToggleFavorite}
        />
      )

      expect(screen.getByText('Costo en UVT - Consultar descripción completa')).toBeInTheDocument()
    })
  })
})
