import React from 'react'
import { render, screen } from '@testing-library/react'
import { 
  RoleGuard, 
  ShowForCitizen, 
  ShowForAdmin, 
  ShowForSuperAdmin,
  ShowForAdminOrSuperAdmin,
  HideForCitizen,
  ConditionalContent
} from '@/components/auth/RoleGuard'
import { mockUsers, setupMockAuthSuccess, cleanupMocks } from '@/tests/utils/test-helpers'

// Mock hooks
jest.mock('@/hooks/useAuth', () => ({
  useAuth: jest.fn(),
}))

jest.mock('@/hooks/useRole', () => ({
  useRole: jest.fn(),
}))

jest.mock('@/hooks/usePermissions', () => ({
  usePermissions: jest.fn(),
}))

const mockUseAuth = require('@/hooks/useAuth').useAuth
const mockUseRole = require('@/hooks/useRole').useRole
const mockUsePermissions = require('@/hooks/usePermissions').usePermissions

describe('RoleGuard Components', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  afterEach(() => {
    cleanupMocks()
  })

  describe('RoleGuard', () => {
    it('shows content when user has required role', () => {
      mockUseAuth.mockReturnValue({
        isAuthenticated: true,
        user: mockUsers.ciudadano,
      })
      
      mockUseRole.mockReturnValue({
        hasRole: jest.fn((role) => role === 'ciudadano'),
        hasAnyRole: jest.fn((roles) => roles.includes('ciudadano')),
      })
      
      mockUsePermissions.mockReturnValue({})

      render(
        <RoleGuard roles={['ciudadano']}>
          <div>Citizen content</div>
        </RoleGuard>
      )

      expect(screen.getByText('Citizen content')).toBeInTheDocument()
    })

    it('hides content when user lacks required role', () => {
      mockUseAuth.mockReturnValue({
        isAuthenticated: true,
        user: mockUsers.ciudadano,
      })
      
      mockUseRole.mockReturnValue({
        hasRole: jest.fn((role) => role === 'ciudadano'),
        hasAnyRole: jest.fn((roles) => roles.includes('ciudadano')),
      })
      
      mockUsePermissions.mockReturnValue({})

      render(
        <RoleGuard roles={['admin']}>
          <div>Admin content</div>
        </RoleGuard>
      )

      expect(screen.queryByText('Admin content')).not.toBeInTheDocument()
    })

    it('shows fallback when user lacks required role', () => {
      mockUseAuth.mockReturnValue({
        isAuthenticated: true,
        user: mockUsers.ciudadano,
      })
      
      mockUseRole.mockReturnValue({
        hasRole: jest.fn((role) => role === 'ciudadano'),
        hasAnyRole: jest.fn((roles) => roles.includes('ciudadano')),
      })
      
      mockUsePermissions.mockReturnValue({})

      render(
        <RoleGuard roles={['admin']} fallback={<div>Access denied</div>}>
          <div>Admin content</div>
        </RoleGuard>
      )

      expect(screen.getByText('Access denied')).toBeInTheDocument()
      expect(screen.queryByText('Admin content')).not.toBeInTheDocument()
    })

    it('hides content when user is not authenticated', () => {
      mockUseAuth.mockReturnValue({
        isAuthenticated: false,
        user: null,
      })
      
      mockUseRole.mockReturnValue({
        hasRole: jest.fn(() => false),
        hasAnyRole: jest.fn(() => false),
      })
      
      mockUsePermissions.mockReturnValue({})

      render(
        <RoleGuard roles={['ciudadano']}>
          <div>Protected content</div>
        </RoleGuard>
      )

      expect(screen.queryByText('Protected content')).not.toBeInTheDocument()
    })

    it('works with requireAll=true', () => {
      mockUseAuth.mockReturnValue({
        isAuthenticated: true,
        user: mockUsers.admin,
      })
      
      mockUseRole.mockReturnValue({
        hasRole: jest.fn((role) => ['admin', 'ciudadano'].includes(role)),
        hasAnyRole: jest.fn((roles) => roles.some(role => ['admin', 'ciudadano'].includes(role))),
      })
      
      mockUsePermissions.mockReturnValue({})

      render(
        <RoleGuard roles={['admin', 'ciudadano']} requireAll={true}>
          <div>Multi-role content</div>
        </RoleGuard>
      )

      expect(screen.getByText('Multi-role content')).toBeInTheDocument()
    })
  })

  describe('ShowForCitizen', () => {
    it('shows content for citizen users', () => {
      mockUseAuth.mockReturnValue({
        isAuthenticated: true,
        user: mockUsers.ciudadano,
      })
      
      mockUseRole.mockReturnValue({
        hasRole: jest.fn((role) => role === 'ciudadano'),
        hasAnyRole: jest.fn((roles) => roles.includes('ciudadano')),
      })
      
      mockUsePermissions.mockReturnValue({})

      render(
        <ShowForCitizen>
          <div>Citizen only content</div>
        </ShowForCitizen>
      )

      expect(screen.getByText('Citizen only content')).toBeInTheDocument()
    })

    it('hides content for non-citizen users', () => {
      mockUseAuth.mockReturnValue({
        isAuthenticated: true,
        user: mockUsers.admin,
      })
      
      mockUseRole.mockReturnValue({
        hasRole: jest.fn((role) => role === 'admin'),
        hasAnyRole: jest.fn((roles) => roles.includes('admin')),
      })
      
      mockUsePermissions.mockReturnValue({})

      render(
        <ShowForCitizen>
          <div>Citizen only content</div>
        </ShowForCitizen>
      )

      expect(screen.queryByText('Citizen only content')).not.toBeInTheDocument()
    })
  })

  describe('ShowForAdmin', () => {
    it('shows content for admin users', () => {
      mockUseAuth.mockReturnValue({
        isAuthenticated: true,
        user: mockUsers.admin,
      })
      
      mockUseRole.mockReturnValue({
        hasRole: jest.fn((role) => role === 'admin'),
        hasAnyRole: jest.fn((roles) => roles.includes('admin')),
      })
      
      mockUsePermissions.mockReturnValue({})

      render(
        <ShowForAdmin>
          <div>Admin only content</div>
        </ShowForAdmin>
      )

      expect(screen.getByText('Admin only content')).toBeInTheDocument()
    })

    it('hides content for non-admin users', () => {
      mockUseAuth.mockReturnValue({
        isAuthenticated: true,
        user: mockUsers.ciudadano,
      })
      
      mockUseRole.mockReturnValue({
        hasRole: jest.fn((role) => role === 'ciudadano'),
        hasAnyRole: jest.fn((roles) => roles.includes('ciudadano')),
      })
      
      mockUsePermissions.mockReturnValue({})

      render(
        <ShowForAdmin>
          <div>Admin only content</div>
        </ShowForAdmin>
      )

      expect(screen.queryByText('Admin only content')).not.toBeInTheDocument()
    })
  })

  describe('ShowForSuperAdmin', () => {
    it('shows content for super admin users', () => {
      mockUseAuth.mockReturnValue({
        isAuthenticated: true,
        user: mockUsers.superAdmin,
      })
      
      mockUseRole.mockReturnValue({
        hasRole: jest.fn((role) => role === 'super_admin'),
        hasAnyRole: jest.fn((roles) => roles.includes('super_admin')),
      })
      
      mockUsePermissions.mockReturnValue({})

      render(
        <ShowForSuperAdmin>
          <div>Super admin only content</div>
        </ShowForSuperAdmin>
      )

      expect(screen.getByText('Super admin only content')).toBeInTheDocument()
    })

    it('hides content for non-super-admin users', () => {
      mockUseAuth.mockReturnValue({
        isAuthenticated: true,
        user: mockUsers.admin,
      })
      
      mockUseRole.mockReturnValue({
        hasRole: jest.fn((role) => role === 'admin'),
        hasAnyRole: jest.fn((roles) => roles.includes('admin')),
      })
      
      mockUsePermissions.mockReturnValue({})

      render(
        <ShowForSuperAdmin>
          <div>Super admin only content</div>
        </ShowForSuperAdmin>
      )

      expect(screen.queryByText('Super admin only content')).not.toBeInTheDocument()
    })
  })

  describe('ShowForAdminOrSuperAdmin', () => {
    it('shows content for admin users', () => {
      mockUseAuth.mockReturnValue({
        isAuthenticated: true,
        user: mockUsers.admin,
      })
      
      mockUseRole.mockReturnValue({
        hasRole: jest.fn((role) => role === 'admin'),
        hasAnyRole: jest.fn((roles) => roles.some(r => ['admin', 'super_admin'].includes(r))),
      })
      
      mockUsePermissions.mockReturnValue({})

      render(
        <ShowForAdminOrSuperAdmin>
          <div>Admin or super admin content</div>
        </ShowForAdminOrSuperAdmin>
      )

      expect(screen.getByText('Admin or super admin content')).toBeInTheDocument()
    })

    it('shows content for super admin users', () => {
      mockUseAuth.mockReturnValue({
        isAuthenticated: true,
        user: mockUsers.superAdmin,
      })
      
      mockUseRole.mockReturnValue({
        hasRole: jest.fn((role) => role === 'super_admin'),
        hasAnyRole: jest.fn((roles) => roles.some(r => ['admin', 'super_admin'].includes(r))),
      })
      
      mockUsePermissions.mockReturnValue({})

      render(
        <ShowForAdminOrSuperAdmin>
          <div>Admin or super admin content</div>
        </ShowForAdminOrSuperAdmin>
      )

      expect(screen.getByText('Admin or super admin content')).toBeInTheDocument()
    })

    it('hides content for citizen users', () => {
      mockUseAuth.mockReturnValue({
        isAuthenticated: true,
        user: mockUsers.ciudadano,
      })
      
      mockUseRole.mockReturnValue({
        hasRole: jest.fn((role) => role === 'ciudadano'),
        hasAnyRole: jest.fn((roles) => roles.includes('ciudadano')),
      })
      
      mockUsePermissions.mockReturnValue({})

      render(
        <ShowForAdminOrSuperAdmin>
          <div>Admin or super admin content</div>
        </ShowForAdminOrSuperAdmin>
      )

      expect(screen.queryByText('Admin or super admin content')).not.toBeInTheDocument()
    })
  })

  describe('HideForCitizen', () => {
    it('hides content for citizen users', () => {
      mockUseAuth.mockReturnValue({
        isAuthenticated: true,
        user: mockUsers.ciudadano,
      })
      
      mockUseRole.mockReturnValue({
        hasRole: jest.fn((role) => role === 'ciudadano'),
        hasAnyRole: jest.fn((roles) => roles.includes('ciudadano')),
      })
      
      mockUsePermissions.mockReturnValue({})

      render(
        <HideForCitizen>
          <div>Hidden from citizens</div>
        </HideForCitizen>
      )

      expect(screen.queryByText('Hidden from citizens')).not.toBeInTheDocument()
    })

    it('shows content for admin users', () => {
      mockUseAuth.mockReturnValue({
        isAuthenticated: true,
        user: mockUsers.admin,
      })
      
      mockUseRole.mockReturnValue({
        hasRole: jest.fn((role) => role === 'admin'),
        hasAnyRole: jest.fn((roles) => roles.includes('admin')),
      })
      
      mockUsePermissions.mockReturnValue({})

      render(
        <HideForCitizen>
          <div>Hidden from citizens</div>
        </HideForCitizen>
      )

      expect(screen.getByText('Hidden from citizens')).toBeInTheDocument()
    })
  })

  describe('ConditionalContent', () => {
    it('shows citizen content for citizen users', () => {
      mockUseAuth.mockReturnValue({
        isAuthenticated: true,
        user: mockUsers.ciudadano,
      })
      
      mockUseRole.mockReturnValue({
        hasRole: jest.fn((role) => role === 'ciudadano'),
        hasAnyRole: jest.fn((roles) => roles.includes('ciudadano')),
      })
      
      mockUsePermissions.mockReturnValue({})

      render(
        <ConditionalContent
          citizenContent="Citizen view"
          adminContent="Admin view"
          superAdminContent="Super admin view"
        >
          Default content
        </ConditionalContent>
      )

      expect(screen.getByText('Citizen view')).toBeInTheDocument()
      expect(screen.queryByText('Admin view')).not.toBeInTheDocument()
      expect(screen.queryByText('Super admin view')).not.toBeInTheDocument()
      expect(screen.queryByText('Default content')).not.toBeInTheDocument()
    })

    it('shows admin content for admin users', () => {
      mockUseAuth.mockReturnValue({
        isAuthenticated: true,
        user: mockUsers.admin,
      })
      
      mockUseRole.mockReturnValue({
        hasRole: jest.fn((role) => role === 'admin'),
        hasAnyRole: jest.fn((roles) => roles.includes('admin')),
      })
      
      mockUsePermissions.mockReturnValue({})

      render(
        <ConditionalContent
          citizenContent="Citizen view"
          adminContent="Admin view"
          superAdminContent="Super admin view"
        >
          Default content
        </ConditionalContent>
      )

      expect(screen.queryByText('Citizen view')).not.toBeInTheDocument()
      expect(screen.getByText('Admin view')).toBeInTheDocument()
      expect(screen.queryByText('Super admin view')).not.toBeInTheDocument()
      expect(screen.queryByText('Default content')).not.toBeInTheDocument()
    })

    it('shows default content when no role-specific content matches', () => {
      mockUseAuth.mockReturnValue({
        isAuthenticated: true,
        user: mockUsers.ciudadano,
      })
      
      mockUseRole.mockReturnValue({
        hasRole: jest.fn((role) => role === 'ciudadano'),
        hasAnyRole: jest.fn((roles) => roles.includes('ciudadano')),
      })
      
      mockUsePermissions.mockReturnValue({})

      render(
        <ConditionalContent
          adminContent="Admin view"
          superAdminContent="Super admin view"
        >
          Default content
        </ConditionalContent>
      )

      expect(screen.queryByText('Admin view')).not.toBeInTheDocument()
      expect(screen.queryByText('Super admin view')).not.toBeInTheDocument()
      expect(screen.getByText('Default content')).toBeInTheDocument()
    })
  })
})
