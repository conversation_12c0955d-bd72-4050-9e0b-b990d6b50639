/**
 * Página del Chatbot Municipal
 * Interfaz principal para interactuar con el asistente virtual de Chía
 */

import { Metadata } from 'next';
import { ChatInterface } from '@/components/chat/ChatInterface';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Bot, MessageCircle, Search, FileText, Clock, Shield } from 'lucide-react';

export const metadata: Metadata = {
  title: 'Asistente Virtual | Municipio de Chía',
  description: 'Chatbot inteligente para consultas sobre trámites y servicios municipales de Chía',
};

/**
 * Página principal del chatbot
 */
export default function ChatPage() {
  return (
    <div className="container mx-auto px-4 py-6 max-w-7xl">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center gap-3 mb-2">
          <Bot className="w-8 h-8 text-blue-600" />
          <h1 className="text-3xl font-bold text-gray-900">
            Asistente Virtual
          </h1>
        </div>
        <p className="text-lg text-gray-600">
          Tu asistente inteligente para trámites y servicios del municipio de Chía
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Panel principal del chat */}
        <div className="lg:col-span-3">
          <ChatInterface 
            className="h-[700px]"
            maxHeight="500px"
            showSources={true}
          />
        </div>

        {/* Panel lateral con información */}
        <div className="space-y-4">
          {/* Capacidades del asistente */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <MessageCircle className="w-5 h-5" />
                ¿En qué puedo ayudarte?
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-start gap-2">
                <Search className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="font-medium text-sm">Buscar trámites</p>
                  <p className="text-xs text-gray-600">
                    Encuentra información sobre procedimientos municipales
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-2">
                <FileText className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="font-medium text-sm">Consultar OPAs</p>
                  <p className="text-xs text-gray-600">
                    Información sobre Otras Prestaciones de Atención
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-2">
                <Clock className="w-4 h-4 text-orange-600 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="font-medium text-sm">Tiempos y costos</p>
                  <p className="text-xs text-gray-600">
                    Conoce requisitos, tiempos de respuesta y tarifas
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-2">
                <Shield className="w-4 h-4 text-purple-600 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="font-medium text-sm">Marco legal</p>
                  <p className="text-xs text-gray-600">
                    Información sobre normativas y regulaciones
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Ejemplos de consultas */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Ejemplos de consultas</CardTitle>
              <CardDescription>
                Prueba estas preguntas para comenzar
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="p-2 bg-gray-50 rounded text-sm">
                "¿Cómo puedo obtener mi certificado de residencia?"
              </div>
              <div className="p-2 bg-gray-50 rounded text-sm">
                "¿Cuáles son los requisitos para el permiso de construcción?"
              </div>
              <div className="p-2 bg-gray-50 rounded text-sm">
                "¿Dónde puedo pagar el impuesto predial?"
              </div>
              <div className="p-2 bg-gray-50 rounded text-sm">
                "¿Qué documentos necesito para registrar mi negocio?"
              </div>
            </CardContent>
          </Card>

          {/* Información adicional */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Información importante</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3 text-sm">
              <div>
                <p className="font-medium text-green-700">✓ Información actualizada</p>
                <p className="text-gray-600">
                  Basada en la normativa vigente del municipio
                </p>
              </div>
              
              <div>
                <p className="font-medium text-blue-700">✓ Respuestas precisas</p>
                <p className="text-gray-600">
                  Generadas a partir de fuentes oficiales
                </p>
              </div>
              
              <div>
                <p className="font-medium text-purple-700">✓ Disponible 24/7</p>
                <p className="text-gray-600">
                  Asistencia virtual las 24 horas del día
                </p>
              </div>
              
              <div className="pt-2 border-t">
                <p className="text-xs text-gray-500">
                  Para casos complejos o trámites específicos, 
                  recomendamos contactar directamente con la 
                  dependencia correspondiente.
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Contacto de emergencia */}
          <Card className="border-orange-200 bg-orange-50">
            <CardHeader>
              <CardTitle className="text-lg text-orange-800">
                ¿Necesitas ayuda adicional?
              </CardTitle>
            </CardHeader>
            <CardContent className="text-sm">
              <p className="text-orange-700 mb-2">
                Si no encuentras la información que buscas:
              </p>
              <ul className="space-y-1 text-orange-600">
                <li>📞 Línea de atención: (601) 123-4567</li>
                <li>📧 Email: <EMAIL></li>
                <li>🏛️ Visita presencial: Alcaldía Municipal</li>
                <li>⏰ Horario: Lunes a Viernes, 8:00 AM - 5:00 PM</li>
              </ul>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
