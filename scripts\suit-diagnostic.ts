/**
 * SUIT Diagnostic Tool
 * Herramienta para diagnosticar problemas de acceso al sitio SUIT
 */

import { createClient } from '@supabase/supabase-js'
import puppeteer from 'puppeteer'
import * as dotenv from 'dotenv'
import * as path from 'path'

// Cargar variables de entorno
dotenv.config({ path: path.join(__dirname, '..', '.env.local') })

const supabaseUrl = process.env['NEXT_PUBLIC_SUPABASE_URL']!
const supabaseKey = process.env['SUPABASE_SERVICE_ROLE_KEY']!
const supabase = createClient(supabaseUrl, supabaseKey)

interface DiagnosticResult {
  fichaId: string
  url: string
  accessible: boolean
  requiresAuth: boolean
  httpStatus?: number
  pageTitle?: string
  contentLength?: number
  errorMessage?: string
  screenshots?: string[]
}

class SuitDiagnostic {
  private browser: puppeteer.Browser | null = null
  private page: puppeteer.Page | null = null

  async initialize(): Promise<void> {
    console.log('🔍 Inicializando diagnóstico SUIT...')
    
    this.browser = await puppeteer.launch({
      headless: false, // Modo visible para diagnóstico
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage'
      ]
    })

    this.page = await this.browser.newPage()
    await this.page.setViewport({ width: 1366, height: 768 })
    
    // User agent más realista
    await this.page.setUserAgent(
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    )

    console.log('✅ Navegador inicializado')
  }

  async testSuitAccess(fichaId: string): Promise<DiagnosticResult> {
    const url = `https://visorsuit.funcionpublica.gov.co/auth/visor?fi=${fichaId}`
    console.log(`\n🧪 Probando acceso a ficha ${fichaId}`)
    console.log(`📍 URL: ${url}`)

    const result: DiagnosticResult = {
      fichaId,
      url,
      accessible: false,
      requiresAuth: false
    }

    try {
      if (!this.page) {
        throw new Error('Navegador no inicializado')
      }

      // Navegar a la página
      console.log('🌐 Navegando...')
      const response = await this.page.goto(url, { 
        waitUntil: 'domcontentloaded',
        timeout: 30000 
      })

      result.httpStatus = response?.status()
      console.log(`📊 Estado HTTP: ${result.httpStatus}`)

      if (result.httpStatus !== 200) {
        result.errorMessage = `HTTP ${result.httpStatus}`
        return result
      }

      // Esperar carga completa
      await new Promise(resolve => setTimeout(resolve, 5000))

      // Obtener información básica de la página
      const pageInfo = await this.page.evaluate(() => {
        return {
          title: document.title,
          contentLength: document.body.innerText.length,
          hasLoginForm: !!document.querySelector('form[action*="login"], input[type="password"], .login'),
          hasAuthError: document.body.innerText.includes('autenticación') || 
                       document.body.innerText.includes('login') ||
                       document.body.innerText.includes('unauthorized'),
          hasJSError: document.body.innerText.includes('__name is not defined') ||
                     document.body.innerText.includes('ReferenceError') ||
                     document.body.innerText.includes('TypeError'),
          bodyText: document.body.innerText.substring(0, 500)
        }
      })

      result.pageTitle = pageInfo.title
      result.contentLength = pageInfo.contentLength
      result.requiresAuth = pageInfo.hasLoginForm || pageInfo.hasAuthError

      console.log(`📄 Título: ${pageInfo.title}`)
      console.log(`📏 Longitud contenido: ${pageInfo.contentLength} caracteres`)
      console.log(`🔐 Requiere autenticación: ${result.requiresAuth ? 'SÍ' : 'NO'}`)

      if (pageInfo.hasJSError) {
        result.errorMessage = 'Error de JavaScript detectado'
        console.log('❌ Error de JavaScript detectado')
      }

      if (pageInfo.hasAuthError) {
        result.errorMessage = 'Autenticación requerida'
        console.log('🔒 Autenticación requerida')
      }

      // Tomar screenshot para análisis
      const screenshotPath = path.join(__dirname, '..', 'logs', `suit-diagnostic-${fichaId}.png`)
      await this.page.screenshot({ path: screenshotPath, fullPage: true })
      result.screenshots = [screenshotPath]
      console.log(`📸 Screenshot guardado: ${screenshotPath}`)

      // Mostrar muestra del contenido
      console.log('📝 Muestra del contenido:')
      console.log(pageInfo.bodyText)

      if (pageInfo.contentLength > 100 && !pageInfo.hasJSError && !pageInfo.hasAuthError) {
        result.accessible = true
        console.log('✅ Página accesible')
      } else {
        console.log('❌ Página no accesible o con problemas')
      }

    } catch (error) {
      result.errorMessage = error instanceof Error ? error.message : 'Error desconocido'
      console.log(`💥 Error: ${result.errorMessage}`)
    }

    return result
  }

  async runDiagnostic(): Promise<void> {
    console.log('🚀 INICIANDO DIAGNÓSTICO SUIT')
    console.log('=' .repeat(50))

    try {
      await this.initialize()

      // Obtener algunas fichas para probar
      const { data: procedures, error } = await supabase
        .from('procedures')
        .select('id, name, suit_link')
        .not('suit_link', 'is', null)
        .neq('suit_link', '')
        .limit(5)

      if (error) {
        console.error('Error obteniendo procedimientos:', error)
        return
      }

      if (!procedures || procedures.length === 0) {
        console.log('No se encontraron procedimientos con enlaces SUIT')
        return
      }

      console.log(`📋 Probando ${procedures.length} procedimientos...`)

      const results: DiagnosticResult[] = []

      for (const procedure of procedures) {
        const fichaId = procedure.suit_link.match(/fi=(\d+)/)?.[1]
        if (!fichaId) {
          console.log(`⚠️  No se pudo extraer ficha ID de: ${procedure.suit_link}`)
          continue
        }

        const result = await this.testSuitAccess(fichaId)
        results.push(result)

        // Pausa entre pruebas
        await new Promise(resolve => setTimeout(resolve, 3000))
      }

      // Resumen de resultados
      console.log('\n📊 RESUMEN DE DIAGNÓSTICO')
      console.log('=' .repeat(50))

      const accessible = results.filter(r => r.accessible).length
      const authRequired = results.filter(r => r.requiresAuth).length
      const errors = results.filter(r => r.errorMessage).length

      console.log(`✅ Páginas accesibles: ${accessible}/${results.length}`)
      console.log(`🔐 Requieren autenticación: ${authRequired}/${results.length}`)
      console.log(`❌ Con errores: ${errors}/${results.length}`)

      if (authRequired > 0) {
        console.log('\n🔒 PROBLEMA IDENTIFICADO: El sitio SUIT requiere autenticación')
        console.log('💡 Soluciones posibles:')
        console.log('   1. Implementar autenticación automática')
        console.log('   2. Usar APIs alternativas si están disponibles')
        console.log('   3. Contactar con la entidad para acceso programático')
      }

      if (errors > 0) {
        console.log('\n❌ ERRORES DETECTADOS:')
        results.filter(r => r.errorMessage).forEach(r => {
          console.log(`   - Ficha ${r.fichaId}: ${r.errorMessage}`)
        })
      }

    } catch (error) {
      console.error('💥 Error durante el diagnóstico:', error)
    } finally {
      await this.cleanup()
    }
  }

  async cleanup(): Promise<void> {
    if (this.browser) {
      await this.browser.close()
      console.log('🧹 Navegador cerrado')
    }
  }
}

// Ejecutar diagnóstico
if (require.main === module) {
  const diagnostic = new SuitDiagnostic()
  diagnostic.runDiagnostic()
    .then(() => {
      console.log('🎉 Diagnóstico completado')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Error en el diagnóstico:', error)
      process.exit(1)
    })
}

export { SuitDiagnostic }
