/**
 * RAG (Retrieval-Augmented Generation) System
 * Sistema de generación aumentada por recuperación para el chatbot municipal
 */

import { generateEmbedding, createChatCompletion, createStreamingChatCompletion } from './openai';
import { 
  searchSimilarProcedures, 
  searchSimilarOPAsRPC, 
  searchKnowledgeBase, 
  searchAllContent,
  ProcedureSearchResult,
  OPASearchResult,
  KnowledgeBaseSearchResult,
  AllContentSearchResult
} from './supabase-vector';

/**
 * Tipos para el sistema RAG
 */
export interface RAGContext {
  procedures: ProcedureSearchResult[];
  opas: OPASearchResult[];
  knowledgeBase: KnowledgeBaseSearchResult[];
  allContent: AllContentSearchResult[];
}

export interface RAGResponse {
  answer: string;
  sources: Array<{
    id: string;
    type: 'procedure' | 'opa' | 'knowledge';
    title: string;
    content: string;
    similarity: number;
  }>;
  context: RAGContext;
}

export interface RAGStreamResponse {
  stream: ReadableStream;
  sources: Array<{
    id: string;
    type: 'procedure' | 'opa' | 'knowledge';
    title: string;
    content: string;
    similarity: number;
  }>;
}

/**
 * Configuración del sistema RAG
 */
export const RAG_CONFIG = {
  MAX_CONTEXT_LENGTH: 4000, // Máximo de caracteres para el contexto
  MIN_SIMILARITY_THRESHOLD: 0.75, // Umbral mínimo de similitud
  MAX_SOURCES_PER_TYPE: 3, // Máximo de fuentes por tipo de contenido
  SYSTEM_PROMPT: `Eres un asistente virtual especializado en trámites y servicios del municipio de Chía, Cundinamarca, Colombia.

Tu función es ayudar a los ciudadanos con información sobre:
- Trámites municipales (procedures)
- Otras Prestaciones de Atención (OPAs)
- Servicios municipales
- Procesos administrativos

Instrucciones importantes:
1. Responde ÚNICAMENTE basándote en la información proporcionada en el contexto
2. Si no tienes información suficiente, indica claramente que necesitas más detalles
3. Proporciona respuestas claras, precisas y útiles
4. Incluye información sobre requisitos, costos, tiempos de respuesta cuando esté disponible
5. Mantén un tono profesional pero amigable
6. Si hay múltiples opciones, explica las diferencias claramente

Formato de respuesta:
- Respuesta directa a la pregunta
- Información relevante sobre el trámite/servicio
- Requisitos necesarios (si aplica)
- Costos y tiempos (si aplica)
- Pasos a seguir (si aplica)`,
} as const;

/**
 * Función principal para generar respuestas RAG
 */
export async function generateRAGResponse(
  query: string,
  userRole: string = 'ciudadano',
  options: {
    includeAllContent?: boolean;
    maxSources?: number;
    temperature?: number;
  } = {}
): Promise<RAGResponse> {
  const {
    includeAllContent = true,
    maxSources = RAG_CONFIG.MAX_SOURCES_PER_TYPE,
    temperature = 0.3
  } = options;

  try {
    // 1. Generar embedding de la consulta
    const queryEmbedding = await generateEmbedding(query);

    // 2. Buscar contenido relevante en paralelo
    const [procedures, opas, knowledgeBase, allContent] = await Promise.all([
      searchSimilarProcedures(queryEmbedding, {
        matchThreshold: RAG_CONFIG.MIN_SIMILARITY_THRESHOLD,
        matchCount: maxSources
      }),
      searchSimilarOPAsRPC(queryEmbedding, {
        matchThreshold: RAG_CONFIG.MIN_SIMILARITY_THRESHOLD,
        matchCount: maxSources
      }),
      searchKnowledgeBase(queryEmbedding, {
        matchThreshold: RAG_CONFIG.MIN_SIMILARITY_THRESHOLD,
        matchCount: maxSources
      }),
      includeAllContent ? searchAllContent(queryEmbedding, {
        matchThreshold: RAG_CONFIG.MIN_SIMILARITY_THRESHOLD,
        matchCount: maxSources * 2
      }) : []
    ]);

    // 3. Construir contexto y fuentes
    const context: RAGContext = { procedures, opas, knowledgeBase, allContent };
    const sources = buildSources(context);
    const contextText = buildContextText(context);

    // 4. Generar respuesta con OpenAI
    const messages = [
      {
        role: 'system' as const,
        content: `${RAG_CONFIG.SYSTEM_PROMPT}\n\nRol del usuario: ${userRole}\n\nContexto disponible:\n${contextText}`
      },
      {
        role: 'user' as const,
        content: query
      }
    ];

    const response = await createChatCompletion({
      messages,
      temperature,
      max_tokens: 1000
    });

    return {
      answer: response.choices[0]?.message?.content || 'No se pudo generar una respuesta.',
      sources,
      context
    };

  } catch (error) {
    console.error('Error generating RAG response:', error);
    throw new Error(`Failed to generate RAG response: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Función para generar respuestas RAG con streaming
 */
export async function generateRAGStreamResponse(
  query: string,
  userRole: string = 'ciudadano',
  options: {
    includeAllContent?: boolean;
    maxSources?: number;
    temperature?: number;
  } = {}
): Promise<RAGStreamResponse> {
  const {
    includeAllContent = true,
    maxSources = RAG_CONFIG.MAX_SOURCES_PER_TYPE,
    temperature = 0.3
  } = options;

  try {
    // 1. Generar embedding de la consulta
    const queryEmbedding = await generateEmbedding(query);

    // 2. Buscar contenido relevante en paralelo
    const [procedures, opas, knowledgeBase, allContent] = await Promise.all([
      searchSimilarProcedures(queryEmbedding, {
        matchThreshold: RAG_CONFIG.MIN_SIMILARITY_THRESHOLD,
        matchCount: maxSources
      }),
      searchSimilarOPAsRPC(queryEmbedding, {
        matchThreshold: RAG_CONFIG.MIN_SIMILARITY_THRESHOLD,
        matchCount: maxSources
      }),
      searchKnowledgeBase(queryEmbedding, {
        matchThreshold: RAG_CONFIG.MIN_SIMILARITY_THRESHOLD,
        matchCount: maxSources
      }),
      includeAllContent ? searchAllContent(queryEmbedding, {
        matchThreshold: RAG_CONFIG.MIN_SIMILARITY_THRESHOLD,
        matchCount: maxSources * 2
      }) : []
    ]);

    // 3. Construir contexto y fuentes
    const context: RAGContext = { procedures, opas, knowledgeBase, allContent };
    const sources = buildSources(context);
    const contextText = buildContextText(context);

    // 4. Generar respuesta streaming con OpenAI
    const messages = [
      {
        role: 'system' as const,
        content: `${RAG_CONFIG.SYSTEM_PROMPT}\n\nRol del usuario: ${userRole}\n\nContexto disponible:\n${contextText}`
      },
      {
        role: 'user' as const,
        content: query
      }
    ];

    const stream = await createStreamingChatCompletion({
      messages,
      temperature,
      max_tokens: 1000
    });

    return {
      stream,
      sources
    };

  } catch (error) {
    console.error('Error generating RAG stream response:', error);
    throw new Error(`Failed to generate RAG stream response: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Función para construir las fuentes de información
 */
function buildSources(context: RAGContext) {
  const sources: Array<{
    id: string;
    type: 'procedure' | 'opa' | 'knowledge';
    title: string;
    content: string;
    similarity: number;
  }> = [];

  // Agregar procedures
  context.procedures.forEach(proc => {
    sources.push({
      id: proc.procedure_id,
      type: 'procedure',
      title: proc.metadata.name || 'Trámite',
      content: proc.content,
      similarity: proc.similarity
    });
  });

  // Agregar OPAs
  context.opas.forEach(opa => {
    sources.push({
      id: opa.opa_id,
      type: 'opa',
      title: opa.metadata.name || 'OPA',
      content: opa.content,
      similarity: opa.similarity
    });
  });

  // Agregar knowledge base
  context.knowledgeBase.forEach(kb => {
    sources.push({
      id: kb.source_id || kb.id,
      type: 'knowledge',
      title: kb.title,
      content: kb.content,
      similarity: kb.similarity
    });
  });

  // Ordenar por similitud descendente
  return sources.sort((a, b) => b.similarity - a.similarity);
}

/**
 * Función para construir el texto de contexto
 */
function buildContextText(context: RAGContext): string {
  let contextText = '';

  // Agregar procedures
  if (context.procedures.length > 0) {
    contextText += '\n=== TRÁMITES MUNICIPALES ===\n';
    context.procedures.forEach((proc, index) => {
      contextText += `${index + 1}. ${proc.metadata.name || 'Trámite'}\n`;
      contextText += `${proc.content}\n\n`;
    });
  }

  // Agregar OPAs
  if (context.opas.length > 0) {
    contextText += '\n=== OTRAS PRESTACIONES DE ATENCIÓN (OPAs) ===\n';
    context.opas.forEach((opa, index) => {
      contextText += `${index + 1}. ${opa.metadata.name || 'OPA'}\n`;
      contextText += `${opa.content}\n\n`;
    });
  }

  // Agregar knowledge base
  if (context.knowledgeBase.length > 0) {
    contextText += '\n=== INFORMACIÓN ADICIONAL ===\n';
    context.knowledgeBase.forEach((kb, index) => {
      contextText += `${index + 1}. ${kb.title}\n`;
      contextText += `${kb.content}\n\n`;
    });
  }

  // Truncar si es muy largo
  if (contextText.length > RAG_CONFIG.MAX_CONTEXT_LENGTH) {
    contextText = contextText.substring(0, RAG_CONFIG.MAX_CONTEXT_LENGTH) + '...\n[Contexto truncado]';
  }

  return contextText;
}
