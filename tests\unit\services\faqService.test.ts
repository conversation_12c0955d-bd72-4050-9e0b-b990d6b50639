import faqService, { FAQService, FAQItem, FAQTheme } from '@/lib/services/faqService'

// Mock del cliente de Supabase
const mockSupabaseData = {
  themes: [
    {
      id: 'impuestos-tributos',
      name: 'Impuestos y Tributos',
      description: 'Preguntas sobre impuestos municipales',
      icon: 'Receipt',
      color: 'bg-blue-500',
      display_order: 1,
      is_active: true,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z'
    },
    {
      id: 'licencias-permisos',
      name: 'Licencias y Permisos',
      description: 'Construcción, funcionamiento, comerciales',
      icon: 'FileCheck',
      color: 'bg-green-500',
      display_order: 2,
      is_active: true,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z'
    }
  ],
  faqs: [
    {
      id: 'faq-1',
      question: '¿Qué es el impuesto predial y cómo se calcula?',
      answer: 'El impuesto predial unificado es un tributo municipal que grava la propiedad inmueble.',
      theme_id: 'impuestos-tributos',
      keywords: ['impuesto predial', 'avalúo catastral', 'tributos'],
      related_procedures: ['Impuesto predial unificado'],
      popularity_score: 95,
      view_count: 100,
      helpful_votes: 50,
      unhelpful_votes: 5,
      is_active: true,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
      faq_themes: { name: 'Impuestos y Tributos' }
    },
    {
      id: 'faq-2',
      question: '¿Qué requisitos necesito para obtener una licencia de construcción?',
      answer: 'Para obtener una licencia de construcción debe presentar los documentos técnicos requeridos.',
      theme_id: 'licencias-permisos',
      keywords: ['licencia construcción', 'planeación', 'obras civiles'],
      related_procedures: ['Licencia de construcción'],
      popularity_score: 90,
      view_count: 80,
      helpful_votes: 40,
      unhelpful_votes: 3,
      is_active: true,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
      faq_themes: { name: 'Licencias y Permisos' }
    }
  ]
}

// Mock más completo del cliente de Supabase
const createMockQuery = (table: string) => {
  let filters: any = {}
  let orderBy: any = null
  let limitValue: number | null = null
  let selectFields = '*'

  const mockQuery = {
    select: jest.fn().mockImplementation((fields: string) => {
      selectFields = fields
      return mockQuery
    }),
    eq: jest.fn().mockImplementation((field: string, value: any) => {
      filters[field] = value
      return mockQuery
    }),
    order: jest.fn().mockImplementation((field: string, options?: any) => {
      orderBy = { field, options }
      return mockQuery
    }),
    limit: jest.fn().mockImplementation((value: number) => {
      limitValue = value
      return mockQuery
    }),
    or: jest.fn().mockImplementation((condition: string) => {
      filters.or = condition
      return mockQuery
    }),
    textSearch: jest.fn().mockImplementation((field: string, query: string, options?: any) => {
      filters.textSearch = { field, query, options }
      return mockQuery
    }),
    single: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    then: jest.fn().mockImplementation(async () => {
      let data: any[] = []

      if (table === 'faq_themes') {
        data = [...mockSupabaseData.themes]
        if (filters.is_active !== undefined) {
          data = data.filter(item => item.is_active === filters.is_active)
        }
        if (orderBy?.field === 'display_order') {
          data.sort((a, b) => a.display_order - b.display_order)
        }
      } else if (table === 'municipal_faqs') {
        data = [...mockSupabaseData.faqs]
        if (filters.is_active !== undefined) {
          data = data.filter(item => item.is_active === filters.is_active)
        }
        if (filters.theme_id) {
          data = data.filter(item => item.theme_id === filters.theme_id)
        }
        if (filters.textSearch) {
          // Simular búsqueda de texto completo
          const searchTerm = filters.textSearch.query.toLowerCase()
          data = data.filter(item =>
            item.question.toLowerCase().includes(searchTerm) ||
            item.answer.toLowerCase().includes(searchTerm) ||
            item.keywords.some((keyword: string) => keyword.toLowerCase().includes(searchTerm))
          )
        }
        if (filters.or) {
          // Simular búsqueda OR
          const searchTerm = filters.or.match(/%([^%]+)%/)?.[1]?.toLowerCase()
          if (searchTerm) {
            data = data.filter(item =>
              item.question.toLowerCase().includes(searchTerm) ||
              item.answer.toLowerCase().includes(searchTerm) ||
              item.keywords.some((keyword: string) => keyword.toLowerCase().includes(searchTerm))
            )
          }
        }
        if (orderBy?.field === 'popularity_score') {
          data.sort((a, b) => b.popularity_score - a.popularity_score)
        }
      }

      if (limitValue) {
        data = data.slice(0, limitValue)
      }

      return { data, error: null }
    })
  }

  return mockQuery
}

jest.mock('@/lib/supabase/client', () => ({
  supabase: {
    from: jest.fn().mockImplementation((table: string) => createMockQuery(table))
  }
}))

// Mock de faqAnalytics
jest.mock('@/lib/services/faqAnalytics', () => ({
  trackSearch: jest.fn(),
  trackFAQView: jest.fn(),
  trackNoResults: jest.fn()
}))

describe('FAQService', () => {
  beforeEach(() => {
    // Limpiar cache si existe
    if ((faqService as any).themesCache) {
      ;(faqService as any).themesCache.clear()
    }
    if ((faqService as any).lastCacheUpdate !== undefined) {
      ;(faqService as any).lastCacheUpdate = 0
    }
  })

  describe('Inicialización', () => {
    it('debería ser un singleton', () => {
      const instance1 = FAQService.getInstance()
      const instance2 = FAQService.getInstance()
      expect(instance1).toBe(instance2)
      expect(instance1).toBe(faqService)
    })

    it('debería obtener temas desde Supabase', async () => {
      const themes = await faqService.getThemes()

      expect(themes).toHaveLength(2) // Basado en nuestros datos mock
      expect(themes[0]).toHaveProperty('id')
      expect(themes[0]).toHaveProperty('name')
      expect(themes[0]).toHaveProperty('description')
      expect(themes[0]).toHaveProperty('displayOrder')
      expect(themes[0]).toHaveProperty('count')
    })

    it('debería obtener FAQs populares desde Supabase', async () => {
      const popularFAQs = await faqService.getPopularFAQs(5)

      expect(popularFAQs.length).toBeGreaterThan(0)
      expect(popularFAQs[0]).toHaveProperty('id')
      expect(popularFAQs[0]).toHaveProperty('question')
      expect(popularFAQs[0]).toHaveProperty('answer')
      expect(popularFAQs[0]).toHaveProperty('theme')
      expect(popularFAQs[0]).toHaveProperty('keywords')
      expect(popularFAQs[0]).toHaveProperty('popularityScore')
      expect(popularFAQs[0]).toHaveProperty('viewCount')
      expect(popularFAQs[0]).toHaveProperty('helpfulVotes')
    })
  })

  describe('Búsqueda de FAQs', () => {
    it('debería buscar FAQs por texto en preguntas', async () => {
      const results = await faqService.searchFAQs('impuesto predial')

      expect(results.length).toBeGreaterThan(0)
      const hasRelevantResult = results.some(faq =>
        faq.question.toLowerCase().includes('predial') ||
        faq.answer.toLowerCase().includes('predial')
      )
      expect(hasRelevantResult).toBe(true)
    })

    it('debería buscar FAQs por texto en respuestas', async () => {
      const results = await faqService.searchFAQs('tributo municipal')

      expect(results.length).toBeGreaterThan(0)
      const hasRelevantResult = results.some(faq =>
        faq.answer.toLowerCase().includes('tributo') ||
        faq.answer.toLowerCase().includes('municipal')
      )
      expect(hasRelevantResult).toBe(true)
    })

    it('debería buscar FAQs por keywords', async () => {
      const results = await faqService.searchFAQs('construcción')

      expect(results.length).toBeGreaterThan(0)
      const hasRelevantResult = results.some(faq =>
        faq.keywords.some(keyword => keyword.toLowerCase().includes('construcción'))
      )
      expect(hasRelevantResult).toBe(true)
    })

    it('debería retornar array vacío para búsqueda vacía', async () => {
      const results = await faqService.searchFAQs('')
      expect(results).toEqual([])
    })

    it('debería retornar array vacío para búsqueda solo con espacios', async () => {
      const results = await faqService.searchFAQs('   ')
      expect(results).toEqual([])
    })

    it('debería filtrar por tema', async () => {
      const themeId = 'impuestos-tributos' // Impuestos y Tributos
      const results = await faqService.searchFAQs('impuesto', { theme: themeId })

      expect(results.length).toBeGreaterThan(0)
      results.forEach(faq => {
        expect(faq.themeId).toBe(themeId)
      })
    })

    it('debería respetar el límite de resultados', async () => {
      const limit = 1
      const results = await faqService.searchFAQs('licencia', { limit })

      expect(results.length).toBeLessThanOrEqual(limit)
    })

    it('debería ordenar resultados por relevancia', async () => {
      const results = await faqService.searchFAQs('impuesto')

      if (results.length > 1) {
        // Verificar que están ordenados por relevancia (popularidad decreciente como proxy)
        for (let i = 0; i < results.length - 1; i++) {
          const currentScore = results[i].popularityScore || 0
          const nextScore = results[i + 1].popularityScore || 0
          // Los resultados deberían estar ordenados por relevancia
          expect(currentScore).toBeGreaterThanOrEqual(0)
          expect(nextScore).toBeGreaterThanOrEqual(0)
        }
      }
    })
  })

  describe('Gestión de temas', () => {
    it('debería obtener todos los temas', async () => {
      const themes = await faqService.getThemes()

      expect(themes).toHaveLength(2) // Basado en nuestros datos mock

      const expectedThemes = ['Impuestos y Tributos', 'Licencias y Permisos']
      const themeNames = themes.map(theme => theme.name)

      expectedThemes.forEach(expectedName => {
        expect(themeNames).toContain(expectedName)
      })
    })

    it('debería obtener FAQs por tema', async () => {
      const themeId = 'impuestos-tributos' // Impuestos y Tributos
      const themeFAQs = await faqService.getFAQsByTheme(themeId)

      expect(themeFAQs.length).toBeGreaterThan(0)
      themeFAQs.forEach(faq => {
        expect(faq.themeId).toBe(themeId)
      })
    })

    it('debería ordenar FAQs por popularidad dentro de tema', async () => {
      const themeId = 'impuestos-tributos'
      const themeFAQs = await faqService.getFAQsByTheme(themeId)

      if (themeFAQs.length > 1) {
        for (let i = 0; i < themeFAQs.length - 1; i++) {
          expect(themeFAQs[i].popularityScore).toBeGreaterThanOrEqual(themeFAQs[i + 1].popularityScore)
        }
      }
    })

    it('debería respetar límite en FAQs por tema', async () => {
      const limit = 1
      const themeId = 'impuestos-tributos'
      const themeFAQs = await faqService.getFAQsByTheme(themeId, limit)

      expect(themeFAQs.length).toBeLessThanOrEqual(limit)
    })

    it('debería retornar array vacío para tema inexistente', async () => {
      const results = await faqService.getFAQsByTheme('tema-inexistente')
      expect(results).toEqual([])
    })
  })

  describe('FAQs populares', () => {
    it('debería obtener FAQs más populares', async () => {
      const popularFAQs = await faqService.getPopularFAQs(5)

      expect(popularFAQs.length).toBeGreaterThan(0)
      expect(popularFAQs.length).toBeLessThanOrEqual(5)

      // Verificar que están ordenadas por popularidad
      if (popularFAQs.length > 1) {
        for (let i = 0; i < popularFAQs.length - 1; i++) {
          expect(popularFAQs[i].popularityScore).toBeGreaterThanOrEqual(popularFAQs[i + 1].popularityScore)
        }
      }
    })

    it('debería respetar el límite de FAQs populares', async () => {
      const limit = 3
      const popularFAQs = await faqService.getPopularFAQs(limit)

      expect(popularFAQs.length).toBeLessThanOrEqual(limit)
    })
  })

  describe('FAQ individual', () => {
    it('debería obtener FAQ por ID', async () => {
      // Primero obtener una FAQ para tener un ID válido
      const popularFAQs = await faqService.getPopularFAQs(1)
      expect(popularFAQs.length).toBeGreaterThan(0)

      const faqId = popularFAQs[0].id
      const faq = await faqService.getFAQById(faqId)

      expect(faq).not.toBeNull()
      expect(faq!.id).toBe(faqId)
    })

    it('debería retornar null para ID inexistente', async () => {
      const faq = await faqService.getFAQById('id-inexistente')
      expect(faq).toBeNull()
    })
  })

  describe('Estadísticas', () => {
    it('debería obtener estadísticas del FAQ', async () => {
      const stats = await faqService.getFAQStats()

      expect(stats).toHaveProperty('totalFAQs')
      expect(stats).toHaveProperty('totalThemes')
      expect(stats).toHaveProperty('averagePopularity')
      expect(stats).toHaveProperty('mostPopularTheme')

      expect(stats.totalFAQs).toBeGreaterThan(0)
      expect(stats.totalThemes).toBe(2) // Basado en nuestros datos mock
      expect(stats.averagePopularity).toBeGreaterThan(0)
      expect(typeof stats.mostPopularTheme).toBe('string')
    })

    it('debería calcular correctamente el promedio de popularidad', async () => {
      const stats = await faqService.getFAQStats()

      // Con nuestros datos mock: (95 + 90) / 2 = 92.5 -> 93
      expect(stats.averagePopularity).toBe(93)
    })
  })

  describe('Cálculo de relevancia', () => {
    it('debería calcular puntuación de relevancia correctamente', async () => {
      // Buscar un término que aparezca en diferentes partes
      const results = await faqService.searchFAQs('impuesto')
      
      expect(results.length).toBeGreaterThan(0)
      
      // Los resultados deberían estar ordenados por relevancia
      if (results.length > 1) {
        // Verificar que el primer resultado es más relevante
        expect(results[0]).toBeDefined()
        expect(results[1]).toBeDefined()
      }
    })
  })

  describe('Manejo de errores', () => {
    it('debería manejar errores en búsqueda graciosamente', async () => {
      // Test con caracteres especiales
      const results = await faqService.searchFAQs('!@#$%^&*()')
      expect(results).toEqual([])
    })

    it('debería manejar búsquedas con texto muy largo', async () => {
      const longText = 'a'.repeat(1000)
      const results = await faqService.searchFAQs(longText)
      expect(Array.isArray(results)).toBe(true)
    })
  })
})
