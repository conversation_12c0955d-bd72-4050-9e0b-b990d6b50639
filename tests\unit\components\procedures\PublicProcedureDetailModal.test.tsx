import { render, screen, fireEvent } from '@testing-library/react'
import { PublicProcedureDetailModal } from '@/components/procedures/PublicProcedureDetailModal'
import { SuitEnhancedProcedure } from '@/types/suit-enhanced-procedure'

// Mock icons
jest.mock('lucide-react', () => ({
  X: () => <div data-testid="x-icon" />,
  Clock: () => <div data-testid="clock-icon" />,
  DollarSign: () => <div data-testid="dollar-icon" />,
  Sparkles: () => <div data-testid="sparkles-icon" />,
  Info: () => <div data-testid="info-icon" />,
  CheckCircle: () => <div data-testid="check-circle-icon" />,
  FileText: () => <div data-testid="file-text-icon" />,
  Building2: () => <div data-testid="building-icon" />,
  Phone: () => <div data-testid="phone-icon" />,
  Mail: () => <div data-testid="mail-icon" />,
  MapPin: () => <div data-testid="map-pin-icon" />
}))

// Mock UI components
jest.mock('@/components/ui/card', () => ({
  Card: ({ children, className }: any) => <div className={className}>{children}</div>,
  CardContent: ({ children }: any) => <div>{children}</div>,
  CardHeader: ({ children }: any) => <div>{children}</div>,
  CardTitle: ({ children }: any) => <h3>{children}</h3>,
  CardDescription: ({ children }: any) => <p>{children}</p>
}))

jest.mock('@/components/ui/badge', () => ({
  Badge: ({ children, className }: any) => <span className={className}>{children}</span>
}))

jest.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick, className }: any) => (
    <button type="button" onClick={onClick} className={className}>{children}</button>
  )
}))

jest.mock('@/components/ui/separator', () => ({
  Separator: () => <hr data-testid="separator" />
}))

jest.mock('@/components/ui/tabs', () => ({
  Tabs: ({ children, value, onValueChange }: any) => (
    <div data-testid="tabs" data-value={value}>{children}</div>
  ),
  TabsList: ({ children }: any) => <div data-testid="tabs-list">{children}</div>,
  TabsTrigger: ({ children, value, onClick }: any) => (
    <button type="button" data-testid={`tab-${value}`} onClick={() => onClick?.(value)}>{children}</button>
  ),
  TabsContent: ({ children, value }: any) => (
    <div data-testid={`tab-content-${value}`}>{children}</div>
  )
}))

describe('PublicProcedureDetailModal', () => {
  const mockOnClose = jest.fn()

  const baseProcedure: SuitEnhancedProcedure = {
    id: '1',
    name: 'Test Procedure',
    description: 'Original description',
    cost: 50000,
    response_time: '10 días',
    requirements: ['Original req 1', 'Original req 2'],
    has_cost: true,
    cost_description: 'Costo original',
    procedure_type: 'TRAMITE',
    dependency: {
      id: 'dep-1',
      name: 'Test Dependency',
      contact_email: '<EMAIL>',
      contact_phone: '************'
    },
    best_description: 'Original description',
    best_requirements: ['Original req 1', 'Original req 2'],
    best_response_time: '10 días',
    best_cost_info: 'Costo original',
    has_suit_enhancement: false
  }

  const suitEnhancedProcedure: SuitEnhancedProcedure = {
    ...baseProcedure,
    suit_data: {
      descripcion_detallada: 'Enhanced SUIT description',
      requisitos: ['SUIT req 1', 'SUIT req 2', 'SUIT req 3'],
      pasos: ['SUIT step 1', 'SUIT step 2'],
      documentos_necesarios: ['SUIT doc 1', 'SUIT doc 2'],
      tiempo_respuesta: '5 días hábiles',
      costo_detallado: 'Gratuito según SUIT',
      base_juridica: 'Ley 123 de 2023',
      entidad_responsable: 'Ministerio de Ejemplo',
      scraping_status: 'success' as const
    },
    best_description: 'Enhanced SUIT description',
    best_requirements: ['SUIT req 1', 'SUIT req 2', 'SUIT req 3'],
    best_process_steps: ['SUIT step 1', 'SUIT step 2'],
    best_response_time: '5 días hábiles',
    best_cost_info: 'Gratuito según SUIT',
    has_suit_enhancement: true
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should render basic procedure information', () => {
    // Test simple rendering first
    const { container } = render(
      <PublicProcedureDetailModal
        procedure={baseProcedure}
        onClose={mockOnClose}
      />
    )

    expect(container).toBeInTheDocument()
    // expect(screen.getByText('Test Procedure')).toBeInTheDocument()
    // expect(screen.getByText('Test Dependency')).toBeInTheDocument()
  })

  it('should display SUIT enhancement banner when SUIT data is available', () => {
    render(
      <PublicProcedureDetailModal
        procedure={suitEnhancedProcedure}
        onClose={mockOnClose}
      />
    )

    expect(screen.getByText('Información Enriquecida con SUIT')).toBeInTheDocument()
    expect(screen.getAllByTestId('sparkles-icon')).toHaveLength(7) // Banner + description + cost + time + requirements + legal + entity
  })

  it('should use enhanced information in overview tab', () => {
    render(
      <PublicProcedureDetailModal
        procedure={suitEnhancedProcedure}
        onClose={mockOnClose}
      />
    )

    // Should display SUIT enhanced description
    expect(screen.getByText('Enhanced SUIT description')).toBeInTheDocument()
    
    // Should display enhanced cost and time (use getAllByText for multiple instances)
    expect(screen.getAllByText('Gratuito según SUIT')).toHaveLength(2) // Header and overview section
    expect(screen.getByText('5 días hábiles')).toBeInTheDocument()
    
    // Should show sparkles indicators
    expect(screen.getAllByTestId('sparkles-icon')).toHaveLength(7) // Banner + description + cost + time + requirements + legal + entity
  })

  it('should display enhanced requirements in requirements tab', () => {
    render(
      <PublicProcedureDetailModal
        procedure={suitEnhancedProcedure}
        onClose={mockOnClose}
      />
    )

    // Should show SUIT enhanced requirements
    expect(screen.getByText('SUIT req 1')).toBeInTheDocument()
    expect(screen.getByText('SUIT req 2')).toBeInTheDocument()
    expect(screen.getByText('SUIT req 3')).toBeInTheDocument()
    
    // Should show SUIT documents section
    expect(screen.getByText('Documentos Necesarios (SUIT)')).toBeInTheDocument()
    expect(screen.getByText('SUIT doc 1')).toBeInTheDocument()
    expect(screen.getByText('SUIT doc 2')).toBeInTheDocument()
  })

  it('should display enhanced process steps in process tab', () => {
    render(
      <PublicProcedureDetailModal
        procedure={suitEnhancedProcedure}
        onClose={mockOnClose}
      />
    )

    // Click on process tab
    fireEvent.click(screen.getByTestId('tab-process'))

    // Should show SUIT enhanced steps
    expect(screen.getByText('SUIT step 1')).toBeInTheDocument()
    expect(screen.getByText('SUIT step 2')).toBeInTheDocument()
    
    // Should show SUIT legal framework
    expect(screen.getByText('Base Jurídica (SUIT)')).toBeInTheDocument()
    expect(screen.getByText('Ley 123 de 2023')).toBeInTheDocument()
    
    // Should show responsible entity
    expect(screen.getByText('Entidad Responsable (SUIT)')).toBeInTheDocument()
    expect(screen.getByText('Ministerio de Ejemplo')).toBeInTheDocument()
  })

  it('should fallback to original data when SUIT data is not available', () => {
    render(
      <PublicProcedureDetailModal
        procedure={baseProcedure}
        onClose={mockOnClose}
      />
    )

    // Should display original description
    expect(screen.getByText('Original description')).toBeInTheDocument()
    
    // Should display original requirements
    expect(screen.getByText('Original req 1')).toBeInTheDocument()
    expect(screen.getByText('Original req 2')).toBeInTheDocument()
    
    // Should not show SUIT enhancement banner
    expect(screen.queryByText('Información Enriquecida con SUIT')).not.toBeInTheDocument()
    
    // Should not show sparkles indicators
    expect(screen.queryByTestId('sparkles-icon')).not.toBeInTheDocument()
  })

  it('should call onClose when close button is clicked', () => {
    render(
      <PublicProcedureDetailModal
        procedure={baseProcedure}
        onClose={mockOnClose}
      />
    )

    const closeButton = screen.getByTestId('x-icon').closest('button')
    fireEvent.click(closeButton!)

    expect(mockOnClose).toHaveBeenCalledTimes(1)
  })
})
