{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "ApQd3e+MSMb4Xm6TZ0nWZXjDt5o2PXJWRh6Kc8U2+lw="}}}, "functions": {}, "sortedMiddleware": ["/"]}