# Sistema de Roles y Permisos

## Descripción General

El Sistema de Atención Ciudadana AI-First implementa un sistema robusto de roles y permisos que permite controlar el acceso a diferentes funcionalidades basado en el rol del usuario y sus permisos específicos.

## Arquitectura

### Hooks Principales

#### `useAuth`
Hook principal para manejo de autenticación que proporciona:
- Estado de autenticación del usuario
- Información del perfil y rol
- Métodos para login, logout, registro, etc.
- Actualización automática del estado de autenticación

```typescript
const { user, profile, role, isAuthenticated, signIn, signOut } = useAuth()
```

#### `useRole`
Hook especializado para manejo de roles que proporciona:
- Información del rol actual del usuario
- Verificaciones de rol (isCitizen, isAdmin, isSuperAdmin)
- Permisos granulares basados en el rol
- Verificación de administración de dependencias

```typescript
const { roleName, isCitizen, isAdmin, isSuperAdmin, permissions, canAccess } = useRole()
```

#### `usePermissions`
Hook para verificaciones específicas de permisos que proporciona:
- Verificaciones de acceso a rutas
- Permisos sobre recursos específicos
- Verificaciones contextuales (por dependencia, usuario, etc.)

```typescript
const { canAccessDashboard, canViewUser, canEditProcedure } = usePermissions()
```

## Roles del Sistema

### 1. Ciudadano (`ciudadano`)
**Descripción**: Usuario estándar del sistema que puede realizar trámites y consultas.

**Permisos**:
- ✅ Ver dashboard
- ✅ Ver y editar su perfil
- ✅ Crear trámites
- ✅ Ver sus propios trámites
- ✅ Usar chat con IA
- ✅ Recibir notificaciones

### 2. Administrador (`admin`)
**Descripción**: Administrador de una dependencia específica con permisos de gestión limitados a su dependencia.

**Permisos** (incluye todos los de ciudadano más):
- ✅ Ver trámites de su dependencia
- ✅ Gestionar trámites de su dependencia
- ✅ Ver usuarios de su dependencia
- ✅ Gestionar usuarios de su dependencia
- ✅ Ver reportes de su dependencia
- ✅ Gestionar base de conocimiento

### 3. Super Administrador (`super_admin`)
**Descripción**: Administrador con acceso completo al sistema.

**Permisos** (incluye todos los anteriores más):
- ✅ Ver todos los trámites
- ✅ Gestionar todos los trámites
- ✅ Ver todos los usuarios
- ✅ Gestionar todos los usuarios
- ✅ Ver todos los reportes
- ✅ Gestionar configuración del sistema
- ✅ Gestionar dependencias
- ✅ Gestionar roles
- ✅ Ver logs de auditoría

## Componentes de Protección

### `ProtectedRoute`
Protege rutas completas basado en roles y permisos.

```tsx
<ProtectedRoute requiredRoles={['admin', 'super_admin']}>
  <AdminPanel />
</ProtectedRoute>
```

### `RoleGuard`
Muestra/oculta contenido basado en roles y permisos.

```tsx
<RoleGuard roles={['admin']}>
  <AdminContent />
</RoleGuard>
```

### `PermissionButton`
Botón que se habilita/deshabilita basado en permisos.

```tsx
<PermissionButton requiredPermissions={['canEditUser']}>
  Editar Usuario
</PermissionButton>
```

## Componentes de Conveniencia

### Componentes de Ruta
```tsx
<AdminRoute>
  <AdminPanel />
</AdminRoute>

<SuperAdminRoute>
  <SystemSettings />
</SuperAdminRoute>

<CitizenRoute>
  <UserDashboard />
</CitizenRoute>
```

### Componentes de Visualización
```tsx
<ShowForAdmin>
  <AdminMenu />
</ShowForAdmin>

<ShowForSuperAdmin>
  <SuperAdminTools />
</ShowForSuperAdmin>

<HideForCitizen>
  <AdminOnlyContent />
</HideForCitizen>
```

### Botones Especializados
```tsx
<AdminButton>Gestionar</AdminButton>
<SuperAdminButton>Configurar Sistema</SuperAdminButton>
<CitizenButton>Crear Trámite</CitizenButton>
```

## Ejemplos de Uso

### Protección de Rutas
```tsx
// app/admin/page.tsx
export default function AdminPage() {
  return (
    <ProtectedRoute requiredRoles={['admin', 'super_admin']}>
      <AdminDashboard />
    </ProtectedRoute>
  )
}
```

### Contenido Condicional
```tsx
function Dashboard() {
  return (
    <div>
      <ConditionalContent
        citizenContent={<CitizenDashboard />}
        adminContent={<AdminDashboard />}
        superAdminContent={<SuperAdminDashboard />}
      />
    </div>
  )
}
```

### Verificación de Permisos
```tsx
function UserList() {
  const { canEditUser, canDeleteUser } = usePermissions()
  
  return (
    <div>
      {users.map(user => (
        <div key={user.id}>
          <span>{user.name}</span>
          
          <PermissionButton
            requiredPermissions={['canEditUser']}
            onClick={() => editUser(user.id)}
          >
            Editar
          </PermissionButton>
          
          <PermissionButton
            requiredPermissions={['canDeleteUser']}
            variant="destructive"
            onClick={() => deleteUser(user.id)}
          >
            Eliminar
          </PermissionButton>
        </div>
      ))}
    </div>
  )
}
```

## Configuración de Permisos por Dependencia

Para verificaciones específicas por dependencia:

```tsx
<PermissionButton
  requiredPermissions={['canManageDependencyUsers']}
  dependencyId={selectedDependency.id}
>
  Gestionar Usuarios
</PermissionButton>
```

## Mejores Prácticas

### 1. Principio de Menor Privilegio
- Asigna solo los permisos mínimos necesarios
- Usa verificaciones específicas en lugar de roles amplios

### 2. Verificación en Cliente y Servidor
- Usa los componentes de protección en el frontend
- Implementa verificaciones adicionales en el backend

### 3. Manejo de Errores
- Proporciona mensajes claros cuando se deniega el acceso
- Ofrece alternativas o rutas de escalación

### 4. Auditoría
- Registra acciones sensibles
- Mantén logs de cambios de permisos

## Extensión del Sistema

Para agregar nuevos roles o permisos:

1. **Actualizar la base de datos**: Agregar nuevos roles en la tabla `roles`
2. **Actualizar tipos**: Modificar los tipos TypeScript en `useRole.ts`
3. **Definir permisos**: Agregar nuevos permisos en la interfaz `RolePermissions`
4. **Implementar lógica**: Actualizar la lógica de permisos en `useRole.ts`
5. **Crear componentes**: Agregar componentes de conveniencia si es necesario

## Seguridad

### Consideraciones Importantes
- Las verificaciones del frontend son solo para UX
- Siempre implementar verificaciones en el servidor
- Usar Row Level Security (RLS) en Supabase
- Validar permisos en cada operación crítica

### Políticas RLS
El sistema utiliza políticas RLS en Supabase para garantizar que:
- Los usuarios solo vean sus propios datos
- Los administradores solo accedan a su dependencia
- Los super administradores tengan acceso completo

## Troubleshooting

### Problemas Comunes

1. **Usuario no tiene permisos esperados**
   - Verificar que el rol esté asignado correctamente
   - Comprobar que el perfil esté completo
   - Revisar las políticas RLS

2. **Componentes no se muestran**
   - Verificar que los hooks estén siendo usados correctamente
   - Comprobar que el usuario esté autenticado
   - Revisar la lógica de permisos

3. **Errores de autenticación**
   - Verificar la configuración de Supabase
   - Comprobar que las cookies estén configuradas
   - Revisar el middleware de autenticación
