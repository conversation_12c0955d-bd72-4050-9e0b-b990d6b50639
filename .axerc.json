{"rules": {"color-contrast": {"enabled": true}, "keyboard": {"enabled": true}, "focus": {"enabled": true}, "aria": {"enabled": true}, "semantics": {"enabled": true}, "structure": {"enabled": true}, "forms": {"enabled": true}, "images": {"enabled": true}, "links": {"enabled": true}, "tables": {"enabled": true}}, "tags": ["wcag2a", "wcag2aa", "wcag21aa", "best-practice"], "exclude": ["#skip-link"], "include": ["main", "[role='main']", "[role='navigation']", "[role='banner']", "[role='contentinfo']"], "reporter": "v2", "outputDir": "./reports/accessibility", "save": true, "timeout": 30000}