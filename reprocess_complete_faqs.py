#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Reprocesador completo de FAQs con códigos oficiales
Procesa todo el contenido FAQ con mapeo correcto de dependencias y subdependencias
"""

import json
import re
from typing import List, Dict, Any, Optional

# Códigos oficiales de dependencias
DEP_CODES = {
    "despacho del alcalde": "000",
    "secretaria de gobierno": "030", 
    "secretaria de hacienda": "040",
    "secretaria de desarrollo social": "060",
    "secretaria de educacion": "070",
    "secretaria de salud": "080",
    "secretaria para el desarrollo economico": "090",
    "secretaria de movilidad": "110",
    "secretaria de participacion ciudadana y accion comunitaria": "120"
}

# Códigos oficiales de subdependencias
SUBDEP_CODES = {
    "oficina de tecnologias de la informacion tic": "005",
    "direccion de seguridad y convivencia ciudadana": "031",
    "direccion asuntos etnicos raciales religiosos y posconflicto": "032", 
    "direccion de derechos y resolucion de conflictos": "033",
    "direccion financiera": "042",
    "direccion de rentas": "041",
    "direccion de accion social": "062",
    "direccion ciudadania juvenil": "061",
    "direccion cultura": "063",
    "direccion de desarrollo agropecuario y empresarial": "091",
    "direccion de turismo": "092"
}

def normalize_text(text: str) -> str:
    """Normaliza texto para comparación"""
    if not text:
        return ""
    
    text = text.lower()
    # Remover acentos
    replacements = {
        'á': 'a', 'é': 'e', 'í': 'i', 'ó': 'o', 'ú': 'u', 'ñ': 'n'
    }
    for old, new in replacements.items():
        text = text.replace(old, new)
    
    # Limpiar caracteres especiales
    text = re.sub(r'[^\w\s]', ' ', text)
    text = re.sub(r'\s+', ' ', text).strip()
    
    return text

def extract_keywords(question: str, answer: str) -> List[str]:
    """Extrae palabras clave de pregunta y respuesta"""
    text = f"{question} {answer}".lower()
    
    keywords = []
    
    # Términos municipales importantes
    municipal_terms = [
        'tramite', 'procedimiento', 'requisito', 'documento', 'certificado',
        'inscripcion', 'registro', 'solicitud', 'costo', 'gratuito',
        'presencial', 'virtual', 'linea', 'horario', 'direccion',
        'programa', 'servicio', 'beneficio', 'apoyo', 'asistencia'
    ]
    
    for term in municipal_terms:
        if term in text:
            keywords.append(term)
    
    # Extraer palabras específicas importantes
    important_words = re.findall(r'\b[a-záéíóúñ]{4,}\b', text)
    keywords.extend([word for word in important_words[:5] if word not in keywords])
    
    return keywords[:8]

def get_dependency_code(dep_name: str) -> str:
    """Obtiene código oficial de dependencia"""
    normalized = normalize_text(dep_name)
    return DEP_CODES.get(normalized, "")

def get_subdependency_code(subdep_name: str) -> str:
    """Obtiene código oficial de subdependencia"""
    if not subdep_name:
        return ""
    
    normalized = normalize_text(subdep_name)
    
    # Casos especiales
    if "gestion del riesgo" in normalized:
        return ""  # No existe en BD oficial
    
    return SUBDEP_CODES.get(normalized, "")

def generate_tema_description(tema: str) -> str:
    """Genera descripción del tema"""
    if not tema:
        return ""
    
    tema_lower = tema.lower()
    
    if 'programa' in tema_lower:
        return f"Información sobre el {tema}"
    elif 'tramite' in tema_lower or 'trámite' in tema_lower:
        return f"Procedimientos y requisitos para {tema}"
    elif 'servicio' in tema_lower:
        return f"Detalles del servicio de {tema}"
    else:
        return f"Preguntas frecuentes sobre {tema}"

def reprocess_complete_faqs(input_file: str) -> Dict[str, Any]:
    """Reprocesa completamente el archivo FAQ con todos los mapeos correctos"""
    
    with open(input_file, 'r', encoding='utf-8') as f:
        lines = [line.strip() for line in f.readlines() if line.strip()]
    
    faqs_structure = {"faqs": []}
    
    # Variables de estado
    current_dependencia = None
    current_dep_code = None
    current_subdependencia = None
    current_subdep_code = None
    current_tema = None
    current_questions = []
    
    # Estructura temporal para agrupar por dependencia/subdependencia
    temp_structure = {}
    
    i = 0
    while i < len(lines):
        line = lines[i]
        
        # Detectar nueva dependencia
        if line.startswith('Dependencia:'):
            # Guardar tema anterior si existe
            if current_tema and current_questions:
                save_tema_to_temp(temp_structure, current_dependencia, current_dep_code,
                                current_subdependencia, current_subdep_code, 
                                current_tema, current_questions)
                current_questions = []
            
            current_dependencia = line.replace('Dependencia:', '').strip()
            current_dep_code = get_dependency_code(current_dependencia)
            current_subdependencia = None
            current_subdep_code = ""
            current_tema = None
            
        # Detectar subdependencia
        elif line.startswith('SUBDEPENDENCIA:') or line.startswith('Subdependencia:'):
            # Guardar tema anterior si existe
            if current_tema and current_questions:
                save_tema_to_temp(temp_structure, current_dependencia, current_dep_code,
                                current_subdependencia, current_subdep_code, 
                                current_tema, current_questions)
                current_questions = []
            
            current_subdependencia = line.split(':', 1)[1].strip()
            current_subdep_code = get_subdependency_code(current_subdependencia)
            current_tema = None
            
        # Detectar tema
        elif line.startswith('TEMA:') or line.startswith('Tema:'):
            # Guardar tema anterior si existe
            if current_tema and current_questions:
                save_tema_to_temp(temp_structure, current_dependencia, current_dep_code,
                                current_subdependencia, current_subdep_code, 
                                current_tema, current_questions)
                current_questions = []
            
            current_tema = line.replace('TEMA:', '').replace('Tema:', '').strip()
            
        # Detectar preguntas
        elif line.startswith('¿') and current_dependencia:
            question = line.strip()
            answer_parts = []
            
            # Recopilar respuesta
            j = i + 1
            while j < len(lines):
                next_line = lines[j]
                if (next_line.startswith('¿') or 
                    next_line.startswith('Dependencia:') or
                    next_line.startswith('SUBDEPENDENCIA:') or
                    next_line.startswith('Subdependencia:') or
                    next_line.startswith('TEMA:') or
                    next_line.startswith('Tema:')):
                    break
                answer_parts.append(next_line.strip())
                j += 1
            
            answer = ' '.join(answer_parts).strip()
            
            if answer:
                keywords = extract_keywords(question, answer)
                current_questions.append({
                    "pregunta": question,
                    "respuesta": answer,
                    "palabras_clave": keywords
                })
            
            i = j - 1
            
        i += 1
    
    # Guardar último tema
    if current_tema and current_questions:
        save_tema_to_temp(temp_structure, current_dependencia, current_dep_code,
                        current_subdependencia, current_subdep_code, 
                        current_tema, current_questions)
    
    # Convertir estructura temporal a formato final
    convert_temp_to_final(temp_structure, faqs_structure)
    
    return faqs_structure

def save_tema_to_temp(temp_structure: Dict, dependencia: str, dep_code: str,
                     subdependencia: str, subdep_code: str, tema: str, questions: List):
    """Guarda tema en estructura temporal"""
    
    if not dependencia or not tema or not questions:
        return
    
    key = f"{dependencia}|{subdependencia or ''}"
    
    if key not in temp_structure:
        temp_structure[key] = {
            "dependencia": dependencia,
            "codigo_dependencia": dep_code,
            "subdependencia": subdependencia or "",
            "codigo_subdependencia": subdep_code,
            "temas": []
        }
    
    temp_structure[key]["temas"].append({
        "tema": tema,
        "descripcion": generate_tema_description(tema),
        "preguntas_frecuentes": questions.copy()
    })

def convert_temp_to_final(temp_structure: Dict, final_structure: Dict):
    """Convierte estructura temporal a formato final"""
    
    for key, data in temp_structure.items():
        final_structure["faqs"].append(data)

def main():
    """Función principal"""
    print("🔄 REPROCESAMIENTO COMPLETO DE FAQs CON CÓDIGOS OFICIALES")
    print("=" * 65)
    
    try:
        # Reprocesar archivo completo
        faqs_data = reprocess_complete_faqs('FAQ_extracted.txt')
        
        # Guardar resultado
        output_file = 'faqs_chia_completo_oficial.json'
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(faqs_data, f, ensure_ascii=False, indent=2)
        
        # Estadísticas
        total_entries = len(faqs_data["faqs"])
        total_temas = sum(len(entry["temas"]) for entry in faqs_data["faqs"])
        total_preguntas = sum(
            len(tema["preguntas_frecuentes"]) 
            for entry in faqs_data["faqs"] 
            for tema in entry["temas"]
        )
        
        entries_with_subdep = len([e for e in faqs_data["faqs"] if e.get("subdependencia")])
        
        print(f"✅ Reprocesamiento completado:")
        print(f"   - Entradas totales: {total_entries}")
        print(f"   - Con subdependencias: {entries_with_subdep}")
        print(f"   - Temas: {total_temas}")
        print(f"   - Preguntas: {total_preguntas}")
        print(f"   - Archivo generado: {output_file}")
        
        # Mostrar mapeos
        print(f"\n📋 MAPEOS APLICADOS:")
        for entry in faqs_data["faqs"]:
            dep_info = f"{entry['dependencia']} ({entry['codigo_dependencia']})"
            if entry.get('subdependencia'):
                subdep_info = f" > {entry['subdependencia']} ({entry['codigo_subdependencia']})"
                print(f"   {dep_info}{subdep_info}")
            else:
                print(f"   {dep_info}")
        
    except Exception as e:
        print(f"❌ Error durante el reprocesamiento: {e}")
        raise

if __name__ == "__main__":
    main()
