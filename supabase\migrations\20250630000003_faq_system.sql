-- =============================================
-- FAQ SYSTEM TABLES
-- =============================================

-- FAQ Themes (Temas de preguntas frecuentes)
CREATE TABLE faq_themes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  dependency_id UUID REFERENCES dependencies(id),
  subdependency_id UUID REFERENCES subdependencies(id),
  icon TEXT,
  color TEXT,
  display_order INTEGER,
  is_active BOOLEAN DEFAULT true,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Municipal FAQs (Preguntas frecuentes municipales)
CREATE TABLE municipal_faqs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  question TEXT NOT NULL,
  answer TEXT NOT NULL,
  theme_id UUID REFERENCES faq_themes(id),
  keywords TEXT[],
  related_procedures TEXT[],
  display_order INTEGER,
  popularity_score INTEGER DEFAULT 0,
  view_count INTEGER DEFAULT 0,
  helpful_votes INTEGER DEFAULT 0,
  unhelpful_votes INTEGER DEFAULT 0,
  search_vector tsvector,
  is_active BOOLEAN DEFAULT true,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- INDEXES FOR FAQ SYSTEM
-- =============================================

-- FAQ Themes indexes
CREATE INDEX idx_faq_themes_dependency ON faq_themes(dependency_id);
CREATE INDEX idx_faq_themes_subdependency ON faq_themes(subdependency_id);
CREATE INDEX idx_faq_themes_active ON faq_themes(is_active);
CREATE INDEX idx_faq_themes_display_order ON faq_themes(display_order);

-- Municipal FAQs indexes
CREATE INDEX idx_municipal_faqs_theme ON municipal_faqs(theme_id);
CREATE INDEX idx_municipal_faqs_active ON municipal_faqs(is_active);
CREATE INDEX idx_municipal_faqs_popularity ON municipal_faqs(popularity_score DESC);
CREATE INDEX idx_municipal_faqs_view_count ON municipal_faqs(view_count DESC);
CREATE INDEX idx_municipal_faqs_display_order ON municipal_faqs(display_order);

-- Full-text search index for Spanish
CREATE INDEX idx_municipal_faqs_search ON municipal_faqs USING gin(search_vector);

-- Keywords search index
CREATE INDEX idx_municipal_faqs_keywords ON municipal_faqs USING gin(keywords);

-- Related procedures search index
CREATE INDEX idx_municipal_faqs_procedures ON municipal_faqs USING gin(related_procedures);

-- =============================================
-- TRIGGERS FOR FAQ SYSTEM
-- =============================================

-- Function to update search vector for full-text search
CREATE OR REPLACE FUNCTION update_faq_search_vector()
RETURNS TRIGGER AS $$
BEGIN
    NEW.search_vector := to_tsvector('spanish', 
        COALESCE(NEW.question, '') || ' ' || 
        COALESCE(NEW.answer, '') || ' ' ||
        COALESCE(array_to_string(NEW.keywords, ' '), '')
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply search vector trigger
CREATE TRIGGER update_municipal_faqs_search_vector
    BEFORE INSERT OR UPDATE ON municipal_faqs
    FOR EACH ROW EXECUTE FUNCTION update_faq_search_vector();

-- Apply updated_at triggers
CREATE TRIGGER update_faq_themes_updated_at 
    BEFORE UPDATE ON faq_themes 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_municipal_faqs_updated_at 
    BEFORE UPDATE ON municipal_faqs 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
