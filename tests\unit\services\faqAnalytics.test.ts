import { FAQAnalyticsService } from '@/lib/services/faqAnalytics'

// Mock de localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}

// Mock de fetch
global.fetch = jest.fn()

// Mock de window
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
})

Object.defineProperty(window, 'location', {
  value: {
    pathname: '/test-path'
  },
  writable: true
})

describe('FAQAnalyticsService', () => {
  let analyticsService: FAQAnalyticsService
  
  beforeEach(() => {
    jest.clearAllMocks()
    localStorageMock.getItem.mockReturnValue(null)
    
    // Crear nueva instancia para cada test
    analyticsService = new (FAQAnalyticsService as any)()
    
    // Mock de variables de entorno
    process.env.NODE_ENV = 'test'
    process.env.NEXT_PUBLIC_ENABLE_FAQ_ANALYTICS = 'true'
  })

  afterEach(() => {
    if (analyticsService && typeof analyticsService.destroy === 'function') {
      analyticsService.destroy()
    }
  })

  describe('Inicialización', () => {
    it('debería crear instancia singleton', () => {
      const instance1 = FAQAnalyticsService.getInstance()
      const instance2 = FAQAnalyticsService.getInstance()
      expect(instance1).toBe(instance2)
    })

    it('debería estar habilitado cuando NEXT_PUBLIC_ENABLE_FAQ_ANALYTICS es true', () => {
      process.env.NEXT_PUBLIC_ENABLE_FAQ_ANALYTICS = 'true'
      const service = new (FAQAnalyticsService as any)()
      expect(service).toBeDefined()
    })

    it('debería cargar eventos desde localStorage al inicializar', () => {
      const mockEvents = JSON.stringify([
        {
          event: 'faq_search',
          timestamp: new Date().toISOString(),
          metadata: { query: 'test' }
        }
      ])
      localStorageMock.getItem.mockReturnValue(mockEvents)
      
      new (FAQAnalyticsService as any)()
      expect(localStorageMock.getItem).toHaveBeenCalledWith('faq_analytics_queue')
    })
  })

  describe('Tracking de eventos', () => {
    beforeEach(() => {
      analyticsService = new (FAQAnalyticsService as any)()
    })

    it('debería registrar evento de búsqueda', () => {
      const spy = jest.spyOn(analyticsService, 'track')
      analyticsService.trackSearch('impuesto predial', 5, 150, 'home')
      
      expect(spy).toHaveBeenCalledWith('faq_search', {
        query: 'impuesto predial',
        resultsCount: 5,
        responseTime: 150,
        context: 'home'
      })
    })

    it('debería registrar visualización de FAQ', () => {
      const spy = jest.spyOn(analyticsService, 'track')
      analyticsService.trackFAQView('faq-123', '¿Qué es el impuesto predial?', 'procedures')
      
      expect(spy).toHaveBeenCalledWith('faq_view', {
        faqId: 'faq-123',
        question: '¿Qué es el impuesto predial?',
        context: 'procedures'
      })
    })

    it('debería registrar filtro de categoría', () => {
      const spy = jest.spyOn(analyticsService, 'track')
      analyticsService.trackCategoryFilter('impuestos', 'home')
      
      expect(spy).toHaveBeenCalledWith('faq_category_filter', {
        category: 'impuestos',
        context: 'home'
      })
    })

    it('debería registrar carga de sección', () => {
      const spy = jest.spyOn(analyticsService, 'track')
      analyticsService.trackSectionLoad('procedures', 250)
      
      expect(spy).toHaveBeenCalledWith('faq_section_load', {
        context: 'procedures',
        responseTime: 250
      })
    })

    it('debería registrar búsquedas sin resultados', () => {
      const spy = jest.spyOn(analyticsService, 'track')
      analyticsService.trackNoResults('término inexistente', 'home')
      
      expect(spy).toHaveBeenCalledWith('faq_no_results', {
        query: 'término inexistente',
        context: 'home'
      })
    })
  })

  describe('Gestión de cola de eventos', () => {
    beforeEach(() => {
      analyticsService = new (FAQAnalyticsService as any)()
    })

    it('debería agregar eventos a la cola', () => {
      analyticsService.track('faq_search', { query: 'test' })
      
      // Verificar que el evento se agregó (accediendo a la propiedad privada para testing)
      const queue = (analyticsService as any).eventQueue
      expect(queue).toHaveLength(1)
      expect(queue[0].event).toBe('faq_search')
      expect(queue[0].metadata.query).toBe('test')
    })

    it('debería incluir metadatos automáticos en eventos', () => {
      analyticsService.track('faq_search', { query: 'test' })
      
      const queue = (analyticsService as any).eventQueue
      const event = queue[0]
      
      expect(event.timestamp).toBeInstanceOf(Date)
      expect(event.sessionId).toMatch(/^faq_session_/)
      expect(event.metadata.page).toBe('/test-path')
    })

    it('debería hacer flush automático cuando se alcanza el tamaño del batch', async () => {
      const flushSpy = jest.spyOn(analyticsService as any, 'flush')
      
      // Configurar batch size pequeño para testing
      ;(analyticsService as any).config.batchSize = 2
      
      analyticsService.track('faq_search', { query: 'test1' })
      analyticsService.track('faq_search', { query: 'test2' })
      
      expect(flushSpy).toHaveBeenCalled()
    })
  })

  describe('Almacenamiento local', () => {
    beforeEach(() => {
      analyticsService = new (FAQAnalyticsService as any)()
    })

    it('debería guardar eventos en localStorage como fallback', async () => {
      const events = [
        {
          event: 'faq_search' as const,
          timestamp: new Date(),
          metadata: { query: 'test' }
        }
      ]
      
      await (analyticsService as any).saveToLocalStorage(events)
      
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'faq_analytics_queue',
        expect.stringContaining('faq_search')
      )
    })

    it('debería manejar errores de localStorage gracefully', async () => {
      localStorageMock.setItem.mockImplementation(() => {
        throw new Error('Storage full')
      })
      
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation()
      
      const events = [{ event: 'faq_search' as const, timestamp: new Date() }]
      await (analyticsService as any).saveToLocalStorage(events)
      
      expect(consoleSpy).toHaveBeenCalledWith(
        'Error guardando analytics en localStorage:',
        expect.any(Error)
      )
      
      consoleSpy.mockRestore()
    })

    it('debería limitar eventos almacenados a 1000', async () => {
      const existingEvents = Array.from({ length: 999 }, (_, i) => ({
        event: 'faq_search',
        timestamp: new Date(),
        metadata: { query: `test${i}` }
      }))
      
      localStorageMock.getItem.mockReturnValue(JSON.stringify(existingEvents))
      
      const newEvents = [
        { event: 'faq_search' as const, timestamp: new Date(), metadata: { query: 'new1' } },
        { event: 'faq_search' as const, timestamp: new Date(), metadata: { query: 'new2' } }
      ]
      
      await (analyticsService as any).saveToLocalStorage(newEvents)
      
      const savedData = JSON.parse(localStorageMock.setItem.mock.calls[0][1])
      expect(savedData).toHaveLength(1000) // Máximo 1000 eventos
    })
  })

  describe('Envío a API', () => {
    beforeEach(() => {
      analyticsService = new (FAQAnalyticsService as any)()
      ;(analyticsService as any).config.apiEndpoint = 'https://api.example.com/analytics'
    })

    it('debería enviar eventos a API cuando está configurada', async () => {
      const mockFetch = fetch as jest.MockedFunction<typeof fetch>
      mockFetch.mockResolvedValue({
        ok: true,
        status: 200
      } as Response)
      
      const events = [
        { event: 'faq_search' as const, timestamp: new Date(), metadata: { query: 'test' } }
      ]
      
      await (analyticsService as any).sendToAPI(events)
      
      expect(mockFetch).toHaveBeenCalledWith(
        'https://api.example.com/analytics',
        {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ events })
        }
      )
    })

    it('debería manejar errores de API', async () => {
      const mockFetch = fetch as jest.MockedFunction<typeof fetch>
      mockFetch.mockResolvedValue({
        ok: false,
        status: 500
      } as Response)
      
      const events = [
        { event: 'faq_search' as const, timestamp: new Date(), metadata: { query: 'test' } }
      ]
      
      await expect((analyticsService as any).sendToAPI(events)).rejects.toThrow('Analytics API error: 500')
    })
  })

  describe('Métricas', () => {
    beforeEach(() => {
      analyticsService = new (FAQAnalyticsService as any)()
    })

    it('debería retornar métricas agregadas', async () => {
      const metrics = await analyticsService.getMetrics()
      
      expect(metrics).toHaveProperty('totalSearches')
      expect(metrics).toHaveProperty('totalViews')
      expect(metrics).toHaveProperty('averageResponseTime')
      expect(metrics).toHaveProperty('topSearchTerms')
      expect(metrics).toHaveProperty('topFAQs')
      expect(metrics).toHaveProperty('topCategories')
      expect(metrics).toHaveProperty('searchSuccessRate')
      expect(metrics).toHaveProperty('contextUsage')
      expect(metrics).toHaveProperty('timeRange')
      
      expect(Array.isArray(metrics.topSearchTerms)).toBe(true)
      expect(Array.isArray(metrics.topFAQs)).toBe(true)
      expect(Array.isArray(metrics.topCategories)).toBe(true)
      expect(Array.isArray(metrics.contextUsage)).toBe(true)
    })

    it('debería aceptar rango de tiempo personalizado', async () => {
      const startDate = new Date('2024-01-01')
      const endDate = new Date('2024-01-31')
      
      const metrics = await analyticsService.getMetrics({ start: startDate, end: endDate })
      
      expect(metrics.timeRange.start).toEqual(startDate)
      expect(metrics.timeRange.end).toEqual(endDate)
    })
  })

  describe('Limpieza de datos', () => {
    beforeEach(() => {
      analyticsService = new (FAQAnalyticsService as any)()
    })

    it('debería limpiar cola de eventos y localStorage', () => {
      analyticsService.track('faq_search', { query: 'test' })
      
      analyticsService.clearData()
      
      const queue = (analyticsService as any).eventQueue
      expect(queue).toHaveLength(0)
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('faq_analytics_queue')
    })
  })

  describe('Contexto automático', () => {
    it('debería detectar contexto home', () => {
      Object.defineProperty(window, 'location', {
        value: { pathname: '/' },
        writable: true
      })
      
      const service = new (FAQAnalyticsService as any)()
      const context = (service as any).getCurrentContext()
      expect(context).toBe('home')
    })

    it('debería detectar contexto procedures', () => {
      Object.defineProperty(window, 'location', {
        value: { pathname: '/consulta-tramites' },
        writable: true
      })
      
      const service = new (FAQAnalyticsService as any)()
      const context = (service as any).getCurrentContext()
      expect(context).toBe('procedures')
    })

    it('debería detectar contexto management', () => {
      Object.defineProperty(window, 'location', {
        value: { pathname: '/gestion-tramites' },
        writable: true
      })
      
      const service = new (FAQAnalyticsService as any)()
      const context = (service as any).getCurrentContext()
      expect(context).toBe('management')
    })

    it('debería detectar contexto citizen', () => {
      Object.defineProperty(window, 'location', {
        value: { pathname: '/tramites' },
        writable: true
      })
      
      const service = new (FAQAnalyticsService as any)()
      const context = (service as any).getCurrentContext()
      expect(context).toBe('citizen')
    })

    it('debería retornar unknown para rutas no reconocidas', () => {
      Object.defineProperty(window, 'location', {
        value: { pathname: '/ruta-desconocida' },
        writable: true
      })
      
      const service = new (FAQAnalyticsService as any)()
      const context = (service as any).getCurrentContext()
      expect(context).toBe('unknown')
    })
  })
})
