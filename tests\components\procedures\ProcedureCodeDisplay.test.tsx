import React from 'react'
import { render, screen } from '@testing-library/react'
import { ProcedureCardEnhanced } from '@/components/procedures/ProcedureCardEnhanced'
import { PublicProcedureDetailModal } from '@/components/procedures/PublicProcedureDetailModal'

// Mock de los componentes UI
jest.mock('@/components/ui/card', () => ({
  Card: ({ children, className }: any) => <div className={className}>{children}</div>,
  CardContent: ({ children }: any) => <div>{children}</div>,
}))

jest.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick, className }: any) => (
    <button onClick={onClick} className={className}>{children}</button>
  ),
}))

jest.mock('@/components/ui/badge', () => ({
  Badge: ({ children, className, variant }: any) => (
    <span className={`badge ${variant} ${className}`}>{children}</span>
  ),
}))

jest.mock('@/components/ui/separator', () => ({
  Separator: () => <hr />,
}))

jest.mock('@/components/ui/tabs', () => ({
  Tabs: ({ children }: any) => <div>{children}</div>,
  TabsContent: ({ children }: any) => <div>{children}</div>,
  TabsList: ({ children }: any) => <div>{children}</div>,
  TabsTrigger: ({ children }: any) => <button>{children}</button>,
}))

describe('Procedure Code Display', () => {
  const mockProcedureTramite = {
    id: '1',
    name: 'Certificado de residencia',
    codigo_tramite: '000-001-001',
    description: 'Certificado que acredita la residencia del ciudadano',
    response_time: '5 días hábiles',
    cost: 0,
    procedure_type: 'TRAMITE' as const,
    dependency: {
      id: 'dep1',
      name: 'Oficina Asesora Jurídica',
      acronym: 'OAJ'
    },
    subdependency: {
      id: 'subdep1',
      name: 'Oficina Asesora Jurídica'
    }
  }

  const mockProcedureOPA = {
    id: '2',
    name: 'Radicación directa al Alcalde',
    code: '000-000-001',
    description: 'Solicitud directa al despacho del alcalde',
    response_time: '10 días hábiles',
    procedure_type: 'OPA' as const,
    dependency: {
      id: 'dep2',
      name: 'Despacho Alcalde',
      acronym: 'DA'
    },
    subdependency: {
      id: 'subdep2',
      name: 'Directo'
    }
  }

  describe('ProcedureCardEnhanced', () => {
    it('should display codigo_tramite for TRAMITE procedures', () => {
      render(
        <ProcedureCardEnhanced
          procedure={mockProcedureTramite}
          onViewDetails={() => {}}
        />
      )

      expect(screen.getByText('000-001-001')).toBeInTheDocument()
      expect(screen.getByText('Certificado de residencia')).toBeInTheDocument()
    })

    it('should display code for OPA procedures', () => {
      render(
        <ProcedureCardEnhanced
          procedure={mockProcedureOPA}
          onViewDetails={() => {}}
        />
      )

      expect(screen.getByText('000-000-001')).toBeInTheDocument()
      expect(screen.getByText('Radicación directa al Alcalde')).toBeInTheDocument()
    })

    it('should not display code badge when no code is available', () => {
      const procedureWithoutCode = {
        ...mockProcedureTramite,
        codigo_tramite: undefined
      }

      render(
        <ProcedureCardEnhanced
          procedure={procedureWithoutCode}
          onViewDetails={() => {}}
        />
      )

      expect(screen.queryByText('000-001-001')).not.toBeInTheDocument()
      expect(screen.getByText('Certificado de residencia')).toBeInTheDocument()
    })
  })

  describe('PublicProcedureDetailModal', () => {
    it('should display codigo_tramite in modal header for TRAMITE procedures', () => {
      render(
        <PublicProcedureDetailModal
          procedure={mockProcedureTramite}
          onClose={() => {}}
        />
      )

      expect(screen.getByText('Código: 000-001-001')).toBeInTheDocument()
      expect(screen.getByText('Certificado de residencia')).toBeInTheDocument()
    })

    it('should display code in modal header for OPA procedures', () => {
      render(
        <PublicProcedureDetailModal
          procedure={mockProcedureOPA}
          onClose={() => {}}
        />
      )

      expect(screen.getByText('Código: 000-000-001')).toBeInTheDocument()
      expect(screen.getByText('Radicación directa al Alcalde')).toBeInTheDocument()
    })

    it('should not display code badge in modal when no code is available', () => {
      const procedureWithoutCode = {
        ...mockProcedureTramite,
        codigo_tramite: undefined
      }

      render(
        <PublicProcedureDetailModal
          procedure={procedureWithoutCode}
          onClose={() => {}}
        />
      )

      expect(screen.queryByText(/Código:/)).not.toBeInTheDocument()
      expect(screen.getByText('Certificado de residencia')).toBeInTheDocument()
    })
  })

  describe('Code Format Validation', () => {
    it('should display codes in correct format XXX-XXX-XXX', () => {
      render(
        <ProcedureCardEnhanced
          procedure={mockProcedureTramite}
          onViewDetails={() => {}}
        />
      )

      const codeElement = screen.getByText('000-001-001')
      expect(codeElement).toBeInTheDocument()
      expect(codeElement.textContent).toMatch(/^\d{3}-\d{3}-\d{3}$/)
    })

    it('should apply correct styling to code badges', () => {
      render(
        <ProcedureCardEnhanced
          procedure={mockProcedureTramite}
          onViewDetails={() => {}}
        />
      )

      const codeElement = screen.getByText('000-001-001')
      expect(codeElement).toHaveClass('badge')
    })
  })
})
