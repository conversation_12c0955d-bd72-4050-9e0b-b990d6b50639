/**
 * API Route para el chatbot municipal
 * Maneja las consultas del chatbot usando RAG (Retrieval-Augmented Generation)
 */

import { NextRequest, NextResponse } from 'next/server';
import { generateRAGResponse } from '@/lib/rag-system';
import { createClient } from '@supabase/supabase-js';

// Configurar Supabase para autenticación
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

/**
 * Tipos para la API
 */
interface ChatRequest {
  message: string;
  userRole?: string;
  conversationHistory?: Array<{
    role: 'user' | 'assistant' | 'system';
    content: string;
  }>;
  sessionId?: string;
}

interface ChatResponse {
  answer: string;
  sources: Array<{
    id: string;
    type: 'procedure' | 'opa' | 'knowledge';
    title: string;
    content: string;
    similarity: number;
  }>;
  sessionId?: string;
  tokensUsed?: number;
  responseTime?: number;
}

/**
 * POST /api/chat
 * Procesa mensajes del chatbot y genera respuestas usando RAG
 */
export async function POST(request: NextRequest) {
  const startTime = Date.now();

  try {
    // Verificar autenticación
    const authHeader = request.headers.get('authorization');
    let userId: string | null = null;
    let userRole = 'ciudadano';

    if (authHeader) {
      const token = authHeader.replace('Bearer ', '');
      const { data: { user }, error } = await supabase.auth.getUser(token);
      
      if (!error && user) {
        userId = user.id;
        
        // Obtener rol del usuario
        const { data: profile } = await supabase
          .from('profiles')
          .select('roles(name)')
          .eq('id', userId)
          .single();
        
        if (profile?.roles?.name) {
          userRole = profile.roles.name;
        }
      }
    }

    // Parsear el cuerpo de la solicitud
    const body: ChatRequest = await request.json();
    const { message, conversationHistory = [], sessionId } = body;

    // Validar entrada
    if (!message || typeof message !== 'string' || message.trim().length === 0) {
      return NextResponse.json(
        { error: 'El mensaje es requerido y no puede estar vacío' },
        { status: 400 }
      );
    }

    if (message.length > 1000) {
      return NextResponse.json(
        { error: 'El mensaje es demasiado largo (máximo 1000 caracteres)' },
        { status: 400 }
      );
    }

    // Generar respuesta usando RAG
    const ragResponse = await generateRAGResponse(message, userRole, {
      includeAllContent: true,
      maxSources: 5,
      temperature: 0.3
    });

    // Calcular tiempo de respuesta
    const responseTime = Date.now() - startTime;

    // Guardar conversación en la base de datos (si el usuario está autenticado)
    let conversationId: string | undefined;
    
    if (userId) {
      try {
        // Crear o obtener conversación
        let conversation;
        
        if (sessionId) {
          const { data: existingConversation } = await supabase
            .from('chat_conversations')
            .select('id')
            .eq('session_id', sessionId)
            .eq('user_id', userId)
            .single();
          
          conversation = existingConversation;
        }
        
        if (!conversation) {
          const { data: newConversation, error: conversationError } = await supabase
            .from('chat_conversations')
            .insert({
              user_id: userId,
              session_id: sessionId || `session_${Date.now()}`,
              title: message.substring(0, 50) + (message.length > 50 ? '...' : ''),
              is_active: true
            })
            .select('id')
            .single();
          
          if (conversationError) {
            console.error('Error creating conversation:', conversationError);
          } else {
            conversation = newConversation;
          }
        }
        
        conversationId = conversation?.id;
        
        // Guardar mensajes
        if (conversationId) {
          const messagesToSave = [
            {
              conversation_id: conversationId,
              role: 'user',
              content: message,
              tokens_used: 0,
              response_time_ms: null,
              sources: []
            },
            {
              conversation_id: conversationId,
              role: 'assistant',
              content: ragResponse.answer,
              tokens_used: 0, // TODO: Calcular tokens reales
              response_time_ms: responseTime,
              sources: ragResponse.sources
            }
          ];
          
          const { error: messagesError } = await supabase
            .from('chat_messages')
            .insert(messagesToSave);
          
          if (messagesError) {
            console.error('Error saving messages:', messagesError);
          }
        }
      } catch (dbError) {
        console.error('Error saving to database:', dbError);
        // No fallar la respuesta por errores de base de datos
      }
    }

    // Preparar respuesta
    const response: ChatResponse = {
      answer: ragResponse.answer,
      sources: ragResponse.sources,
      sessionId: sessionId || `session_${Date.now()}`,
      responseTime
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Error in chat API:', error);
    
    // Determinar tipo de error y respuesta apropiada
    if (error instanceof Error) {
      if (error.message.includes('OpenAI')) {
        return NextResponse.json(
          { error: 'Error en el servicio de IA. Por favor, intenta nuevamente.' },
          { status: 503 }
        );
      }
      
      if (error.message.includes('embedding') || error.message.includes('vector')) {
        return NextResponse.json(
          { error: 'Error en la búsqueda de información. Por favor, intenta nuevamente.' },
          { status: 503 }
        );
      }
    }

    return NextResponse.json(
      { error: 'Error interno del servidor. Por favor, intenta nuevamente.' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/chat
 * Obtiene el historial de conversaciones del usuario
 */
export async function GET(request: NextRequest) {
  try {
    // Verificar autenticación
    const authHeader = request.headers.get('authorization');
    
    if (!authHeader) {
      return NextResponse.json(
        { error: 'Token de autenticación requerido' },
        { status: 401 }
      );
    }

    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Token de autenticación inválido' },
        { status: 401 }
      );
    }

    // Obtener parámetros de consulta
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = parseInt(searchParams.get('offset') || '0');

    // Obtener conversaciones del usuario
    const { data: conversations, error } = await supabase
      .from('chat_conversations')
      .select(`
        id,
        session_id,
        title,
        created_at,
        updated_at,
        chat_messages(
          id,
          role,
          content,
          created_at
        )
      `)
      .eq('user_id', user.id)
      .eq('is_active', true)
      .order('updated_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('Error fetching conversations:', error);
      return NextResponse.json(
        { error: 'Error al obtener el historial de conversaciones' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      conversations: conversations || [],
      total: conversations?.length || 0
    });

  } catch (error) {
    console.error('Error in chat GET API:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

/**
 * OPTIONS /api/chat
 * Maneja preflight requests para CORS
 */
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
