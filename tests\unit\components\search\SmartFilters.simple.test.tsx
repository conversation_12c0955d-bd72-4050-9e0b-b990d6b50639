import React from 'react'
import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { SmartFilters, FilterState } from '../../../../components/search/SmartFilters'

// Simple test to verify the component renders without the Select error
describe('SmartFilters - Simple Test', () => {
  const mockDependencies = [
    { id: 'dep1', name: 'Secretaría de Planeación' },
    { id: 'dep2', name: 'Secretaría de Gobierno' },
    { id: 'dep3', name: 'Secretaría de Hacienda' }
  ]

  const mockOnFiltersChange = jest.fn()

  const defaultProps = {
    dependencies: mockDependencies,
    onFiltersChange: mockOnFiltersChange
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should render without crashing', () => {
    render(<SmartFilters {...defaultProps} />)
    
    // Should render the filter button
    expect(screen.getByRole('button', { name: /filtros/i })).toBeInTheDocument()
  })

  it('should expand and show filter sections when clicked', async () => {
    const user = userEvent.setup()
    render(<SmartFilters {...defaultProps} />)

    // Click to expand filters
    const filterButton = screen.getByRole('button', { name: /filtros/i })
    await user.click(filterButton)

    // Should show filter labels
    expect(screen.getByText('Dependencia')).toBeInTheDocument()
    expect(screen.getByText('Modalidad')).toBeInTheDocument()
    expect(screen.getByText('Tiempo de Respuesta')).toBeInTheDocument()
    expect(screen.getByText('Costo')).toBeInTheDocument()
    expect(screen.getByText('Tipo')).toBeInTheDocument()
  })

  it('should show clear filters button when filters are active', () => {
    const initialFilters = { dependency: 'dep1' }
    render(<SmartFilters {...defaultProps} initialFilters={initialFilters} />)

    // Should show clear filters button
    expect(screen.getByRole('button', { name: /limpiar filtros/i })).toBeInTheDocument()
    
    // Should show filter count badge
    expect(screen.getByText('1')).toBeInTheDocument()
  })

  it('should call onFiltersChange when initialized with filters', () => {
    const initialFilters = { dependency: 'dep1', cost: 'gratuito' }
    render(<SmartFilters {...defaultProps} initialFilters={initialFilters} />)

    // Should call onFiltersChange with initial filters
    expect(mockOnFiltersChange).toHaveBeenCalledWith(initialFilters)
  })

  it('should clear all filters when clear button is clicked', async () => {
    const user = userEvent.setup()
    const initialFilters = { dependency: 'dep1', cost: 'gratuito' }
    render(<SmartFilters {...defaultProps} initialFilters={initialFilters} />)

    // Click clear filters button
    const clearButton = screen.getByRole('button', { name: /limpiar filtros/i })
    await user.click(clearButton)

    // Should call onFiltersChange with empty filters
    expect(mockOnFiltersChange).toHaveBeenCalledWith({})
  })
})
