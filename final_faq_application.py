#!/usr/bin/env python3
"""
Final FAQ application - Apply all remaining questions efficiently
"""

import re
import json

def extract_remaining_questions():
    """Extract remaining questions from corrected file"""
    
    # Load the corrected SQL file
    with open('corrected_faq_insertion.sql', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Extract all INSERT statements
    insert_statements = re.findall(r'INSERT INTO municipal_faqs.*?;', content, re.DOTALL)
    
    print(f"Found {len(insert_statements)} total INSERT statements")
    
    # Since we have 20 questions already, we need the remaining 363
    remaining_statements = insert_statements[20:]  # Skip first 20
    
    print(f"Need to apply {len(remaining_statements)} remaining questions")
    
    # Create final application file with all remaining questions
    with open('final_faq_application.sql', 'w', encoding='utf-8') as f:
        f.write("-- FINAL FAQ APPLICATION\n")
        f.write(f"-- Applying remaining {len(remaining_statements)} questions\n")
        f.write("-- This completes the 383 FAQ questions integration\n\n")
        
        # Write in manageable batches for readability
        batch_size = 50
        for i in range(0, len(remaining_statements), batch_size):
            batch = remaining_statements[i:i+batch_size]
            batch_num = i // batch_size + 1
            
            f.write(f"-- Batch {batch_num}: Questions {i+21} to {min(i+20+batch_size, 383)}\n")
            f.write("\n".join(batch))
            f.write("\n\n")
    
    print(f"Created final_faq_application.sql with {len(remaining_statements)} questions")
    return len(remaining_statements)

def create_summary_report():
    """Create a summary report of the FAQ integration"""
    
    summary = {
        "total_questions_target": 383,
        "questions_already_applied": 20,
        "questions_remaining": 363,
        "total_themes": 37,
        "status": "Ready for final application"
    }
    
    with open('faq_integration_summary.json', 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2, ensure_ascii=False)
    
    print("Created faq_integration_summary.json")
    return summary

def main():
    """Main function"""
    print("Final FAQ Application Processor")
    print("=" * 35)
    
    try:
        # Extract remaining questions
        remaining_count = extract_remaining_questions()
        
        # Create summary report
        summary = create_summary_report()
        
        print(f"\n✅ Successfully prepared final application")
        print(f"📊 Questions already in database: {summary['questions_already_applied']}")
        print(f"📊 Questions to be applied: {remaining_count}")
        print(f"📊 Total target: {summary['total_questions_target']}")
        print(f"📁 Final application file: final_faq_application.sql")
        print(f"📋 Summary report: faq_integration_summary.json")
        
        print(f"\n🚀 Ready to complete FAQ integration!")
        print(f"   Apply final_faq_application.sql to complete all 383 questions")
        
        return 0
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
