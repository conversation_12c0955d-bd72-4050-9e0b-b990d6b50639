#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Actualizador de códigos de FAQs para el sistema municipal de Chía
Reemplaza códigos generados automáticamente con códigos oficiales de la base de datos
"""

import json
import re
from typing import Dict, List, Tuple, Optional

# Mapeo de dependencias oficiales de la base de datos
OFFICIAL_DEPENDENCIES = {
    "000": "Despacho Alcalde",
    "010": "Secretaria de Planeacion", 
    "020": "Secretaria General",
    "030": "Secretaria de Gobierno",
    "040": "Secretaria de Hacienda",
    "050": "Secretaria de Obras Publicas",
    "060": "Secretaria de Desarrollo Social",
    "070": "Secretaria de Educacion",
    "080": "Secretaria de Salud",
    "090": "Secretaria para el Desarrollo Economico",
    "100": "Secretaria de Medio Ambiente",
    "110": "Secretaria de Movilidad",
    "120": "Secretaria de Participacion Ciudadana y Accion Comunitaria",
    "200": "Descentralizados"
}

# Mapeo de subdependencias oficiales de la base de datos
OFFICIAL_SUBDEPENDENCIES = {
    # Despacho Alcalde (000)
    "000": ("000", "Directo"),
    "001": ("000", "Oficina Asesora Jurídica"),
    "002": ("000", "Oficina de Contratación"),
    "003": ("000", "Oficina Defensa Judicial"),
    "004": ("000", "Oficina de Control Interno"),
    "005": ("000", "Oficina de tecnologías de la información y las comunicaciones TICS"),
    "006": ("000", "Oficina Asesora de comunicación Prensa y Protocolo"),
    
    # Secretaria de Planeacion (010)
    "010": ("010", "Directo"),
    "011": ("010", "Dirección Sistemas de la Información y Estadísticas"),
    "012": ("010", "Dirección de Planificación del Desarrollo"),
    "013": ("010", "Dirección de Ordenamiento Territorial y Plusvalía"),
    "014": ("010", "Dirección de Urbanismo"),
    "015": ("010", "Dirección de Servicios Públicos"),
    
    # Secretaria General (020)
    "020": ("020", "Directo"),
    "021": ("020", "Dirección de Función Publica"),
    "022": ("020", "Dirección de Servicios Administrativos"),
    "023": ("020", "Dirección Centro de Atención al Ciudadano"),
    "024": ("020", "Dirección de Control Interno Disciplinario"),
    
    # Secretaria de Gobierno (030)
    "030": ("030", "Directo"),
    "031": ("030", "Dirección de seguridad y Convivencia Ciudadana"),
    "032": ("030", "Dirección de Asuntos Étnicos Raciales Religiosos y Posconflicto"),
    "033": ("030", "Dirección de Derechos y Resolución de Conflictos"),
    "330": ("030", "Comisaria Primera de Familia"),
    "331": ("030", "Comisaria Segunda de Familia"),
    "332": ("030", "Comisaria Tercera de Familia"),
    "333": ("030", "Comisaria Cuarta de Familia"),
    "334": ("030", "Inspección Primera de Policía"),
    "335": ("030", "Inspección Segunda de Policía"),
    "336": ("030", "Inspección Tercera de Policía"),
    "337": ("030", "Inspección Cuarta de Policía"),
    "338": ("030", "Inspección Quinta de Policía"),
    "339": ("030", "Inspección Sexta de Policía"),
    
    # Secretaria de Hacienda (040)
    "040": ("040", "Directo"),
    "041": ("040", "Dirección de Rentas"),
    "042": ("040", "Dirección Financiera"),
    "043": ("040", "Dirección de Tesorería"),
    
    # Secretaria de Obras Publicas (050)
    "050": ("050", "Directo"),
    "051": ("050", "Dirección de Infraestructura"),
    "052": ("050", "Dirección de Programación, Estudios y Diseños"),
    "053": ("050", "Dirección de Valorización"),
    
    # Secretaria de Desarrollo Social (060)
    "060": ("060", "Directo"),
    "061": ("060", "Dirección de Ciudadanía Juvenil"),
    "062": ("060", "Dirección de Acción Social"),
    "063": ("060", "Dirección de Cultura"),
    
    # Secretaria de Educacion (070)
    "070": ("070", "Directo"),
    "071": ("070", "Dirección de Inspección y Vigilancia"),
    "072": ("070", "Dirección de Gestión y Fomento a la Educación"),
    "073": ("070", "Dirección Administrativa y Financiera"),
    
    # Secretaria de Salud (080)
    "080": ("080", "Directo"),
    "081": ("080", "Dirección de Salud Publica"),
    "082": ("080", "Dirección de Vigilancia y Control"),
    
    # Secretaria para el Desarrollo Economico (090)
    "090": ("090", "Directo"),
    "091": ("090", "Dirección de Desarrollo Agropecuario y Empresarial"),
    "092": ("090", "Dirección de Turismo"),
    
    # Secretaria de Medio Ambiente (100)
    "101": ("100", "Forestal"),
    "102": ("100", "Control y conservación ambiental"),
    "103": ("100", "Sistema hídrico"),
    "104": ("100", "Informes Técnicos"),
    "105": ("100", "PGIRS"),
    "106": ("100", "Biodiversidad"),
    "107": ("100", "Educación ambiental"),
    "108": ("100", "Visita Técnica vigilancia y control fuentes contaminantes"),
    "109": ("100", "Publicidad"),
    
    # Secretaria de Movilidad (110)
    "110": ("110", "Directo"),
    "111": ("110", "Dirección de Servicios de Movilidad y Gestión del Transporte"),
    "112": ("110", "Dirección de Educación, Seguridad Vial y Control de Transito"),
    "113": ("110", "Unión Temporal Circulemos Chía"),
    
    # Secretaria de Participacion Ciudadana y Accion Comunitaria (120)
    "120": ("120", "Directo"),
    
    # Descentralizados (200)
    "201": ("200", "IDUVI"),
    "202": ("200", "IMRD"),
    "203": ("200", "EMSERCHIA"),
    "204": ("200", "PERSONERIA"),
    "212": ("200", "AGUSTIN CODAZZI"),
    "214": ("200", "CUERPO DE BOMBEROS CHIA"),
    "215": ("200", "DEFENSA CIVIL COLOMBIANA")
}

def normalize_text(text: str) -> str:
    """Normaliza texto para comparación"""
    if not text:
        return ""
    
    # Convertir a minúsculas y remover acentos/caracteres especiales
    text = text.lower()
    replacements = {
        'á': 'a', 'é': 'e', 'í': 'i', 'ó': 'o', 'ú': 'u', 'ñ': 'n',
        'ü': 'u', 'ç': 'c'
    }
    for old, new in replacements.items():
        text = text.replace(old, new)
    
    # Remover caracteres especiales y espacios extra
    text = re.sub(r'[^\w\s]', ' ', text)
    text = re.sub(r'\s+', ' ', text).strip()
    
    return text

def find_dependency_match(faq_dep_name: str) -> Optional[str]:
    """Encuentra el código de dependencia que mejor coincida"""
    faq_normalized = normalize_text(faq_dep_name)
    
    # Mapeo directo de nombres FAQ a códigos oficiales
    direct_mappings = {
        "despacho del alcalde": "000",
        "secretaria de gobierno": "030", 
        "secretaria de hacienda": "040",
        "secretaria de desarrollo social": "060",
        "secretaria de educacion": "070",
        "secretaria de salud": "080",
        "secretaria para el desarrollo economico": "090",
        "secretaria de movilidad": "110",
        "secretaria de participacion ciudadana y accion comunitaria": "120"
    }
    
    if faq_normalized in direct_mappings:
        return direct_mappings[faq_normalized]
    
    # Búsqueda por similitud
    for code, official_name in OFFICIAL_DEPENDENCIES.items():
        official_normalized = normalize_text(official_name)
        if official_normalized in faq_normalized or faq_normalized in official_normalized:
            return code
    
    return None

def find_subdependency_match(faq_subdep_name: str, dependency_code: str) -> Optional[str]:
    """Encuentra el código de subdependencia que mejor coincida"""
    if not faq_subdep_name:
        return None
        
    faq_normalized = normalize_text(faq_subdep_name)
    
    # Mapeo directo de nombres FAQ a códigos oficiales
    direct_mappings = {
        "oficina de tecnologias de la informacion – tic": "005",
        "oficina de tecnologias de la informacion y las comunicaciones tics": "005",
        "direccion financiera": "042",
        "direccion de rentas": "041",
        "direccion de accion social": "062",
        "direccion ciudadania juvenil": "061",
        "direccion cultura": "063",
        "direccion de desarrollo agropecuario y empresarial": "091",
        "direccion de turismo": "092",
        "gestion del riesgo de desastres": None,  # No existe en BD
        "direccion de seguridad y convivencia ciudadana": "031",
        "direccion asuntos etnicos, raciales, religiosos y posconflicto": "032",
        "direccion de derechos y resolucion de conflictos": "033"
    }
    
    if faq_normalized in direct_mappings:
        return direct_mappings[faq_normalized]
    
    # Búsqueda por similitud dentro de la dependencia
    for code, (dep_code, subdep_name) in OFFICIAL_SUBDEPENDENCIES.items():
        if dep_code == dependency_code:
            subdep_normalized = normalize_text(subdep_name)
            if (subdep_normalized in faq_normalized or 
                faq_normalized in subdep_normalized or
                any(word in subdep_normalized for word in faq_normalized.split() if len(word) > 3)):
                return code
    
    return None

def update_faq_codes(input_file: str, output_file: str) -> Dict:
    """Actualiza los códigos del archivo FAQ con códigos oficiales"""
    
    print("🔄 Cargando archivo FAQ original...")
    with open(input_file, 'r', encoding='utf-8') as f:
        faq_data = json.load(f)
    
    # Estadísticas de mapeo
    stats = {
        "dependencies_mapped": 0,
        "dependencies_not_found": [],
        "subdependencies_mapped": 0,
        "subdependencies_not_found": [],
        "total_entries": len(faq_data["faqs"])
    }
    
    print("🔍 Mapeando dependencias y subdependencias...")
    
    for faq_entry in faq_data["faqs"]:
        dep_name = faq_entry["dependencia"]
        subdep_name = faq_entry["subdependencia"]
        
        # Mapear dependencia
        dep_code = find_dependency_match(dep_name)
        if dep_code:
            faq_entry["codigo_dependencia"] = dep_code
            stats["dependencies_mapped"] += 1
            print(f"✅ {dep_name} → {dep_code}")
        else:
            stats["dependencies_not_found"].append(dep_name)
            print(f"❌ No encontrada: {dep_name}")
        
        # Mapear subdependencia
        if subdep_name and dep_code:
            subdep_code = find_subdependency_match(subdep_name, dep_code)
            if subdep_code:
                faq_entry["codigo_subdependencia"] = subdep_code
                stats["subdependencies_mapped"] += 1
                print(f"  ✅ {subdep_name} → {subdep_code}")
            else:
                stats["subdependencies_not_found"].append(f"{dep_name} > {subdep_name}")
                faq_entry["codigo_subdependencia"] = ""
                print(f"  ❌ No encontrada: {subdep_name}")
        elif not subdep_name:
            faq_entry["codigo_subdependencia"] = ""
    
    # Guardar archivo actualizado
    print(f"💾 Guardando archivo actualizado: {output_file}")
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(faq_data, f, ensure_ascii=False, indent=2)
    
    return stats

def main():
    """Función principal"""
    print("🏛️ ACTUALIZADOR DE CÓDIGOS FAQ - SISTEMA MUNICIPAL DE CHÍA")
    print("=" * 70)
    
    try:
        # Actualizar códigos
        stats = update_faq_codes(
            'faqs_chia_estructurado.json',
            'faqs_chia_estructurado_updated.json'
        )
        
        # Mostrar estadísticas
        print("\n📊 ESTADÍSTICAS DE MAPEO:")
        print(f"   Total de entradas procesadas: {stats['total_entries']}")
        print(f"   Dependencias mapeadas: {stats['dependencies_mapped']}")
        print(f"   Subdependencias mapeadas: {stats['subdependencies_mapped']}")
        
        if stats['dependencies_not_found']:
            print(f"\n❌ DEPENDENCIAS NO ENCONTRADAS ({len(stats['dependencies_not_found'])}):")
            for dep in stats['dependencies_not_found']:
                print(f"   - {dep}")
        
        if stats['subdependencies_not_found']:
            print(f"\n❌ SUBDEPENDENCIAS NO ENCONTRADAS ({len(stats['subdependencies_not_found'])}):")
            for subdep in stats['subdependencies_not_found']:
                print(f"   - {subdep}")
        
        print(f"\n✅ Proceso completado exitosamente!")
        print(f"   Archivo actualizado: faqs_chia_estructurado_updated.json")
        
    except Exception as e:
        print(f"❌ Error durante el procesamiento: {e}")
        raise

if __name__ == "__main__":
    main()
