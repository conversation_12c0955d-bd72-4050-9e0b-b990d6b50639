'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { ProtectedNavigation } from '@/components/navigation/ProtectedNavigation'
import { useAuth } from '@/hooks'

interface ProtectedLayoutProps {
  children: React.ReactNode
  requireAuth?: boolean
  allowedRoles?: string[]
  fallbackUrl?: string
}

export function ProtectedLayout({ 
  children, 
  requireAuth = true,
  allowedRoles,
  fallbackUrl = '/auth/login'
}: ProtectedLayoutProps) {
  const { user, profile, loading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!loading) {
      // Check authentication requirement
      if (requireAuth && !user) {
        router.push(fallbackUrl)
        return
      }

      // Check role requirements
      if (allowedRoles && profile && !allowedRoles.includes(profile.role?.name || '')) {
        // Redirect based on user role
        if (profile.role?.name === 'ciudadano') {
          router.push('/dashboard')
        } else if (profile.role?.name === 'admin' || profile.role?.name === 'super_admin') {
          router.push('/admin')
        } else {
          router.push('/auth/login')
        }
        return
      }
    }
  }, [user, profile, loading, requireAuth, allowedRoles, router, fallbackUrl])

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 bg-chia-blue-600 rounded-lg flex items-center justify-center mx-auto mb-4 animate-pulse">
            <span className="text-white font-bold text-lg">C</span>
          </div>
          <p className="text-gray-600">Cargando...</p>
        </div>
      </div>
    )
  }

  // Don't render if authentication is required but user is not authenticated
  if (requireAuth && !user) {
    return null
  }

  // Don't render if role requirements are not met
  if (allowedRoles && profile && !allowedRoles.includes(profile.role?.name || '')) {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation - only show for authenticated users */}
      {user && profile && <ProtectedNavigation />}
      
      {/* Main Content */}
      <main className="flex-1">
        {children}
      </main>
      
      {/* Footer */}
      <footer className="bg-white border-t mt-auto">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center space-x-3 mb-4 md:mb-0">
              <div className="w-6 h-6 bg-chia-blue-600 rounded flex items-center justify-center">
                <span className="text-white font-bold text-xs">C</span>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-900">Municipio de Chía</p>
                <p className="text-xs text-gray-500">Sistema de Atención Ciudadana</p>
              </div>
            </div>
            
            <div className="flex flex-col md:flex-row items-center space-y-2 md:space-y-0 md:space-x-6 text-sm text-gray-500">
              <a href="/informacion" className="hover:text-chia-blue-600 transition-colors">
                Información
              </a>
              <a href="/contacto" className="hover:text-chia-blue-600 transition-colors">
                Contacto
              </a>
              <a href="/ayuda" className="hover:text-chia-blue-600 transition-colors">
                Ayuda
              </a>
              <a href="/privacidad" className="hover:text-chia-blue-600 transition-colors">
                Privacidad
              </a>
            </div>
            
            <div className="text-xs text-gray-400 mt-4 md:mt-0">
              © 2024 Municipio de Chía. Todos los derechos reservados.
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
