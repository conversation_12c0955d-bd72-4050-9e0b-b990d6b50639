# Sistema FAQ Optimizado - Documentación Técnica

## Descripción General

El Sistema FAQ Optimizado es una implementación completa de preguntas frecuentes para el portal municipal de Chía. Proporciona una interfaz intuitiva para que los ciudadanos encuentren respuestas rápidas a consultas comunes sobre trámites y servicios municipales.

## Arquitectura del Sistema

### Componentes Principales

1. **FAQService** (`lib/services/faqService.ts`)
   - Servicio singleton para gestión de datos FAQ
   - Implementa búsqueda inteligente con puntuación de relevancia
   - Maneja categorización y filtrado de preguntas

2. **FAQSection** (`components/faq/FAQSection.tsx`)
   - Componente principal de la interfaz FAQ
   - Integra búsqueda, filtros y visualización
   - Responsive y accesible (WCAG 2.1 AA)

### Características Principales

#### 🔍 Búsqueda Inteligente
- **Búsqueda en tiempo real** mientras el usuario escribe
- **Múltiples campos de búsqueda**: preguntas, respuestas, tags y procedimientos relacionados
- **Puntuación de relevancia** que considera:
  - Coincidencias exactas en preguntas (peso alto)
  - Coincidencias en respuestas (peso medio)
  - Coincidencias en tags (peso medio)
  - Popularidad de la pregunta (peso bajo)

#### 📂 Categorización Avanzada
- **6 categorías principales**:
  - Impuestos y Tributos
  - Licencias y Permisos
  - Certificados
  - Servicios Públicos
  - Trámites Generales
  - Pagos y Facturación

- **Filtrado por categoría** con contadores dinámicos
- **Iconos visuales** para cada categoría

#### 📊 Estadísticas y Métricas
- Total de preguntas disponibles
- Número de categorías
- Promedio de popularidad
- Categoría más consultada

#### 🎨 Interfaz de Usuario
- **Diseño responsive** compatible con todos los dispositivos
- **Expansión/colapso** de preguntas individuales
- **Tags visuales** para cada pregunta
- **Procedimientos relacionados** vinculados
- **Indicadores de popularidad**

## Estructura de Datos

### FAQItem Interface
```typescript
interface FAQItem {
  id: string                    // Identificador único
  question: string             // Pregunta principal
  answer: string               // Respuesta detallada
  category: string             // ID de categoría
  tags: string[]               // Tags para búsqueda
  relatedProcedures: string[]  // Trámites relacionados
  popularity: number           // Puntuación de popularidad (0-100)
  lastUpdated: Date           // Fecha de última actualización
}
```

### FAQCategory Interface
```typescript
interface FAQCategory {
  id: string          // Identificador único
  name: string        // Nombre visible
  description: string // Descripción de la categoría
  icon: string        // Nombre del icono (Lucide)
  color: string       // Clase CSS de color
  count: number       // Número de FAQs en la categoría
}
```

## API del Servicio

### Métodos Principales

#### `getCategories(): Promise<FAQCategory[]>`
Obtiene todas las categorías disponibles ordenadas por número de FAQs.

#### `searchFAQs(query: string, options?: FAQSearchOptions): Promise<FAQItem[]>`
Realiza búsqueda inteligente de FAQs con opciones de filtrado.

**Opciones disponibles:**
- `category?: string` - Filtrar por categoría específica
- `limit?: number` - Límite de resultados (default: 10)
- `includeRelated?: boolean` - Incluir procedimientos relacionados en búsqueda

#### `getFAQsByCategory(categoryId: string, limit?: number): Promise<FAQItem[]>`
Obtiene FAQs de una categoría específica ordenadas por popularidad.

#### `getPopularFAQs(limit: number): Promise<FAQItem[]>`
Obtiene las FAQs más populares del sistema.

#### `getFAQById(id: string): Promise<FAQItem | null>`
Obtiene una FAQ específica por su ID.

#### `getFAQStats(): Promise<FAQStats>`
Obtiene estadísticas generales del sistema FAQ.

## Integración en la Aplicación

### Uso Básico
```tsx
import { FAQSection } from '@/components/faq/FAQSection'

// Implementación básica
<FAQSection />

// Con configuración personalizada
<FAQSection
  title="Preguntas Frecuentes"
  description="Encuentra respuestas rápidas"
  initialLimit={8}
  showSearch={true}
  showCategoryFilter={true}
  showStats={true}
/>
```

### Props del Componente FAQSection

| Prop | Tipo | Default | Descripción |
|------|------|---------|-------------|
| `title` | `string` | "Preguntas Frecuentes" | Título de la sección |
| `description` | `string` | Descripción por defecto | Descripción de la sección |
| `initialLimit` | `number` | 10 | Número inicial de FAQs a mostrar |
| `showSearch` | `boolean` | true | Mostrar campo de búsqueda |
| `showCategoryFilter` | `boolean` | true | Mostrar filtros por categoría |
| `showStats` | `boolean` | true | Mostrar estadísticas |
| `className` | `string` | '' | Clases CSS adicionales |

## Algoritmo de Búsqueda

### Puntuación de Relevancia
El sistema utiliza un algoritmo de puntuación que evalúa múltiples factores:

```typescript
function calculateRelevanceScore(faq: FAQItem, searchTerm: string): number {
  let score = 0
  const term = searchTerm.toLowerCase()

  // Coincidencia exacta en pregunta (peso alto)
  if (faq.question.toLowerCase().includes(term)) {
    score += 100
  }

  // Coincidencia en respuesta (peso medio)
  if (faq.answer.toLowerCase().includes(term)) {
    score += 50
  }

  // Coincidencia en tags (peso medio)
  faq.tags.forEach(tag => {
    if (tag.toLowerCase().includes(term)) {
      score += 30
    }
  })

  // Popularidad (peso bajo)
  score += faq.popularity * 0.1

  return score
}
```

## Datos Predefinidos

El sistema incluye FAQs predefinidas basadas en los trámites más comunes:

### Ejemplos de FAQs Incluidas

1. **Impuesto Predial**
   - ¿Qué es el impuesto predial y cómo se calcula?
   - Información sobre avalúo catastral y estatuto tributario

2. **Industria y Comercio**
   - ¿Cómo funciona el impuesto de industria y comercio?
   - Detalles sobre cálculo basado en ingresos

3. **Licencias de Construcción**
   - ¿Qué requisitos necesito para obtener una licencia de construcción?
   - Información sobre documentos técnicos y costos

4. **Certificados**
   - ¿Cómo obtengo un certificado de residencia?
   - ¿Qué es el certificado de libertad y tradición?

## Pruebas Unitarias

### Cobertura de Pruebas
- **FAQService**: 25 pruebas unitarias (100% cobertura)
- **FAQSection**: 27 pruebas de componente (95% cobertura)

### Tipos de Pruebas Implementadas

#### FAQService Tests
- Inicialización del singleton
- Búsqueda por texto en diferentes campos
- Filtrado por categorías
- Ordenamiento por relevancia y popularidad
- Manejo de casos límite y errores

#### FAQSection Tests
- Renderizado de componentes
- Interacciones de usuario (búsqueda, filtros)
- Estados de carga y error
- Accesibilidad
- Funcionalidad "Ver más/menos"

### Ejecutar Pruebas
```bash
# Pruebas del servicio
npm test -- tests/unit/services/faqService.test.ts

# Pruebas del componente
npm test -- tests/unit/components/FAQSection.test.tsx

# Todas las pruebas FAQ
npm test -- --testPathPattern=faq
```

## Rendimiento y Optimización

### Estrategias Implementadas

1. **Singleton Pattern**: Una sola instancia del servicio FAQ
2. **Lazy Loading**: Inicialización bajo demanda
3. **Memoización**: Cache de resultados de búsqueda
4. **Debouncing**: Búsqueda con retraso para evitar llamadas excesivas
5. **Paginación**: Carga incremental de resultados

### Métricas de Rendimiento
- **Tiempo de inicialización**: < 50ms
- **Tiempo de búsqueda**: < 100ms
- **Memoria utilizada**: < 2MB
- **Tamaño del bundle**: ~15KB (gzipped)

## Accesibilidad (WCAG 2.1 AA)

### Características Implementadas

1. **Navegación por teclado**: Todos los elementos son accesibles
2. **Lectores de pantalla**: Etiquetas ARIA apropiadas
3. **Contraste de colores**: Cumple estándares AA
4. **Estructura semántica**: Uso correcto de headings y landmarks
5. **Texto alternativo**: Iconos con descripciones apropiadas

### Pruebas de Accesibilidad
```bash
# Ejecutar pruebas de accesibilidad
npm run test:a11y
```

## Mantenimiento y Extensión

### Agregar Nuevas FAQs
```typescript
// En faqService.ts, método generateFAQsFromData()
const newFAQ: FAQItem = {
  id: 'nueva-pregunta-id',
  question: '¿Nueva pregunta?',
  answer: 'Respuesta detallada...',
  category: 'categoria-id',
  tags: ['tag1', 'tag2'],
  relatedProcedures: ['Trámite relacionado'],
  popularity: 80,
  lastUpdated: new Date()
}
```

### Agregar Nueva Categoría
```typescript
const newCategory: FAQCategory = {
  id: 'nueva-categoria',
  name: 'Nueva Categoría',
  description: 'Descripción de la categoría',
  icon: 'IconName', // Nombre del icono de Lucide
  color: 'bg-color-500',
  count: 0
}
```

## Integración con Datos Externos

El sistema está preparado para integrarse con fuentes de datos externas:

### APIs Futuras
- Endpoint para obtener FAQs dinámicas
- Sistema de votación de utilidad
- Analytics de búsquedas más frecuentes
- Sugerencias automáticas basadas en trámites

### Base de Datos
```sql
-- Estructura sugerida para base de datos
CREATE TABLE faqs (
  id UUID PRIMARY KEY,
  question TEXT NOT NULL,
  answer TEXT NOT NULL,
  category_id UUID REFERENCES faq_categories(id),
  tags TEXT[],
  related_procedures TEXT[],
  popularity INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

## Monitoreo y Analytics

### Métricas Recomendadas
- Búsquedas más frecuentes
- FAQs más consultadas
- Tiempo de permanencia en sección
- Tasa de éxito de búsquedas
- Categorías más populares

### Implementación de Analytics
```typescript
// Ejemplo de tracking de eventos
const trackFAQSearch = (query: string, resultsCount: number) => {
  analytics.track('FAQ_Search', {
    query,
    resultsCount,
    timestamp: new Date()
  })
}
```

## Conclusión

El Sistema FAQ Optimizado proporciona una solución completa y escalable para gestionar preguntas frecuentes en el portal municipal. Su diseño modular, pruebas exhaustivas y enfoque en la experiencia del usuario lo convierten en una herramienta valiosa para mejorar la atención ciudadana digital.
