'use client'

/**
 * Accessibility utilities for WCAG 2.1 AA compliance
 * Includes keyboard navigation, screen reader support, and color contrast utilities
 */

import React, { useEffect, useRef, useState } from 'react'

// WCAG 2.1 AA color contrast ratios
const CONTRAST_RATIOS = {
  AA_NORMAL: 4.5,
  AA_LARGE: 3,
  AAA_NORMAL: 7,
  AAA_LARGE: 4.5
}

// Color contrast calculation
export function calculateContrastRatio(color1: string, color2: string): number {
  const getLuminance = (color: string): number => {
    // Convert hex to RGB
    const hex = color.replace('#', '')
    const r = parseInt(hex.substr(0, 2), 16) / 255
    const g = parseInt(hex.substr(2, 2), 16) / 255
    const b = parseInt(hex.substr(4, 2), 16) / 255

    // Calculate relative luminance
    const getRGB = (c: number) => {
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4)
    }

    return 0.2126 * getRGB(r) + 0.7152 * getRGB(g) + 0.0722 * getRGB(b)
  }

  const lum1 = getLuminance(color1)
  const lum2 = getLuminance(color2)
  const brightest = Math.max(lum1, lum2)
  const darkest = Math.min(lum1, lum2)

  return (brightest + 0.05) / (darkest + 0.05)
}

// Check if color combination meets WCAG standards
export function meetsContrastRequirement(
  foreground: string,
  background: string,
  level: 'AA' | 'AAA' = 'AA',
  isLargeText: boolean = false
): boolean {
  const ratio = calculateContrastRatio(foreground, background)
  
  if (level === 'AA') {
    return ratio >= (isLargeText ? CONTRAST_RATIOS.AA_LARGE : CONTRAST_RATIOS.AA_NORMAL)
  } else {
    return ratio >= (isLargeText ? CONTRAST_RATIOS.AAA_LARGE : CONTRAST_RATIOS.AAA_NORMAL)
  }
}

// Keyboard navigation hook
export function useKeyboardNavigation(
  containerRef: React.RefObject<HTMLElement>,
  options: {
    focusableSelector?: string
    loop?: boolean
    autoFocus?: boolean
  } = {}
) {
  const {
    focusableSelector = 'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])',
    loop = true,
    autoFocus = false
  } = options

  useEffect(() => {
    const container = containerRef.current
    if (!container) return

    const focusableElements = container.querySelectorAll(focusableSelector) as NodeListOf<HTMLElement>
    
    if (autoFocus && focusableElements.length > 0) {
      focusableElements[0].focus()
    }

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Tab') {
        const currentIndex = Array.from(focusableElements).indexOf(document.activeElement as HTMLElement)
        
        if (e.shiftKey) {
          // Shift + Tab (previous)
          if (currentIndex <= 0) {
            if (loop) {
              e.preventDefault()
              focusableElements[focusableElements.length - 1].focus()
            }
          }
        } else {
          // Tab (next)
          if (currentIndex >= focusableElements.length - 1) {
            if (loop) {
              e.preventDefault()
              focusableElements[0].focus()
            }
          }
        }
      }

      // Arrow key navigation for lists and grids
      if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(e.key)) {
        const currentIndex = Array.from(focusableElements).indexOf(document.activeElement as HTMLElement)
        let nextIndex = currentIndex

        switch (e.key) {
          case 'ArrowDown':
          case 'ArrowRight':
            nextIndex = currentIndex + 1
            break
          case 'ArrowUp':
          case 'ArrowLeft':
            nextIndex = currentIndex - 1
            break
        }

        if (nextIndex >= 0 && nextIndex < focusableElements.length) {
          e.preventDefault()
          focusableElements[nextIndex].focus()
        } else if (loop) {
          e.preventDefault()
          if (nextIndex < 0) {
            focusableElements[focusableElements.length - 1].focus()
          } else {
            focusableElements[0].focus()
          }
        }
      }

      // Home/End navigation
      if (e.key === 'Home') {
        e.preventDefault()
        focusableElements[0].focus()
      } else if (e.key === 'End') {
        e.preventDefault()
        focusableElements[focusableElements.length - 1].focus()
      }
    }

    container.addEventListener('keydown', handleKeyDown)
    return () => container.removeEventListener('keydown', handleKeyDown)
  }, [containerRef, focusableSelector, loop, autoFocus])
}

// Screen reader announcements
export function announceToScreenReader(message: string, priority: 'polite' | 'assertive' = 'polite') {
  const announcement = document.createElement('div')
  announcement.setAttribute('aria-live', priority)
  announcement.setAttribute('aria-atomic', 'true')
  announcement.className = 'sr-only'
  announcement.textContent = message

  document.body.appendChild(announcement)

  // Remove after announcement
  setTimeout(() => {
    document.body.removeChild(announcement)
  }, 1000)
}

// Focus management hook
export function useFocusManagement() {
  const previousFocusRef = useRef<HTMLElement | null>(null)

  const saveFocus = () => {
    previousFocusRef.current = document.activeElement as HTMLElement
  }

  const restoreFocus = () => {
    if (previousFocusRef.current) {
      previousFocusRef.current.focus()
    }
  }

  const trapFocus = (containerRef: React.RefObject<HTMLElement>) => {
    const container = containerRef.current
    if (!container) return

    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    ) as NodeListOf<HTMLElement>

    const firstElement = focusableElements[0]
    const lastElement = focusableElements[focusableElements.length - 1]

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Tab') {
        if (e.shiftKey) {
          if (document.activeElement === firstElement) {
            e.preventDefault()
            lastElement.focus()
          }
        } else {
          if (document.activeElement === lastElement) {
            e.preventDefault()
            firstElement.focus()
          }
        }
      }

      if (e.key === 'Escape') {
        restoreFocus()
      }
    }

    container.addEventListener('keydown', handleKeyDown)
    firstElement?.focus()

    return () => {
      container.removeEventListener('keydown', handleKeyDown)
    }
  }

  return { saveFocus, restoreFocus, trapFocus }
}

// Skip link component for keyboard navigation
export function SkipLink({ href, children }: { href: string; children: React.ReactNode }) {
  return (
    <a
      href={href}
      className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-blue-600 focus:text-white focus:rounded-md focus:shadow-lg"
    >
      {children}
    </a>
  )
}

// ARIA live region hook for dynamic content updates
export function useAriaLiveRegion() {
  const [liveRegion, setLiveRegion] = useState<HTMLElement | null>(null)

  useEffect(() => {
    const region = document.createElement('div')
    region.setAttribute('aria-live', 'polite')
    region.setAttribute('aria-atomic', 'true')
    region.className = 'sr-only'
    document.body.appendChild(region)
    setLiveRegion(region)

    return () => {
      if (document.body.contains(region)) {
        document.body.removeChild(region)
      }
    }
  }, [])

  const announce = (message: string) => {
    if (liveRegion) {
      liveRegion.textContent = message
    }
  }

  return announce
}

// Reduced motion detection
export function useReducedMotion() {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false)

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)')
    setPrefersReducedMotion(mediaQuery.matches)

    const handleChange = (e: MediaQueryListEvent) => {
      setPrefersReducedMotion(e.matches)
    }

    mediaQuery.addEventListener('change', handleChange)
    return () => mediaQuery.removeEventListener('change', handleChange)
  }, [])

  return prefersReducedMotion
}

// High contrast mode detection
export function useHighContrast() {
  const [prefersHighContrast, setPrefersHighContrast] = useState(false)

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-contrast: high)')
    setPrefersHighContrast(mediaQuery.matches)

    const handleChange = (e: MediaQueryListEvent) => {
      setPrefersHighContrast(e.matches)
    }

    mediaQuery.addEventListener('change', handleChange)
    return () => mediaQuery.removeEventListener('change', handleChange)
  }, [])

  return prefersHighContrast
}

// Form accessibility utilities
export function getFormFieldProps(
  id: string,
  label: string,
  error?: string,
  description?: string,
  required?: boolean
) {
  const describedBy = []
  if (description) describedBy.push(`${id}-description`)
  if (error) describedBy.push(`${id}-error`)

  return {
    field: {
      id,
      'aria-describedby': describedBy.length > 0 ? describedBy.join(' ') : undefined,
      'aria-invalid': error ? 'true' : undefined,
      'aria-required': required ? 'true' : undefined
    },
    label: {
      htmlFor: id,
      children: label + (required ? ' *' : '')
    },
    description: description ? {
      id: `${id}-description`,
      children: description
    } : null,
    error: error ? {
      id: `${id}-error`,
      role: 'alert',
      'aria-live': 'polite',
      children: error
    } : null
  }
}

// Table accessibility utilities
export function getTableProps(caption: string, summary?: string) {
  return {
    table: {
      role: 'table',
      'aria-label': caption
    },
    caption: {
      children: caption,
      className: 'sr-only'
    },
    summary: summary ? {
      children: summary,
      className: 'sr-only'
    } : null
  }
}

// Modal accessibility utilities
export function getModalProps(
  titleId: string,
  descriptionId?: string,
  onClose?: () => void
) {
  return {
    modal: {
      role: 'dialog',
      'aria-modal': 'true',
      'aria-labelledby': titleId,
      'aria-describedby': descriptionId,
      onKeyDown: (e: React.KeyboardEvent) => {
        if (e.key === 'Escape' && onClose) {
          onClose()
        }
      }
    },
    title: {
      id: titleId
    },
    description: descriptionId ? {
      id: descriptionId
    } : null,
    closeButton: {
      'aria-label': 'Cerrar modal',
      onClick: onClose
    }
  }
}

// Button accessibility utilities
export function getButtonProps(
  label: string,
  options: {
    expanded?: boolean
    controls?: string
    pressed?: boolean
    disabled?: boolean
  } = {}
) {
  const { expanded, controls, pressed, disabled } = options

  return {
    'aria-label': label,
    'aria-expanded': expanded !== undefined ? expanded : undefined,
    'aria-controls': controls,
    'aria-pressed': pressed !== undefined ? pressed : undefined,
    disabled,
    type: 'button' as const
  }
}

// Link accessibility utilities
export function getLinkProps(
  text: string,
  options: {
    external?: boolean
    download?: boolean
    current?: boolean
  } = {}
) {
  const { external, download, current } = options

  return {
    'aria-label': text + (external ? ' (abre en nueva ventana)' : '') + (download ? ' (descarga archivo)' : ''),
    'aria-current': current ? 'page' : undefined,
    target: external ? '_blank' : undefined,
    rel: external ? 'noopener noreferrer' : undefined,
    download: download ? true : undefined
  }
}
