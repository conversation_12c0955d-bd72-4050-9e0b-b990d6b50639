"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dependencias/page",{

/***/ "(app-pages-browser)/./components/dependencies/DependencyDetailModal.tsx":
/*!***********************************************************!*\
  !*** ./components/dependencies/DependencyDetailModal.tsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DependencyDetailModal: function() { return /* binding */ DependencyDetailModal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./components/ui/scroll-area.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,ChevronRight,Clock,DollarSign,ExternalLink,FileText,Mail,MapPin,Network,Phone,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,ChevronRight,Clock,DollarSign,ExternalLink,FileText,Mail,MapPin,Network,Phone,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,ChevronRight,Clock,DollarSign,ExternalLink,FileText,Mail,MapPin,Network,Phone,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,ChevronRight,Clock,DollarSign,ExternalLink,FileText,Mail,MapPin,Network,Phone,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,ChevronRight,Clock,DollarSign,ExternalLink,FileText,Mail,MapPin,Network,Phone,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/network.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,ChevronRight,Clock,DollarSign,ExternalLink,FileText,Mail,MapPin,Network,Phone,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,ChevronRight,Clock,DollarSign,ExternalLink,FileText,Mail,MapPin,Network,Phone,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,ChevronRight,Clock,DollarSign,ExternalLink,FileText,Mail,MapPin,Network,Phone,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,ChevronRight,Clock,DollarSign,ExternalLink,FileText,Mail,MapPin,Network,Phone,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,ChevronRight,Clock,DollarSign,ExternalLink,FileText,Mail,MapPin,Network,Phone,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,ChevronRight,Clock,DollarSign,ExternalLink,FileText,Mail,MapPin,Network,Phone,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,ChevronRight,Clock,DollarSign,ExternalLink,FileText,Mail,MapPin,Network,Phone,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* harmony import */ var _components_ui_loading_states__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/loading-states */ \"(app-pages-browser)/./components/ui/loading-states.tsx\");\n/* harmony import */ var _components_ui_error_states__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/error-states */ \"(app-pages-browser)/./components/ui/error-states.tsx\");\n/* harmony import */ var _components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/breadcrumb */ \"(app-pages-browser)/./components/ui/breadcrumb.tsx\");\n/* harmony import */ var _lib_services_dependencyService__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/services/dependencyService */ \"(app-pages-browser)/./lib/services/dependencyService.ts\");\n/* __next_internal_client_entry_do_not_use__ DependencyDetailModal auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction DependencyDetailModal(param) {\n    let { dependency, isOpen, onClose, onProcedureSelect, showBreadcrumbs = true, onNavigateBack } = param;\n    _s();\n    const [proceduresData, setProceduresData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"overview\");\n    // Cargar datos detallados cuando se abre el modal\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isOpen && dependency) {\n            loadProceduresData();\n        }\n    }, [\n        isOpen,\n        dependency\n    ]);\n    const loadProceduresData = async ()=>{\n        if (!dependency) return;\n        setIsLoading(true);\n        setError(null);\n        try {\n            await _lib_services_dependencyService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].initialize();\n            const data = await _lib_services_dependencyService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].getProceduresByDependency(dependency.id);\n            setProceduresData(data);\n        } catch (err) {\n            console.error(\"Error cargando datos de procedimientos:\", err);\n            setError(err instanceof Error ? err.message : \"Error al cargar los datos\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleRetry = ()=>{\n        loadProceduresData();\n    };\n    const handleProcedureClick = (procedure)=>{\n        if (onProcedureSelect) {\n            onProcedureSelect(procedure);\n        }\n    };\n    if (!dependency) return null;\n    // Obtener icono de la dependencia\n    const getIcon = ()=>{\n        const iconMap = {\n            crown: _barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            shield: _barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            banknote: _barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            map: _barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            users: _barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            \"graduation-cap\": _barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            \"heart-pulse\": _barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            car: _barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            building: _barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            folder: _barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n        };\n        const IconComponent = iconMap[dependency.icon || \"building\"] || _barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"];\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n            className: \"h-6 w-6\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n            lineNumber: 112,\n            columnNumber: 12\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.Dialog, {\n        open: isOpen,\n        onOpenChange: onClose,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogContent, {\n            className: \"max-w-5xl max-h-[90vh] overflow-hidden bg-white/95 backdrop-blur-md border-0 shadow-2xl rounded-3xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogHeader, {\n                    className: \"pb-6\",\n                    children: [\n                        showBreadcrumbs && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_11__.DependencyBreadcrumb, {\n                                dependency: {\n                                    name: dependency.name,\n                                    id: dependency.id\n                                },\n                                onNavigate: onNavigateBack\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.cn)(\"w-16 h-16 rounded-2xl flex items-center justify-center shadow-lg\", dependency.color === \"blue\" ? \"bg-gradient-to-br from-primary/10 to-primary/20 text-primary\" : dependency.color === \"green\" ? \"bg-gradient-to-br from-chia-green-100 to-chia-green-200 text-chia-green-600\" : dependency.color === \"purple\" ? \"bg-gradient-to-br from-purple-100 to-purple-200 text-purple-600\" : dependency.color === \"red\" ? \"bg-gradient-to-br from-red-100 to-red-200 text-red-600\" : dependency.color === \"yellow\" ? \"bg-gradient-to-br from-yellow-100 to-yellow-200 text-yellow-600\" : \"bg-gradient-to-br from-gray-100 to-gray-200 text-gray-600\"),\n                                    children: getIcon()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogTitle, {\n                                            className: \"text-3xl font-bold text-gradient-blue mb-2\",\n                                            children: dependency.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogDescription, {\n                                            className: \"text-lg\",\n                                            children: [\n                                                dependency.sigla && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                    variant: \"outline\",\n                                                    className: \"mr-3 text-sm font-semibold\",\n                                                    children: dependency.sigla\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                    lineNumber: 147,\n                                                    columnNumber: 19\n                                                }, this),\n                                                dependency.description\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                    value: activeTab,\n                    onValueChange: setActiveTab,\n                    className: \"w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                            className: \"grid w-full grid-cols-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"overview\",\n                                    children: \"Resumen\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"procedures\",\n                                    children: \"Procedimientos\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"contact\",\n                                    children: \"Contacto\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_7__.ScrollArea, {\n                            className: \"h-[60vh] mt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                    value: \"overview\",\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                        className: \"p-4 text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-8 w-8 text-chia-green-600 mx-auto mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                lineNumber: 170,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-chia-green-700\",\n                                                                children: dependency.tramitesCount\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                lineNumber: 171,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Tr\\xe1mites\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                lineNumber: 174,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                        lineNumber: 169,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                        className: \"p-4 text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"h-8 w-8 text-chia-blue-600 mx-auto mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                lineNumber: 180,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-chia-blue-700\",\n                                                                children: dependency.opasCount\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                lineNumber: 181,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"OPAs\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                lineNumber: 184,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                        lineNumber: 179,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                        className: \"p-4 text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"h-8 w-8 text-purple-600 mx-auto mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                lineNumber: 190,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-purple-700\",\n                                                                children: dependency.subdependenciasCount\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                lineNumber: 191,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Subdependencias\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                lineNumber: 194,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                        lineNumber: 189,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                        className: \"p-4 text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-8 w-8 text-primary mx-auto mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                lineNumber: 200,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-primary\",\n                                                                children: dependency.totalProcedures\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                lineNumber: 201,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Total\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                lineNumber: 204,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                lineNumber: 213,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Acerca de esta dependencia\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                lineNumber: 214,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-700 leading-relaxed\",\n                                                        children: dependency.description || \"La \".concat(dependency.name, \" es una dependencia municipal encargada de brindar servicios y tr\\xe1mites especializados a los ciudadanos de Ch\\xeda. Cuenta con \").concat(dependency.totalProcedures, \" procedimientos disponibles distribuidos en \").concat(dependency.tramitesCount, \" tr\\xe1mites y \").concat(dependency.opasCount, \" otras prestaciones administrativas.\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                    value: \"procedures\",\n                                    className: \"space-y-4\",\n                                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_loading_states__WEBPACK_IMPORTED_MODULE_9__.TabContentLoading, {\n                                        message: \"Cargando procedimientos...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 17\n                                    }, this) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_error_states__WEBPACK_IMPORTED_MODULE_10__.ErrorState, {\n                                        type: \"server\",\n                                        title: \"Error al cargar procedimientos\",\n                                        description: error,\n                                        onRetry: handleRetry,\n                                        size: \"md\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 17\n                                    }, this) : proceduresData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            proceduresData.tramites.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-900 mb-3 flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-5 w-5 text-chia-green-600 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                lineNumber: 243,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Tr\\xe1mites (\",\n                                                            proceduresData.tramites.length,\n                                                            \")\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: proceduresData.tramites.map((tramite, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                                                className: \"cursor-pointer hover:shadow-md transition-shadow\",\n                                                                onClick: ()=>handleProcedureClick(tramite),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                                    className: \"p-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                        className: \"font-medium text-gray-900 mb-1\",\n                                                                                        children: tramite.Nombre\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                                        lineNumber: 256,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center space-x-4 text-sm text-gray-600\",\n                                                                                        children: [\n                                                                                            tramite[\"\\xbfTiene pago?\"] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"flex items-center space-x-1\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                                        className: \"h-4 w-4\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                                                        lineNumber: 262,\n                                                                                                        columnNumber: 41\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        children: tramite[\"\\xbfTiene pago?\"] === \"No\" ? \"Gratuito\" : tramite[\"\\xbfTiene pago?\"]\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                                                        lineNumber: 263,\n                                                                                                        columnNumber: 41\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                                                lineNumber: 261,\n                                                                                                columnNumber: 39\n                                                                                            }, this),\n                                                                                            tramite[\"Tiempo de respuesta\"] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"flex items-center space-x-1\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                                        className: \"h-4 w-4\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                                                        lineNumber: 268,\n                                                                                                        columnNumber: 41\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        children: tramite[\"Tiempo de respuesta\"]\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                                                        lineNumber: 269,\n                                                                                                        columnNumber: 41\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                                                lineNumber: 267,\n                                                                                                columnNumber: 39\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                                        lineNumber: 259,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                                lineNumber: 255,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                className: \"h-5 w-5 text-gray-400\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                                lineNumber: 274,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                        lineNumber: 254,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                    lineNumber: 253,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, index, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                lineNumber: 248,\n                                                                columnNumber: 27\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 21\n                                            }, this),\n                                            proceduresData.opas.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-900 mb-3 flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"h-5 w-5 text-chia-blue-600 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                lineNumber: 287,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Otras Prestaciones Administrativas (\",\n                                                            proceduresData.opas.length,\n                                                            \")\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                        lineNumber: 286,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: proceduresData.opas.map((opa, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                                                className: \"cursor-pointer hover:shadow-md transition-shadow\",\n                                                                onClick: ()=>handleProcedureClick(opa),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                                    className: \"p-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                        className: \"font-medium text-gray-900 mb-1\",\n                                                                                        children: opa.OPA\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                                        lineNumber: 300,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center space-x-4 text-sm text-gray-600\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                children: opa.subdependencia\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                                                lineNumber: 304,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"flex items-center space-x-1\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                                        className: \"h-4 w-4\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                                                        lineNumber: 306,\n                                                                                                        columnNumber: 39\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        children: \"Gratuito\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                                                        lineNumber: 307,\n                                                                                                        columnNumber: 39\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                                                lineNumber: 305,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                                        lineNumber: 303,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                                lineNumber: 299,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                className: \"h-5 w-5 text-gray-400\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                                lineNumber: 311,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                        lineNumber: 298,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                    lineNumber: 297,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, index, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                lineNumber: 292,\n                                                                columnNumber: 27\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-12 w-12 text-gray-300 mx-auto mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                children: \"No hay procedimientos disponibles\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Esta dependencia no tiene procedimientos registrados actualmente.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                    value: \"contact\",\n                                    className: \"space-y-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                            lineNumber: 337,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Informaci\\xf3n de Contacto\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                            lineNumber: 338,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-primary\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                        lineNumber: 344,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-medium\",\n                                                                                children: \"Tel\\xe9fono\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                                lineNumber: 346,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-gray-600\",\n                                                                                children: \"(601) 884-5500\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                                lineNumber: 347,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                        lineNumber: 345,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                lineNumber: 343,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-primary\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                        lineNumber: 352,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-medium\",\n                                                                                children: \"Email\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                                lineNumber: 354,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-gray-600\",\n                                                                                children: \"<EMAIL>\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                                lineNumber: 355,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                        lineNumber: 353,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                lineNumber: 351,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-primary\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                        lineNumber: 360,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-medium\",\n                                                                                children: \"Direcci\\xf3n\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                                lineNumber: 362,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-gray-600\",\n                                                                                children: \"Carrera 11 No. 17-25, Ch\\xeda, Cundinamarca\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                                lineNumber: 363,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                        lineNumber: 361,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                lineNumber: 359,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-primary\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                        lineNumber: 368,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-medium\",\n                                                                                children: \"Horario de Atenci\\xf3n\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                                lineNumber: 370,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-gray-600\",\n                                                                                children: \"Lunes a Viernes: 8:00 AM - 5:00 PM\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                                lineNumber: 371,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                        lineNumber: 369,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                lineNumber: 367,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"pt-4 border-t\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            className: \"w-full bg-primary hover:bg-primary/90\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                    lineNumber: 378,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Visitar P\\xe1gina Web Oficial\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                            lineNumber: 377,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n            lineNumber: 117,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n        lineNumber: 116,\n        columnNumber: 5\n    }, this);\n}\n_s(DependencyDetailModal, \"YbWk/uVEdXHGT1WYN1aPTnYG1ks=\");\n_c = DependencyDetailModal;\nvar _c;\n$RefreshReg$(_c, \"DependencyDetailModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/dependencies/DependencyDetailModal.tsx\n"));

/***/ })

});