/**
 * Script de verificación para la extracción automática de información de costos
 * Verifica que la función extract_cost_info_from_description funcione correctamente
 */

import { createClient } from '@supabase/supabase-js'
import * as dotenv from 'dotenv'

// Cargar variables de entorno
dotenv.config()

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

const supabase = createClient(supabaseUrl, supabaseKey)

interface CostExtractionResult {
  has_cost: boolean
  cost_description: string
}

interface ProcedureWithCost {
  id: string
  name: string
  description: string
  has_cost: boolean
  cost_description: string
  tipo: 'TRAMITE' | 'OPA'
}

async function testCostExtraction() {
  console.log('🔍 Iniciando verificación de extracción de costos...\n')

  try {
    // 1. Probar la función de extracción con casos específicos
    console.log('📋 Probando función de extracción con casos de prueba...')
    
    const testCases = [
      'Formulario: No. Tiempo de respuesta: 15 días hábiles. ¿Tiene pago?: No',
      'Formulario: No. Tiempo de respuesta: 15 días hábiles. ¿Tiene pago?: 1.5 SMLDV',
      'Tiempo de respuesta: 15 días hábiles. ¿Tiene pago?: 0,2 UVT',
      'Tarifa por hora productor: 0,74 UVT. Tarifa mayor: 1 UVT',
      'Formulario: Solicitud. Tiempo: 45 días. ¿Tiene pago?: Estratos 1 y 2: 4 SMLDV',
      'Derechos de tránsito: 1,261 UVT RUNT: $3900',
      'Servicio gratuito para todos los ciudadanos',
      'Sin costo adicional'
    ]

    for (const testCase of testCases) {
      const { data, error } = await supabase
        .rpc('extract_cost_info_from_description', { description_text: testCase })

      if (error) {
        console.error(`❌ Error en caso de prueba: ${testCase.substring(0, 50)}...`)
        console.error(error)
        continue
      }

      const result = data[0] as CostExtractionResult
      console.log(`✅ "${testCase.substring(0, 50)}..." → Has cost: ${result.has_cost}, Description: "${result.cost_description}"`)
    }

    console.log('\n📊 Verificando estadísticas de extracción en la base de datos...')

    // 2. Obtener estadísticas de procedures
    const { data: proceduresStats, error: proceduresError } = await supabase
      .from('procedures')
      .select('has_cost, cost_description')
      .not('description', 'is', null)

    if (proceduresError) {
      console.error('❌ Error obteniendo estadísticas de procedures:', proceduresError)
      return
    }

    const proceduresWithCost = proceduresStats.filter(p => p.has_cost).length
    const proceduresFree = proceduresStats.filter(p => !p.has_cost).length
    const proceduresTotal = proceduresStats.length

    console.log(`\n📋 PROCEDURES:`)
    console.log(`   Total: ${proceduresTotal}`)
    console.log(`   Con costo: ${proceduresWithCost} (${((proceduresWithCost / proceduresTotal) * 100).toFixed(1)}%)`)
    console.log(`   Gratuitos: ${proceduresFree} (${((proceduresFree / proceduresTotal) * 100).toFixed(1)}%)`)

    // 3. Obtener estadísticas de OPAs
    const { data: opasStats, error: opasError } = await supabase
      .from('opas')
      .select('has_cost, cost_description')
      .not('description', 'is', null)

    if (opasError) {
      console.error('❌ Error obteniendo estadísticas de OPAs:', opasError)
      return
    }

    const opasWithCost = opasStats.filter(o => o.has_cost).length
    const opasFree = opasStats.filter(o => !o.has_cost).length
    const opasTotal = opasStats.length

    console.log(`\n📋 OPAS:`)
    console.log(`   Total: ${opasTotal}`)
    console.log(`   Con costo: ${opasWithCost} (${((opasWithCost / opasTotal) * 100).toFixed(1)}%)`)
    console.log(`   Gratuitos: ${opasFree} (${((opasFree / opasTotal) * 100).toFixed(1)}%)`)

    // 4. Mostrar ejemplos de procedimientos con costo
    console.log('\n💰 Ejemplos de procedimientos con costo:')
    
    const { data: costExamples, error: costError } = await supabase
      .from('procedures')
      .select('name, has_cost, cost_description, description')
      .eq('has_cost', true)
      .not('description', 'is', null)
      .limit(5)

    if (costError) {
      console.error('❌ Error obteniendo ejemplos con costo:', costError)
    } else {
      costExamples.forEach((example, index) => {
        console.log(`   ${index + 1}. ${example.name}`)
        console.log(`      Costo: ${example.cost_description}`)
        console.log(`      Descripción: ${example.description.substring(0, 100)}...`)
        console.log('')
      })
    }

    // 5. Mostrar ejemplos de procedimientos gratuitos
    console.log('\n🆓 Ejemplos de procedimientos gratuitos:')
    
    const { data: freeExamples, error: freeError } = await supabase
      .from('procedures')
      .select('name, has_cost, cost_description, description')
      .eq('has_cost', false)
      .not('description', 'is', null)
      .limit(3)

    if (freeError) {
      console.error('❌ Error obteniendo ejemplos gratuitos:', freeError)
    } else {
      freeExamples.forEach((example, index) => {
        console.log(`   ${index + 1}. ${example.name}`)
        console.log(`      Costo: ${example.cost_description}`)
        console.log(`      Descripción: ${example.description.substring(0, 100)}...`)
        console.log('')
      })
    }

    // 6. Verificar vista unificada
    console.log('\n🔗 Verificando vista unificada...')
    
    const { data: unifiedView, error: viewError } = await supabase
      .from('vista_codigos_procedimientos')
      .select('codigo, name, tipo, has_cost, cost_description')
      .limit(5)

    if (viewError) {
      console.error('❌ Error verificando vista unificada:', viewError)
    } else {
      console.log('✅ Vista unificada funcionando correctamente:')
      unifiedView.forEach(item => {
        console.log(`   ${item.codigo} - ${item.name} (${item.tipo}) - ${item.cost_description}`)
      })
    }

    // 7. Resumen final
    const totalProcedures = proceduresTotal + opasTotal
    const totalWithCost = proceduresWithCost + opasWithCost
    const totalFree = proceduresFree + opasFree

    console.log('\n📈 RESUMEN FINAL:')
    console.log(`   Total de procedimientos analizados: ${totalProcedures}`)
    console.log(`   Con información de costo: ${totalWithCost} (${((totalWithCost / totalProcedures) * 100).toFixed(1)}%)`)
    console.log(`   Gratuitos: ${totalFree} (${((totalFree / totalProcedures) * 100).toFixed(1)}%)`)
    console.log('\n✅ Extracción de información de costos completada exitosamente!')

  } catch (error) {
    console.error('❌ Error durante la verificación:', error)
  }
}

// Ejecutar verificación
if (require.main === module) {
  testCostExtraction()
    .then(() => {
      console.log('\n🎉 Verificación completada')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Error fatal:', error)
      process.exit(1)
    })
}

export { testCostExtraction }
