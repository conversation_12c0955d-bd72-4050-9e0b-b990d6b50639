# Plan de Consolidación de Tablas Obsoletas FAQ
## Sistema Municipal de Chía

**Fecha**: 2025-07-02  
**Estado**: ANÁLISIS COMPLETO - LISTO PARA EJECUCIÓN  
**Criticidad**: ALTA - Dependencias activas encontradas

---

## 🔍 RESUMEN DEL ANÁLISIS

### Tablas Obsoletas Identificadas
| Tabla | Registros | Estado | Dependencias |
|-------|-----------|--------|--------------|
| `faqs` | 10 | ❌ Obsoleta | ⚠️ FK desde `faq_analytics` |
| `faq_categories` | 6 | ❌ Obsoleta | ⚠️ FK desde `faq_analytics` |

### Tablas Actuales (EN USO)
| Tabla | Registros | Estado | Uso |
|-------|-----------|--------|-----|
| `municipal_faqs` | 383 | ✅ ACTIVA | Sistema principal |
| `faq_themes` | 37 | ✅ ACTIVA | Categorización |

### ⚠️ DEPENDENCIAS CRÍTICAS ENCONTRADAS

**Tabla `faq_analytics` tiene foreign keys hacia tablas obsoletas:**
- `faq_analytics.faq_id` → `faqs.id`
- `faq_analytics.category_id` → `faq_categories.id`
- **Estado**: Tabla vacía (0 registros) pero con constraints activos

---

## 📊 COMPARACIÓN DE DATOS

### Diferencias Estructurales Críticas

#### `faqs` vs `municipal_faqs`
```sql
-- OBSOLETA: faqs
- category_id (UUID) → FK a faq_categories
- popularity (INTEGER)
- tags (TEXT[])

-- ACTUAL: municipal_faqs  
- theme_id (UUID) → FK a faq_themes
- popularity_score (INTEGER)
- keywords (TEXT[])
- search_vector (TSVECTOR) -- Para búsqueda full-text
```

#### `faq_categories` vs `faq_themes`
```sql
-- OBSOLETA: faq_categories (6 registros genéricos)
- Categorías básicas: "Impuestos", "Licencias", etc.

-- ACTUAL: faq_themes (37 registros específicos)
- Temas específicos por trámite: "COMPARENDOS", "LICENCIA DE FUNCIONAMIENTO", etc.
- Vinculados a dependencias/subdependencias municipales
```

### Contenido de Datos

#### Datos en `faqs` (10 registros)
- ✅ **Contenido único**: Preguntas específicas no migradas
- ✅ **Calidad alta**: Respuestas detalladas con información oficial
- ⚠️ **Mapeo requerido**: `category_id` → `theme_id`

#### Datos en `faq_categories` (6 registros)
- ❌ **Contenido genérico**: Categorías básicas ya superadas
- ❌ **Obsoleto**: Reemplazado por sistema de temas específicos
- ❌ **No migrar**: Datos no compatibles con nueva estructura

---

## 🎯 ESTRATEGIA DE CONSOLIDACIÓN

### FASE 1: Migración de Datos Únicos ✅ REQUERIDA

#### 1.1 Migrar FAQs de `faqs` → `municipal_faqs`
```sql
-- Mapear categorías obsoletas a temas actuales
INSERT INTO municipal_faqs (
  question, answer, theme_id, keywords, related_procedures,
  popularity_score, view_count, helpful_votes, unhelpful_votes,
  is_active, created_at, updated_at
)
SELECT 
  f.question,
  f.answer,
  -- MAPEO MANUAL REQUERIDO: category_id → theme_id apropiado
  CASE f.category_id
    WHEN '31cbe208-e53f-4cba-8eba-f8bd7ab24a77' THEN (SELECT id FROM faq_themes WHERE name ILIKE '%impuesto%' LIMIT 1)
    -- Más mapeos según análisis de contenido
  END as theme_id,
  f.tags as keywords,
  f.related_procedures,
  f.popularity as popularity_score,
  f.view_count,
  f.helpful_votes,
  f.unhelpful_votes,
  f.is_active,
  f.created_at,
  f.updated_at
FROM faqs f
WHERE f.is_active = true;
```

### FASE 2: Actualización de Dependencias ✅ CRÍTICA

#### 2.1 Actualizar `faq_analytics` (TABLA VACÍA)
```sql
-- Opción A: Actualizar estructura para usar tablas actuales
ALTER TABLE faq_analytics 
  DROP CONSTRAINT faq_analytics_faq_id_fkey,
  DROP CONSTRAINT faq_analytics_category_id_fkey;

ALTER TABLE faq_analytics 
  ADD CONSTRAINT faq_analytics_faq_id_fkey 
    FOREIGN KEY (faq_id) REFERENCES municipal_faqs(id),
  ADD CONSTRAINT faq_analytics_theme_id_fkey 
    FOREIGN KEY (category_id) REFERENCES faq_themes(id);

-- Renombrar columna para claridad
ALTER TABLE faq_analytics 
  RENAME COLUMN category_id TO theme_id;
```

#### 2.2 Actualizar Triggers
```sql
-- Eliminar triggers de tablas obsoletas
DROP TRIGGER IF EXISTS update_faqs_updated_at ON faqs;
DROP TRIGGER IF EXISTS update_faq_categories_updated_at ON faq_categories;
```

### FASE 3: Eliminación Segura ✅ FINAL

#### 3.1 Eliminar Foreign Keys
```sql
-- Ya eliminados en Fase 2
```

#### 3.2 Eliminar Tablas Obsoletas
```sql
DROP TABLE IF EXISTS faqs CASCADE;
DROP TABLE IF EXISTS faq_categories CASCADE;
```

---

## 🛡️ PLAN DE ROLLBACK

### Backup Completo Antes de Ejecución
```sql
-- Crear tablas de backup
CREATE TABLE faqs_backup AS SELECT * FROM faqs;
CREATE TABLE faq_categories_backup AS SELECT * FROM faq_categories;
CREATE TABLE faq_analytics_backup AS SELECT * FROM faq_analytics;
```

### Script de Restauración
```sql
-- En caso de problemas, restaurar desde backup
CREATE TABLE faqs AS SELECT * FROM faqs_backup;
CREATE TABLE faq_categories AS SELECT * FROM faq_categories_backup;
-- Restaurar constraints originales
```

---

## ⚡ SCRIPTS DE EJECUCIÓN

### Script 1: Análisis Pre-Migración
- ✅ Verificar integridad de datos actuales
- ✅ Confirmar mapeo de categorías → temas
- ✅ Validar que `faq_analytics` está vacía

### Script 2: Migración de Datos
- ✅ Migrar 10 FAQs únicas de `faqs` → `municipal_faqs`
- ✅ Aplicar mapeo de `category_id` → `theme_id`
- ✅ Preservar metadatos (popularidad, votos, fechas)

### Script 3: Actualización de Dependencias
- ✅ Modificar constraints de `faq_analytics`
- ✅ Eliminar triggers obsoletos
- ✅ Actualizar referencias en código (si las hay)

### Script 4: Limpieza Final
- ✅ Eliminar tablas obsoletas
- ✅ Verificar integridad del sistema
- ✅ Ejecutar tests de validación

---

## 🧪 VALIDACIÓN POST-CONSOLIDACIÓN

### Tests Requeridos
1. ✅ **Funcionalidad FAQ**: Verificar que el filtrado funciona
2. ✅ **Integridad de datos**: Confirmar que no se perdieron FAQs
3. ✅ **Performance**: Verificar que las consultas funcionan correctamente
4. ✅ **Analytics**: Confirmar que `faq_analytics` puede registrar eventos

### Métricas de Éxito
- ✅ **0 referencias** a tablas obsoletas en código
- ✅ **393 FAQs totales** (383 existentes + 10 migradas)
- ✅ **37 temas activos** funcionando correctamente
- ✅ **Sistema FAQ** completamente funcional

---

## 🚨 RIESGOS Y MITIGACIONES

### Riesgo Alto: Pérdida de Datos
- **Mitigación**: Backup completo antes de ejecución
- **Validación**: Conteo de registros antes/después

### Riesgo Medio: Ruptura de Analytics
- **Mitigación**: Actualizar constraints gradualmente
- **Validación**: Test de inserción en `faq_analytics`

### Riesgo Bajo: Referencias en Código
- **Mitigación**: Análisis completo ya realizado
- **Validación**: Scripts de validación automatizados

---

## ✅ CRITERIOS DE APROBACIÓN

### Pre-requisitos
- [x] Análisis completo de dependencias
- [x] Identificación de datos únicos
- [x] Plan de migración detallado
- [x] Scripts de rollback preparados

### Criterios de Éxito
- [ ] Migración exitosa de 10 FAQs únicas
- [ ] Eliminación segura de tablas obsoletas
- [ ] Sistema FAQ funcionando al 100%
- [ ] 0 referencias obsoletas en código
- [ ] Tests de validación pasando

**ESTADO**: ✅ **LISTO PARA EJECUCIÓN**
