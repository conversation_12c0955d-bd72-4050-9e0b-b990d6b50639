import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { BrowserRouter } from 'react-router-dom'
import { mockUsers, setupMockAuthSuccess, cleanupMocks } from '@/tests/utils/test-helpers'

// Mock Next.js router
const mockPush = jest.fn()
const mockReplace = jest.fn()

jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
    replace: mockReplace,
    refresh: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
  }),
  usePathname: () => '/dashboard',
  useSearchParams: () => new URLSearchParams(),
}))

// Mock Supabase client with comprehensive data
const mockSupabaseData = {
  procedures: [
    {
      id: 1,
      name: 'Certificado de Residencia',
      description: 'Certificado que acredita la residencia en el municipio',
      category: 'Certificados',
      dependency: 'Secretaría General',
      cost: 15000,
      duration_days: 5,
      requirements: ['Cédula de ciudadanía', 'Recibo de servicios públicos'],
      status: 'active',
    },
    {
      id: 2,
      name: 'Licencia de Construcción',
      description: 'Permiso para construcción de vivienda',
      category: 'Licencias',
      dependency: 'Planeación',
      cost: 500000,
      duration_days: 30,
      requirements: ['Planos arquitectónicos', 'Estudio de suelos'],
      status: 'active',
    },
  ],
  citizenProcedures: [
    {
      id: 'cp-1',
      procedure_id: 1,
      procedure_name: 'Certificado de Residencia',
      status: 'en_proceso',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
      documents_required: 2,
      documents_uploaded: 1,
    },
    {
      id: 'cp-2',
      procedure_id: 2,
      procedure_name: 'Licencia de Construcción',
      status: 'documentos_pendientes',
      created_at: '2024-01-02T00:00:00Z',
      updated_at: '2024-01-02T00:00:00Z',
      documents_required: 3,
      documents_uploaded: 0,
    },
  ],
  documents: [
    {
      id: 'doc-1',
      name: 'cedula.pdf',
      type: 'application/pdf',
      size: 1024000,
      procedure_id: 'cp-1',
      uploaded_at: '2024-01-01T00:00:00Z',
    },
  ],
  notifications: [
    {
      id: 'notif-1',
      title: 'Documento aprobado',
      message: 'Tu cédula ha sido aprobada para el Certificado de Residencia',
      type: 'success',
      read: false,
      created_at: '2024-01-01T00:00:00Z',
    },
    {
      id: 'notif-2',
      title: 'Documentos pendientes',
      message: 'Faltan documentos para tu Licencia de Construcción',
      type: 'warning',
      read: false,
      created_at: '2024-01-02T00:00:00Z',
    },
  ],
}

const mockSupabase = {
  from: jest.fn((table: string) => {
    const mockData = {
      procedures: mockSupabaseData.procedures,
      citizen_procedures: mockSupabaseData.citizenProcedures,
      document_attachments: mockSupabaseData.documents,
      notifications: mockSupabaseData.notifications,
    }

    return {
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          order: jest.fn(() => ({
            limit: jest.fn(() => Promise.resolve({
              data: mockData[table as keyof typeof mockData] || [],
              error: null,
            })),
          })),
          limit: jest.fn(() => Promise.resolve({
            data: mockData[table as keyof typeof mockData] || [],
            error: null,
          })),
        })),
        order: jest.fn(() => ({
          limit: jest.fn(() => Promise.resolve({
            data: mockData[table as keyof typeof mockData] || [],
            error: null,
          })),
        })),
        limit: jest.fn(() => Promise.resolve({
          data: mockData[table as keyof typeof mockData] || [],
          error: null,
        })),
      })),
      insert: jest.fn(() => ({
        select: jest.fn(() => Promise.resolve({
          data: [{ id: 'new-id', ...mockData[table as keyof typeof mockData]?.[0] }],
          error: null,
        })),
      })),
      update: jest.fn(() => ({
        eq: jest.fn(() => ({
          select: jest.fn(() => Promise.resolve({
            data: [mockData[table as keyof typeof mockData]?.[0]],
            error: null,
          })),
        })),
      })),
    }
  }),
  storage: {
    from: jest.fn(() => ({
      upload: jest.fn(() => Promise.resolve({
        data: { path: 'test-path' },
        error: null,
      })),
      list: jest.fn(() => Promise.resolve({
        data: mockSupabaseData.documents,
        error: null,
      })),
      download: jest.fn(() => Promise.resolve({
        data: new Blob(['test content']),
        error: null,
      })),
    })),
  },
}

jest.mock('@/lib/supabase/client', () => ({
  supabase: mockSupabase,
}))

// Mock auth hook
jest.mock('@/hooks/useAuth', () => ({
  useAuth: () => ({
    user: mockUsers.ciudadano,
    profile: mockUsers.ciudadano.profile,
    loading: false,
    signOut: jest.fn(),
  }),
}))

// Mock role hook
jest.mock('@/hooks/useRole', () => ({
  useRole: () => ({
    role: 'ciudadano',
    isLoading: false,
    hasRole: jest.fn((role) => role === 'ciudadano'),
  }),
}))

// Test wrapper component
const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  return <BrowserRouter>{children}</BrowserRouter>
}

describe('Citizen Portal Integration Flow', () => {
  const user = userEvent.setup()

  beforeEach(() => {
    setupMockAuthSuccess('ciudadano')
    jest.clearAllMocks()
  })

  afterEach(() => {
    cleanupMocks()
  })

  describe('Complete Dashboard to Procedure Management Flow', () => {
    it('navigates from dashboard to procedure search and starts new procedure', async () => {
      // Import components dynamically to avoid module loading issues
      const { CitizenDashboard } = await import('@/components/dashboard/CitizenDashboard')
      const { ProcedureSearch } = await import('@/components/procedures/ProcedureSearch')
      const { ProcedureManagement } = await import('@/components/procedures/ProcedureManagement')

      // Start at dashboard
      const { rerender } = render(
        <TestWrapper>
          <CitizenDashboard />
        </TestWrapper>
      )

      // Verify dashboard loads with user data
      await waitFor(() => {
        expect(screen.getByText('Ciudadano Test')).toBeInTheDocument()
        expect(screen.getByText('Trámites Activos')).toBeInTheDocument()
      })

      // Click on "Nuevo Trámite" quick action
      const newProcedureButton = screen.getByText('Nuevo Trámite')
      await user.click(newProcedureButton)

      // Verify navigation to procedure search
      expect(mockPush).toHaveBeenCalledWith('/gestion-tramites')

      // Simulate navigation to procedure search
      rerender(
        <TestWrapper>
          <ProcedureSearch />
        </TestWrapper>
      )

      // Verify procedure search loads
      await waitFor(() => {
        expect(screen.getByText('Certificado de Residencia')).toBeInTheDocument()
        expect(screen.getByText('Licencia de Construcción')).toBeInTheDocument()
      })

      // Search for specific procedure
      const searchInput = screen.getByPlaceholderText(/Buscar trámites/i)
      await user.type(searchInput, 'certificado')

      await waitFor(() => {
        expect(screen.getByText('Certificado de Residencia')).toBeInTheDocument()
      })

      // Select procedure
      const procedureCard = screen.getByText('Certificado de Residencia')
      await user.click(procedureCard)

      // Verify procedure details are shown
      await waitFor(() => {
        expect(screen.getByText('$15.000')).toBeInTheDocument()
        expect(screen.getByText('5 días')).toBeInTheDocument()
        expect(screen.getByText('Secretaría General')).toBeInTheDocument()
      })

      // Start procedure
      const startButton = screen.getByText('Iniciar Trámite')
      await user.click(startButton)

      // Verify procedure creation
      expect(mockSupabase.from).toHaveBeenCalledWith('citizen_procedures')
    })

    it('completes document upload flow from procedure management', async () => {
      const { ProcedureManagement } = await import('@/components/procedures/ProcedureManagement')
      const { DocumentPortal } = await import('@/components/documents/DocumentPortal')

      // Start at procedure management
      const { rerender } = render(
        <TestWrapper>
          <ProcedureManagement />
        </TestWrapper>
      )

      // Verify active procedures are shown
      await waitFor(() => {
        expect(screen.getByText('Certificado de Residencia')).toBeInTheDocument()
        expect(screen.getByText('en_proceso')).toBeInTheDocument()
      })

      // Click on procedure to see details
      const procedureCard = screen.getByText('Certificado de Residencia')
      await user.click(procedureCard)

      // Verify procedure details and document requirements
      await waitFor(() => {
        expect(screen.getByText('Documentos requeridos')).toBeInTheDocument()
        expect(screen.getByText('1 de 2 documentos subidos')).toBeInTheDocument()
      })

      // Click upload documents button
      const uploadButton = screen.getByText('Subir Documentos')
      await user.click(uploadButton)

      // Navigate to document portal
      expect(mockPush).toHaveBeenCalledWith('/documentos')

      // Simulate navigation to document portal
      rerender(
        <TestWrapper>
          <DocumentPortal />
        </TestWrapper>
      )

      // Verify document portal loads
      await waitFor(() => {
        expect(screen.getByText('Portal de Documentos')).toBeInTheDocument()
        expect(screen.getByText('cedula.pdf')).toBeInTheDocument()
      })

      // Upload new document
      const uploadDocButton = screen.getByText('Subir Documento')
      await user.click(uploadDocButton)

      await waitFor(() => {
        expect(screen.getByText('Subir Nuevo Documento')).toBeInTheDocument()
      })

      // Select file
      const fileInput = screen.getByLabelText('Seleccionar archivos')
      const file = new File(['test content'], 'recibo_servicios.pdf', { 
        type: 'application/pdf' 
      })

      await user.upload(fileInput, file)

      await waitFor(() => {
        expect(screen.getByText('recibo_servicios.pdf')).toBeInTheDocument()
      })

      // Submit upload
      const submitButton = screen.getByText('Subir Archivo')
      await user.click(submitButton)

      // Verify upload was called
      await waitFor(() => {
        expect(mockSupabase.storage.from().upload).toHaveBeenCalled()
      })
    })

    it('handles notification flow and status updates', async () => {
      const { CitizenDashboard } = await import('@/components/dashboard/CitizenDashboard')

      render(
        <TestWrapper>
          <CitizenDashboard />
        </TestWrapper>
      )

      // Verify notifications are displayed
      await waitFor(() => {
        expect(screen.getByText('Notificaciones Recientes')).toBeInTheDocument()
        expect(screen.getByText('Documento aprobado')).toBeInTheDocument()
        expect(screen.getByText('Documentos pendientes')).toBeInTheDocument()
      })

      // Click on notification
      const notification = screen.getByText('Documento aprobado')
      await user.click(notification)

      // Verify notification details are shown
      await waitFor(() => {
        expect(screen.getByText('Tu cédula ha sido aprobada')).toBeInTheDocument()
      })

      // Mark notification as read
      const markReadButton = screen.getByText('Marcar como leída')
      await user.click(markReadButton)

      // Verify update was called
      expect(mockSupabase.from).toHaveBeenCalledWith('notifications')
    })

    it('handles error states gracefully throughout the flow', async () => {
      // Mock error response
      mockSupabase.from.mockImplementation(() => ({
        select: jest.fn(() => ({
          eq: jest.fn(() => ({
            order: jest.fn(() => ({
              limit: jest.fn(() => Promise.resolve({
                data: null,
                error: { message: 'Database connection error' },
              })),
            })),
          })),
        })),
      }))

      const { CitizenDashboard } = await import('@/components/dashboard/CitizenDashboard')

      render(
        <TestWrapper>
          <CitizenDashboard />
        </TestWrapper>
      )

      // Verify error handling
      await waitFor(() => {
        // Dashboard should still render basic structure
        expect(screen.getByText('Ciudadano Test')).toBeInTheDocument()
        
        // Error message should be shown for failed data
        expect(screen.getByText('Error al cargar datos')).toBeInTheDocument()
      })

      // Verify retry functionality
      const retryButton = screen.getByText('Reintentar')
      await user.click(retryButton)

      // Verify retry was attempted
      expect(mockSupabase.from).toHaveBeenCalledTimes(2)
    })

    it('maintains state consistency across navigation', async () => {
      const { CitizenDashboard } = await import('@/components/dashboard/CitizenDashboard')
      const { ProcedureManagement } = await import('@/components/procedures/ProcedureManagement')

      // Start at dashboard
      const { rerender } = render(
        <TestWrapper>
          <CitizenDashboard />
        </TestWrapper>
      )

      // Verify initial state
      await waitFor(() => {
        expect(screen.getByText('Trámites Activos')).toBeInTheDocument()
        expect(screen.getByText('1')).toBeInTheDocument() // Active procedures count
      })

      // Navigate to procedure management
      const manageButton = screen.getByText('Gestionar Trámites')
      await user.click(manageButton)

      // Simulate navigation
      rerender(
        <TestWrapper>
          <ProcedureManagement />
        </TestWrapper>
      )

      // Verify consistent data
      await waitFor(() => {
        expect(screen.getByText('Certificado de Residencia')).toBeInTheDocument()
        expect(screen.getByText('en_proceso')).toBeInTheDocument()
      })

      // Update procedure status
      const updateButton = screen.getByText('Actualizar Estado')
      await user.click(updateButton)

      // Navigate back to dashboard
      rerender(
        <TestWrapper>
          <CitizenDashboard />
        </TestWrapper>
      )

      // Verify updated state is reflected
      await waitFor(() => {
        // Should show updated information
        expect(screen.getByText('Trámites Activos')).toBeInTheDocument()
      })
    })

    it('handles real-time updates correctly', async () => {
      const { CitizenDashboard } = await import('@/components/dashboard/CitizenDashboard')

      render(
        <TestWrapper>
          <CitizenDashboard />
        </TestWrapper>
      )

      // Verify initial state
      await waitFor(() => {
        expect(screen.getByText('en_proceso')).toBeInTheDocument()
      })

      // Simulate real-time update
      const updatedData = {
        ...mockSupabaseData.citizenProcedures[0],
        status: 'completado',
        updated_at: new Date().toISOString(),
      }

      // Mock updated response
      mockSupabase.from.mockImplementation(() => ({
        select: jest.fn(() => ({
          eq: jest.fn(() => ({
            order: jest.fn(() => ({
              limit: jest.fn(() => Promise.resolve({
                data: [updatedData],
                error: null,
              })),
            })),
          })),
        })),
      }))

      // Trigger refresh
      const refreshButton = screen.getByLabelText('Actualizar datos')
      await user.click(refreshButton)

      // Verify updated status
      await waitFor(() => {
        expect(screen.getByText('completado')).toBeInTheDocument()
      })
    })

    it('validates accessibility throughout the flow', async () => {
      const { CitizenDashboard } = await import('@/components/dashboard/CitizenDashboard')

      const { container } = render(
        <TestWrapper>
          <CitizenDashboard />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByText('Ciudadano Test')).toBeInTheDocument()
      })

      // Check heading structure
      const headings = container.querySelectorAll('h1, h2, h3, h4, h5, h6')
      expect(headings.length).toBeGreaterThan(0)

      // Check button accessibility
      const buttons = screen.getAllByRole('button')
      buttons.forEach(button => {
        expect(
          button.textContent || 
          button.getAttribute('aria-label') || 
          button.getAttribute('title')
        ).toBeTruthy()
      })

      // Check link accessibility
      const links = screen.getAllByRole('link')
      links.forEach(link => {
        expect(
          link.textContent || 
          link.getAttribute('aria-label') || 
          link.getAttribute('title')
        ).toBeTruthy()
      })

      // Check form controls
      const inputs = container.querySelectorAll('input, textarea, select')
      inputs.forEach(input => {
        const id = input.getAttribute('id')
        if (id) {
          const label = container.querySelector(`label[for="${id}"]`)
          expect(label || input.getAttribute('aria-label')).toBeTruthy()
        }
      })
    })
  })
})
