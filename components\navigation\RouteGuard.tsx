'use client'

import { useEffect, useState } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { useAuth, useRole, usePermissions } from '@/hooks'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Shield, AlertTriangle, ArrowLeft, Home } from 'lucide-react'

interface RouteGuardProps {
  children: React.ReactNode
  requiredRoles?: string[]
  requiredPermissions?: string[]
  fallbackUrl?: string
  showUnauthorized?: boolean
}

interface RouteConfig {
  path: string
  roles: string[]
  permissions?: string[]
  description: string
}

// Define route access configuration
const routeConfigs: RouteConfig[] = [
  {
    path: '/dashboard',
    roles: ['ciudadano', 'admin', 'super_admin'],
    description: 'Panel principal del usuario'
  },
  {
    path: '/tramites',
    roles: ['ciudadano'],
    description: 'Gestión de trámites ciudadanos'
  },
  {
    path: '/opas',
    roles: ['ciudadano'],
    description: 'Otros procedimientos administrativos'
  },
  {
    path: '/chat',
    roles: ['ciudadano', 'admin', 'super_admin'],
    description: 'Asistente virtual inteligente'
  },
  {
    path: '/perfil',
    roles: ['ciudadano', 'admin', 'super_admin'],
    description: 'Configuración del perfil de usuario'
  },
  {
    path: '/notificaciones',
    roles: ['ciudadano', 'admin', 'super_admin'],
    description: 'Centro de notificaciones'
  },
  {
    path: '/admin',
    roles: ['admin', 'super_admin'],
    permissions: ['canViewAdminDashboard'],
    description: 'Panel de administración'
  },
  {
    path: '/configuracion',
    roles: ['ciudadano', 'admin', 'super_admin'],
    description: 'Configuración de la cuenta'
  }
]

export function RouteGuard({ 
  children, 
  requiredRoles,
  requiredPermissions,
  fallbackUrl,
  showUnauthorized = true
}: RouteGuardProps) {
  const { user, profile, loading } = useAuth()
  const { role } = useRole()
  const { hasPermission, hasAnyPermission } = usePermissions()
  const router = useRouter()
  const pathname = usePathname()
  const [isAuthorized, setIsAuthorized] = useState<boolean | null>(null)

  useEffect(() => {
    if (loading) return

    // If no user, redirect to login
    if (!user) {
      router.push('/auth/login?redirectTo=' + encodeURIComponent(pathname))
      return
    }

    // If no profile, redirect to setup
    if (!profile) {
      router.push('/auth/setup-profile')
      return
    }

    // Find route configuration
    const routeConfig = routeConfigs.find(config => 
      pathname.startsWith(config.path)
    )

    let authorized = true

    // Check explicit required roles
    if (requiredRoles && requiredRoles.length > 0) {
      authorized = requiredRoles.includes(role?.name || '')
    }
    // Check route configuration roles
    else if (routeConfig && routeConfig.roles.length > 0) {
      authorized = routeConfig.roles.includes(role?.name || '')
    }

    // Check explicit required permissions
    if (authorized && requiredPermissions && requiredPermissions.length > 0) {
      authorized = hasAnyPermission(requiredPermissions)
    }
    // Check route configuration permissions
    else if (authorized && routeConfig && routeConfig.permissions) {
      authorized = hasAnyPermission(routeConfig.permissions)
    }

    setIsAuthorized(authorized)

    // Handle unauthorized access
    if (!authorized) {
      if (fallbackUrl) {
        router.push(fallbackUrl)
      } else {
        // Redirect based on user role
        const userRole = role?.name
        if (userRole === 'ciudadano') {
          router.push('/dashboard')
        } else if (userRole === 'admin' || userRole === 'super_admin') {
          router.push('/admin')
        } else {
          router.push('/auth/login')
        }
      }
    }
  }, [user, profile, role, loading, pathname, requiredRoles, requiredPermissions, fallbackUrl, hasAnyPermission, router])

  // Loading state
  if (loading || isAuthorized === null) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 bg-chia-blue-600 rounded-lg flex items-center justify-center mx-auto mb-4 animate-pulse">
            <span className="text-white font-bold text-lg">C</span>
          </div>
          <p className="text-gray-600">Verificando permisos...</p>
        </div>
      </div>
    )
  }

  // Unauthorized state
  if (!isAuthorized && showUnauthorized) {
    const routeConfig = routeConfigs.find(config => 
      pathname.startsWith(config.path)
    )

    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <div className="text-center">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <AlertTriangle className="h-8 w-8 text-red-600" />
              </div>
              
              <h2 className="text-xl font-semibold text-gray-900 mb-2">
                Acceso No Autorizado
              </h2>
              
              <p className="text-gray-600 mb-4">
                No tienes permisos para acceder a esta sección.
                {routeConfig && (
                  <span className="block mt-2 text-sm">
                    <strong>Sección:</strong> {routeConfig.description}
                  </span>
                )}
              </p>

              {role && (
                <div className="bg-gray-50 rounded-lg p-3 mb-4">
                  <p className="text-sm text-gray-600">
                    <strong>Tu rol actual:</strong> {role.name}
                  </p>
                  {routeConfig && (
                    <p className="text-sm text-gray-600 mt-1">
                      <strong>Roles requeridos:</strong> {routeConfig.roles.join(', ')}
                    </p>
                  )}
                </div>
              )}

              <div className="flex flex-col sm:flex-row gap-3">
                <Button 
                  variant="outline" 
                  onClick={() => router.back()}
                  className="flex items-center"
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Volver
                </Button>
                
                <Button 
                  onClick={() => {
                    const userRole = role?.name
                    if (userRole === 'ciudadano') {
                      router.push('/dashboard')
                    } else if (userRole === 'admin' || userRole === 'super_admin') {
                      router.push('/admin')
                    } else {
                      router.push('/')
                    }
                  }}
                  className="flex items-center"
                >
                  <Home className="h-4 w-4 mr-2" />
                  Ir al Inicio
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Don't render anything if not authorized and showUnauthorized is false
  if (!isAuthorized) {
    return null
  }

  // Render children if authorized
  return <>{children}</>
}

// Higher-order component for easy route protection
export function withRouteGuard<P extends object>(
  Component: React.ComponentType<P>,
  guardProps?: Omit<RouteGuardProps, 'children'>
) {
  return function GuardedComponent(props: P) {
    return (
      <RouteGuard {...guardProps}>
        <Component {...props} />
      </RouteGuard>
    )
  }
}

// Specific route guards for common use cases
export function AdminRouteGuard({ children }: { children: React.ReactNode }) {
  return (
    <RouteGuard requiredRoles={['admin', 'super_admin']}>
      {children}
    </RouteGuard>
  )
}

export function SuperAdminRouteGuard({ children }: { children: React.ReactNode }) {
  return (
    <RouteGuard requiredRoles={['super_admin']}>
      {children}
    </RouteGuard>
  )
}

export function CitizenRouteGuard({ children }: { children: React.ReactNode }) {
  return (
    <RouteGuard requiredRoles={['ciudadano']}>
      {children}
    </RouteGuard>
  )
}
