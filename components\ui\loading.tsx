'use client'

import { cn } from '@/lib/utils'
import { Loader2, FileText, Users, BarChart3, Settings, MessageSquare, Folder } from 'lucide-react'

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  className?: string
}

export function LoadingSpinner({ size = 'md', className }: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8',
    xl: 'h-12 w-12'
  }

  return (
    <Loader2 
      className={cn(
        'animate-spin text-blue-600',
        sizeClasses[size],
        className
      )} 
    />
  )
}

interface SkeletonProps {
  className?: string
  children?: React.ReactNode
}

export function Skeleton({ className, children, ...props }: SkeletonProps) {
  return (
    <div
      className={cn(
        'animate-pulse rounded-md bg-gray-200',
        className
      )}
      {...props}
    >
      {children}
    </div>
  )
}

// Card skeleton for dashboard components
export function CardSkeleton() {
  return (
    <div className="rounded-lg border bg-white p-6 shadow-sm">
      <div className="space-y-3">
        <Skeleton className="h-4 w-1/3" />
        <Skeleton className="h-8 w-1/2" />
        <Skeleton className="h-3 w-full" />
        <Skeleton className="h-3 w-2/3" />
      </div>
    </div>
  )
}

// Table skeleton for data tables
export function TableSkeleton({ rows = 5, columns = 4 }: { rows?: number; columns?: number }) {
  return (
    <div className="rounded-lg border bg-white">
      {/* Header */}
      <div className="border-b p-4">
        <div className="flex space-x-4">
          {Array.from({ length: columns }).map((_, i) => (
            <Skeleton key={i} className="h-4 flex-1" />
          ))}
        </div>
      </div>
      
      {/* Rows */}
      <div className="divide-y">
        {Array.from({ length: rows }).map((_, i) => (
          <div key={i} className="p-4">
            <div className="flex space-x-4">
              {Array.from({ length: columns }).map((_, j) => (
                <Skeleton key={j} className="h-4 flex-1" />
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

// Document grid skeleton
export function DocumentGridSkeleton({ count = 8 }: { count?: number }) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
      {Array.from({ length: count }).map((_, i) => (
        <div key={i} className="rounded-lg border bg-white p-4">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Skeleton className="h-8 w-8 rounded" />
              <Skeleton className="h-6 w-16" />
            </div>
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-3 w-2/3" />
            <div className="flex space-x-2">
              <Skeleton className="h-8 flex-1" />
              <Skeleton className="h-8 flex-1" />
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}

// Chat skeleton for chat interface
export function ChatSkeleton() {
  return (
    <div className="space-y-4">
      {/* Assistant message */}
      <div className="flex items-start space-x-3">
        <Skeleton className="h-8 w-8 rounded-full" />
        <div className="flex-1 space-y-2">
          <Skeleton className="h-4 w-3/4" />
          <Skeleton className="h-4 w-1/2" />
        </div>
      </div>
      
      {/* User message */}
      <div className="flex items-start space-x-3 justify-end">
        <div className="flex-1 space-y-2 text-right">
          <Skeleton className="h-4 w-1/3 ml-auto" />
        </div>
        <Skeleton className="h-8 w-8 rounded-full" />
      </div>
      
      {/* Assistant message */}
      <div className="flex items-start space-x-3">
        <Skeleton className="h-8 w-8 rounded-full" />
        <div className="flex-1 space-y-2">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-2/3" />
          <Skeleton className="h-4 w-1/2" />
        </div>
      </div>
    </div>
  )
}

// Page loading with context-aware content
interface PageLoadingProps {
  type?: 'dashboard' | 'documents' | 'chat' | 'admin' | 'procedures' | 'generic'
  title?: string
  description?: string
}

export function PageLoading({ type = 'generic', title, description }: PageLoadingProps) {
  const getIcon = () => {
    switch (type) {
      case 'dashboard':
        return <BarChart3 className="h-8 w-8 text-blue-600" />
      case 'documents':
        return <Folder className="h-8 w-8 text-blue-600" />
      case 'chat':
        return <MessageSquare className="h-8 w-8 text-blue-600" />
      case 'admin':
        return <Settings className="h-8 w-8 text-blue-600" />
      case 'procedures':
        return <FileText className="h-8 w-8 text-blue-600" />
      default:
        return <LoadingSpinner size="lg" />
    }
  }

  const getDefaultTitle = () => {
    switch (type) {
      case 'dashboard':
        return 'Cargando Dashboard...'
      case 'documents':
        return 'Cargando Documentos...'
      case 'chat':
        return 'Iniciando Asistente Virtual...'
      case 'admin':
        return 'Cargando Panel Administrativo...'
      case 'procedures':
        return 'Cargando Trámites...'
      default:
        return 'Cargando...'
    }
  }

  const getDefaultDescription = () => {
    switch (type) {
      case 'dashboard':
        return 'Preparando tu panel de control personalizado'
      case 'documents':
        return 'Organizando tus documentos y archivos'
      case 'chat':
        return 'Conectando con el sistema de inteligencia artificial'
      case 'admin':
        return 'Configurando herramientas administrativas'
      case 'procedures':
        return 'Consultando información de trámites municipales'
      default:
        return 'Por favor espera un momento'
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center space-y-6 max-w-md mx-auto px-4">
        {/* Icon */}
        <div className="flex justify-center">
          <div className="animate-pulse">
            {getIcon()}
          </div>
        </div>
        
        {/* Title */}
        <h2 className="text-2xl font-semibold text-gray-900">
          {title || getDefaultTitle()}
        </h2>
        
        {/* Description */}
        <p className="text-gray-600">
          {description || getDefaultDescription()}
        </p>
        
        {/* Loading bar */}
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div className="bg-blue-600 h-2 rounded-full animate-pulse" style={{ width: '60%' }}></div>
        </div>
        
        {/* Spinner */}
        <div className="flex justify-center">
          <LoadingSpinner size="md" />
        </div>
      </div>
    </div>
  )
}

// Inline loading for components
export function InlineLoading({ message = 'Cargando...' }: { message?: string }) {
  return (
    <div className="flex items-center justify-center space-x-2 py-4">
      <LoadingSpinner size="sm" />
      <span className="text-sm text-gray-600">{message}</span>
    </div>
  )
}

// Button loading state
export function ButtonLoading({ children, loading = false, ...props }: any) {
  return (
    <button disabled={loading} {...props}>
      {loading ? (
        <div className="flex items-center space-x-2">
          <LoadingSpinner size="sm" />
          <span>Cargando...</span>
        </div>
      ) : (
        children
      )}
    </button>
  )
}

// Error boundary fallback
export function ErrorFallback({ error, resetError }: { error: Error; resetError: () => void }) {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center space-y-6 max-w-md mx-auto px-4">
        <div className="text-red-600">
          <svg className="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        </div>
        
        <h2 className="text-2xl font-semibold text-gray-900">
          Algo salió mal
        </h2>
        
        <p className="text-gray-600">
          Ha ocurrido un error inesperado. Por favor, intenta nuevamente.
        </p>
        
        <button
          onClick={resetError}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
        >
          Intentar nuevamente
        </button>
        
        {process.env.NODE_ENV === 'development' && (
          <details className="text-left text-sm text-gray-500 mt-4">
            <summary className="cursor-pointer">Detalles del error</summary>
            <pre className="mt-2 whitespace-pre-wrap">{error.message}</pre>
          </details>
        )}
      </div>
    </div>
  )
}
