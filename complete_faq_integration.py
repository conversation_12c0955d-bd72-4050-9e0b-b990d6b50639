#!/usr/bin/env python3
"""
Complete FAQ Integration - Apply all remaining questions efficiently
"""

import re
import json

def extract_remaining_questions_from_corrected():
    """Extract remaining questions from corrected file starting from question 49"""
    
    # Load the corrected SQL file
    with open('corrected_faq_insertion.sql', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Extract all INSERT statements
    insert_statements = re.findall(r'INSERT INTO municipal_faqs.*?;', content, re.DOTALL)
    
    print(f"Found {len(insert_statements)} total INSERT statements in corrected file")
    
    # Since we have 48 questions already, we need the remaining ones
    # Skip the first 48 statements
    remaining_statements = insert_statements[48:]
    
    print(f"Need to apply {len(remaining_statements)} remaining questions")
    
    # Create manageable batches of 50 questions each
    batch_size = 50
    batch_files = []
    
    for i in range(0, len(remaining_statements), batch_size):
        batch = remaining_statements[i:i+batch_size]
        batch_num = i // batch_size + 1
        
        filename = f'complete_faq_batch_{batch_num:02d}.sql'
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"-- Complete FAQ Integration Batch {batch_num}\n")
            f.write(f"-- Questions {i+49} to {min(i+48+batch_size, 383)}\n")
            f.write(f"-- Total questions in batch: {len(batch)}\n\n")
            f.write("\n".join(batch))
            f.write("\n")
        
        batch_files.append((filename, len(batch)))
        print(f"Created {filename} with {len(batch)} questions")
    
    return batch_files, len(remaining_statements)

def create_integration_summary():
    """Create integration summary"""
    
    summary = {
        "current_status": {
            "questions_in_database": 48,
            "target_questions": 383,
            "remaining_questions": 335,
            "completion_percentage": round((48/383)*100, 1)
        },
        "next_steps": [
            "Apply complete FAQ batches systematically",
            "Verify final count matches 383 questions",
            "Generate theme distribution report",
            "Confirm data integrity"
        ]
    }
    
    with open('complete_integration_summary.json', 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2, ensure_ascii=False)
    
    return summary

def main():
    """Main function"""
    print("Complete FAQ Integration Processor")
    print("=" * 35)
    
    try:
        # Extract remaining questions
        batch_files, remaining_count = extract_remaining_questions_from_corrected()
        
        # Create summary
        summary = create_integration_summary()
        
        print(f"\n✅ Successfully prepared complete integration")
        print(f"📊 Current questions in database: {summary['current_status']['questions_in_database']}")
        print(f"📊 Questions to be applied: {remaining_count}")
        print(f"📊 Total target: {summary['current_status']['target_questions']}")
        print(f"📊 Completion: {summary['current_status']['completion_percentage']}%")
        
        print(f"\n📁 Created {len(batch_files)} batch files:")
        for i, (filename, count) in enumerate(batch_files, 1):
            print(f"   {i}. {filename} ({count} questions)")
        
        print(f"\n🚀 Ready to complete FAQ integration!")
        print(f"   Apply batches sequentially to reach 383 total questions")
        
        return 0
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
