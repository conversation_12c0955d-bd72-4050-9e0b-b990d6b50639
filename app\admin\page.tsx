import { createClient } from '@/lib/supabase/server'
import { getCurrentUser, getUserProfile } from '@/lib/supabase/server'
import { redirect } from 'next/navigation'
import { AdminDashboard } from '@/components/admin/AdminDashboard'
import { ProtectedLayout } from '@/components/layout'
import { AdminRouteGuard } from '@/components/navigation'

export default async function AdminPage() {
  const { user, error } = await getCurrentUser()
  
  if (error || !user) {
    redirect('/auth/login')
  }

  const { data: profile } = await getUserProfile(user.id)
  
  if (!profile) {
    redirect('/auth/setup-profile')
  }

  // Check if user has admin or super_admin role
  if (!profile.role || !['admin', 'super_admin'].includes(profile.role.name)) {
    redirect('/dashboard?error=unauthorized')
  }

  const supabase = createClient()

  // Get admin statistics based on role
  let stats = {
    totalUsers: 0,
    totalProcedures: 0,
    pendingProcedures: 0,
    completedProcedures: 0,
    totalDependencies: 0,
    recentActivity: []
  }

  try {
    if (profile.role.name === 'super_admin') {
      // Super admin can see all data
      const [
        { count: totalUsers },
        { count: totalProcedures },
        { count: pendingProcedures },
        { count: completedProcedures },
        { count: totalDependencies },
        { data: recentActivity }
      ] = await Promise.all([
        supabase.from('profiles').select('*', { count: 'exact', head: true }),
        supabase.from('citizen_procedures').select('*', { count: 'exact', head: true }),
        supabase.from('citizen_procedures').select('*', { count: 'exact', head: true }).eq('status_id', 'pending'),
        supabase.from('citizen_procedures').select('*', { count: 'exact', head: true }).eq('status_id', 'completed'),
        supabase.from('dependencies').select('*', { count: 'exact', head: true }),
        supabase
          .from('citizen_procedures')
          .select(`
            id,
            created_at,
            status_id,
            citizen:profiles!citizen_id(full_name),
            procedure:procedures(name),
            dependency:dependencies(name)
          `)
          .order('created_at', { ascending: false })
          .limit(10)
      ])

      stats = {
        totalUsers: totalUsers || 0,
        totalProcedures: totalProcedures || 0,
        pendingProcedures: pendingProcedures || 0,
        completedProcedures: completedProcedures || 0,
        totalDependencies: totalDependencies || 0,
        recentActivity: recentActivity || []
      }
    } else if (profile.role.name === 'admin' && profile.dependency_id) {
      // Admin can only see data from their dependency
      const [
        { count: totalUsers },
        { count: totalProcedures },
        { count: pendingProcedures },
        { count: completedProcedures },
        { data: recentActivity }
      ] = await Promise.all([
        supabase
          .from('profiles')
          .select('*', { count: 'exact', head: true })
          .eq('dependency_id', profile.dependency_id),
        supabase
          .from('citizen_procedures')
          .select('*', { count: 'exact', head: true })
          .in('procedure_id', 
            supabase
              .from('procedures')
              .select('id')
              .eq('dependency_id', profile.dependency_id)
          ),
        supabase
          .from('citizen_procedures')
          .select('*', { count: 'exact', head: true })
          .eq('status_id', 'pending')
          .in('procedure_id', 
            supabase
              .from('procedures')
              .select('id')
              .eq('dependency_id', profile.dependency_id)
          ),
        supabase
          .from('citizen_procedures')
          .select('*', { count: 'exact', head: true })
          .eq('status_id', 'completed')
          .in('procedure_id', 
            supabase
              .from('procedures')
              .select('id')
              .eq('dependency_id', profile.dependency_id)
          ),
        supabase
          .from('citizen_procedures')
          .select(`
            id,
            created_at,
            status_id,
            citizen:profiles!citizen_id(full_name),
            procedure:procedures!inner(name, dependency_id)
          `)
          .eq('procedure.dependency_id', profile.dependency_id)
          .order('created_at', { ascending: false })
          .limit(10)
      ])

      stats = {
        totalUsers: totalUsers || 0,
        totalProcedures: totalProcedures || 0,
        pendingProcedures: pendingProcedures || 0,
        completedProcedures: completedProcedures || 0,
        totalDependencies: 1, // Admin only manages their own dependency
        recentActivity: recentActivity || []
      }
    }
  } catch (error) {
    console.error('Error fetching admin stats:', error)
  }

  return (
    <ProtectedLayout requireAuth={true} allowedRoles={['admin', 'super_admin']}>
      <AdminRouteGuard>
        <AdminDashboard
          user={user}
          profile={profile}
          stats={stats}
        />
      </AdminRouteGuard>
    </ProtectedLayout>
  )
}
