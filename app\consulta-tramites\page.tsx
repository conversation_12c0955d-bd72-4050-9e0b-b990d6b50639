import { createClient } from '@/lib/supabase/server'
import { PublicProcedureSearchInterface } from '@/components/procedures/PublicProcedureSearchInterface'
import { ContextualFAQSection } from '@/components/faq/ContextualFAQSection'
import { PublicLayout } from '@/components/layout/PublicLayout'
import { getAllProceduresWithSuit } from '@/lib/services/suitEnhancedProcedureService'

export default async function ConsultaTramitesPage() {
  const supabase = createClient()

  // Get all procedures with SUIT enhancement using the consolidated service
  const allProcedures = await getAllProceduresWithSuit()

  // Get all dependencies for filtering
  const { data: dependencies } = await supabase
    .from('dependencies')
    .select('*')
    .eq('is_active', true)
    .order('name', { ascending: true })

  // Get all subdependencies for filtering
  const { data: subdependencies } = await supabase
    .from('subdependencies')
    .select('*')
    .eq('is_active', true)
    .order('name', { ascending: true })

  // Get procedure categories from the consolidated procedures
  const categories = [...new Set(
    allProcedures
      .map(p => p.category)
      .filter(Boolean)
  )]

  // Count procedures by type
  const tramitesCount = allProcedures.filter(p => p.procedure_type === 'TRAMITE').length
  const opasCount = allProcedures.filter(p => p.procedure_type === 'OPA').length

  return (
    <PublicLayout>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-gradient-to-r from-chia-blue-600 to-chia-blue-800 text-white shadow-lg">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-8">
              <div>
                <h1 className="text-3xl font-bold">
                  Consulta de Trámites y OPAs
                </h1>
                <p className="text-blue-100 mt-1">
                  Consulta información detallada sobre trámites y otros procedimientos administrativos municipales
                </p>
                <div className="flex items-center mt-3 space-x-4 text-sm">
                  <div className="flex items-center">
                    <span className="font-medium">{allProcedures?.length || 0}</span>
                    <span className="ml-1">procedimientos disponibles</span>
                  </div>
                  <div className="flex items-center">
                    <span className="font-medium">{tramitesCount}</span>
                    <span className="ml-1">trámites</span>
                  </div>
                  <div className="flex items-center">
                    <span className="font-medium">{opasCount}</span>
                    <span className="ml-1">OPAs</span>
                  </div>
                  <div className="flex items-center">
                    <span className="font-medium">{dependencies?.length || 0}</span>
                    <span className="ml-1">dependencias</span>
                  </div>
                  <div className="flex items-center">
                    <span className="font-medium">{categories.length}</span>
                    <span className="ml-1">categorías</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <PublicProcedureSearchInterface
            procedures={allProcedures || []}
            dependencies={dependencies || []}
            subdependencies={subdependencies || []}
            categories={categories}
          />
        </div>

        {/* FAQ Section - Contextual for procedure consultation */}
        <div className="bg-white border-t">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <ContextualFAQSection context="procedures" />
          </div>
        </div>
      </div>
    </PublicLayout>
  )
}
