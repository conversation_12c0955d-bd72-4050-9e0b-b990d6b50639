'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Search,
  FileText,
  Building2,
  Crown,
  Shield,
  Banknote,
  Map,
  Users,
  GraduationCap,
  HeartPulse,
  Car,
  Building,
  Folder,
  ArrowRight,
  Filter,
  BarChart3,
  Network
} from 'lucide-react'
import DependencyService, { type Dependency, type DependencyStats } from '@/lib/services/dependencyService'
import { AdvancedDependencySearch } from './AdvancedDependencySearch'
import { DependencyDetailModal } from './DependencyDetailModal'
import { ProcedureDetailModal } from './ProcedureDetailModal'
import { SearchResult } from '@/lib/services/searchService'
import { DependencyGridLoading } from '@/components/ui/loading-states'
import { ErrorState } from '@/components/ui/error-states'
import { useDependencyNavigation } from '@/components/ui/breadcrumb'
import { cn } from '@/lib/utils'

// Mapeo de iconos
const iconMap = {
  crown: Crown,
  shield: Shield,
  banknote: Banknote,
  map: Map,
  users: Users,
  'graduation-cap': GraduationCap,
  'heart-pulse': HeartPulse,
  car: Car,
  building: Building,
  folder: Folder
}

interface DependencyGridProps {
  onDependencySelect?: (dependency: Dependency) => void
  showSearch?: boolean
  showStats?: boolean
  maxItems?: number
  className?: string
}

export function DependencyGrid({
  onDependencySelect,
  showSearch = true,
  showStats = true,
  maxItems,
  className = ''
}: DependencyGridProps) {
  const [dependencies, setDependencies] = useState<Dependency[]>([])
  const [filteredDependencies, setFilteredDependencies] = useState<Dependency[]>([])
  const [stats, setStats] = useState<DependencyStats | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [searchResults, setSearchResults] = useState<SearchResult[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Estados para modales
  const [selectedDependency, setSelectedDependency] = useState<Dependency | null>(null)
  const [selectedProcedure, setSelectedProcedure] = useState<any | null>(null)
  const [isDependencyModalOpen, setIsDependencyModalOpen] = useState(false)
  const [isProcedureModalOpen, setIsProcedureModalOpen] = useState(false)

  // Cargar datos iniciales
  useEffect(() => {
    loadDependencies()
  }, [])

  // Filtrar dependencias cuando cambia la búsqueda
  useEffect(() => {
    filterDependencies()
  }, [searchQuery, dependencies])

  const loadDependencies = async () => {
    try {
      setIsLoading(true)
      setError(null)

      const [allDependencies, dependencyStats] = await Promise.all([
        DependencyService.getAllDependencies(),
        showStats ? DependencyService.getDependencyStats() : Promise.resolve(null)
      ])

      setDependencies(allDependencies)
      setStats(dependencyStats)
    } catch (err) {
      console.error('Error cargando dependencias:', err)
      setError('Error al cargar las dependencias. Por favor, intenta de nuevo.')
    } finally {
      setIsLoading(false)
    }
  }

  const filterDependencies = async () => {
    try {
      if (!searchQuery.trim()) {
        let result = dependencies
        if (maxItems) {
          result = result.slice(0, maxItems)
        }
        setFilteredDependencies(result)
        return
      }

      const searchResults = await DependencyService.searchDependencies(searchQuery)
      let result = searchResults
      if (maxItems) {
        result = result.slice(0, maxItems)
      }
      setFilteredDependencies(result)
    } catch (err) {
      console.error('Error filtrando dependencias:', err)
    }
  }

  const handleDependencyClick = (dependency: Dependency) => {
    if (onDependencySelect) {
      onDependencySelect(dependency)
    } else {
      // Abrir modal de dependencia
      setSelectedDependency(dependency)
      setIsDependencyModalOpen(true)
    }
  }

  // Manejar búsqueda avanzada
  const handleAdvancedSearch = (query: string, results: SearchResult[]) => {
    setSearchQuery(query)
    setSearchResults(results)
  }

  // Manejar selección de resultado de búsqueda
  const handleSearchResultSelect = (result: SearchResult) => {
    if (result.type === 'DEPENDENCIA') {
      // Buscar la dependencia completa
      const dependency = dependencies.find(dep => dep.id === result.id)
      if (dependency) {
        setSelectedDependency(dependency)
        setIsDependencyModalOpen(true)
      }
    } else {
      // Es un trámite o OPA - abrir modal de procedimiento
      setSelectedProcedure(result)
      setIsProcedureModalOpen(true)
    }
  }

  // Manejar selección de procedimiento desde modal de dependencia
  const handleProcedureSelect = (procedure: any) => {
    setIsDependencyModalOpen(false)
    setSelectedProcedure(procedure)
    setIsProcedureModalOpen(true)
  }

  const getIcon = (iconName: string) => {
    const IconComponent = iconMap[iconName as keyof typeof iconMap] || Folder
    return IconComponent
  }

  if (isLoading) {
    return (
      <div className={className}>
        <DependencyGridLoading />
      </div>
    )
  }

  if (error) {
    return (
      <div className={className}>
        <ErrorState
          type="server"
          title="Error al cargar dependencias"
          description={error}
          onRetry={loadDependencies}
          onGoHome={() => window.location.reload()}
          size="lg"
        />
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Estadísticas */}
      {showStats && stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="hover:shadow-xl transition-all duration-300 group">
            <CardContent className="p-6">
              <div className="flex items-center space-x-3">
                <div className="p-3 bg-primary/10 rounded-xl group-hover:bg-primary/20 transition-colors duration-300">
                  <Building2 className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <p className="text-sm font-semibold text-gray-600">Dependencias</p>
                  <p className="text-3xl font-bold text-primary group-hover:scale-105 transition-transform duration-300">{stats.totalDependencies}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-glow-green transition-all duration-300 group">
            <CardContent className="p-6">
              <div className="flex items-center space-x-3">
                <div className="p-3 bg-chia-green-100 rounded-xl group-hover:bg-chia-green-200 transition-colors duration-300">
                  <FileText className="h-6 w-6 text-chia-green-600" />
                </div>
                <div>
                  <p className="text-sm font-semibold text-gray-600">Trámites</p>
                  <p className="text-3xl font-bold text-chia-green-900 group-hover:scale-105 transition-transform duration-300">{stats.totalTramites.toLocaleString()}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-xl transition-all duration-300 group">
            <CardContent className="p-6">
              <div className="flex items-center space-x-3">
                <div className="p-3 bg-chia-blue-100 rounded-xl group-hover:bg-chia-blue-200 transition-colors duration-300">
                  <BarChart3 className="h-6 w-6 text-chia-blue-600" />
                </div>
                <div>
                  <p className="text-sm font-semibold text-gray-600">OPAs</p>
                  <p className="text-3xl font-bold text-chia-blue-900 group-hover:scale-105 transition-transform duration-300">{stats.totalOPAs.toLocaleString()}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-xl transition-all duration-300 group">
            <CardContent className="p-6">
              <div className="flex items-center space-x-3">
                <div className="p-3 bg-purple-100 rounded-xl group-hover:bg-purple-200 transition-colors duration-300">
                  <Filter className="h-6 w-6 text-purple-600" />
                </div>
                <div>
                  <p className="text-sm font-semibold text-gray-600">Total Procedimientos</p>
                  <p className="text-3xl font-bold text-purple-900 group-hover:scale-105 transition-transform duration-300">{stats.totalProcedures.toLocaleString()}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Búsqueda avanzada */}
      {showSearch && (
        <AdvancedDependencySearch
          onSearch={handleAdvancedSearch}
          onResultSelect={handleSearchResultSelect}
          placeholder="Buscar dependencias, trámites o servicios..."
          className="max-w-2xl"
          maxResults={15}
        />
      )}

      {/* Grid de dependencias */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {filteredDependencies.map((dependency) => {
          const IconComponent = getIcon(dependency.icon || 'folder')

          return (
            <Card
              key={dependency.id}
              className={`cursor-pointer transition-all duration-300 hover:shadow-xl hover:scale-105 border-2 group ${dependency.color} hover:border-primary/40`}
              onClick={() => handleDependencyClick(dependency)}
            >
              <CardHeader className="pb-4">
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="p-3 rounded-2xl bg-white/80 backdrop-blur-sm shadow-lg group-hover:shadow-xl group-hover:scale-110 transition-all duration-300">
                      <IconComponent className="h-7 w-7 text-primary" />
                    </div>
                    <div className="flex-1">
                      <CardTitle className="text-xl font-bold text-primary leading-tight group-hover:text-primary/80 transition-colors">
                        {dependency.name}
                      </CardTitle>
                      <Badge variant="secondary" className="mt-2 text-xs font-semibold">
                        {dependency.sigla}
                      </Badge>
                    </div>
                  </div>
                  <ArrowRight className="h-5 w-5 text-gray-400 group-hover:text-primary group-hover:translate-x-1 transition-all duration-300" />
                </div>

                {dependency.description && (
                  <CardDescription className="text-sm text-gray-700 mt-3 leading-relaxed">
                    {dependency.description}
                  </CardDescription>
                )}
              </CardHeader>
              
              <CardContent className="pt-2">
                <div className="space-y-4">
                  {/* Información consolidada en grid */}
                  <div className="grid grid-cols-2 gap-4">
                    {/* Trámites */}
                    <div className="bg-gradient-to-br from-chia-green-50 to-chia-green-100 rounded-xl p-4 border border-chia-green-200/50">
                      <div className="flex items-center justify-between mb-2">
                        <div className="p-2 bg-chia-green-200 rounded-lg">
                          <FileText className="h-4 w-4 text-chia-green-700" />
                        </div>
                        <Badge variant="secondary" className="bg-chia-green-200 text-chia-green-800 text-xs">
                          Trámites
                        </Badge>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold text-chia-green-800 group-hover:scale-110 transition-transform duration-300">
                          {dependency.tramitesCount}
                        </p>
                        <p className="text-xs font-medium text-chia-green-600">disponibles</p>
                      </div>
                    </div>

                    {/* OPAs */}
                    <div className="bg-gradient-to-br from-chia-blue-50 to-chia-blue-100 rounded-xl p-4 border border-chia-blue-200/50">
                      <div className="flex items-center justify-between mb-2">
                        <div className="p-2 bg-chia-blue-200 rounded-lg">
                          <BarChart3 className="h-4 w-4 text-chia-blue-700" />
                        </div>
                        <Badge variant="secondary" className="bg-chia-blue-200 text-chia-blue-800 text-xs">
                          OPAs
                        </Badge>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold text-chia-blue-800 group-hover:scale-110 transition-transform duration-300">
                          {dependency.opasCount}
                        </p>
                        <p className="text-xs font-medium text-chia-blue-600">disponibles</p>
                      </div>
                    </div>
                  </div>

                  {/* Información adicional */}
                  <div className="flex items-center justify-between pt-3 border-t border-gray-200/60">
                    {/* Subdependencias */}
                    {dependency.subdependenciasCount > 0 ? (
                      <div className="flex items-center space-x-2">
                        <div className="p-1.5 bg-purple-100 rounded-lg">
                          <Network className="h-4 w-4 text-purple-600" />
                        </div>
                        <span className="font-semibold text-purple-700 text-sm">
                          {dependency.subdependenciasCount} subdependencias
                        </span>
                      </div>
                    ) : (
                      <div className="flex items-center space-x-2">
                        <div className="p-1.5 bg-gray-100 rounded-lg">
                          <Building2 className="h-4 w-4 text-gray-500" />
                        </div>
                        <span className="font-medium text-gray-500 text-sm">
                          Dependencia única
                        </span>
                      </div>
                    )}

                    {/* Total consolidado */}
                    <div className="text-right">
                      <p className="text-lg font-bold text-primary group-hover:scale-110 transition-transform duration-300">
                        {dependency.totalProcedures}
                      </p>
                      <p className="text-xs font-medium text-gray-500">total servicios</p>
                    </div>
                  </div>

                  {/* Indicador de estado */}
                  <div className="flex items-center justify-center pt-2">
                    <Badge
                      variant="outline"
                      className={cn(
                        "text-xs font-semibold transition-all duration-300",
                        dependency.totalProcedures > 10
                          ? "text-chia-green-600 border-chia-green-300 bg-chia-green-50 group-hover:bg-chia-green-100"
                          : dependency.totalProcedures > 5
                          ? "text-orange-600 border-orange-300 bg-orange-50 group-hover:bg-orange-100"
                          : "text-gray-600 border-gray-300 bg-gray-50 group-hover:bg-gray-100"
                      )}
                    >
                      {dependency.totalProcedures > 10 ? "Alta disponibilidad" :
                       dependency.totalProcedures > 5 ? "Disponibilidad media" : "Disponibilidad básica"}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Mensaje cuando no hay resultados */}
      {filteredDependencies.length === 0 && !isLoading && (
        <div className="text-center py-12">
          <Building2 className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No se encontraron dependencias
          </h3>
          <p className="text-gray-600 mb-4">
            {searchQuery 
              ? `No hay dependencias que coincidan con "${searchQuery}"`
              : 'No hay dependencias disponibles en este momento'
            }
          </p>
          {searchQuery && (
            <Button 
              variant="outline" 
              onClick={() => setSearchQuery('')}
            >
              Limpiar búsqueda
            </Button>
          )}
        </div>
      )}

      {/* Modales */}
      <DependencyDetailModal
        dependency={selectedDependency}
        isOpen={isDependencyModalOpen}
        onClose={() => {
          setIsDependencyModalOpen(false)
          setSelectedDependency(null)
        }}
        onProcedureSelect={handleProcedureSelect}
      />

      <ProcedureDetailModal
        procedure={selectedProcedure}
        isOpen={isProcedureModalOpen}
        onClose={() => {
          setIsProcedureModalOpen(false)
          setSelectedProcedure(null)
        }}
      />
    </div>
  )
}
