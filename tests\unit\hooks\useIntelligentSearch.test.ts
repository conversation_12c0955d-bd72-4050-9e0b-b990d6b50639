import { renderHook, act } from '@testing-library/react'
import { useIntelligentSearch } from '../../../hooks/useIntelligentSearch'
import { FilterState } from '../../../components/search/SmartFilters'

// Mock lodash debounce
jest.mock('lodash', () => ({
  debounce: (fn: any) => {
    fn.cancel = jest.fn()
    return fn
  }
}))

const mockProcedures = [
  {
    id: '1',
    name: 'Licencia de Construcción',
    description: 'Permiso para construir edificaciones',
    cost: 150000,
    response_time: '15 días hábiles',
    procedure_type: 'TRAMITE' as const,
    has_cost: true,
    online_available: false,
    dependency: { id: 'dep1', name: 'Secretaría de Planeación' },
    subdependency: { id: 'sub1', name: 'Oficina de Licencias' },
    requirements: ['Planos arquitectónicos', 'Certificado de tradición'],
    tags: ['construcción', 'edificación', 'permiso']
  },
  {
    id: '2',
    name: 'Certificado de Residencia',
    description: 'Documento que certifica el lugar de residencia',
    cost: 0,
    response_time: 'Inmediato',
    procedure_type: 'OPA' as const,
    has_cost: false,
    online_available: true,
    dependency: { id: 'dep2', name: 'Secretaría de Gobierno' },
    subdependency: { id: 'sub2', name: 'Oficina de Atención al Ciudadano' },
    requirements: ['Cédula de ciudadanía', 'Recibo de servicios públicos'],
    tags: ['residencia', 'domicilio', 'certificado']
  },
  {
    id: '3',
    name: 'Registro de Empresa',
    description: 'Inscripción de nueva empresa en el registro mercantil',
    cost: 75000,
    response_time: '5 días hábiles',
    procedure_type: 'TRAMITE' as const,
    has_cost: true,
    online_available: true,
    dependency: { id: 'dep3', name: 'Secretaría de Desarrollo Económico' },
    subdependency: { id: 'sub3', name: 'Oficina de Registro Empresarial' },
    requirements: ['Formulario de registro', 'Documento de identidad'],
    tags: ['empresa', 'negocio', 'registro']
  }
]

const mockDependencies = [
  { id: 'dep1', name: 'Secretaría de Planeación' },
  { id: 'dep2', name: 'Secretaría de Gobierno' },
  { id: 'dep3', name: 'Secretaría de Desarrollo Económico' }
]

const mockSubdependencies = [
  { id: 'sub1', name: 'Oficina de Licencias' },
  { id: 'sub2', name: 'Oficina de Atención al Ciudadano' },
  { id: 'sub3', name: 'Oficina de Registro Empresarial' }
]

describe('useIntelligentSearch', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should initialize with default values', () => {
    const { result } = renderHook(() =>
      useIntelligentSearch({
        procedures: mockProcedures,
        dependencies: mockDependencies,
        subdependencies: mockSubdependencies
      })
    )

    expect(result.current.searchTerm).toBe('')
    expect(result.current.filters).toEqual({})
    expect(result.current.searchResults).toHaveLength(3) // All procedures initially
    expect(result.current.isSearching).toBe(false)
    expect(result.current.searchHistory).toEqual([])
  })

  it('should update search term', () => {
    const { result } = renderHook(() =>
      useIntelligentSearch({
        procedures: mockProcedures,
        dependencies: mockDependencies,
        subdependencies: mockSubdependencies
      })
    )

    act(() => {
      result.current.updateSearchTerm('licencia')
    })

    expect(result.current.searchTerm).toBe('licencia')
  })

  it('should filter procedures by search term', () => {
    const { result } = renderHook(() =>
      useIntelligentSearch({
        procedures: mockProcedures,
        dependencies: mockDependencies,
        subdependencies: mockSubdependencies
      })
    )

    act(() => {
      result.current.updateSearchTerm('construcción')
    })

    // Should find the construction license
    expect(result.current.searchResults).toHaveLength(1)
    expect(result.current.searchResults[0].name).toBe('Licencia de Construcción')
  })

  it('should filter by dependency', () => {
    const { result } = renderHook(() =>
      useIntelligentSearch({
        procedures: mockProcedures,
        dependencies: mockDependencies,
        subdependencies: mockSubdependencies
      })
    )

    const filters: FilterState = {
      dependency: 'dep2'
    }

    act(() => {
      result.current.updateFilters(filters)
    })

    expect(result.current.searchResults).toHaveLength(1)
    expect(result.current.searchResults[0].name).toBe('Certificado de Residencia')
  })

  it('should filter by modality', () => {
    const { result } = renderHook(() =>
      useIntelligentSearch({
        procedures: mockProcedures,
        dependencies: mockDependencies,
        subdependencies: mockSubdependencies
      })
    )

    const filters: FilterState = {
      modality: 'virtual'
    }

    act(() => {
      result.current.updateFilters(filters)
    })

    // Should find procedures available online
    expect(result.current.searchResults).toHaveLength(2)
    expect(result.current.searchResults.every(p => p.online_available === true)).toBe(true)
  })

  it('should filter by cost', () => {
    const { result } = renderHook(() =>
      useIntelligentSearch({
        procedures: mockProcedures,
        dependencies: mockDependencies,
        subdependencies: mockSubdependencies
      })
    )

    const filters: FilterState = {
      cost: 'gratuito'
    }

    act(() => {
      result.current.updateFilters(filters)
    })

    expect(result.current.searchResults).toHaveLength(1)
    expect(result.current.searchResults[0].name).toBe('Certificado de Residencia')
  })

  it('should combine search term and filters', () => {
    const { result } = renderHook(() =>
      useIntelligentSearch({
        procedures: mockProcedures,
        dependencies: mockDependencies,
        subdependencies: mockSubdependencies
      })
    )

    act(() => {
      result.current.updateSearchTerm('certificado')
      result.current.updateFilters({ cost: 'gratuito' })
    })

    expect(result.current.searchResults).toHaveLength(1)
    expect(result.current.searchResults[0].name).toBe('Certificado de Residencia')
  })

  it('should calculate match scores correctly', () => {
    const { result } = renderHook(() =>
      useIntelligentSearch({
        procedures: mockProcedures,
        dependencies: mockDependencies,
        subdependencies: mockSubdependencies
      })
    )

    act(() => {
      result.current.updateSearchTerm('licencia')
    })

    const results = result.current.searchResults
    expect(results[0].matchScore).toBeGreaterThan(0)
    // Exact name match should have higher score
    expect(results.find(r => r.name.includes('Licencia'))?.matchScore).toBeGreaterThan(
      results.find(r => !r.name.includes('Licencia'))?.matchScore || 0
    )
  })

  it('should provide search suggestions', () => {
    const { result } = renderHook(() =>
      useIntelligentSearch({
        procedures: mockProcedures,
        dependencies: mockDependencies,
        subdependencies: mockSubdependencies
      })
    )

    const suggestions = result.current.getSuggestions('lic')
    expect(suggestions).toContain('Licencia de Construcción')
  })

  it('should clear search and filters', () => {
    const { result } = renderHook(() =>
      useIntelligentSearch({
        procedures: mockProcedures,
        dependencies: mockDependencies,
        subdependencies: mockSubdependencies
      })
    )

    act(() => {
      result.current.updateSearchTerm('test')
      result.current.updateFilters({ dependency: 'dep1' })
    })

    act(() => {
      result.current.clearSearch()
    })

    expect(result.current.searchTerm).toBe('')
    expect(result.current.filters).toEqual({})
  })

  it('should provide accurate search stats', () => {
    const { result } = renderHook(() =>
      useIntelligentSearch({
        procedures: mockProcedures,
        dependencies: mockDependencies,
        subdependencies: mockSubdependencies
      })
    )

    expect(result.current.searchStats.totalProcedures).toBe(3)
    expect(result.current.searchStats.filteredCount).toBe(3)
    expect(result.current.searchStats.hasActiveFilters).toBe(false)
    expect(result.current.searchStats.hasSearchTerm).toBe(false)

    act(() => {
      result.current.updateSearchTerm('test')
      result.current.updateFilters({ dependency: 'dep1' })
    })

    expect(result.current.searchStats.hasActiveFilters).toBe(true)
    expect(result.current.searchStats.hasSearchTerm).toBe(true)
  })
})
