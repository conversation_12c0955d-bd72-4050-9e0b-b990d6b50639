import { redirect } from 'next/navigation'
import { createClient } from '@/lib/supabase/server'
import { DocumentPortal } from '@/components/documents/DocumentPortal'

export default async function DocumentosPage() {
  const supabase = createClient()

  // Check authentication
  const { data: { user }, error: authError } = await supabase.auth.getUser()
  
  if (authError || !user) {
    redirect('/auth/login')
  }

  // Get user profile and role
  const { data: profile } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', user.id)
    .single()

  if (!profile) {
    redirect('/auth/setup-profile')
  }

  // Get user's documents from citizen_procedures
  const { data: userProcedures } = await supabase
    .from('citizen_procedures')
    .select(`
      id,
      reference_number,
      attachments,
      created_at,
      updated_at,
      status_id,
      procedure:procedures(
        id,
        name,
        dependency:dependencies(name)
      ),
      status:procedure_statuses(
        name,
        display_name,
        color
      )
    `)
    .eq('citizen_id', user.id)
    .order('updated_at', { ascending: false })

  // Get all documents from storage for this user
  const { data: storageFiles } = await supabase.storage
    .from('documents')
    .list(user.id, {
      limit: 1000,
      sortBy: { column: 'updated_at', order: 'desc' }
    })

  // Get document statistics
  const totalDocuments = userProcedures?.reduce((total, procedure) => {
    return total + (procedure.attachments?.length || 0)
  }, 0) || 0

  const proceduresWithDocuments = userProcedures?.filter(p => 
    p.attachments && p.attachments.length > 0
  ).length || 0

  // Calculate storage usage (approximate)
  const totalStorageUsed = storageFiles?.reduce((total, file) => {
    return total + (file.metadata?.size || 0)
  }, 0) || 0

  // Get recent document activity
  const recentActivity = userProcedures?.slice(0, 5).map(procedure => ({
    id: procedure.id,
    type: 'document_upload',
    title: `Documentos - ${procedure.procedure.name}`,
    description: `${procedure.attachments?.length || 0} documento(s) en ${procedure.reference_number}`,
    date: procedure.updated_at,
    procedureId: procedure.id,
    referenceNumber: procedure.reference_number
  })) || []

  return (
    <div className="min-h-screen bg-gray-50">
      <DocumentPortal
        user={user}
        profile={profile}
        procedures={userProcedures || []}
        storageFiles={storageFiles || []}
        statistics={{
          totalDocuments,
          proceduresWithDocuments,
          totalStorageUsed,
          storageLimit: 100 * 1024 * 1024 // 100MB limit
        }}
        recentActivity={recentActivity}
      />
    </div>
  )
}
