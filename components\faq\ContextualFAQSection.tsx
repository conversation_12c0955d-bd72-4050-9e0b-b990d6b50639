'use client'

import React from 'react'
import { FAQSection } from './FAQSection'

/**
 * Contextos disponibles para FAQs especializadas
 */
export type FAQContext = 
  | 'home'           // Página principal
  | 'procedures'     // Consulta de trámites
  | 'management'     // Gestión de trámites
  | 'citizen'        // Panel ciudadano
  | 'payments'       // Pagos y facturación
  | 'certificates'   // Certificados
  | 'licenses'       // Licencias y permisos

/**
 * Props para el componente ContextualFAQSection
 */
interface ContextualFAQSectionProps {
  context: FAQContext
  className?: string
  compact?: boolean
}

/**
 * Configuraciones específicas para cada contexto
 */
const contextConfigs: Record<FAQContext, {
  title: string
  description: string
  initialLimit: number
  showSearch: boolean
  showCategoryFilter: boolean
  showStats: boolean
  preferredCategories?: string[]
}> = {
  home: {
    title: 'Preguntas Frecuentes',
    description: 'Encuentra respuestas rápidas a las consultas más comunes sobre trámites y servicios municipales de Chía',
    initialLimit: 8,
    showSearch: true,
    showCategoryFilter: true,
    showStats: true
  },
  procedures: {
    title: '¿Necesitas ayuda con los trámites?',
    description: 'Encuentra respuestas rápidas sobre cómo consultar y gestionar tus trámites municipales',
    initialLimit: 6,
    showSearch: true,
    showCategoryFilter: true,
    showStats: false,
    preferredCategories: ['tramites-generales', 'licencias', 'certificados']
  },
  management: {
    title: 'Ayuda con la Gestión de Trámites',
    description: 'Resuelve dudas sobre cómo iniciar, seguir y completar tus trámites municipales',
    initialLimit: 6,
    showSearch: true,
    showCategoryFilter: true,
    showStats: false,
    preferredCategories: ['tramites-generales', 'pagos']
  },
  citizen: {
    title: '¿Tienes dudas sobre tus trámites?',
    description: 'Encuentra respuestas sobre el estado, requisitos y proceso de tus trámites municipales',
    initialLimit: 5,
    showSearch: true,
    showCategoryFilter: false,
    showStats: false,
    preferredCategories: ['tramites-generales']
  },
  payments: {
    title: 'Ayuda con Pagos y Facturación',
    description: 'Resuelve dudas sobre impuestos, pagos en línea y facturación municipal',
    initialLimit: 6,
    showSearch: true,
    showCategoryFilter: false,
    showStats: false,
    preferredCategories: ['impuestos', 'pagos']
  },
  certificates: {
    title: 'Información sobre Certificados',
    description: 'Todo lo que necesitas saber sobre certificados municipales y su obtención',
    initialLimit: 5,
    showSearch: true,
    showCategoryFilter: false,
    showStats: false,
    preferredCategories: ['certificados']
  },
  licenses: {
    title: 'Guía de Licencias y Permisos',
    description: 'Información detallada sobre licencias de construcción, funcionamiento y permisos especiales',
    initialLimit: 6,
    showSearch: true,
    showCategoryFilter: false,
    showStats: false,
    preferredCategories: ['licencias']
  }
}

/**
 * Componente FAQ contextual que adapta su contenido según el contexto de uso
 * 
 * @param context - Contexto específico para mostrar FAQs relevantes
 * @param className - Clases CSS adicionales
 * @param compact - Si true, muestra una versión más compacta
 */
export function ContextualFAQSection({ 
  context, 
  className = '',
  compact = false 
}: ContextualFAQSectionProps) {
  const config = contextConfigs[context]
  
  // Ajustar configuración para modo compacto
  const finalConfig = compact ? {
    ...config,
    initialLimit: Math.min(config.initialLimit, 4),
    showStats: false,
    showCategoryFilter: false
  } : config

  return (
    <div className={`contextual-faq-section ${className}`}>
      <FAQSection
        title={finalConfig.title}
        description={finalConfig.description}
        initialLimit={finalConfig.initialLimit}
        showSearch={finalConfig.showSearch}
        showCategoryFilter={finalConfig.showCategoryFilter}
        showStats={finalConfig.showStats}
        className="mb-0"
      />
      
      {/* Indicador de contexto para desarrollo/debug */}
      {process.env.NODE_ENV === 'development' && (
        <div className="mt-2 text-xs text-gray-400 text-center">
          FAQ Context: {context}
        </div>
      )}
    </div>
  )
}

/**
 * Hook para obtener configuración de contexto FAQ
 */
export function useFAQContext(context: FAQContext) {
  return contextConfigs[context]
}

/**
 * Componente FAQ compacto para sidebars o espacios reducidos
 */
export function CompactFAQSection({ 
  context, 
  className = '' 
}: Omit<ContextualFAQSectionProps, 'compact'>) {
  return (
    <ContextualFAQSection 
      context={context} 
      className={className} 
      compact={true} 
    />
  )
}

/**
 * Componente FAQ flotante para mostrar ayuda contextual
 */
export function FloatingFAQSection({ 
  context, 
  isVisible = true,
  onClose
}: {
  context: FAQContext
  isVisible?: boolean
  onClose?: () => void
}) {
  if (!isVisible) return null

  return (
    <div className="fixed bottom-4 right-4 z-50 max-w-md bg-white rounded-lg shadow-xl border border-gray-200">
      <div className="p-4">
        <div className="flex justify-between items-center mb-3">
          <h3 className="font-semibold text-gray-900">Ayuda Rápida</h3>
          {onClose && (
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
              aria-label="Cerrar ayuda"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          )}
        </div>
        <CompactFAQSection context={context} />
      </div>
    </div>
  )
}

export default ContextualFAQSection
