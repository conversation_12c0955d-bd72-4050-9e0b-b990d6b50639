# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
NEXT_PUBLIC_SUPABASE_PROJECT_ID=your-project-id

# Application Configuration
NEXT_PUBLIC_APP_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_APP_NAME="Sistema de Atención Ciudadana - Chía"
NEXT_PUBLIC_APP_DESCRIPTION="Plataforma digital para la atención ciudadana del municipio de Chía"

# AI Services Configuration
OPENAI_API_KEY=your-openai-api-key
OPENAI_MODEL=gpt-4-turbo-preview
OPENAI_EMBEDDING_MODEL=text-embedding-3-small

# External Integrations
SUIT_API_URL=https://visorsuit.funcionpublica.gov.co
GOV_CO_API_URL=https://www.gov.co
CHIA_GOV_API_URL=https://chia.gov.co

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
FROM_EMAIL=<EMAIL>

# Monitoring and Analytics
SENTRY_DSN=your-sentry-dsn
NEXT_PUBLIC_ANALYTICS_ID=your-analytics-id
NEXT_PUBLIC_HOTJAR_ID=your-hotjar-id

# Security
NEXTAUTH_SECRET=your-nextauth-secret
NEXTAUTH_URL=http://localhost:3000

# File Upload Configuration
MAX_FILE_SIZE=10485760  # 10MB in bytes
ALLOWED_FILE_TYPES=pdf,doc,docx,jpg,jpeg,png,txt

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=900000  # 15 minutes in milliseconds

# Cache Configuration
REDIS_URL=redis://localhost:6379
CACHE_TTL=3600  # 1 hour in seconds

# Development Tools
NEXT_PUBLIC_ENABLE_DEVTOOLS=true
NEXT_PUBLIC_SHOW_DEBUG_INFO=false
