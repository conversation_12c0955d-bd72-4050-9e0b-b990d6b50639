import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { ProcedureSearchInterface } from '../../../../components/procedures/ProcedureSearchInterface'

// Mock data
const mockProcedures = [
  {
    id: '1',
    name: 'Certificado de Residencia',
    description: 'Certificado que acredita la residencia en el municipio',
    cost: 5000,
    response_time: '3 días hábiles',
    category: 'Certificaciones',
    difficulty_level: 1,
    popularity_score: 85,
    requirements: ['Cédula de ciudadanía', 'Recibo de servicios públicos'],
    documents_required: ['Fotocopia de cédula', 'Recibo de servicios'],
    process_steps: ['Presentar documentos', 'Pagar tasa', 'Recibir certificado'],
    online_available: true,
    tags: ['certificado', 'residencia', 'vivienda'],
    dependency: {
      id: 'dep1',
      name: 'Secretaría de Gobierno',
      acronym: 'SEGOB',
      description: 'Dependencia encargada de asuntos gubernamentales',
      contact_email: '<EMAIL>',
      contact_phone: '(*************',
      address: 'Carrera 11 # 17-25'
    }
  },
  {
    id: '2',
    name: 'Licencia de Construcción',
    description: 'Permiso para realizar construcciones en el municipio',
    cost: 150000,
    response_time: '15 días hábiles',
    category: 'Construcción',
    difficulty_level: 3,
    popularity_score: 65,
    requirements: ['Planos arquitectónicos', 'Estudio de suelos'],
    documents_required: ['Planos', 'Estudio técnico'],
    process_steps: ['Radicar solicitud', 'Revisión técnica', 'Aprobación'],
    online_available: false,
    tags: ['construcción', 'licencia', 'obras'],
    dependency: {
      id: 'dep2',
      name: 'Secretaría de Planeación',
      acronym: 'SEPLAN',
      description: 'Dependencia de planeación urbana',
      contact_email: '<EMAIL>',
      contact_phone: '(*************',
      address: 'Carrera 12 # 18-30'
    }
  }
]

const mockDependencies = [
  {
    id: 'dep1',
    name: 'Secretaría de Gobierno',
    acronym: 'SEGOB'
  },
  {
    id: 'dep2',
    name: 'Secretaría de Planeación',
    acronym: 'SEPLAN'
  }
]

const mockCategories = ['Certificaciones', 'Construcción']
const mockUserProcedures = []

describe('ProcedureSearchInterface', () => {
  const defaultProps = {
    procedures: mockProcedures,
    dependencies: mockDependencies,
    categories: mockCategories,
    userProcedures: mockUserProcedures,
    currentUserId: 'user1'
  }

  it('renders search interface correctly', () => {
    render(<ProcedureSearchInterface {...defaultProps} />)
    
    expect(screen.getByText('Buscar Trámites')).toBeInTheDocument()
    expect(screen.getByPlaceholderText(/Buscar por nombre/)).toBeInTheDocument()
    expect(screen.getByText('2 de 2 trámites encontrados')).toBeInTheDocument()
  })

  it('filters procedures by search term', async () => {
    render(<ProcedureSearchInterface {...defaultProps} />)
    
    const searchInput = screen.getByPlaceholderText(/Buscar por nombre/)
    fireEvent.change(searchInput, { target: { value: 'certificado' } })
    
    await waitFor(() => {
      expect(screen.getByText('1 de 2 trámites encontrados')).toBeInTheDocument()
      expect(screen.getByText('Certificado de Residencia')).toBeInTheDocument()
      expect(screen.queryByText('Licencia de Construcción')).not.toBeInTheDocument()
    })
  })

  it('displays procedure details correctly', () => {
    render(<ProcedureSearchInterface {...defaultProps} />)
    
    // Check if procedure information is displayed
    expect(screen.getByText('Certificado de Residencia')).toBeInTheDocument()
    expect(screen.getByText('Secretaría de Gobierno')).toBeInTheDocument()
    expect(screen.getByText('$5.000')).toBeInTheDocument()
    expect(screen.getByText('3 días hábiles')).toBeInTheDocument()
    expect(screen.getByText('Nivel 1')).toBeInTheDocument()
  })

  it('shows no results message when no procedures match', async () => {
    render(<ProcedureSearchInterface {...defaultProps} />)
    
    const searchInput = screen.getByPlaceholderText(/Buscar por nombre/)
    fireEvent.change(searchInput, { target: { value: 'nonexistent' } })
    
    await waitFor(() => {
      expect(screen.getByText('No se encontraron trámites')).toBeInTheDocument()
      expect(screen.getByText('Intenta ajustar tus criterios de búsqueda o filtros')).toBeInTheDocument()
    })
  })

  it('clears filters when clear button is clicked', async () => {
    render(<ProcedureSearchInterface {...defaultProps} />)
    
    // Apply a search filter
    const searchInput = screen.getByPlaceholderText(/Buscar por nombre/)
    fireEvent.change(searchInput, { target: { value: 'certificado' } })
    
    await waitFor(() => {
      expect(screen.getByText('1 de 2 trámites encontrados')).toBeInTheDocument()
    })
    
    // Clear filters
    const clearButton = screen.getByText('Limpiar búsqueda')
    fireEvent.click(clearButton)
    
    await waitFor(() => {
      expect(screen.getByText('2 de 2 trámites encontrados')).toBeInTheDocument()
      expect(searchInput).toHaveValue('')
    })
  })
})
