'use client'

import { useState } from 'react'
import Link from 'next/link'
import { supabase } from '@/lib/supabase/client'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, Mail, ArrowLeft } from 'lucide-react'
import { validateEmail } from '@/lib/utils'

export default function ForgotPasswordPage() {
  const [email, setEmail] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!email || !validateEmail(email)) {
      setError('Por favor ingresa un email válido')
      return
    }

    setIsLoading(true)
    setError(null)
    setSuccess(null)

    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/auth/reset-password`,
      })

      if (error) {
        throw error
      }

      setSuccess(
        'Se ha enviado un enlace de recuperación a tu correo electrónico. Revisa tu bandeja de entrada y sigue las instrucciones.'
      )
    } catch (error: any) {
      console.error('Password reset error:', error)
      
      if (error.message.includes('rate limit')) {
        setError('Has solicitado demasiados resets. Espera unos minutos antes de intentar de nuevo.')
      } else if (error.message.includes('not found')) {
        setError('No se encontró una cuenta con este email.')
      } else {
        setError(error.message || 'Error al enviar el enlace de recuperación. Intenta de nuevo.')
      }
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-gray-900">Recuperar Contraseña</h2>
        <p className="mt-2 text-gray-600">
          Ingresa tu email y te enviaremos un enlace para restablecer tu contraseña
        </p>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert variant="success">
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="email">Correo Electrónico</Label>
          <div className="relative">
            <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              id="email"
              name="email"
              type="email"
              autoComplete="email"
              required
              placeholder="<EMAIL>"
              value={email}
              onChange={(e) => {
                setEmail(e.target.value)
                if (error) setError(null)
              }}
              className="pl-10"
              disabled={isLoading}
            />
          </div>
        </div>

        <Button
          type="submit"
          className="w-full bg-chia-blue-600 hover:bg-chia-blue-700"
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Enviando enlace...
            </>
          ) : (
            'Enviar Enlace de Recuperación'
          )}
        </Button>
      </form>

      <div className="text-center space-y-4">
        <Link
          href="/auth/login"
          className="inline-flex items-center text-sm text-chia-blue-600 hover:text-chia-blue-500 font-medium"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Volver al inicio de sesión
        </Link>
        
        <div className="text-sm text-gray-600">
          ¿No tienes una cuenta?{' '}
          <Link
            href="/auth/register"
            className="text-chia-blue-600 hover:text-chia-blue-500 font-medium"
          >
            Regístrate aquí
          </Link>
        </div>
      </div>

      {/* Instructions */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <h3 className="text-sm font-medium text-blue-900 mb-2">
          Instrucciones importantes:
        </h3>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• Revisa tu bandeja de entrada y carpeta de spam</li>
          <li>• El enlace de recuperación expira en 1 hora</li>
          <li>• Si no recibes el email, verifica que el correo sea correcto</li>
          <li>• Puedes solicitar un nuevo enlace si el anterior expiró</li>
        </ul>
      </div>
    </div>
  )
}
