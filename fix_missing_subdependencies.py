#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Corrector de subdependencias faltantes en FAQs
Identifica y documenta subdependencias que no fueron mapeadas correctamente
"""

import json
import re
from typing import Dict, List, Set

# Mapeo de códigos oficiales de subdependencias
SUBDEP_MAPPINGS = {
    "005": ("000", "Oficina de tecnologías de la información y las comunicaciones TICS"),
    "031": ("030", "Dirección de seguridad y Convivencia Ciudadana"), 
    "032": ("030", "Dirección de Asuntos Étnicos Raciales Religiosos y Posconflicto"),
    "033": ("030", "Dirección de Derechos y Resolución de Conflictos"),
    "041": ("040", "Dirección de Rentas"),
    "042": ("040", "Dirección Financiera"),
    "061": ("060", "Dirección de Ciudadanía Juvenil"),
    "062": ("060", "Dirección de Acción Social"),
    "063": ("060", "Dirección de Cultura"),
    "091": ("090", "Dirección de Desarrollo Agropecuario y Empresarial"),
    "092": ("090", "Dirección de Turismo")
}

def normalize_subdep_name(name: str) -> str:
    """Normaliza nombre de subdependencia para comparación"""
    if not name:
        return ""
    
    name = name.lower()
    # Remover acentos
    replacements = {
        'á': 'a', 'é': 'e', 'í': 'i', 'ó': 'o', 'ú': 'u', 'ñ': 'n'
    }
    for old, new in replacements.items():
        name = name.replace(old, new)
    
    # Limpiar caracteres especiales
    name = re.sub(r'[^\w\s]', ' ', name)
    name = re.sub(r'\s+', ' ', name).strip()
    
    return name

def find_subdep_code(subdep_name: str, dep_code: str) -> str:
    """Encuentra código de subdependencia basado en nombre y dependencia"""
    if not subdep_name:
        return ""
    
    normalized_name = normalize_subdep_name(subdep_name)
    
    # Mapeo directo
    direct_mappings = {
        "oficina de tecnologias de la informacion tic": "005",
        "direccion de seguridad y convivencia ciudadana": "031",
        "direccion asuntos etnicos raciales religiosos y posconflicto": "032", 
        "direccion de derechos y resolucion de conflictos": "033",
        "direccion financiera": "042",
        "direccion de rentas": "041",
        "direccion de accion social": "062",
        "direccion ciudadania juvenil": "061",
        "direccion cultura": "063",
        "direccion de desarrollo agropecuario y empresarial": "091",
        "direccion de turismo": "092"
    }
    
    if normalized_name in direct_mappings:
        return direct_mappings[normalized_name]
    
    # Búsqueda por similitud
    for code, (code_dep, official_name) in SUBDEP_MAPPINGS.items():
        if code_dep == dep_code:
            official_normalized = normalize_subdep_name(official_name)
            if (official_normalized in normalized_name or 
                normalized_name in official_normalized or
                any(word in official_normalized for word in normalized_name.split() if len(word) > 3)):
                return code
    
    return ""

def analyze_missing_subdependencies():
    """Analiza subdependencias faltantes en el archivo FAQ"""
    
    print("🔍 ANÁLISIS DE SUBDEPENDENCIAS FALTANTES")
    print("=" * 50)
    
    # Leer archivo FAQ original extraído
    with open('FAQ_extracted.txt', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # Encontrar todas las subdependencias mencionadas
    subdeps_found = set()
    current_dep = None
    
    for line in lines:
        line = line.strip()
        
        if line.startswith('Dependencia:'):
            current_dep = line.replace('Dependencia:', '').strip()
            
        elif line.startswith('SUBDEPENDENCIA:') or line.startswith('Subdependencia:'):
            subdep_name = line.split(':', 1)[1].strip()
            subdeps_found.add((current_dep, subdep_name))
    
    print(f"📋 Subdependencias encontradas en FAQ original: {len(subdeps_found)}")
    for dep, subdep in sorted(subdeps_found):
        print(f"   {dep} > {subdep}")
    
    # Leer archivo JSON actualizado
    with open('faqs_chia_estructurado_updated.json', 'r', encoding='utf-8') as f:
        json_data = json.load(f)
    
    print(f"\n📋 Subdependencias en archivo JSON: {len([e for e in json_data['faqs'] if e.get('subdependencia')])}")
    
    # Identificar faltantes
    json_subdeps = set()
    for entry in json_data['faqs']:
        if entry.get('subdependencia'):
            json_subdeps.add((entry['dependencia'], entry['subdependencia']))
    
    missing = subdeps_found - json_subdeps
    
    if missing:
        print(f"\n❌ SUBDEPENDENCIAS FALTANTES ({len(missing)}):")
        for dep, subdep in sorted(missing):
            print(f"   {dep} > {subdep}")
    else:
        print(f"\n✅ Todas las subdependencias están incluidas")
    
    return subdeps_found, json_subdeps, missing

def create_inconsistency_report():
    """Crea reporte de inconsistencias encontradas"""
    
    print("\n📊 REPORTE DE INCONSISTENCIAS")
    print("=" * 40)
    
    # Subdependencias que no existen en la base de datos oficial
    non_existent_subdeps = [
        "GESTIÓN DEL RIESGO DE DESASTRES"
    ]
    
    print("❌ SUBDEPENDENCIAS NO EXISTENTES EN BASE DE DATOS:")
    for subdep in non_existent_subdeps:
        print(f"   - {subdep}")
        print(f"     Recomendación: Mapear a dependencia directa o crear nueva subdependencia")
    
    # Verificar mapeos actuales
    with open('faqs_chia_estructurado_updated.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print(f"\n✅ MAPEOS CORRECTOS APLICADOS:")
    for entry in data['faqs']:
        if entry.get('subdependencia') and entry.get('codigo_subdependencia'):
            print(f"   {entry['dependencia']} ({entry['codigo_dependencia']}) > {entry['subdependencia']} ({entry['codigo_subdependencia']})")

def main():
    """Función principal"""
    try:
        # Analizar subdependencias faltantes
        subdeps_found, json_subdeps, missing = analyze_missing_subdependencies()
        
        # Crear reporte de inconsistencias
        create_inconsistency_report()
        
        print(f"\n📈 RESUMEN:")
        print(f"   - Subdependencias en FAQ original: {len(subdeps_found)}")
        print(f"   - Subdependencias en JSON: {len(json_subdeps)}")
        print(f"   - Subdependencias faltantes: {len(missing)}")
        
        if missing:
            print(f"\n⚠️  ACCIÓN REQUERIDA:")
            print(f"   Algunas subdependencias del FAQ original no fueron procesadas.")
            print(f"   Revisar el procesamiento original para incluir todas las subdependencias.")
        else:
            print(f"\n✅ ESTADO: Todas las subdependencias están correctamente mapeadas")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        raise

if __name__ == "__main__":
    main()
