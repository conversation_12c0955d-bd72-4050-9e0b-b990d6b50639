import { renderHook, act, waitFor } from '@testing-library/react'
import { useAuth } from '@/hooks/useAuth'
import { createClient } from '@/lib/supabase/client'
import { useRouter } from 'next/navigation'

// Mock dependencies
jest.mock('@/lib/supabase/client')
jest.mock('next/navigation')

const mockSupabase = {
  auth: {
    getUser: jest.fn(),
    signInWithPassword: jest.fn(),
    signUp: jest.fn(),
    signOut: jest.fn(),
    resetPasswordForEmail: jest.fn(),
    updateUser: jest.fn(),
    onAuthStateChange: jest.fn(),
  },
  from: jest.fn(() => ({
    select: jest.fn(() => ({
      eq: jest.fn(() => ({
        single: jest.fn(),
      })),
    })),
  })),
}

const mockRouter = {
  push: jest.fn(),
  replace: jest.fn(),
  refresh: jest.fn(),
}

describe('useAuth Hook', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    ;(createClient as jest.Mock).mockReturnValue(mockSupabase)
    ;(useRouter as jest.Mock).mockReturnValue(mockRouter)
    
    // Mock successful auth state change subscription
    mockSupabase.auth.onAuthStateChange.mockReturnValue({
      data: { subscription: { unsubscribe: jest.fn() } },
    })
  })

  describe('Initial State', () => {
    it('should initialize with loading state', () => {
      mockSupabase.auth.getUser.mockResolvedValue({ data: { user: null }, error: null })
      
      const { result } = renderHook(() => useAuth())
      
      expect(result.current.loading).toBe(true)
      expect(result.current.user).toBe(null)
      expect(result.current.profile).toBe(null)
    })

    it('should load user and profile on mount', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        email_confirmed_at: '2023-01-01T00:00:00Z',
      }
      
      const mockProfile = {
        id: 'user-123',
        full_name: 'Test User',
        role: 'ciudadano',
      }

      mockSupabase.auth.getUser.mockResolvedValue({ 
        data: { user: mockUser }, 
        error: null 
      })
      
      mockSupabase.from().select().eq().single.mockResolvedValue({
        data: mockProfile,
        error: null,
      })

      const { result } = renderHook(() => useAuth())

      await waitFor(() => {
        expect(result.current.loading).toBe(false)
      })

      expect(result.current.user).toEqual(mockUser)
      expect(result.current.profile).toEqual(mockProfile)
    })
  })

  describe('Authentication Methods', () => {
    it('should handle successful login', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
      }

      mockSupabase.auth.signInWithPassword.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      })

      mockSupabase.auth.getUser.mockResolvedValue({ 
        data: { user: null }, 
        error: null 
      })

      const { result } = renderHook(() => useAuth())

      await act(async () => {
        const response = await result.current.signIn('<EMAIL>', 'password123')
        expect(response.success).toBe(true)
      })

      expect(mockSupabase.auth.signInWithPassword).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123',
      })
    })

    it('should handle login error', async () => {
      const mockError = { message: 'Invalid credentials' }

      mockSupabase.auth.signInWithPassword.mockResolvedValue({
        data: { user: null },
        error: mockError,
      })

      mockSupabase.auth.getUser.mockResolvedValue({ 
        data: { user: null }, 
        error: null 
      })

      const { result } = renderHook(() => useAuth())

      await act(async () => {
        const response = await result.current.signIn('<EMAIL>', 'wrongpassword')
        expect(response.success).toBe(false)
        expect(response.error).toBe('Invalid credentials')
      })
    })

    it('should handle successful signup', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
      }

      mockSupabase.auth.signUp.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      })

      mockSupabase.auth.getUser.mockResolvedValue({ 
        data: { user: null }, 
        error: null 
      })

      const { result } = renderHook(() => useAuth())

      await act(async () => {
        const response = await result.current.signUp('<EMAIL>', 'password123')
        expect(response.success).toBe(true)
      })

      expect(mockSupabase.auth.signUp).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123',
      })
    })

    it('should handle successful logout', async () => {
      mockSupabase.auth.signOut.mockResolvedValue({ error: null })
      mockSupabase.auth.getUser.mockResolvedValue({ 
        data: { user: null }, 
        error: null 
      })

      const { result } = renderHook(() => useAuth())

      await act(async () => {
        await result.current.signOut()
      })

      expect(mockSupabase.auth.signOut).toHaveBeenCalled()
      expect(mockRouter.push).toHaveBeenCalledWith('/auth/login')
    })

    it('should handle password reset', async () => {
      mockSupabase.auth.resetPasswordForEmail.mockResolvedValue({ error: null })
      mockSupabase.auth.getUser.mockResolvedValue({ 
        data: { user: null }, 
        error: null 
      })

      const { result } = renderHook(() => useAuth())

      await act(async () => {
        const response = await result.current.resetPassword('<EMAIL>')
        expect(response.success).toBe(true)
      })

      expect(mockSupabase.auth.resetPasswordForEmail).toHaveBeenCalledWith(
        '<EMAIL>',
        { redirectTo: expect.stringContaining('/auth/reset-password') }
      )
    })
  })

  describe('Profile Management', () => {
    it('should update user profile', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
      }

      const updatedProfile = {
        full_name: 'Updated Name',
        phone: '123456789',
      }

      mockSupabase.auth.getUser.mockResolvedValue({ 
        data: { user: mockUser }, 
        error: null 
      })

      mockSupabase.from().select().eq().single
        .mockResolvedValueOnce({ data: null, error: null })
        .mockResolvedValueOnce({ data: updatedProfile, error: null })

      const { result } = renderHook(() => useAuth())

      await waitFor(() => {
        expect(result.current.loading).toBe(false)
      })

      await act(async () => {
        const response = await result.current.updateProfile(updatedProfile)
        expect(response.success).toBe(true)
      })
    })
  })

  describe('Error Handling', () => {
    it('should handle network errors gracefully', async () => {
      mockSupabase.auth.getUser.mockRejectedValue(new Error('Network error'))

      const { result } = renderHook(() => useAuth())

      await waitFor(() => {
        expect(result.current.loading).toBe(false)
      })

      expect(result.current.user).toBe(null)
      expect(result.current.profile).toBe(null)
    })

    it('should handle auth state change errors', async () => {
      mockSupabase.auth.onAuthStateChange.mockReturnValue({
        data: { subscription: { unsubscribe: jest.fn() } },
      })

      mockSupabase.auth.getUser.mockResolvedValue({ 
        data: { user: null }, 
        error: null 
      })

      const { result } = renderHook(() => useAuth())

      // Simulate auth state change with error
      const authStateChangeCallback = mockSupabase.auth.onAuthStateChange.mock.calls[0][0]
      
      await act(async () => {
        authStateChangeCallback('SIGNED_IN', null)
      })

      expect(result.current.user).toBe(null)
    })
  })
})
