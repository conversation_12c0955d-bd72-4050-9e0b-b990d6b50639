'use client'

import Image from 'next/image'
import { useState, useRef, useEffect } from 'react'
import { cn } from '@/lib/utils'
import { useIntersectionObserver } from '@/lib/performance'
import { useDeviceDetection, getMobileImageSizes } from '@/lib/mobile'
import { Skeleton } from '@/components/ui/loading'

interface OptimizedImageProps {
  src: string
  alt: string
  width?: number
  height?: number
  className?: string
  priority?: boolean
  quality?: number
  placeholder?: 'blur' | 'empty'
  blurDataURL?: string
  sizes?: string
  fill?: boolean
  objectFit?: 'contain' | 'cover' | 'fill' | 'none' | 'scale-down'
  objectPosition?: string
  loading?: 'lazy' | 'eager'
  onLoad?: () => void
  onError?: () => void
  fallbackSrc?: string
  showSkeleton?: boolean
  aspectRatio?: number
  responsive?: boolean
}

export function OptimizedImage({
  src,
  alt,
  width,
  height,
  className,
  priority = false,
  quality = 75,
  placeholder = 'empty',
  blurDataURL,
  sizes,
  fill = false,
  objectFit = 'cover',
  objectPosition = 'center',
  loading = 'lazy',
  onLoad,
  onError,
  fallbackSrc,
  showSkeleton = true,
  aspectRatio,
  responsive = true,
  ...props
}: OptimizedImageProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)
  const [currentSrc, setCurrentSrc] = useState(src)
  const imageRef = useRef<HTMLDivElement>(null)
  const { screenSize, isMobile } = useDeviceDetection()
  
  // Intersection observer for lazy loading
  const isInView = useIntersectionObserver(imageRef, {
    threshold: 0.1,
    rootMargin: '50px'
  })

  // Auto-generate responsive sizes if not provided
  const responsiveSizes = sizes || (responsive ? getMobileImageSizes(screenSize) : undefined)

  // Optimize quality for mobile devices
  const optimizedQuality = isMobile && quality > 60 ? 60 : quality

  // Handle image load
  const handleLoad = () => {
    setIsLoading(false)
    onLoad?.()
  }

  // Handle image error
  const handleError = () => {
    setHasError(true)
    setIsLoading(false)
    
    if (fallbackSrc && currentSrc !== fallbackSrc) {
      setCurrentSrc(fallbackSrc)
      setHasError(false)
      setIsLoading(true)
    }
    
    onError?.()
  }

  // Generate blur placeholder for better UX
  const generateBlurDataURL = (width: number, height: number) => {
    if (blurDataURL) return blurDataURL
    
    // Generate a simple blur placeholder
    const canvas = document.createElement('canvas')
    canvas.width = width
    canvas.height = height
    const ctx = canvas.getContext('2d')
    
    if (ctx) {
      // Create a simple gradient
      const gradient = ctx.createLinearGradient(0, 0, width, height)
      gradient.addColorStop(0, '#f3f4f6')
      gradient.addColorStop(1, '#e5e7eb')
      ctx.fillStyle = gradient
      ctx.fillRect(0, 0, width, height)
    }
    
    return canvas.toDataURL()
  }

  // Calculate dimensions with aspect ratio
  const calculateDimensions = () => {
    if (fill) return { fill: true }
    
    if (aspectRatio && width && !height) {
      return { width, height: Math.round(width / aspectRatio) }
    }
    
    if (aspectRatio && height && !width) {
      return { width: Math.round(height * aspectRatio), height }
    }
    
    return { width, height }
  }

  const dimensions = calculateDimensions()

  // Error fallback
  if (hasError && !fallbackSrc) {
    return (
      <div
        ref={imageRef}
        className={cn(
          'flex items-center justify-center bg-gray-100 text-gray-400 text-sm',
          className
        )}
        style={{
          width: dimensions.width,
          height: dimensions.height,
          aspectRatio: aspectRatio ? `${aspectRatio}` : undefined
        }}
      >
        <div className="text-center p-4">
          <svg
            className="w-8 h-8 mx-auto mb-2 text-gray-300"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
            />
          </svg>
          <p>Imagen no disponible</p>
        </div>
      </div>
    )
  }

  // Loading skeleton
  if (showSkeleton && isLoading && (!isInView && loading === 'lazy')) {
    return (
      <div ref={imageRef}>
        <Skeleton
          className={cn('rounded-md', className)}
          style={{
            width: dimensions.width,
            height: dimensions.height,
            aspectRatio: aspectRatio ? `${aspectRatio}` : undefined
          }}
        />
      </div>
    )
  }

  // Don't render image until it's in view (for lazy loading)
  if (loading === 'lazy' && !isInView && !priority) {
    return (
      <div
        ref={imageRef}
        className={cn('bg-gray-100', className)}
        style={{
          width: dimensions.width,
          height: dimensions.height,
          aspectRatio: aspectRatio ? `${aspectRatio}` : undefined
        }}
      />
    )
  }

  return (
    <div
      ref={imageRef}
      className={cn('relative overflow-hidden', className)}
      style={{
        width: fill ? '100%' : dimensions.width,
        height: fill ? '100%' : dimensions.height,
        aspectRatio: aspectRatio ? `${aspectRatio}` : undefined
      }}
    >
      {/* Loading overlay */}
      {isLoading && showSkeleton && (
        <div className="absolute inset-0 z-10">
          <Skeleton className="w-full h-full" />
        </div>
      )}

      <Image
        src={currentSrc}
        alt={alt}
        {...dimensions}
        className={cn(
          'transition-opacity duration-300',
          isLoading ? 'opacity-0' : 'opacity-100',
          objectFit && `object-${objectFit}`,
          objectPosition && `object-${objectPosition}`
        )}
        priority={priority}
        quality={optimizedQuality}
        placeholder={placeholder}
        blurDataURL={
          placeholder === 'blur' 
            ? blurDataURL || (width && height ? generateBlurDataURL(width, height) : undefined)
            : undefined
        }
        sizes={responsiveSizes}
        loading={loading}
        onLoad={handleLoad}
        onError={handleError}
        {...props}
      />
    </div>
  )
}

// Specialized components for common use cases
export function AvatarImage({
  src,
  alt,
  size = 40,
  className,
  fallbackInitials,
  ...props
}: Omit<OptimizedImageProps, 'width' | 'height'> & {
  size?: number
  fallbackInitials?: string
}) {
  const [hasError, setHasError] = useState(false)

  if (hasError && fallbackInitials) {
    return (
      <div
        className={cn(
          'flex items-center justify-center bg-blue-600 text-white font-medium rounded-full',
          className
        )}
        style={{ width: size, height: size }}
      >
        {fallbackInitials}
      </div>
    )
  }

  return (
    <OptimizedImage
      src={src}
      alt={alt}
      width={size}
      height={size}
      className={cn('rounded-full', className)}
      objectFit="cover"
      onError={() => setHasError(true)}
      {...props}
    />
  )
}

export function HeroImage({
  src,
  alt,
  className,
  ...props
}: OptimizedImageProps) {
  return (
    <OptimizedImage
      src={src}
      alt={alt}
      fill
      className={cn('object-cover', className)}
      priority
      quality={85}
      sizes="100vw"
      {...props}
    />
  )
}

export function ThumbnailImage({
  src,
  alt,
  size = 120,
  className,
  ...props
}: Omit<OptimizedImageProps, 'width' | 'height'> & {
  size?: number
}) {
  return (
    <OptimizedImage
      src={src}
      alt={alt}
      width={size}
      height={size}
      className={cn('rounded-lg', className)}
      objectFit="cover"
      quality={60}
      {...props}
    />
  )
}

export function DocumentPreview({
  src,
  alt,
  className,
  ...props
}: OptimizedImageProps) {
  return (
    <OptimizedImage
      src={src}
      alt={alt}
      width={200}
      height={280}
      className={cn('border border-gray-200 rounded-lg shadow-sm', className)}
      objectFit="contain"
      quality={70}
      {...props}
    />
  )
}
