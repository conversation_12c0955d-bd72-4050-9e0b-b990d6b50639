/**
 * Script de prueba para el scraper SUIT
 * Valida el funcionamiento con una muestra pequeña antes del scraping completo
 */

import { SuitProductionScraper } from './suit-production-scraper'
import { createClient } from '@supabase/supabase-js'
import * as dotenv from 'dotenv'
import * as path from 'path'

// Cargar variables de entorno desde .env.local
dotenv.config({ path: path.join(__dirname, '..', '.env.local') })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const supabase = createClient(supabaseUrl, supabaseKey)

async function testSuitScraper() {
  console.log('🧪 INICIANDO PRUEBA DEL SCRAPER SUIT')
  console.log('=' .repeat(50))

  try {
    // Configuración para pruebas (más verbose)
    const testConfig = {
      headless: false,        // Visible para debugging
      rateLimit: 3000,        // 3 segundos para pruebas más rápidas
      batchSize: 3,           // Solo 3 procedimientos para prueba
      maxRetries: 2,          // Menos reintentos para pruebas
      timeout: 20000          // Timeout más corto
    }

    console.log('📋 Configuración de prueba:', testConfig)

    // Obtener algunos procedimientos para prueba
    console.log('\n📊 Obteniendo procedimientos de prueba...')
    
    const { data: testProcedures, error } = await supabase
      .rpc('get_procedures_needing_scraping', { limit_count: 3 })

    if (error) {
      console.error('❌ Error obteniendo procedimientos:', error)
      return
    }

    if (!testProcedures || testProcedures.length === 0) {
      console.log('⚠️ No hay procedimientos pendientes de scraping')
      return
    }

    console.log(`✅ Obtenidos ${testProcedures.length} procedimientos para prueba:`)
    testProcedures.forEach((proc, index) => {
      console.log(`   ${index + 1}. ${proc.procedure_name} (Ficha: ${proc.ficha_id})`)
    })

    // Crear instancia del scraper
    console.log('\n🚀 Inicializando scraper...')
    const scraper = new SuitProductionScraper(testConfig)

    try {
      await scraper.initialize()
      console.log('✅ Scraper inicializado correctamente')

      // Procesar cada procedimiento individualmente para análisis detallado
      console.log('\n📋 PROCESANDO PROCEDIMIENTOS DE PRUEBA')
      console.log('-' .repeat(40))

      for (let i = 0; i < testProcedures.length; i++) {
        const proc = testProcedures[i]
        console.log(`\n🔍 Procesando ${i + 1}/${testProcedures.length}: ${proc.procedure_name}`)
        console.log(`   Ficha ID: ${proc.ficha_id}`)
        console.log(`   URL: https://visorsuit.funcionpublica.gov.co/auth/visor?fi=${proc.ficha_id}`)

        const startTime = Date.now()
        
        try {
          const result = await scraper.scrapeSuitPage(proc.ficha_id, proc.procedure_id)
          const processingTime = Date.now() - startTime

          console.log(`   ⏱️ Tiempo de procesamiento: ${processingTime}ms`)
          console.log(`   📊 Resultado: ${result.success ? '✅ Éxito' : '❌ Fallo'}`)

          if (result.success && result.data) {
            console.log(`   📝 Título extraído: ${result.data.titulo?.substring(0, 80) || 'N/A'}...`)
            console.log(`   📄 Descripción: ${result.data.descripcionDetallada?.length || 0} caracteres`)
            console.log(`   📋 Requisitos: ${result.data.requisitos?.length || 0} elementos`)
            console.log(`   🔢 Pasos: ${result.data.pasos?.length || 0} elementos`)
            
            if (result.data.entidadResponsable) {
              console.log(`   🏛️ Entidad: ${result.data.entidadResponsable.substring(0, 50)}...`)
            }
            if (result.data.tiempoRespuesta) {
              console.log(`   ⏰ Tiempo respuesta: ${result.data.tiempoRespuesta}`)
            }
            if (result.data.costoDetallado) {
              console.log(`   💰 Costo: ${result.data.costoDetallado}`)
            }
          } else {
            console.log(`   ❌ Error: ${result.error}`)
          }

          // Guardar resultado en la base de datos
          await scraper.saveScrapingResult(result)
          console.log(`   💾 Resultado guardado en base de datos`)

          // Pausa entre procedimientos
          if (i < testProcedures.length - 1) {
            console.log(`   ⏸️ Pausa de ${testConfig.rateLimit}ms...`)
            await new Promise(resolve => setTimeout(resolve, testConfig.rateLimit))
          }

        } catch (error) {
          console.log(`   💥 Error procesando: ${error}`)
        }
      }

      // Verificar resultados en la base de datos
      console.log('\n📊 VERIFICANDO RESULTADOS EN BASE DE DATOS')
      console.log('-' .repeat(40))

      const { data: stats, error: statsError } = await supabase
        .from('vista_suit_scraping_stats')
        .select('*')
        .single()

      if (statsError) {
        console.error('❌ Error obteniendo estadísticas:', statsError)
      } else {
        console.log('📈 Estadísticas actualizadas:')
        console.log(`   Total procedimientos: ${stats.total_procedures_with_suit}`)
        console.log(`   Exitosos: ${stats.successfully_scraped}`)
        console.log(`   Fallidos: ${stats.failed_scraping}`)
        console.log(`   Pendientes: ${stats.pending_scraping}`)
        console.log(`   Tasa de éxito: ${stats.success_rate_percent}%`)
        
        if (stats.avg_description_length) {
          console.log(`   Longitud promedio descripción: ${Math.round(stats.avg_description_length)} caracteres`)
        }
      }

      // Mostrar algunos ejemplos de datos extraídos
      const { data: examples, error: examplesError } = await supabase
        .from('suit_scraped_data')
        .select('ficha_id, titulo, descripcion_detallada, requisitos, pasos')
        .eq('scraping_status', 'success')
        .not('descripcion_detallada', 'is', null)
        .order('scraped_at', { ascending: false })
        .limit(2)

      if (!examplesError && examples && examples.length > 0) {
        console.log('\n📝 EJEMPLOS DE DATOS EXTRAÍDOS:')
        console.log('-' .repeat(40))
        
        examples.forEach((example, index) => {
          console.log(`\n${index + 1}. Ficha ${example.ficha_id}:`)
          console.log(`   Título: ${example.titulo || 'N/A'}`)
          console.log(`   Descripción: ${example.descripcion_detallada?.substring(0, 150) || 'N/A'}...`)
          console.log(`   Requisitos: ${Array.isArray(example.requisitos) ? example.requisitos.length : 0} elementos`)
          console.log(`   Pasos: ${Array.isArray(example.pasos) ? example.pasos.length : 0} elementos`)
        })
      }

    } finally {
      await scraper.cleanup()
      console.log('\n🧹 Scraper cerrado correctamente')
    }

    console.log('\n🎉 PRUEBA COMPLETADA EXITOSAMENTE')
    console.log('=' .repeat(50))
    console.log('✅ El scraper está funcionando correctamente')
    console.log('🚀 Listo para ejecutar el scraping completo')

  } catch (error) {
    console.error('\n💥 ERROR EN LA PRUEBA:', error)
    console.log('❌ Revisar configuración y dependencias antes de continuar')
  }
}

// Función para limpiar datos de prueba si es necesario
async function cleanupTestData() {
  console.log('🧹 Limpiando datos de prueba...')
  
  try {
    // Resetear solo los registros que se procesaron en la prueba
    const { error } = await supabase
      .from('suit_scraped_data')
      .update({
        scraping_status: 'pending',
        titulo: null,
        descripcion_detallada: null,
        requisitos: [],
        pasos: [],
        documentos_necesarios: [],
        entidad_responsable: null,
        tiempo_respuesta: null,
        costo_detallado: null,
        base_juridica: null,
        raw_html: null,
        raw_text: null,
        error_message: null
      })
      .in('scraping_status', ['success', 'failed'])
      .gte('scraped_at', new Date(Date.now() - 10 * 60 * 1000).toISOString()) // Últimos 10 minutos

    if (error) {
      console.error('❌ Error limpiando datos:', error)
    } else {
      console.log('✅ Datos de prueba limpiados')
    }
  } catch (error) {
    console.error('💥 Error en cleanup:', error)
  }
}

// Ejecutar según argumentos de línea de comandos
if (require.main === module) {
  const args = process.argv.slice(2)
  
  if (args.includes('--cleanup')) {
    cleanupTestData()
      .then(() => process.exit(0))
      .catch(() => process.exit(1))
  } else {
    testSuitScraper()
      .then(() => process.exit(0))
      .catch(() => process.exit(1))
  }
}

export { testSuitScraper, cleanupTestData }
