"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/consulta-tramites/page",{

/***/ "(app-pages-browser)/./components/procedures/PublicProcedureDetailModal.tsx":
/*!**************************************************************!*\
  !*** ./components/procedures/PublicProcedureDetailModal.tsx ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PublicProcedureDetailModal: function() { return /* binding */ PublicProcedureDetailModal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Building2_CheckCircle_Clock_DollarSign_ExternalLink_FileText_Info_Mail_MapPin_Phone_Sparkles_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,CheckCircle,Clock,DollarSign,ExternalLink,FileText,Info,Mail,MapPin,Phone,Sparkles,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_CheckCircle_Clock_DollarSign_ExternalLink_FileText_Info_Mail_MapPin_Phone_Sparkles_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,CheckCircle,Clock,DollarSign,ExternalLink,FileText,Info,Mail,MapPin,Phone,Sparkles,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_CheckCircle_Clock_DollarSign_ExternalLink_FileText_Info_Mail_MapPin_Phone_Sparkles_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,CheckCircle,Clock,DollarSign,ExternalLink,FileText,Info,Mail,MapPin,Phone,Sparkles,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_CheckCircle_Clock_DollarSign_ExternalLink_FileText_Info_Mail_MapPin_Phone_Sparkles_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,CheckCircle,Clock,DollarSign,ExternalLink,FileText,Info,Mail,MapPin,Phone,Sparkles,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_CheckCircle_Clock_DollarSign_ExternalLink_FileText_Info_Mail_MapPin_Phone_Sparkles_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,CheckCircle,Clock,DollarSign,ExternalLink,FileText,Info,Mail,MapPin,Phone,Sparkles,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_CheckCircle_Clock_DollarSign_ExternalLink_FileText_Info_Mail_MapPin_Phone_Sparkles_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,CheckCircle,Clock,DollarSign,ExternalLink,FileText,Info,Mail,MapPin,Phone,Sparkles,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_CheckCircle_Clock_DollarSign_ExternalLink_FileText_Info_Mail_MapPin_Phone_Sparkles_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,CheckCircle,Clock,DollarSign,ExternalLink,FileText,Info,Mail,MapPin,Phone,Sparkles,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_CheckCircle_Clock_DollarSign_ExternalLink_FileText_Info_Mail_MapPin_Phone_Sparkles_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,CheckCircle,Clock,DollarSign,ExternalLink,FileText,Info,Mail,MapPin,Phone,Sparkles,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_CheckCircle_Clock_DollarSign_ExternalLink_FileText_Info_Mail_MapPin_Phone_Sparkles_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,CheckCircle,Clock,DollarSign,ExternalLink,FileText,Info,Mail,MapPin,Phone,Sparkles,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_CheckCircle_Clock_DollarSign_ExternalLink_FileText_Info_Mail_MapPin_Phone_Sparkles_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,CheckCircle,Clock,DollarSign,ExternalLink,FileText,Info,Mail,MapPin,Phone,Sparkles,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_CheckCircle_Clock_DollarSign_ExternalLink_FileText_Info_Mail_MapPin_Phone_Sparkles_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,CheckCircle,Clock,DollarSign,ExternalLink,FileText,Info,Mail,MapPin,Phone,Sparkles,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_CheckCircle_Clock_DollarSign_ExternalLink_FileText_Info_Mail_MapPin_Phone_Sparkles_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,CheckCircle,Clock,DollarSign,ExternalLink,FileText,Info,Mail,MapPin,Phone,Sparkles,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_CheckCircle_Clock_DollarSign_ExternalLink_FileText_Info_Mail_MapPin_Phone_Sparkles_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,CheckCircle,Clock,DollarSign,ExternalLink,FileText,Info,Mail,MapPin,Phone,Sparkles,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _types_suit_enhanced_procedure__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/types/suit-enhanced-procedure */ \"(app-pages-browser)/./types/suit-enhanced-procedure.ts\");\n/* __next_internal_client_entry_do_not_use__ PublicProcedureDetailModal auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction PublicProcedureDetailModal(param) {\n    let { procedure, onClose } = param;\n    var _procedure_suit_data, _procedure_suit_data1, _procedure_suit_data2, _procedure_suit_data3, _procedure_suit_data4, _procedure_suit_data5, _procedure_suit_data6, _procedure_suit_data7, _procedure_suit_data8, _procedure_suit_data9, _procedure_suit_data10, _procedure_suit_data11;\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"overview\");\n    // Obtener estadísticas de mejora SUIT\n    const suitStats = (0,_types_suit_enhanced_procedure__WEBPACK_IMPORTED_MODULE_6__.getSuitEnhancementStats)(procedure);\n    const getDifficultyLabel = (level)=>{\n        if (!level) return \"No especificado\";\n        if (level <= 2) return \"B\\xe1sico\";\n        if (level <= 4) return \"Intermedio\";\n        return \"Avanzado\";\n    };\n    const getDifficultyColor = (level)=>{\n        if (!level) return \"bg-gray-100 text-gray-800\";\n        if (level <= 2) return \"bg-green-100 text-green-800\";\n        if (level <= 4) return \"bg-yellow-100 text-yellow-800\";\n        return \"bg-red-100 text-red-800\";\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-start p-6 border-b\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                (procedure.codigo_tramite || procedure.code) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                        variant: \"outline\",\n                                        className: \"text-sm font-mono bg-primary/5 text-primary border-primary/20\",\n                                        children: [\n                                            \"C\\xf3digo: \",\n                                            procedure.codigo_tramite || procedure.code\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3 mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: procedure.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            className: \"bg-primary/10 text-primary\",\n                                            children: \"Informaci\\xf3n\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, this),\n                                procedure.dependency && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center text-gray-600 mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_CheckCircle_Clock_DollarSign_ExternalLink_FileText_Info_Mail_MapPin_Phone_Sparkles_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: procedure.dependency.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 17\n                                        }, this),\n                                        procedure.dependency.acronym && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2 text-sm\",\n                                            children: [\n                                                \"(\",\n                                                procedure.dependency.acronym,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4 text-sm text-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_CheckCircle_Clock_DollarSign_ExternalLink_FileText_Info_Mail_MapPin_Phone_Sparkles_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                    lineNumber: 87,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: (0,_types_suit_enhanced_procedure__WEBPACK_IMPORTED_MODULE_6__.formatEnhancedCost)(procedure)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                    lineNumber: 88,\n                                                    columnNumber: 17\n                                                }, this),\n                                                ((_procedure_suit_data = procedure.suit_data) === null || _procedure_suit_data === void 0 ? void 0 : _procedure_suit_data.costo_detallado) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_CheckCircle_Clock_DollarSign_ExternalLink_FileText_Info_Mail_MapPin_Phone_Sparkles_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-3 w-3 ml-1 text-amber-500\",\n                                                    title: \"Informaci\\xf3n de costo mejorada con SUIT\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                    lineNumber: 90,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 15\n                                        }, this),\n                                        procedure.best_response_time && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_CheckCircle_Clock_DollarSign_ExternalLink_FileText_Info_Mail_MapPin_Phone_Sparkles_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                    lineNumber: 95,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: (0,_types_suit_enhanced_procedure__WEBPACK_IMPORTED_MODULE_6__.formatEnhancedResponseTime)(procedure)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                    lineNumber: 96,\n                                                    columnNumber: 19\n                                                }, this),\n                                                ((_procedure_suit_data1 = procedure.suit_data) === null || _procedure_suit_data1 === void 0 ? void 0 : _procedure_suit_data1.tiempo_respuesta) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_CheckCircle_Clock_DollarSign_ExternalLink_FileText_Info_Mail_MapPin_Phone_Sparkles_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-3 w-3 ml-1 text-amber-500\",\n                                                    title: \"Tiempo de respuesta mejorado con SUIT\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 17\n                                        }, this),\n                                        procedure.difficulty_level && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            className: getDifficultyColor(procedure.difficulty_level),\n                                            children: getDifficultyLabel(procedure.difficulty_level)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            size: \"sm\",\n                            onClick: onClose,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_CheckCircle_Clock_DollarSign_ExternalLink_FileText_Info_Mail_MapPin_Phone_Sparkles_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 overflow-y-auto max-h-[calc(90vh-140px)]\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.Tabs, {\n                        value: activeTab,\n                        onValueChange: (value)=>setActiveTab(value),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsList, {\n                                className: \"grid w-full grid-cols-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                        value: \"overview\",\n                                        children: \"Informaci\\xf3n General\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                        value: \"requirements\",\n                                        children: \"Requisitos\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                        value: \"process\",\n                                        children: \"Proceso\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                        value: \"contact\",\n                                        children: \"Contacto\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                                value: \"overview\",\n                                className: \"space-y-6\",\n                                children: [\n                                    suitStats.hasEnhancement && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                        className: \"border-amber-200 bg-amber-50\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            className: \"p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_CheckCircle_Clock_DollarSign_ExternalLink_FileText_Info_Mail_MapPin_Phone_Sparkles_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-5 w-5 text-amber-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                        lineNumber: 131,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-amber-800\",\n                                                                children: \"Informaci\\xf3n Enriquecida con SUIT\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                lineNumber: 133,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-amber-700\",\n                                                                children: [\n                                                                    \"Este procedimiento tiene \",\n                                                                    suitStats.enhancementCount,\n                                                                    \" mejoras: \",\n                                                                    suitStats.improvements.join(\", \")\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                lineNumber: 134,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                        lineNumber: 132,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 17\n                                    }, this),\n                                    procedure.best_description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    className: \"text-lg flex items-center gap-2\",\n                                                    children: [\n                                                        \"Descripci\\xf3n\",\n                                                        ((_procedure_suit_data2 = procedure.suit_data) === null || _procedure_suit_data2 === void 0 ? void 0 : _procedure_suit_data2.descripcion_detallada) && procedure.best_description === procedure.suit_data.descripcion_detallada && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_CheckCircle_Clock_DollarSign_ExternalLink_FileText_Info_Mail_MapPin_Phone_Sparkles_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-4 w-4 text-amber-500\",\n                                                            title: \"Descripci\\xf3n mejorada con SUIT\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                            lineNumber: 151,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                    lineNumber: 147,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-700\",\n                                                    children: procedure.best_description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                    className: \"p-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-2 bg-primary/10 rounded-lg\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_CheckCircle_Clock_DollarSign_ExternalLink_FileText_Info_Mail_MapPin_Phone_Sparkles_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-primary\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                    lineNumber: 167,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                lineNumber: 166,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: \"Costo\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                        lineNumber: 170,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-semibold\",\n                                                                        children: (0,_types_suit_enhanced_procedure__WEBPACK_IMPORTED_MODULE_6__.formatEnhancedCost)(procedure)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                        lineNumber: 171,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                lineNumber: 169,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 17\n                                            }, this),\n                                            procedure.best_response_time && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                    className: \"p-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-2 bg-chia-green-100 rounded-lg\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_CheckCircle_Clock_DollarSign_ExternalLink_FileText_Info_Mail_MapPin_Phone_Sparkles_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-chia-green-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                    lineNumber: 182,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                lineNumber: 181,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600 flex items-center gap-1\",\n                                                                        children: [\n                                                                            \"Tiempo de Respuesta\",\n                                                                            ((_procedure_suit_data3 = procedure.suit_data) === null || _procedure_suit_data3 === void 0 ? void 0 : _procedure_suit_data3.tiempo_respuesta) && procedure.best_response_time === procedure.suit_data.tiempo_respuesta && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_CheckCircle_Clock_DollarSign_ExternalLink_FileText_Info_Mail_MapPin_Phone_Sparkles_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                                className: \"h-3 w-3 text-amber-500\",\n                                                                                title: \"Tiempo mejorado con SUIT\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                                lineNumber: 189,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                        lineNumber: 185,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-semibold\",\n                                                                        children: procedure.best_response_time\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                        lineNumber: 192,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                lineNumber: 184,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 19\n                                            }, this),\n                                            procedure.category && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                    className: \"p-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-2 bg-purple-100 rounded-lg\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_CheckCircle_Clock_DollarSign_ExternalLink_FileText_Info_Mail_MapPin_Phone_Sparkles_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-purple-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                    lineNumber: 204,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                lineNumber: 203,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: \"Categor\\xeda\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                        lineNumber: 207,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-semibold\",\n                                                                        children: procedure.category\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                        lineNumber: 208,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                lineNumber: 206,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                        lineNumber: 202,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                    lineNumber: 201,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 19\n                                            }, this),\n                                            procedure.best_modality && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                    className: \"p-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-2 bg-indigo-100 rounded-lg\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_CheckCircle_Clock_DollarSign_ExternalLink_FileText_Info_Mail_MapPin_Phone_Sparkles_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-indigo-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                    lineNumber: 220,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                lineNumber: 219,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: \"Modalidad\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                        lineNumber: 223,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-semibold\",\n                                                                        children: procedure.best_modality\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                        lineNumber: 224,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                lineNumber: 222,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, this),\n                                    procedure.tags && procedure.tags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    className: \"text-lg\",\n                                                    children: \"Etiquetas\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-2\",\n                                                    children: procedure.tags.map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                            variant: \"outline\",\n                                                            children: tag\n                                                        }, index, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                            lineNumber: 241,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                                value: \"requirements\",\n                                className: \"space-y-6\",\n                                children: [\n                                    procedure.best_requirements && procedure.best_requirements.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                        className: \"text-lg flex items-center gap-2\",\n                                                        children: [\n                                                            \"Requisitos\",\n                                                            ((_procedure_suit_data4 = procedure.suit_data) === null || _procedure_suit_data4 === void 0 ? void 0 : _procedure_suit_data4.requisitos) && procedure.best_requirements === procedure.suit_data.requisitos && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_CheckCircle_Clock_DollarSign_ExternalLink_FileText_Info_Mail_MapPin_Phone_Sparkles_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"h-4 w-4 text-amber-500\",\n                                                                title: \"Requisitos mejorados con SUIT\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                lineNumber: 260,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                        children: [\n                                                            \"Documentos y condiciones necesarias para realizar el tr\\xe1mite\",\n                                                            ((_procedure_suit_data5 = procedure.suit_data) === null || _procedure_suit_data5 === void 0 ? void 0 : _procedure_suit_data5.requisitos) && procedure.best_requirements === procedure.suit_data.requisitos && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-amber-600 font-medium\",\n                                                                children: \" (Informaci\\xf3n actualizada desde SUIT)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                lineNumber: 267,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-2\",\n                                                    children: procedure.best_requirements.map((requirement, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-start space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_CheckCircle_Clock_DollarSign_ExternalLink_FileText_Info_Mail_MapPin_Phone_Sparkles_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-chia-green-600 mt-0.5 flex-shrink-0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                    lineNumber: 275,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-700\",\n                                                                    children: requirement\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                    lineNumber: 276,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                            lineNumber: 274,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 17\n                                    }, this),\n                                    ((_procedure_suit_data6 = procedure.suit_data) === null || _procedure_suit_data6 === void 0 ? void 0 : _procedure_suit_data6.documentos_necesarios) && procedure.suit_data.documentos_necesarios.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                        className: \"border-amber-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                        className: \"text-lg flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_CheckCircle_Clock_DollarSign_ExternalLink_FileText_Info_Mail_MapPin_Phone_Sparkles_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"h-4 w-4 text-amber-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                lineNumber: 289,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Documentos Necesarios (SUIT)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                        lineNumber: 288,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                        children: \"Lista detallada de documentos seg\\xfan el Sistema \\xdanico de Informaci\\xf3n de Tr\\xe1mites\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-2\",\n                                                    children: procedure.suit_data.documentos_necesarios.map((document, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-start space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_CheckCircle_Clock_DollarSign_ExternalLink_FileText_Info_Mail_MapPin_Phone_Sparkles_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-amber-600 mt-0.5 flex-shrink-0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                    lineNumber: 300,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-700\",\n                                                                    children: document\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                    lineNumber: 301,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                            lineNumber: 299,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 17\n                                    }, this),\n                                    procedure.documents_required && procedure.documents_required.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                        className: \"text-lg\",\n                                                        children: \"Documentos Requeridos\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                        lineNumber: 313,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                        children: \"Lista de documentos que debe presentar\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-2\",\n                                                    children: procedure.documents_required.map((document, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-start space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_CheckCircle_Clock_DollarSign_ExternalLink_FileText_Info_Mail_MapPin_Phone_Sparkles_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-chia-blue-600 mt-0.5 flex-shrink-0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                    lineNumber: 322,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-700\",\n                                                                    children: document\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                    lineNumber: 323,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                            lineNumber: 321,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 17\n                                    }, this),\n                                    (procedure.suit_link || procedure.govco_link) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                        className: \"text-lg\",\n                                                        children: \"Enlaces de Requisitos\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                        children: \"Consulta los requisitos oficiales en las plataformas gubernamentales\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        procedure.suit_link && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"p-2 bg-chia-blue-100 rounded-lg\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_CheckCircle_Clock_DollarSign_ExternalLink_FileText_Info_Mail_MapPin_Phone_Sparkles_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                className: \"h-4 w-4 text-chia-blue-600\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                                lineNumber: 346,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                            lineNumber: 345,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-medium text-gray-900\",\n                                                                                    children: \"SUIT - Sistema \\xdanico de Informaci\\xf3n de Tr\\xe1mites\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                                    lineNumber: 349,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-gray-600\",\n                                                                                    children: \"Consulta los requisitos oficiales\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                                    lineNumber: 350,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                            lineNumber: 348,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                    lineNumber: 344,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    onClick: ()=>window.open(procedure.suit_link, \"_blank\"),\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_CheckCircle_Clock_DollarSign_ExternalLink_FileText_Info_Mail_MapPin_Phone_Sparkles_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                            lineNumber: 359,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Ver en SUIT\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                            lineNumber: 360,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                    lineNumber: 353,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                            lineNumber: 343,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        procedure.govco_link && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"p-2 bg-chia-green-100 rounded-lg\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_CheckCircle_Clock_DollarSign_ExternalLink_FileText_Info_Mail_MapPin_Phone_Sparkles_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                className: \"h-4 w-4 text-chia-green-600\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                                lineNumber: 369,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                            lineNumber: 368,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-medium text-gray-900\",\n                                                                                    children: \"Gov.co - Portal del Estado Colombiano\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                                    lineNumber: 372,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-gray-600\",\n                                                                                    children: \"Informaci\\xf3n oficial del gobierno\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                                    lineNumber: 373,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                            lineNumber: 371,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                    lineNumber: 367,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    onClick: ()=>window.open(procedure.govco_link, \"_blank\"),\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_CheckCircle_Clock_DollarSign_ExternalLink_FileText_Info_Mail_MapPin_Phone_Sparkles_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                            lineNumber: 382,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Ver en Gov.co\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                            lineNumber: 383,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                    lineNumber: 376,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                            lineNumber: 366,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                    lineNumber: 341,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                                value: \"process\",\n                                className: \"space-y-6\",\n                                children: [\n                                    procedure.best_process_steps && procedure.best_process_steps.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                        className: \"text-lg flex items-center gap-2\",\n                                                        children: [\n                                                            \"Pasos del Proceso\",\n                                                            ((_procedure_suit_data7 = procedure.suit_data) === null || _procedure_suit_data7 === void 0 ? void 0 : _procedure_suit_data7.pasos) && procedure.best_process_steps === procedure.suit_data.pasos && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_CheckCircle_Clock_DollarSign_ExternalLink_FileText_Info_Mail_MapPin_Phone_Sparkles_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"h-4 w-4 text-amber-500\",\n                                                                title: \"Pasos mejorados con SUIT\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                lineNumber: 402,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                        lineNumber: 398,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                        children: [\n                                                            \"Sigue estos pasos para completar el tr\\xe1mite\",\n                                                            ((_procedure_suit_data8 = procedure.suit_data) === null || _procedure_suit_data8 === void 0 ? void 0 : _procedure_suit_data8.pasos) && procedure.best_process_steps === procedure.suit_data.pasos && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-amber-600 font-medium\",\n                                                                children: \" (Informaci\\xf3n actualizada desde SUIT)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                lineNumber: 409,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                        lineNumber: 405,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                lineNumber: 397,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: procedure.best_process_steps.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start space-x-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-shrink-0 w-8 h-8 bg-chia-blue-100 text-chia-blue-600 rounded-full flex items-center justify-center text-sm font-semibold\",\n                                                                    children: index + 1\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                    lineNumber: 417,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-700\",\n                                                                        children: step\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                        lineNumber: 421,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                    lineNumber: 420,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                            lineNumber: 416,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                    lineNumber: 414,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                lineNumber: 413,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 17\n                                    }, this),\n                                    ((_procedure_suit_data9 = procedure.suit_data) === null || _procedure_suit_data9 === void 0 ? void 0 : _procedure_suit_data9.base_juridica) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                        className: \"border-amber-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                        className: \"text-lg flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_CheckCircle_Clock_DollarSign_ExternalLink_FileText_Info_Mail_MapPin_Phone_Sparkles_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"h-4 w-4 text-amber-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                lineNumber: 435,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Base Jur\\xeddica (SUIT)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                        children: \"Marco legal seg\\xfan el Sistema \\xdanico de Informaci\\xf3n de Tr\\xe1mites\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                        lineNumber: 438,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-700\",\n                                                    children: procedure.suit_data.base_juridica\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                    lineNumber: 443,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                lineNumber: 442,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 17\n                                    }, this),\n                                    ((_procedure_suit_data10 = procedure.suit_data) === null || _procedure_suit_data10 === void 0 ? void 0 : _procedure_suit_data10.entidad_responsable) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                        className: \"border-amber-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    className: \"text-lg flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_CheckCircle_Clock_DollarSign_ExternalLink_FileText_Info_Mail_MapPin_Phone_Sparkles_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-4 w-4 text-amber-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                            lineNumber: 453,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"Entidad Responsable (SUIT)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                    lineNumber: 452,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                lineNumber: 451,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-700\",\n                                                    children: procedure.suit_data.entidad_responsable\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                        lineNumber: 450,\n                                        columnNumber: 17\n                                    }, this),\n                                    procedure.legal_framework && !((_procedure_suit_data11 = procedure.suit_data) === null || _procedure_suit_data11 === void 0 ? void 0 : _procedure_suit_data11.base_juridica) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    className: \"text-lg\",\n                                                    children: \"Marco Legal\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                    lineNumber: 467,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                lineNumber: 466,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-700\",\n                                                    children: procedure.legal_framework\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                    lineNumber: 470,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                        lineNumber: 465,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                lineNumber: 393,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                                value: \"contact\",\n                                className: \"space-y-6\",\n                                children: [\n                                    procedure.dependency && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                        className: \"text-lg\",\n                                                        children: \"Informaci\\xf3n de Contacto\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                        lineNumber: 481,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                        children: procedure.dependency.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                        lineNumber: 482,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                lineNumber: 480,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    procedure.dependency.contact_email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_CheckCircle_Clock_DollarSign_ExternalLink_FileText_Info_Mail_MapPin_Phone_Sparkles_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"h-5 w-5 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                lineNumber: 489,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: \"Correo Electr\\xf3nico\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                        lineNumber: 491,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium\",\n                                                                        children: procedure.dependency.contact_email\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                        lineNumber: 492,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                lineNumber: 490,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                        lineNumber: 488,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    procedure.dependency.contact_phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_CheckCircle_Clock_DollarSign_ExternalLink_FileText_Info_Mail_MapPin_Phone_Sparkles_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"h-5 w-5 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                lineNumber: 499,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: \"Tel\\xe9fono\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                        lineNumber: 501,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium\",\n                                                                        children: procedure.dependency.contact_phone\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                        lineNumber: 502,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                lineNumber: 500,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                        lineNumber: 498,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    procedure.dependency.address && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_CheckCircle_Clock_DollarSign_ExternalLink_FileText_Info_Mail_MapPin_Phone_Sparkles_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"h-5 w-5 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                lineNumber: 509,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: \"Direcci\\xf3n\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                        lineNumber: 511,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium\",\n                                                                        children: procedure.dependency.address\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                        lineNumber: 512,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                lineNumber: 510,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                        lineNumber: 508,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    procedure.dependency.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"pt-4 border-t\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600 mb-2\",\n                                                                children: \"Descripci\\xf3n\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                lineNumber: 519,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-700\",\n                                                                children: procedure.dependency.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                lineNumber: 520,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                        lineNumber: 518,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                lineNumber: 486,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                        lineNumber: 479,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                        className: \"bg-chia-blue-50 border-chia-blue-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            className: \"p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_CheckCircle_Clock_DollarSign_ExternalLink_FileText_Info_Mail_MapPin_Phone_Sparkles_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-5 w-5 text-chia-blue-600 mt-0.5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                        lineNumber: 531,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-medium text-chia-blue-900\",\n                                                                children: \"Para iniciar este tr\\xe1mite\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                lineNumber: 533,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-chia-blue-700 mt-1\",\n                                                                children: \"Debes crear una cuenta en el sistema y autenticarte para poder iniciar y hacer seguimiento a tus tr\\xe1mites.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                                lineNumber: 536,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                        lineNumber: 532,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                                lineNumber: 530,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                            lineNumber: 529,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                        lineNumber: 528,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                                lineNumber: 476,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n            lineNumber: 54,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\PublicProcedureDetailModal.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\n_s(PublicProcedureDetailModal, \"chU/96YDf6tpLdqvgqS2ZL+A4Bo=\");\n_c = PublicProcedureDetailModal;\nvar _c;\n$RefreshReg$(_c, \"PublicProcedureDetailModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/procedures/PublicProcedureDetailModal.tsx\n"));

/***/ })

});