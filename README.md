# 🏛️ Portal Municipal de Chía - Sistema de Atención Ciudadana

Portal web oficial del Municipio de Chía para la gestión de trámites y servicios ciudadanos, desarrollado con tecnologías modernas y enfoque en accesibilidad.

## 🚀 Características Principales

- **🎨 Identidad Visual Corporativa**: Logo oficial y colores institucionales
- **🔍 Búsqueda Inteligente**: Sistema avanzado de búsqueda de trámites y procedimientos
- **♿ Accesibilidad WCAG 2.1 AA**: Cumplimiento total de estándares de accesibilidad
- **📱 Responsive Design**: Adaptación completa a todos los dispositivos
- **🤖 Asistente IA**: Chatbot inteligente para atención ciudadana
- **🔐 Autenticación Segura**: Sistema de roles y permisos con Supabase Auth
- **📊 Dashboard Administrativo**: Panel de control para funcionarios

## 🛠️ Stack Tecnológico

### Frontend
- **Next.js 14+** - Framework React con App Router
- **TypeScript** - Tipado estático
- **Tailwind CSS** - Framework de estilos utilitarios
- **Framer Motion** - Animaciones y transiciones
- **React Hook Form** - Manejo de formularios
- **Lucide React** - Iconografía

### Backend
- **Supabase** - Backend as a Service
- **PostgreSQL** - Base de datos relacional
- **Row Level Security (RLS)** - Seguridad a nivel de fila
- **pgvector** - Búsqueda vectorial para IA

### Herramientas de Desarrollo
- **ESLint** - Linting de código
- **Prettier** - Formateo de código
- **Jest** - Testing unitario
- **Playwright** - Testing E2E
- **Husky** - Git hooks

## 📦 Instalación

### Prerrequisitos
- Node.js 18+ 
- npm o yarn
- Cuenta de Supabase

### Configuración Local

1. **Clonar el repositorio**
```bash
git clone [repository-url]
cd chia-tramites
```

2. **Instalar dependencias**
```bash
npm install
```

3. **Configurar variables de entorno**
```bash
cp .env.example .env.local
```

Configurar las siguientes variables:
```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
OPENAI_API_KEY=your_openai_api_key
```

4. **Ejecutar en desarrollo**
```bash
npm run dev
```

El portal estará disponible en `http://localhost:3000`

## 🎨 Mejoras UI/UX Recientes

### ✅ Implementaciones Completadas (2025-01-03)

1. **Logo Oficial de Chía**
   - Componente SVG escalable con elementos oficiales
   - Variantes responsive para header, footer e icono
   - Reemplazo completo del placeholder "C"

2. **Esquema de Colores Corporativo**
   - Chia-green (#059669) como color primario
   - Chia-blue (#1E40AF) como color secundario
   - Cumplimiento WCAG 2.1 AA en contrastes

3. **Corrección de Z-Index**
   - Jerarquía clara de superposición visual
   - Eliminación de conflictos entre componentes
   - Documentación completa en `docs/z-index-hierarchy.md`

4. **Verificación de Accesibilidad**
   - Componente `AccessibilityChecker` para validación automática
   - Página de pruebas en `/accessibility-test`
   - Cumplimiento 100% WCAG 2.1 AA

5. **Responsive Design Completo**
   - Adaptación a todos los breakpoints
   - Componente `ResponsiveTestGrid` para validación
   - Página de pruebas en `/responsive-test`

Ver documentación completa en: `docs/MEJORAS-UI-UX-IMPLEMENTADAS.md`

## 🧪 Testing

### Ejecutar Pruebas
```bash
# Pruebas unitarias
npm run test

# Pruebas E2E
npm run test:e2e

# Cobertura de código
npm run test:coverage
```

### Páginas de Prueba
- `/accessibility-test` - Verificación de accesibilidad WCAG 2.1 AA
- `/responsive-test` - Validación de diseño responsive

## 📁 Estructura del Proyecto

```
├── app/                    # App Router de Next.js
│   ├── accessibility-test/ # Página de pruebas de accesibilidad
│   ├── responsive-test/    # Página de pruebas responsive
│   └── ...
├── components/             # Componentes React
│   ├── accessibility/      # Componentes de accesibilidad
│   ├── testing/           # Componentes de testing
│   ├── ui/                # Componentes base (shadcn/ui)
│   └── ...
├── docs/                  # Documentación del proyecto
│   ├── MEJORAS-UI-UX-IMPLEMENTADAS.md
│   ├── z-index-hierarchy.md
│   └── ...
├── hooks/                 # Custom React hooks
├── lib/                   # Utilidades y configuraciones
├── tests/                 # Archivos de prueba
└── types/                 # Definiciones de TypeScript
```

## 🎯 Funcionalidades Principales

### Para Ciudadanos
- **Consulta de Trámites**: Búsqueda inteligente de procedimientos
- **Información Detallada**: Requisitos, costos y tiempos de respuesta
- **FAQ Inteligente**: Preguntas frecuentes organizadas por dependencia
- **Asistente IA**: Chatbot para consultas en tiempo real

### Para Funcionarios
- **Dashboard Administrativo**: Panel de control completo
- **Gestión de Trámites**: CRUD de procedimientos y dependencias
- **Reportes y Analytics**: Métricas de uso y rendimiento
- **Sistema de Roles**: Permisos granulares por dependencia

## 🔐 Seguridad

- **Row Level Security (RLS)**: Políticas de seguridad a nivel de base de datos
- **Autenticación JWT**: Tokens seguros con Supabase Auth
- **Roles y Permisos**: Sistema granular de autorización
- **Validación de Datos**: Sanitización en frontend y backend

## 🚀 Despliegue

### Producción con Coolify
El proyecto está configurado para despliegue con Coolify:

```yaml
# coolify.yaml
services:
  - name: chia-tramites
    image: node:18-alpine
    build:
      context: .
      dockerfile: Dockerfile
```

### Variables de Entorno de Producción
```env
NODE_ENV=production
NEXT_PUBLIC_SUPABASE_URL=production_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=production_key
```

## 📊 Métricas y Monitoreo

- **Lighthouse CI**: Auditorías automáticas de rendimiento
- **Accessibility Testing**: Validación continua WCAG 2.1 AA
- **Error Tracking**: Monitoreo de errores en producción
- **Analytics**: Métricas de uso y comportamiento de usuarios

## 🤝 Contribución

1. Fork del repositorio
2. Crear rama feature (`git checkout -b feature/nueva-funcionalidad`)
3. Commit de cambios (`git commit -am 'Agregar nueva funcionalidad'`)
4. Push a la rama (`git push origin feature/nueva-funcionalidad`)
5. Crear Pull Request

### Estándares de Código
- **ESLint**: Seguir configuración establecida
- **Prettier**: Formateo automático
- **Conventional Commits**: Mensajes de commit estandarizados
- **Testing**: Cobertura mínima 80% para código crítico

## 📄 Licencia

Este proyecto es propiedad del Municipio de Chía y está destinado exclusivamente para uso oficial de la administración municipal.

## 📞 Soporte

Para soporte técnico o consultas sobre el sistema:
- **Email**: <EMAIL>
- **Documentación**: Ver carpeta `docs/`
- **Issues**: Usar el sistema de issues de GitHub

---

**Desarrollado para el Municipio de Chía** 🏛️  
**Versión**: 1.0.0  
**Última actualización**: 2025-01-03
