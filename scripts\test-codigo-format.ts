#!/usr/bin/env tsx

/**
 * Script para verificar el formato de códigos XXX-XXX-XXX
 * Verifica que todos los códigos de trámites y OPAs estén en el formato correcto
 */

import { createClient } from '@supabase/supabase-js'
import { Database } from '../src/types/supabase'

// Configuración de Supabase
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Variables de entorno de Supabase no configuradas')
  process.exit(1)
}

const supabase = createClient<Database>(supabaseUrl, supabaseKey)

// Regex para validar formato XXX-XXX-XXX
const CODE_FORMAT_REGEX = /^[0-9]{3}-[0-9]{3}-[0-9]{3}$/

async function testCodigoFormat() {
  console.log('🔍 Verificando formato de códigos XXX-XXX-XXX...\n')

  try {
    // Verificar códigos de trámites
    console.log('📋 Verificando códigos de trámites...')
    const { data: tramites, error: tramitesError } = await supabase
      .from('procedures')
      .select('id, name, codigo_tramite')
      .not('codigo_tramite', 'is', null)

    if (tramitesError) {
      console.error('❌ Error al obtener trámites:', tramitesError)
      return false
    }

    let tramitesValidos = 0
    let tramitesInvalidos = 0

    tramites?.forEach(tramite => {
      if (CODE_FORMAT_REGEX.test(tramite.codigo_tramite!)) {
        tramitesValidos++
      } else {
        tramitesInvalidos++
        console.log(`❌ Trámite con código inválido: ${tramite.codigo_tramite} - ${tramite.name}`)
      }
    })

    console.log(`✅ Trámites válidos: ${tramitesValidos}`)
    console.log(`❌ Trámites inválidos: ${tramitesInvalidos}`)

    // Verificar códigos de OPAs
    console.log('\n📋 Verificando códigos de OPAs...')
    const { data: opas, error: opasError } = await supabase
      .from('opas')
      .select('id, name, code')
      .not('code', 'is', null)

    if (opasError) {
      console.error('❌ Error al obtener OPAs:', opasError)
      return false
    }

    let opasValidas = 0
    let opasInvalidas = 0

    opas?.forEach(opa => {
      if (CODE_FORMAT_REGEX.test(opa.code!)) {
        opasValidas++
      } else {
        opasInvalidas++
        console.log(`❌ OPA con código inválido: ${opa.code} - ${opa.name}`)
      }
    })

    console.log(`✅ OPAs válidas: ${opasValidas}`)
    console.log(`❌ OPAs inválidas: ${opasInvalidas}`)

    // Verificar vista unificada
    console.log('\n📋 Verificando vista unificada...')
    const { data: vista, error: vistaError } = await supabase
      .from('vista_codigos_procedimientos')
      .select('codigo, tipo, name')
      .not('codigo', 'is', null)
      .limit(10)

    if (vistaError) {
      console.error('❌ Error al obtener vista unificada:', vistaError)
      return false
    }

    console.log('\n📊 Ejemplos de la vista unificada:')
    vista?.forEach(item => {
      const isValid = CODE_FORMAT_REGEX.test(item.codigo!)
      const status = isValid ? '✅' : '❌'
      console.log(`${status} ${item.tipo}: ${item.codigo} - ${item.name}`)
    })

    // Resumen final
    const totalValidos = tramitesValidos + opasValidas
    const totalInvalidos = tramitesInvalidos + opasInvalidas
    const totalProcedimientos = totalValidos + totalInvalidos

    console.log('\n📊 RESUMEN FINAL:')
    console.log(`Total de procedimientos: ${totalProcedimientos}`)
    console.log(`✅ Códigos válidos (XXX-XXX-XXX): ${totalValidos}`)
    console.log(`❌ Códigos inválidos: ${totalInvalidos}`)
    console.log(`📈 Porcentaje de éxito: ${((totalValidos / totalProcedimientos) * 100).toFixed(2)}%`)

    if (totalInvalidos === 0) {
      console.log('\n🎉 ¡Todos los códigos están en formato XXX-XXX-XXX correcto!')
      return true
    } else {
      console.log('\n⚠️  Algunos códigos necesitan corrección.')
      return false
    }

  } catch (error) {
    console.error('❌ Error durante la verificación:', error)
    return false
  }
}

// Ejecutar verificación
testCodigoFormat()
  .then(success => {
    process.exit(success ? 0 : 1)
  })
  .catch(error => {
    console.error('❌ Error fatal:', error)
    process.exit(1)
  })
