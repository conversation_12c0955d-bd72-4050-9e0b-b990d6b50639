#!/usr/bin/env node

/**
 * Script de Validación de Referencias de Código FAQ
 *
 * Este script verifica que no haya referencias a tablas obsoletas en el código
 * sin requerir conexión a la base de datos.
 */

const { readFileSync, readdirSync, statSync } = require('fs')
const { join } = require('path')

// interface ValidationResult {
//   file: string
//   line: number
//   pattern: string
//   context: string
//   severity: 'ERROR' | 'WARNING'
// }

class CodeReferenceValidator {
  constructor() {
    this.results = []
  }
  
  // Patrones obsoletos que NO deberían aparecer en el código
  getObsoletePatterns() {
    return [
      {
        pattern: /from\(['"`]faqs['"`]\)/g,
        replacement: "from('municipal_faqs')",
        severity: 'ERROR',
        description: 'Tabla "faqs" obsoleta, usar "municipal_faqs"'
      },
      {
        pattern: /from\(['"`]faq_categories['"`]\)/g,
        replacement: "from('faq_themes')",
        severity: 'ERROR',
        description: 'Tabla "faq_categories" obsoleta, usar "faq_themes"'
      },
      {
        pattern: /\.faq_categories\b/g,
        replacement: '.faq_themes',
        severity: 'ERROR',
        description: 'Referencia a tabla obsoleta "faq_categories"'
      },
      {
        pattern: /category_id/g,
        replacement: 'theme_id',
        severity: 'WARNING',
        description: 'Campo "category_id" posiblemente obsoleto, verificar si debería ser "theme_id"'
      }
    ]
  }

  // Archivos a verificar
  getFilesToCheck() {
    return [
      'lib/services/faqService.ts',
      'components/faq/FAQSection.tsx',
      'scripts/test-faq-service.ts',
      'lib/database.types.ts'
    ]
  }

  /**
   * Ejecutar validación de referencias de código
   */
  runValidation() {
    console.log('💻 Validando referencias de código FAQ...\n')

    this.getFilesToCheck().forEach(filePath => {
      this.validateFile(filePath)
    })

    this.printResults()
  }

  /**
   * Validar un archivo específico
   */
  validateFile(filePath) {
    try {
      const content = readFileSync(filePath, 'utf-8')
      const lines = content.split('\n')

      this.getObsoletePatterns().forEach(({ pattern, severity, description }) => {
        let match
        pattern.lastIndex = 0 // Reset regex state
        
        while ((match = pattern.exec(content)) !== null) {
          const lineNumber = this.getLineNumber(content, match.index)
          const lineContent = lines[lineNumber - 1]?.trim() || ''
          
          this.results.push({
            file: filePath,
            line: lineNumber,
            pattern: description,
            context: lineContent,
            severity: severity
          })
        }
      })

      console.log(`✅ Verificado: ${filePath}`)
    } catch (error) {
      console.log(`⚠️  No se pudo leer: ${filePath} (${error})`)
    }
  }

  /**
   * Obtener número de línea para un índice de carácter
   */
  getLineNumber(content, charIndex) {
    return content.substring(0, charIndex).split('\n').length
  }

  /**
   * Imprimir resultados de validación
   */
  printResults() {
    console.log('\n📋 RESULTADOS DE VALIDACIÓN DE CÓDIGO\n')
    console.log('=' .repeat(70))

    if (this.results.length === 0) {
      console.log('🎉 ¡EXCELENTE! No se encontraron referencias obsoletas en el código.')
      console.log('\n✅ Todos los archivos están usando las tablas correctas:')
      console.log('   • municipal_faqs (en lugar de faqs)')
      console.log('   • faq_themes (en lugar de faq_categories)')
      return
    }

    const errors = this.results.filter(r => r.severity === 'ERROR')
    const warnings = this.results.filter(r => r.severity === 'WARNING')

    // Mostrar errores
    if (errors.length > 0) {
      console.log('❌ ERRORES CRÍTICOS (deben ser corregidos):')
      errors.forEach(result => {
        console.log(`   📁 ${result.file}:${result.line}`)
        console.log(`   🔍 ${result.pattern}`)
        console.log(`   📝 ${result.context}`)
        console.log('')
      })
    }

    // Mostrar advertencias
    if (warnings.length > 0) {
      console.log('⚠️  ADVERTENCIAS (revisar):')
      warnings.forEach(result => {
        console.log(`   📁 ${result.file}:${result.line}`)
        console.log(`   🔍 ${result.pattern}`)
        console.log(`   📝 ${result.context}`)
        console.log('')
      })
    }

    console.log('=' .repeat(70))
    console.log(`📊 RESUMEN: ${errors.length} errores | ${warnings.length} advertencias`)

    if (errors.length > 0) {
      console.log('\n🚨 ACCIÓN REQUERIDA: Corregir errores críticos antes de continuar.')
      console.log('\n💡 GUÍA DE CORRECCIÓN:')
      console.log('   • Cambiar "faqs" por "municipal_faqs"')
      console.log('   • Cambiar "faq_categories" por "faq_themes"')
      console.log('   • Verificar que "category_id" sea "theme_id" donde corresponda')
    } else if (warnings.length > 0) {
      console.log('\n⚠️  Revisar advertencias para asegurar consistencia completa.')
    }
  }
}

// Ejecutar validación si el script se ejecuta directamente
if (require.main === module) {
  const validator = new CodeReferenceValidator()
  validator.runValidation()
}

module.exports = CodeReferenceValidator
