-- =============================================
-- CREAR TABLA OPAS PARA RESOLVER INCONSISTENCIAS
-- =============================================
-- Fecha: 2025-07-03
-- Descripción: Crea la tabla 'opas' que es referenciada en el código pero no existe
-- Esta tabla será un alias/vista de administrative_procedures con campos adicionales

-- 1. Crear tabla opas basada en administrative_procedures
CREATE TABLE IF NOT EXISTS opas (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  dependency_id UUID REFERENCES dependencies(id),
  subdependency_id UUID REFERENCES subdependencies(id),
  code TEXT NOT NULL,
  name TEXT, -- Campo adicional para nombre
  description TEXT NOT NULL,
  has_cost BOOLEAN DEFAULT false,
  cost_description TEXT,
  status procedure_status DEFAULT 'active',
  is_active BOOLEAN DEFAULT true,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Crear índices para optimización
CREATE INDEX IF NOT EXISTS idx_opas_code ON opas(code);
CREATE INDEX IF NOT EXISTS idx_opas_dependency_id ON opas(dependency_id);
CREATE INDEX IF NOT EXISTS idx_opas_subdependency_id ON opas(subdependency_id);
CREATE INDEX IF NOT EXISTS idx_opas_status ON opas(status);
CREATE INDEX IF NOT EXISTS idx_opas_is_active ON opas(is_active);

-- 3. Migrar datos de administrative_procedures a opas
INSERT INTO opas (
  id, dependency_id, subdependency_id, code, name, description, 
  status, is_active, metadata, created_at, updated_at
)
SELECT 
  id, dependency_id, subdependency_id, code, 
  code as name, -- Usar código como nombre temporal
  description, status, 
  CASE WHEN status = 'active' THEN true ELSE false END as is_active,
  metadata, created_at, updated_at
FROM administrative_procedures
ON CONFLICT (id) DO NOTHING;

-- 4. Agregar trigger para mantener sincronización
CREATE OR REPLACE FUNCTION sync_administrative_procedures_to_opas()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    INSERT INTO opas (
      id, dependency_id, subdependency_id, code, name, description,
      status, is_active, metadata, created_at, updated_at
    ) VALUES (
      NEW.id, NEW.dependency_id, NEW.subdependency_id, NEW.code,
      NEW.code, NEW.description, NEW.status,
      CASE WHEN NEW.status = 'active' THEN true ELSE false END,
      NEW.metadata, NEW.created_at, NEW.updated_at
    );
    RETURN NEW;
  ELSIF TG_OP = 'UPDATE' THEN
    UPDATE opas SET
      dependency_id = NEW.dependency_id,
      subdependency_id = NEW.subdependency_id,
      code = NEW.code,
      name = NEW.code,
      description = NEW.description,
      status = NEW.status,
      is_active = CASE WHEN NEW.status = 'active' THEN true ELSE false END,
      metadata = NEW.metadata,
      updated_at = NEW.updated_at
    WHERE id = NEW.id;
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    DELETE FROM opas WHERE id = OLD.id;
    RETURN OLD;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 5. Crear trigger en administrative_procedures
CREATE TRIGGER sync_to_opas_trigger
  AFTER INSERT OR UPDATE OR DELETE ON administrative_procedures
  FOR EACH ROW EXECUTE FUNCTION sync_administrative_procedures_to_opas();

-- 6. Agregar trigger de updated_at para opas
CREATE TRIGGER update_opas_updated_at 
  BEFORE UPDATE ON opas 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 7. Habilitar RLS en opas
ALTER TABLE opas ENABLE ROW LEVEL SECURITY;

-- 8. Crear políticas RLS para opas (similar a administrative_procedures)
CREATE POLICY "Users can view active opas" ON opas
  FOR SELECT TO authenticated USING (status = 'active');

CREATE POLICY "Admins can manage dependency opas" ON opas
  FOR ALL USING (
    get_user_role() IN ('admin', 'super_admin') AND
    (is_super_admin() OR is_dependency_admin(dependency_id))
  );

-- 9. Comentarios para documentación
COMMENT ON TABLE opas IS 'Tabla de OPAs (Otros Procedimientos Administrativos) - Sincronizada con administrative_procedures';
COMMENT ON COLUMN opas.code IS 'Código único del procedimiento administrativo';
COMMENT ON COLUMN opas.name IS 'Nombre del procedimiento (derivado del código)';
COMMENT ON COLUMN opas.has_cost IS 'Indica si el procedimiento tiene costo asociado';
COMMENT ON COLUMN opas.cost_description IS 'Descripción del costo del procedimiento';
COMMENT ON COLUMN opas.is_active IS 'Indica si el procedimiento está activo (derivado del status)';

-- 10. Actualizar vista unificada para incluir opas
DROP VIEW IF EXISTS vista_codigos_procedimientos;
CREATE VIEW vista_codigos_procedimientos AS
SELECT 
    p.id,
    p.name,
    p.codigo_tramite as codigo,
    'TRAMITE' as tipo,
    d.code as dep_code,
    d.name as dep_name,
    s.code as subdep_code,
    s.name as subdep_name
FROM procedures p
LEFT JOIN subdependencies s ON p.subdependency_id = s.id
LEFT JOIN dependencies d ON s.dependency_id = d.id
WHERE p.is_active = true

UNION ALL

SELECT 
    o.id,
    o.name,
    o.code as codigo,
    'OPA' as tipo,
    d.code as dep_code,
    d.name as dep_name,
    s.code as subdep_code,
    s.name as subdep_name
FROM opas o
LEFT JOIN subdependencies s ON o.subdependency_id = s.id
LEFT JOIN dependencies d ON s.dependency_id = d.id
WHERE o.is_active = true;

COMMENT ON VIEW vista_codigos_procedimientos IS 'Vista unificada de códigos de trámites y OPAs';
