'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { supabase } from '@/lib/supabase/client'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, Eye, EyeOff, Mail, Lock, User, Phone, MapPin } from 'lucide-react'
import { validateEmail, validateCedula, validatePhone } from '@/lib/utils'

export default function RegisterPage() {
  const router = useRouter()

  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    fullName: '',
    documentNumber: '',
    phone: '',
    city: 'Chía',
    department: 'Cundinamarca',
  })
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
    // Clear error when user starts typing
    if (error) setError(null)
  }

  const validateForm = () => {
    if (!formData.email || !validateEmail(formData.email)) {
      setError('Por favor ingresa un email válido')
      return false
    }

    if (!formData.password || formData.password.length < 8) {
      setError('La contraseña debe tener al menos 8 caracteres')
      return false
    }

    if (formData.password !== formData.confirmPassword) {
      setError('Las contraseñas no coinciden')
      return false
    }

    if (!formData.fullName || formData.fullName.trim().length < 2) {
      setError('Por favor ingresa tu nombre completo')
      return false
    }

    if (!formData.documentNumber || !validateCedula(formData.documentNumber)) {
      setError('Por favor ingresa un número de cédula válido')
      return false
    }

    if (!formData.phone || !validatePhone(formData.phone)) {
      setError('Por favor ingresa un número de teléfono válido')
      return false
    }

    return true
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) return

    setIsLoading(true)
    setError(null)
    setSuccess(null)

    try {
      // Check if user already exists
      const { data: existingUser } = await supabase
        .from('profiles')
        .select('email, document_number')
        .or(`email.eq.${formData.email},document_number.eq.${formData.documentNumber}`)
        .single()

      if (existingUser) {
        if (existingUser.email === formData.email) {
          throw new Error('Ya existe una cuenta con este email')
        }
        if (existingUser.document_number === formData.documentNumber) {
          throw new Error('Ya existe una cuenta con este número de documento')
        }
      }

      // Create auth user
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: formData.email,
        password: formData.password,
        options: {
          data: {
            full_name: formData.fullName,
            document_number: formData.documentNumber,
            phone: formData.phone,
          }
        }
      })

      if (authError) {
        throw authError
      }

      if (authData.user) {
        // Get the default citizen role
        const { data: citizenRole } = await supabase
          .from('roles')
          .select('id')
          .eq('name', 'ciudadano')
          .single()

        if (!citizenRole) {
          throw new Error('Error en la configuración del sistema. Contacta al administrador.')
        }

        // Create user profile
        const { error: profileError } = await supabase
          .from('profiles')
          .insert({
            id: authData.user.id,
            email: formData.email,
            full_name: formData.fullName,
            document_number: formData.documentNumber,
            document_type: 'cedula',
            phone: formData.phone,
            city: formData.city,
            department: formData.department,
            role_id: citizenRole.id,
            is_active: true,
          })

        if (profileError) {
          throw profileError
        }

        setSuccess(
          'Cuenta creada exitosamente. Por favor revisa tu email para confirmar tu cuenta antes de iniciar sesión.'
        )

        // Redirect to login after 3 seconds
        setTimeout(() => {
          router.push('/auth/login')
        }, 3000)
      }
    } catch (error: any) {
      console.error('Registration error:', error)
      
      // Handle specific error messages
      if (error.message === 'User already registered') {
        setError('Ya existe una cuenta con este email')
      } else if (error.message.includes('duplicate key')) {
        setError('Ya existe una cuenta con estos datos')
      } else {
        setError(error.message || 'Error al crear la cuenta. Intenta de nuevo.')
      }
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-gray-900">Crear Cuenta</h2>
        <p className="mt-2 text-gray-600">
          Regístrate para acceder a todos los servicios municipales
        </p>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert variant="success">
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="fullName">Nombre Completo</Label>
            <div className="relative">
              <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                id="fullName"
                name="fullName"
                type="text"
                required
                placeholder="Tu nombre completo"
                value={formData.fullName}
                onChange={handleInputChange}
                className="pl-10"
                disabled={isLoading}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="documentNumber">Número de Cédula</Label>
            <Input
              id="documentNumber"
              name="documentNumber"
              type="text"
              required
              placeholder="12345678"
              value={formData.documentNumber}
              onChange={handleInputChange}
              disabled={isLoading}
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="email">Correo Electrónico</Label>
          <div className="relative">
            <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              id="email"
              name="email"
              type="email"
              autoComplete="email"
              required
              placeholder="<EMAIL>"
              value={formData.email}
              onChange={handleInputChange}
              className="pl-10"
              disabled={isLoading}
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="phone">Teléfono</Label>
          <div className="relative">
            <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              id="phone"
              name="phone"
              type="tel"
              required
              placeholder="3001234567"
              value={formData.phone}
              onChange={handleInputChange}
              className="pl-10"
              disabled={isLoading}
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="city">Ciudad</Label>
            <div className="relative">
              <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                id="city"
                name="city"
                type="text"
                required
                value={formData.city}
                onChange={handleInputChange}
                className="pl-10"
                disabled={isLoading}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="department">Departamento</Label>
            <Input
              id="department"
              name="department"
              type="text"
              required
              value={formData.department}
              onChange={handleInputChange}
              disabled={isLoading}
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="password">Contraseña</Label>
          <div className="relative">
            <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              id="password"
              name="password"
              type={showPassword ? 'text' : 'password'}
              autoComplete="new-password"
              required
              placeholder="Mínimo 8 caracteres"
              value={formData.password}
              onChange={handleInputChange}
              className="pl-10 pr-10"
              disabled={isLoading}
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              disabled={isLoading}
            >
              {showPassword ? (
                <EyeOff className="h-4 w-4" />
              ) : (
                <Eye className="h-4 w-4" />
              )}
            </button>
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="confirmPassword">Confirmar Contraseña</Label>
          <div className="relative">
            <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              id="confirmPassword"
              name="confirmPassword"
              type={showConfirmPassword ? 'text' : 'password'}
              autoComplete="new-password"
              required
              placeholder="Confirma tu contraseña"
              value={formData.confirmPassword}
              onChange={handleInputChange}
              className="pl-10 pr-10"
              disabled={isLoading}
            />
            <button
              type="button"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              disabled={isLoading}
            >
              {showConfirmPassword ? (
                <EyeOff className="h-4 w-4" />
              ) : (
                <Eye className="h-4 w-4" />
              )}
            </button>
          </div>
        </div>

        <Button
          type="submit"
          className="w-full bg-chia-blue-600 hover:bg-chia-blue-700"
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Creando cuenta...
            </>
          ) : (
            'Crear Cuenta'
          )}
        </Button>
      </form>

      <div className="text-center">
        <p className="text-sm text-gray-600">
          ¿Ya tienes una cuenta?{' '}
          <Link
            href="/auth/login"
            className="text-chia-blue-600 hover:text-chia-blue-500 font-medium"
          >
            Inicia sesión aquí
          </Link>
        </p>
      </div>
    </div>
  )
}
