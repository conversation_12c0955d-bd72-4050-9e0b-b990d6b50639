import { NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function GET() {
  try {
    // Check database connection
    const supabase = createClient()
    const { data, error } = await supabase
      .from('dependencies')
      .select('count')
      .limit(1)
      .single()

    if (error) {
      throw new Error(`Database connection failed: ${error.message}`)
    }

    // Get system status
    const status = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: process.env.APP_VERSION || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      database: 'connected',
      uptime: process.uptime(),
      memory: {
        used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
        total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
      },
      services: {
        supabase: 'connected',
        auth: 'operational',
        api: 'operational'
      }
    }

    return NextResponse.json(status, { status: 200 })
  } catch (error) {
    console.error('Health check failed:', error)
    
    const errorStatus = {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      version: process.env.APP_VERSION || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      error: error instanceof Error ? error.message : 'Unknown error',
      uptime: process.uptime(),
      memory: {
        used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
        total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
      }
    }

    return NextResponse.json(errorStatus, { status: 503 })
  }
}
