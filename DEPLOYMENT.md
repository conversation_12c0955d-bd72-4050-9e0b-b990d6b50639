# Guía de Despliegue - Sistema de Atención Ciudadana Chía

## Requisitos Previos

### Servidor
- **CPU**: <PERSON><PERSON><PERSON> 2 cores, recomendado 4 cores
- **RAM**: <PERSON><PERSON><PERSON> 4GB, recomendado 8GB
- **Almacenamiento**: <PERSON><PERSON><PERSON> 20GB SSD
- **Sistema Operativo**: Ubuntu 20.04+ o similar
- **Docker**: Versión 20.10+
- **Docker Compose**: Versión 2.0+

### Servicios Externos
- **Supabase**: Proyecto configurado con base de datos PostgreSQL
- **Dominio**: Dominio configurado apuntando al servidor
- **SSL**: Certificado SSL (Let's Encrypt recomendado)

## Configuración de Variables de Entorno

### Variables Requeridas

```bash
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Authentication
NEXTAUTH_SECRET=your-super-secret-key-min-32-chars
NEXTAUTH_URL=https://your-domain.com

# Application
DOMAIN_NAME=your-domain.com
APP_VERSION=1.0.0
```

### Generar NEXTAUTH_SECRET
```bash
openssl rand -base64 32
```

## Despliegue con Coolify

### 1. Configuración en Coolify Dashboard

1. **Crear Nuevo Proyecto**
   - Nombre: `chia-tramites`
   - Tipo: `Docker Compose`

2. **Configurar Repositorio**
   - URL del repositorio Git
   - Rama: `main` o `production`
   - Dockerfile: `Dockerfile`

3. **Variables de Entorno**
   - Agregar todas las variables listadas arriba
   - Marcar como "secretas" las keys de Supabase y NEXTAUTH_SECRET

4. **Configuración de Dominio**
   - Dominio principal: `tramites.chia-cundinamarca.gov.co`
   - Habilitar HTTPS automático
   - Habilitar redirección HTTP → HTTPS

### 2. Configuración de Red

```yaml
# En Coolify, configurar:
- Puerto interno: 3000
- Puerto externo: 80/443 (automático con proxy)
- Health check: /api/health
```

### 3. Configuración de Recursos

```yaml
# Límites recomendados:
CPU: 1-2 cores
RAM: 1-2GB
Almacenamiento: 10GB
```

## Despliegue Manual con Docker

### 1. Clonar Repositorio
```bash
git clone <repository-url>
cd chia-tramites
```

### 2. Configurar Variables de Entorno
```bash
cp .env.example .env.local
# Editar .env.local con las variables correctas
```

### 3. Construir y Ejecutar
```bash
# Construcción
docker-compose build

# Ejecución
docker-compose up -d

# Verificar estado
docker-compose ps
docker-compose logs -f app
```

### 4. Configurar Proxy Reverso (Nginx)

```nginx
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    location /api/health {
        proxy_pass http://localhost:3000/api/health;
        access_log off;
    }
}
```

## Monitoreo y Mantenimiento

### Health Checks
- **Endpoint**: `/api/health`
- **Intervalo**: 30 segundos
- **Timeout**: 10 segundos
- **Reintentos**: 3

### Logs
```bash
# Ver logs en tiempo real
docker-compose logs -f app

# Ver logs específicos
docker-compose logs --tail=100 app
```

### Backup
```bash
# Backup de configuración
tar -czf backup-config-$(date +%Y%m%d).tar.gz .env.local docker-compose.yml

# Backup de logs (opcional)
docker-compose logs app > logs-backup-$(date +%Y%m%d).log
```

### Actualizaciones
```bash
# Actualizar código
git pull origin main

# Reconstruir y reiniciar
docker-compose build --no-cache
docker-compose up -d

# Verificar
docker-compose ps
curl -f http://localhost:3000/api/health
```

## Solución de Problemas

### Problemas Comunes

1. **Error de conexión a Supabase**
   - Verificar variables NEXT_PUBLIC_SUPABASE_URL y keys
   - Comprobar conectividad de red
   - Revisar logs: `docker-compose logs app`

2. **Error 500 en la aplicación**
   - Verificar NEXTAUTH_SECRET está configurado
   - Comprobar que todas las variables de entorno están presentes
   - Revisar logs de la aplicación

3. **Problemas de SSL/HTTPS**
   - Verificar configuración del proxy reverso
   - Comprobar certificados SSL
   - Verificar NEXTAUTH_URL incluye https://

### Comandos de Diagnóstico
```bash
# Estado de contenedores
docker-compose ps

# Uso de recursos
docker stats

# Health check manual
curl -f http://localhost:3000/api/health

# Logs detallados
docker-compose logs --tail=50 app
```

## Seguridad

### Recomendaciones
1. **Firewall**: Solo puertos 80, 443 y SSH abiertos
2. **Updates**: Mantener sistema operativo actualizado
3. **Backups**: Backup regular de configuración
4. **Monitoreo**: Configurar alertas de disponibilidad
5. **SSL**: Usar certificados válidos y actualizados

### Variables Sensibles
- Nunca commitear archivos .env al repositorio
- Usar secretos seguros para NEXTAUTH_SECRET
- Rotar keys de Supabase periódicamente
- Limitar acceso SSH al servidor

## Contacto y Soporte

Para problemas de despliegue o configuración:
- **Email**: <EMAIL>
- **Documentación**: Ver README.md del proyecto
- **Logs**: Siempre incluir logs relevantes en reportes de problemas
