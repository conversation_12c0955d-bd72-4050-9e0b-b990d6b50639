'use client'

import React, { useState, useEffect } from 'react'
import { Building2, Clock, DollarSign, Monitor, FileText, X, ChevronDown } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { cn } from '@/lib/utils'

interface Dependency {
  id: string
  name: string
  acronym?: string
}

interface Subdependency {
  id: string
  name: string
  dependency_id: string
}

interface SmartFiltersProps {
  dependencies: Dependency[]
  subdependencies: Subdependency[]
  onFiltersChange: (filters: FilterState) => void
  className?: string
  initialFilters?: Partial<FilterState>
}

export interface FilterState {
  dependency?: string
  subdependency?: string
  modality?: 'presencial' | 'virtual' | 'mixto'
  responseTime?: 'inmediato' | '1-5-dias' | 'mas-5-dias'
  cost?: 'gratuito' | 'con-costo'
  procedureType?: 'TRAMITE' | 'OPA'
}

const MODALITY_OPTIONS = [
  { value: 'presencial', label: 'Presencial', icon: Building2, color: 'bg-blue-100 text-blue-800' },
  { value: 'virtual', label: 'Virtual', icon: Monitor, color: 'bg-green-100 text-green-800' },
  { value: 'mixto', label: 'Mixto', icon: FileText, color: 'bg-purple-100 text-purple-800' }
] as const

const RESPONSE_TIME_OPTIONS = [
  { value: 'inmediato', label: 'Inmediato', icon: Clock, color: 'bg-red-100 text-red-800' },
  { value: '1-5-dias', label: '1-5 días', icon: Clock, color: 'bg-yellow-100 text-yellow-800' },
  { value: 'mas-5-dias', label: 'Más de 5 días', icon: Clock, color: 'bg-gray-100 text-gray-800' }
] as const

const COST_OPTIONS = [
  { value: 'gratuito', label: 'Gratuito', icon: DollarSign, color: 'bg-green-100 text-green-800' },
  { value: 'con-costo', label: 'Con costo', icon: DollarSign, color: 'bg-orange-100 text-orange-800' }
] as const

const PROCEDURE_TYPE_OPTIONS = [
  { value: 'TRAMITE', label: 'Trámites', icon: FileText, color: 'bg-chia-blue-100 text-chia-blue-800' },
  { value: 'OPA', label: 'OPAs', icon: Building2, color: 'bg-chia-green-100 text-chia-green-800' }
] as const

export function SmartFilters({
  dependencies,
  subdependencies,
  onFiltersChange,
  className,
  initialFilters = {}
}: SmartFiltersProps) {
  const [filters, setFilters] = useState<FilterState>(initialFilters)
  const [isExpanded, setIsExpanded] = useState(true) // Siempre expandido por defecto
  const [activeFiltersCount, setActiveFiltersCount] = useState(0)

  // Calcular número de filtros activos
  useEffect(() => {
    const count = Object.values(filters).filter(value => value !== undefined && value !== '').length
    setActiveFiltersCount(count)
  }, [filters])

  // Notificar cambios de filtros
  useEffect(() => {
    onFiltersChange(filters)
  }, [filters, onFiltersChange])

  const updateFilter = (key: keyof FilterState, value: string | undefined) => {
    setFilters(prev => {
      const newFilters = {
        ...prev,
        [key]: value === 'all' ? undefined : value
      }

      // Si se cambia la dependencia, limpiar subdependencia
      if (key === 'dependency') {
        newFilters.subdependency = undefined
      }

      return newFilters
    })
  }

  // Obtener subdependencias filtradas por dependencia seleccionada
  const filteredSubdependencies = filters.dependency
    ? subdependencies.filter(sub => sub.dependency_id === filters.dependency)
    : []

  const clearAllFilters = () => {
    setFilters({})
  }

  const removeFilter = (key: keyof FilterState) => {
    setFilters(prev => {
      const newFilters = { ...prev }
      delete newFilters[key]
      return newFilters
    })
  }

  const getFilterLabel = (key: keyof FilterState, value: string): string => {
    switch (key) {
      case 'dependency':
        return dependencies.find(d => d.id === value)?.name || value
      case 'subdependency':
        return subdependencies.find(s => s.id === value)?.name || value
      case 'modality':
        return MODALITY_OPTIONS.find(o => o.value === value)?.label || value
      case 'responseTime':
        return RESPONSE_TIME_OPTIONS.find(o => o.value === value)?.label || value
      case 'cost':
        return COST_OPTIONS.find(o => o.value === value)?.label || value
      case 'procedureType':
        return PROCEDURE_TYPE_OPTIONS.find(o => o.value === value)?.label || value
      default:
        return value
    }
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* Filter Header - Always Visible */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <span className="text-lg font-semibold text-gray-900">Filtros</span>
          {activeFiltersCount > 0 && (
            <Badge variant="secondary" className="ml-2">
              {activeFiltersCount}
            </Badge>
          )}
        </div>

        {activeFiltersCount > 0 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={clearAllFilters}
            className="text-gray-500 hover:text-gray-700"
          >
            Limpiar filtros
          </Button>
        )}
      </div>

      {/* Active Filters Pills */}
      {activeFiltersCount > 0 && (
        <div className="flex flex-wrap gap-2">
          {Object.entries(filters).map(([key, value]) => {
            if (!value) return null
            return (
              <Badge
                key={key}
                variant="secondary"
                className="flex items-center space-x-1 pr-1"
              >
                <span>{getFilterLabel(key as keyof FilterState, value)}</span>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-4 w-4 p-0 hover:bg-transparent"
                  onClick={() => removeFilter(key as keyof FilterState)}
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            )
          })}
        </div>
      )}

      {/* Filter Controls - Always Visible */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4 p-4 bg-gray-50 rounded-lg border">
        {/* Dependencia */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700 flex items-center">
            <Building2 className="h-4 w-4 mr-1 text-chia-blue-600" />
            Dependencia
          </label>
          <Select
            value={filters.dependency || 'all'}
            onValueChange={(value) => updateFilter('dependency', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Seleccionar dependencia" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Todas las dependencias</SelectItem>
              {dependencies.map((dep) => (
                <SelectItem key={dep.id} value={dep.id}>
                  {dep.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Subdependencia */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700 flex items-center">
            <Building2 className="h-4 w-4 mr-1 text-chia-blue-400" />
            Subdependencia
          </label>
          <Select
            value={filters.subdependency || 'all'}
            onValueChange={(value) => updateFilter('subdependency', value)}
            disabled={!filters.dependency || filteredSubdependencies.length === 0}
          >
            <SelectTrigger>
              <SelectValue placeholder={
                !filters.dependency
                  ? "Selecciona una dependencia primero"
                  : filteredSubdependencies.length === 0
                    ? "Sin subdependencias"
                    : "Seleccionar subdependencia"
              } />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Todas las subdependencias</SelectItem>
              {filteredSubdependencies.map((subdep) => (
                <SelectItem key={subdep.id} value={subdep.id}>
                  {subdep.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

          {/* Modalidad */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700 flex items-center">
              <Monitor className="h-4 w-4 mr-1 text-chia-green-600" />
              Modalidad
            </label>
            <Select
              value={filters.modality || 'all'}
              onValueChange={(value) => updateFilter('modality', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="¿Cómo prefieres hacerlo?" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Cualquier modalidad</SelectItem>
                {MODALITY_OPTIONS.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    <div className="flex items-center">
                      <option.icon className="h-4 w-4 mr-2" />
                      {option.label}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Tiempo de Respuesta */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700 flex items-center">
              <Clock className="h-4 w-4 mr-1 text-yellow-600" />
              Tiempo de Respuesta
            </label>
            <Select
              value={filters.responseTime || 'all'}
              onValueChange={(value) => updateFilter('responseTime', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="¿Qué tan urgente?" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Cualquier tiempo</SelectItem>
                {RESPONSE_TIME_OPTIONS.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    <div className="flex items-center">
                      <option.icon className="h-4 w-4 mr-2" />
                      {option.label}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Costo */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700 flex items-center">
              <DollarSign className="h-4 w-4 mr-1 text-green-600" />
              Costo
            </label>
            <Select
              value={filters.cost || 'all'}
              onValueChange={(value) => updateFilter('cost', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Seleccionar costo" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Cualquier costo</SelectItem>
                {COST_OPTIONS.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    <div className="flex items-center">
                      <option.icon className="h-4 w-4 mr-2" />
                      {option.label}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Tipo de Procedimiento */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700 flex items-center">
              <FileText className="h-4 w-4 mr-1 text-purple-600" />
              Tipo
            </label>
            <Select
              value={filters.procedureType || 'all'}
              onValueChange={(value) => updateFilter('procedureType', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Seleccionar tipo" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos los tipos</SelectItem>
                {PROCEDURE_TYPE_OPTIONS.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    <div className="flex items-center">
                      <option.icon className="h-4 w-4 mr-2" />
                      {option.label}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
      </div>
    </div>
  )
}
