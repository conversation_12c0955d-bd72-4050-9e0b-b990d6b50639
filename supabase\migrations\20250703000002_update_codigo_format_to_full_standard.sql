-- Migración para actualizar formato de códigos a XXX-XXX-XXX estándar
-- Fecha: 2025-07-03
-- Descripción: Modifica códigos de trámites de XXX-XXX a XXX-XXX-XXX para alinearse con formato de OPAs

-- 0. Eliminar constraints existentes que interfieren con la migración
ALTER TABLE procedures DROP CONSTRAINT IF EXISTS check_codigo_tramite_format;
ALTER TABLE procedures DROP CONSTRAINT IF EXISTS procedures_codigo_tramite_format_check;

-- 1. <PERSON>ro, crear función auxiliar para obtener código de dependencia
CREATE OR REPLACE FUNCTION get_dependency_code_from_subdependency(p_subdependency_id UUID)
RETURNS TEXT AS $$
DECLARE
    dep_code TEXT;
BEGIN
    SELECT d.code INTO dep_code
    FROM subdependencies s
    JOIN dependencies d ON s.dependency_id = d.id
    WHERE s.id = p_subdependency_id;
    
    RETURN COALESCE(dep_code, '000');
END;
$$ LANGUAGE plpgsql;

-- 2. Actualizar función generate_consecutive_code para formato XXX-XXX-XXX
CREATE OR REPLACE FUNCTION generate_consecutive_code(
    p_subdependency_id UUID,
    p_table_name TEXT DEFAULT 'procedures'
) RETURNS TEXT AS $$
DECLARE
    dep_code TEXT;
    subdep_code TEXT;
    next_consecutive INTEGER;
    new_code TEXT;
BEGIN
    -- Obtener código de dependencia y subdependencia
    SELECT d.code, s.code INTO dep_code, subdep_code
    FROM subdependencies s
    JOIN dependencies d ON s.dependency_id = d.id
    WHERE s.id = p_subdependency_id;
    
    -- Si no se encuentra, usar valores por defecto
    IF dep_code IS NULL OR subdep_code IS NULL THEN
        dep_code := '000';
        subdep_code := '000';
    END IF;
    
    -- Obtener el siguiente consecutivo para esta subdependencia
    IF p_table_name = 'procedures' THEN
        SELECT COALESCE(MAX(
            CAST(SPLIT_PART(codigo_tramite, '-', 3) AS INTEGER)
        ), 0) + 1 INTO next_consecutive
        FROM procedures 
        WHERE subdependency_id = p_subdependency_id 
        AND codigo_tramite IS NOT NULL;
    ELSE
        SELECT COALESCE(MAX(
            CAST(SPLIT_PART(code, '-', 3) AS INTEGER)
        ), 0) + 1 INTO next_consecutive
        FROM opas 
        WHERE subdependency_id = p_subdependency_id 
        AND code IS NOT NULL;
    END IF;
    
    -- Formatear código como XXX-XXX-XXX
    new_code := LPAD(dep_code, 3, '0') || '-' || 
                LPAD(subdep_code, 3, '0') || '-' || 
                LPAD(next_consecutive::TEXT, 3, '0');
    
    RETURN new_code;
END;
$$ LANGUAGE plpgsql;

-- 3. Función para migrar códigos existentes de trámites al nuevo formato
CREATE OR REPLACE FUNCTION migrate_tramite_codes_to_full_format()
RETURNS VOID AS $$
DECLARE
    procedure_record RECORD;
    dep_code TEXT;
    new_code TEXT;
BEGIN
    -- Iterar sobre todos los procedimientos con códigos en formato XXX-XXX
    FOR procedure_record IN 
        SELECT id, codigo_tramite, subdependency_id
        FROM procedures 
        WHERE codigo_tramite IS NOT NULL 
        AND codigo_tramite ~ '^[0-9]{3}-[0-9]{3}$'
    LOOP
        -- Obtener código de dependencia
        SELECT get_dependency_code_from_subdependency(procedure_record.subdependency_id) 
        INTO dep_code;
        
        -- Construir nuevo código en formato XXX-XXX-XXX
        new_code := LPAD(dep_code, 3, '0') || '-' || procedure_record.codigo_tramite;
        
        -- Actualizar el registro
        UPDATE procedures 
        SET codigo_tramite = new_code
        WHERE id = procedure_record.id;
        
        RAISE NOTICE 'Migrado código: % -> %', procedure_record.codigo_tramite, new_code;
    END LOOP;
    
    RAISE NOTICE 'Migración de códigos de trámites completada';
END;
$$ LANGUAGE plpgsql;

-- 4. Ejecutar migración de códigos existentes ANTES de agregar constraint
SELECT migrate_tramite_codes_to_full_format();

-- 5. Actualizar constraint de formato para XXX-XXX-XXX (después de migración)
ALTER TABLE procedures DROP CONSTRAINT IF EXISTS procedures_codigo_tramite_format_check;
ALTER TABLE procedures ADD CONSTRAINT procedures_codigo_tramite_format_check
    CHECK (codigo_tramite ~ '^[0-9]{3}-[0-9]{3}-[0-9]{3}$');

-- 6. Actualizar vista unificada para el nuevo formato
DROP VIEW IF EXISTS vista_codigos_procedimientos;
CREATE VIEW vista_codigos_procedimientos AS
SELECT 
    p.id,
    p.name,
    p.codigo_tramite as codigo,
    'TRAMITE' as tipo,
    d.code as dep_code,
    d.name as dep_name,
    s.code as subdep_code,
    s.name as subdep_name
FROM procedures p
LEFT JOIN subdependencies s ON p.subdependency_id = s.id
LEFT JOIN dependencies d ON s.dependency_id = d.id
WHERE p.is_active = true

UNION ALL

SELECT 
    o.id,
    o.name,
    o.code as codigo,
    'OPA' as tipo,
    d.code as dep_code,
    d.name as dep_name,
    s.code as subdep_code,
    s.name as subdep_name
FROM opas o
LEFT JOIN subdependencies s ON o.subdependency_id = s.id
LEFT JOIN dependencies d ON s.dependency_id = d.id
WHERE o.is_active = true;

-- 7. Actualizar trigger para usar nueva función
DROP TRIGGER IF EXISTS trigger_generate_codigo_tramite ON procedures;
CREATE TRIGGER trigger_generate_codigo_tramite
    BEFORE INSERT ON procedures
    FOR EACH ROW
    WHEN (NEW.codigo_tramite IS NULL AND NEW.subdependency_id IS NOT NULL)
    EXECUTE FUNCTION set_codigo_tramite();

-- 8. Función auxiliar para el trigger (actualizada)
CREATE OR REPLACE FUNCTION set_codigo_tramite()
RETURNS TRIGGER AS $$
BEGIN
    NEW.codigo_tramite := generate_consecutive_code(NEW.subdependency_id, 'procedures');
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 9. Limpiar función auxiliar temporal
DROP FUNCTION IF EXISTS migrate_tramite_codes_to_full_format();

-- 10. Comentarios para documentación
COMMENT ON FUNCTION generate_consecutive_code(UUID, TEXT) IS 
'Genera códigos consecutivos en formato XXX-XXX-XXX donde XXX es dependencia, XXX subdependencia, XXX consecutivo';

COMMENT ON VIEW vista_codigos_procedimientos IS 
'Vista unificada de códigos de procedimientos (trámites y OPAs) en formato estándar XXX-XXX-XXX';

COMMENT ON CONSTRAINT procedures_codigo_tramite_format_check ON procedures IS 
'Valida que codigo_tramite tenga formato XXX-XXX-XXX (dependencia-subdependencia-consecutivo)';
