import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { ErrorBoundary } from '@/components/error/ErrorBoundary'
import { SkipLink } from '@/lib/accessibility'
import { MobileOptimized } from '@/lib/mobile'

const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
  preload: true,
  variable: '--font-inter'
})

export const metadata: Metadata = {
  title: {
    default: 'Sistema de Atención Ciudadana - Municipio de Chía',
    template: '%s | Municipio de Chía'
  },
  description: 'Plataforma digital para trámites y servicios municipales del Municipio de Chía, Cundinamarca. Accede a servicios gubernamentales de forma rápida y segura.',
  keywords: 'Chía, municipio, trámites, servicios, ciudadanos, gobierno digital, OPAs, procedimientos, documentos',
  authors: [{ name: 'Municipio de Chía', url: 'https://chia.gov.co' }],
  creator: '<PERSON><PERSON><PERSON><PERSON> Chía',
  publisher: '<PERSON>nic<PERSON><PERSON> de Chía',
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    title: 'Sistema de Atención Ciudadana - Municipio de Chía',
    description: 'Plataforma digital para trámites y servicios municipales',
    type: 'website',
    locale: 'es_CO',
    siteName: 'Municipio de Chía',
    url: 'https://tramites.chia.gov.co',
    images: [
      {
        url: '/images/og-image.png',
        width: 1200,
        height: 630,
        alt: 'Sistema de Atención Ciudadana - Municipio de Chía',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Sistema de Atención Ciudadana - Municipio de Chía',
    description: 'Plataforma digital para trámites y servicios municipales',
    images: ['/images/twitter-image.png'],
    creator: '@ChiaOficial',
  },
  icons: {
    icon: [
      { url: '/favicon-16x16.png', sizes: '16x16', type: 'image/png' },
      { url: '/favicon-32x32.png', sizes: '32x32', type: 'image/png' },
    ],
    apple: [
      { url: '/apple-touch-icon.png', sizes: '180x180', type: 'image/png' },
    ],
    other: [
      { rel: 'mask-icon', url: '/safari-pinned-tab.svg', color: '#2563eb' },
    ],
  },
  manifest: '/manifest.json',
  metadataBase: new URL('https://tramites.chia.gov.co'),
  alternates: {
    canonical: '/',
    languages: {
      'es-CO': '/',
    },
  },
  category: 'government',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="es" className={inter.variable}>
      <head>
        {/* Preconnect to external domains for performance */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />

        {/* DNS prefetch for Supabase */}
        <link rel="dns-prefetch" href="https://supabase.co" />

        {/* Viewport meta with safe area support */}
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1, viewport-fit=cover"
        />

        {/* Theme color for mobile browsers */}
        <meta name="theme-color" content="#2563eb" />
        <meta name="msapplication-TileColor" content="#2563eb" />

        {/* Security headers */}
        <meta httpEquiv="X-Content-Type-Options" content="nosniff" />
        <meta httpEquiv="X-Frame-Options" content="DENY" />
        <meta httpEquiv="X-XSS-Protection" content="1; mode=block" />

        {/* Performance hints */}
        <meta httpEquiv="Accept-CH" content="DPR, Viewport-Width, Width" />
      </head>
      <body className={`${inter.className} font-sans antialiased`}>
        {/* Skip link for keyboard navigation */}
        <SkipLink href="#main-content">
          Saltar al contenido principal
        </SkipLink>

        {/* Error boundary for the entire application */}
        <ErrorBoundary level="critical" showDetails={process.env.NODE_ENV === 'development'}>
          {/* Mobile optimization wrapper */}
          <MobileOptimized>
            {/* Main content with proper landmark */}
            <main id="main-content" role="main">
              {children}
            </main>
          </MobileOptimized>
        </ErrorBoundary>

        {/* Performance monitoring script */}
        {process.env.NODE_ENV === 'production' && (
          <script
            dangerouslySetInnerHTML={{
              __html: `
                // Core Web Vitals monitoring
                if ('PerformanceObserver' in window) {
                  const observer = new PerformanceObserver((list) => {
                    for (const entry of list.getEntries()) {
                      if (entry.entryType === 'largest-contentful-paint') {
                        console.log('LCP:', entry.startTime);
                      }
                      if (entry.entryType === 'first-input') {
                        console.log('FID:', entry.processingStart - entry.startTime);
                      }
                      if (entry.entryType === 'layout-shift' && !entry.hadRecentInput) {
                        console.log('CLS:', entry.value);
                      }
                    }
                  });

                  observer.observe({ entryTypes: ['largest-contentful-paint', 'first-input', 'layout-shift'] });
                }
              `,
            }}
          />
        )}
      </body>
    </html>
  )
}
