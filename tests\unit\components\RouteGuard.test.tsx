import { render, screen, waitFor } from '@testing-library/react'
import { RouteGuard } from '@/components/navigation/RouteGuard'
import { useAuth } from '@/hooks/useAuth'
import { useRole } from '@/hooks/useRole'
import { useRouter, usePathname } from 'next/navigation'

// Mock dependencies
jest.mock('@/hooks/useAuth')
jest.mock('@/hooks/useRole')
jest.mock('next/navigation')

const mockUseAuth = useAuth as jest.MockedFunction<typeof useAuth>
const mockUseRole = useRole as jest.MockedFunction<typeof useRole>
const mockUseRouter = useRouter as jest.MockedFunction<typeof useRouter>
const mockUsePathname = usePathname as jest.MockedFunction<typeof usePathname>

const mockRouter = {
  push: jest.fn(),
  replace: jest.fn(),
  refresh: jest.fn(),
}

const TestComponent = () => <div>Protected Content</div>

describe('RouteGuard Component', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    mockUseRouter.mockReturnValue(mockRouter as any)
    mockUsePathname.mockReturnValue('/dashboard')
  })

  describe('Authentication Checks', () => {
    it('should render children when user is authenticated and has required role', async () => {
      mockUseAuth.mockReturnValue({
        user: { id: 'user-123', email: '<EMAIL>' },
        profile: { 
          id: 'user-123', 
          role: 'ciudadano',
          full_name: 'Test User'
        },
        loading: false,
      } as any)

      mockUseRole.mockReturnValue({
        role: 'ciudadano',
        hasRole: jest.fn((roles) => roles.includes('ciudadano')),
      } as any)

      render(
        <RouteGuard requiredRoles={['ciudadano']}>
          <TestComponent />
        </RouteGuard>
      )

      expect(screen.getByText('Protected Content')).toBeInTheDocument()
    })

    it('should redirect to login when user is not authenticated', async () => {
      mockUseAuth.mockReturnValue({
        user: null,
        profile: null,
        loading: false,
      } as any)

      mockUseRole.mockReturnValue({
        role: null,
        hasRole: jest.fn(() => false),
      } as any)

      render(
        <RouteGuard requiredRoles={['ciudadano']}>
          <TestComponent />
        </RouteGuard>
      )

      await waitFor(() => {
        expect(mockRouter.push).toHaveBeenCalledWith('/auth/login')
      })
    })

    it('should show loading state when auth is loading', () => {
      mockUseAuth.mockReturnValue({
        user: null,
        profile: null,
        loading: true,
      } as any)

      mockUseRole.mockReturnValue({
        role: null,
        hasRole: jest.fn(() => false),
      } as any)

      render(
        <RouteGuard requiredRoles={['ciudadano']}>
          <TestComponent />
        </RouteGuard>
      )

      expect(screen.getByTestId('route-guard-loading')).toBeInTheDocument()
    })
  })

  describe('Role-based Access Control', () => {
    it('should allow access when user has required role', () => {
      mockUseAuth.mockReturnValue({
        user: { id: 'admin-123', email: '<EMAIL>' },
        profile: { 
          id: 'admin-123', 
          role: 'admin',
          full_name: 'Admin User'
        },
        loading: false,
      } as any)

      mockUseRole.mockReturnValue({
        role: 'admin',
        hasRole: jest.fn((roles) => roles.includes('admin')),
      } as any)

      render(
        <RouteGuard requiredRoles={['admin']}>
          <TestComponent />
        </RouteGuard>
      )

      expect(screen.getByText('Protected Content')).toBeInTheDocument()
    })

    it('should deny access when user does not have required role', async () => {
      mockUseAuth.mockReturnValue({
        user: { id: 'user-123', email: '<EMAIL>' },
        profile: { 
          id: 'user-123', 
          role: 'ciudadano',
          full_name: 'Test User'
        },
        loading: false,
      } as any)

      mockUseRole.mockReturnValue({
        role: 'ciudadano',
        hasRole: jest.fn((roles) => roles.includes('ciudadano')),
      } as any)

      render(
        <RouteGuard requiredRoles={['admin']}>
          <TestComponent />
        </RouteGuard>
      )

      expect(screen.getByText('Acceso Denegado')).toBeInTheDocument()
      expect(screen.getByText('No tienes permisos para acceder a esta página')).toBeInTheDocument()
    })

    it('should allow access when user has one of multiple required roles', () => {
      mockUseAuth.mockReturnValue({
        user: { id: 'user-123', email: '<EMAIL>' },
        profile: { 
          id: 'user-123', 
          role: 'ciudadano',
          full_name: 'Test User'
        },
        loading: false,
      } as any)

      mockUseRole.mockReturnValue({
        role: 'ciudadano',
        hasRole: jest.fn((roles) => roles.includes('ciudadano')),
      } as any)

      render(
        <RouteGuard requiredRoles={['ciudadano', 'admin']}>
          <TestComponent />
        </RouteGuard>
      )

      expect(screen.getByText('Protected Content')).toBeInTheDocument()
    })
  })

  describe('Permission-based Access Control', () => {
    it('should allow access when user has required permissions', () => {
      mockUseAuth.mockReturnValue({
        user: { id: 'admin-123', email: '<EMAIL>' },
        profile: { 
          id: 'admin-123', 
          role: 'admin',
          full_name: 'Admin User',
          permissions: ['canViewAdminDashboard', 'canManageUsers']
        },
        loading: false,
      } as any)

      mockUseRole.mockReturnValue({
        role: 'admin',
        hasRole: jest.fn((roles) => roles.includes('admin')),
      } as any)

      render(
        <RouteGuard 
          requiredRoles={['admin']} 
          requiredPermissions={['canViewAdminDashboard']}
        >
          <TestComponent />
        </RouteGuard>
      )

      expect(screen.getByText('Protected Content')).toBeInTheDocument()
    })

    it('should deny access when user lacks required permissions', () => {
      mockUseAuth.mockReturnValue({
        user: { id: 'admin-123', email: '<EMAIL>' },
        profile: { 
          id: 'admin-123', 
          role: 'admin',
          full_name: 'Admin User',
          permissions: ['canViewReports']
        },
        loading: false,
      } as any)

      mockUseRole.mockReturnValue({
        role: 'admin',
        hasRole: jest.fn((roles) => roles.includes('admin')),
      } as any)

      render(
        <RouteGuard 
          requiredRoles={['admin']} 
          requiredPermissions={['canManageUsers']}
        >
          <TestComponent />
        </RouteGuard>
      )

      expect(screen.getByText('Permisos Insuficientes')).toBeInTheDocument()
    })
  })

  describe('Route-specific Configuration', () => {
    it('should apply route-specific access rules for admin routes', async () => {
      mockUsePathname.mockReturnValue('/admin')
      
      mockUseAuth.mockReturnValue({
        user: { id: 'user-123', email: '<EMAIL>' },
        profile: { 
          id: 'user-123', 
          role: 'ciudadano',
          full_name: 'Test User'
        },
        loading: false,
      } as any)

      mockUseRole.mockReturnValue({
        role: 'ciudadano',
        hasRole: jest.fn((roles) => roles.includes('ciudadano')),
      } as any)

      render(
        <RouteGuard>
          <TestComponent />
        </RouteGuard>
      )

      expect(screen.getByText('Acceso Denegado')).toBeInTheDocument()
    })

    it('should allow access to dashboard for all authenticated users', () => {
      mockUsePathname.mockReturnValue('/dashboard')
      
      mockUseAuth.mockReturnValue({
        user: { id: 'user-123', email: '<EMAIL>' },
        profile: { 
          id: 'user-123', 
          role: 'ciudadano',
          full_name: 'Test User'
        },
        loading: false,
      } as any)

      mockUseRole.mockReturnValue({
        role: 'ciudadano',
        hasRole: jest.fn(() => true),
      } as any)

      render(
        <RouteGuard>
          <TestComponent />
        </RouteGuard>
      )

      expect(screen.getByText('Protected Content')).toBeInTheDocument()
    })
  })

  describe('Custom Fallback URLs', () => {
    it('should redirect to custom fallback URL when provided', async () => {
      mockUseAuth.mockReturnValue({
        user: null,
        profile: null,
        loading: false,
      } as any)

      mockUseRole.mockReturnValue({
        role: null,
        hasRole: jest.fn(() => false),
      } as any)

      render(
        <RouteGuard 
          requiredRoles={['admin']} 
          fallbackUrl="/custom-login"
        >
          <TestComponent />
        </RouteGuard>
      )

      await waitFor(() => {
        expect(mockRouter.push).toHaveBeenCalledWith('/custom-login')
      })
    })
  })

  describe('Error Handling', () => {
    it('should handle auth errors gracefully', () => {
      mockUseAuth.mockReturnValue({
        user: null,
        profile: null,
        loading: false,
        error: 'Authentication failed',
      } as any)

      mockUseRole.mockReturnValue({
        role: null,
        hasRole: jest.fn(() => false),
      } as any)

      render(
        <RouteGuard requiredRoles={['ciudadano']}>
          <TestComponent />
        </RouteGuard>
      )

      expect(screen.getByText('Error de Autenticación')).toBeInTheDocument()
    })
  })
})
