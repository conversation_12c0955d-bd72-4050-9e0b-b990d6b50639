'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { 
  Users, 
  FileText, 
  Building, 
  BarChart3, 
  Settings, 
  Clock,
  CheckCircle,
  AlertCircle,
  TrendingUp,
  Activity,
  Shield,
  Database
} from 'lucide-react'

// Import our auth components
import { useAuth, useRole, usePermissions } from '@/hooks'
import { 
  ShowForSuperAdmin, 
  ShowForAdmin,
  PermissionButton,
  UserRoleDisplay,
  ConditionalContent
} from '@/components/auth'

// Import admin components
import { UserManagement } from './UserManagement'
import { ProcedureManagement } from './ProcedureManagement'
import { DependencyManagement } from './DependencyManagement'
import { AdminReports } from './AdminReports'
import { SystemSettings } from './SystemSettings'

interface AdminDashboardProps {
  user: any
  profile: any
  stats: {
    totalUsers: number
    totalProcedures: number
    pendingProcedures: number
    completedProcedures: number
    totalDependencies: number
    recentActivity: any[]
  }
}

export function AdminDashboard({ user, profile, stats }: AdminDashboardProps) {
  const [activeTab, setActiveTab] = useState('overview')
  const { isSuperAdmin, isAdmin } = useRole()
  const { canManageAllUsers, canManageDependencies, canViewSystemSettings } = usePermissions()

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <ConditionalContent
                adminContent={
                  <div>
                    <h1 className="text-3xl font-bold text-gray-900">
                      Panel de Administración
                    </h1>
                    <p className="text-gray-600 mt-1">
                      Gestión de {profile?.dependency?.name || 'Dependencia'}
                    </p>
                  </div>
                }
                superAdminContent={
                  <div>
                    <h1 className="text-3xl font-bold text-gray-900">
                      Panel de Super Administrador
                    </h1>
                    <p className="text-gray-600 mt-1">
                      Control total del sistema municipal
                    </p>
                  </div>
                }
              >
                <div>
                  <h1 className="text-3xl font-bold text-gray-900">
                    Panel Administrativo
                  </h1>
                  <p className="text-gray-600 mt-1">
                    Sistema de gestión
                  </p>
                </div>
              </ConditionalContent>
            </div>
            
            <div className="flex items-center space-x-4">
              <UserRoleDisplay showIcon={true} showDependency={true} />
              <Badge variant="outline" className="text-sm">
                {profile?.full_name}
              </Badge>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-2 lg:grid-cols-6">
            <TabsTrigger value="overview">Resumen</TabsTrigger>
            <TabsTrigger value="users">Usuarios</TabsTrigger>
            <TabsTrigger value="procedures">Trámites</TabsTrigger>
            <ShowForSuperAdmin>
              <TabsTrigger value="dependencies">Dependencias</TabsTrigger>
            </ShowForSuperAdmin>
            <TabsTrigger value="reports">Reportes</TabsTrigger>
            <ShowForSuperAdmin>
              <TabsTrigger value="settings">Sistema</TabsTrigger>
            </ShowForSuperAdmin>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            {/* Statistics Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Usuarios</CardTitle>
                  <Users className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.totalUsers}</div>
                  <p className="text-xs text-muted-foreground">
                    <ConditionalContent
                      adminContent="En tu dependencia"
                      superAdminContent="En todo el sistema"
                    >
                      Usuarios registrados
                    </ConditionalContent>
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Trámites</CardTitle>
                  <FileText className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.totalProcedures}</div>
                  <p className="text-xs text-muted-foreground">
                    Trámites procesados
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">En Proceso</CardTitle>
                  <Clock className="h-4 w-4 text-orange-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-orange-600">{stats.pendingProcedures}</div>
                  <p className="text-xs text-muted-foreground">
                    Esperando atención
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Completados</CardTitle>
                  <CheckCircle className="h-4 w-4 text-green-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-green-600">{stats.completedProcedures}</div>
                  <p className="text-xs text-muted-foreground">
                    Trámites finalizados
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Quick Actions */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Acciones Rápidas</CardTitle>
                  <CardDescription>
                    Acceso directo a funciones administrativas
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <PermissionButton
                    requiredPermissions={['canViewDependencyUsers']}
                    variant="outline"
                    className="w-full justify-start"
                    onClick={() => setActiveTab('users')}
                  >
                    <Users className="h-4 w-4 mr-2" />
                    Gestionar Usuarios
                  </PermissionButton>
                  
                  <PermissionButton
                    requiredPermissions={['canViewDependencyProcedures']}
                    variant="outline"
                    className="w-full justify-start"
                    onClick={() => setActiveTab('procedures')}
                  >
                    <FileText className="h-4 w-4 mr-2" />
                    Gestionar Trámites
                  </PermissionButton>
                  
                  <PermissionButton
                    requiredPermissions={['canViewDependencyReports']}
                    variant="outline"
                    className="w-full justify-start"
                    onClick={() => setActiveTab('reports')}
                  >
                    <BarChart3 className="h-4 w-4 mr-2" />
                    Ver Reportes
                  </PermissionButton>
                  
                  <ShowForSuperAdmin>
                    <PermissionButton
                      requiredPermissions={['canManageDependencies']}
                      variant="outline"
                      className="w-full justify-start"
                      onClick={() => setActiveTab('dependencies')}
                    >
                      <Building className="h-4 w-4 mr-2" />
                      Gestionar Dependencias
                    </PermissionButton>
                  </ShowForSuperAdmin>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Actividad Reciente</CardTitle>
                  <CardDescription>
                    Últimos trámites procesados
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {stats.recentActivity.length > 0 ? (
                      stats.recentActivity.slice(0, 5).map((activity, index) => (
                        <div key={activity.id || index} className="flex items-center space-x-4">
                          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                          <div className="flex-1">
                            <p className="text-sm font-medium">
                              {activity.procedure?.name || 'Trámite'}
                            </p>
                            <p className="text-xs text-gray-500">
                              {activity.citizen?.full_name || 'Usuario'} - {' '}
                              {new Date(activity.created_at).toLocaleDateString('es-CO')}
                            </p>
                          </div>
                          <Badge variant="outline" className="text-xs">
                            {activity.status_id}
                          </Badge>
                        </div>
                      ))
                    ) : (
                      <p className="text-sm text-gray-500">No hay actividad reciente</p>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Users Tab */}
          <TabsContent value="users">
            <UserManagement />
          </TabsContent>

          {/* Procedures Tab */}
          <TabsContent value="procedures">
            <ProcedureManagement />
          </TabsContent>

          {/* Dependencies Tab - Super Admin Only */}
          <ShowForSuperAdmin>
            <TabsContent value="dependencies">
              <DependencyManagement />
            </TabsContent>
          </ShowForSuperAdmin>

          {/* Reports Tab */}
          <TabsContent value="reports">
            <AdminReports />
          </TabsContent>

          {/* Settings Tab - Super Admin Only */}
          <ShowForSuperAdmin>
            <TabsContent value="settings">
              <SystemSettings />
            </TabsContent>
          </ShowForSuperAdmin>
        </Tabs>
      </div>
    </div>
  )
}
