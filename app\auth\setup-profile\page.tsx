'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { supabase } from '@/lib/supabase/client'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, User, Phone, MapPin, FileText } from 'lucide-react'
import { validateCedula, validatePhone } from '@/lib/utils'

export default function SetupProfilePage() {
  const router = useRouter()

  const [formData, setFormData] = useState({
    fullName: '',
    documentNumber: '',
    documentType: 'cedula',
    phone: '',
    city: 'Chía',
    department: 'Cundinamarca',
  })
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [user, setUser] = useState<any>(null)

  useEffect(() => {
    const checkUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      
      if (!user) {
        router.push('/auth/login')
        return
      }

      setUser(user)

      // Pre-fill form with available user data
      if (user.user_metadata?.full_name) {
        setFormData(prev => ({
          ...prev,
          fullName: user.user_metadata.full_name
        }))
      }
    }

    checkUser()
  }, [router])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
    // Clear error when user starts typing
    if (error) setError(null)
  }

  const validateForm = () => {
    if (!formData.fullName || formData.fullName.trim().length < 2) {
      setError('Por favor ingresa tu nombre completo')
      return false
    }

    if (!formData.documentNumber || !validateCedula(formData.documentNumber)) {
      setError('Por favor ingresa un número de cédula válido')
      return false
    }

    if (!formData.phone || !validatePhone(formData.phone)) {
      setError('Por favor ingresa un número de teléfono válido')
      return false
    }

    return true
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm() || !user) return

    setIsLoading(true)
    setError(null)

    try {
      // Check if document number is already in use
      const { data: existingProfile } = await supabase
        .from('profiles')
        .select('document_number')
        .eq('document_number', formData.documentNumber)
        .single()

      if (existingProfile) {
        throw new Error('Ya existe una cuenta con este número de documento')
      }

      // Get the default citizen role
      const { data: citizenRole } = await supabase
        .from('roles')
        .select('id')
        .eq('name', 'ciudadano')
        .single()

      if (!citizenRole) {
        throw new Error('Error en la configuración del sistema. Contacta al administrador.')
      }

      // Create user profile
      const { error: profileError } = await supabase
        .from('profiles')
        .insert({
          id: user.id,
          email: user.email,
          full_name: formData.fullName,
          document_number: formData.documentNumber,
          document_type: formData.documentType,
          phone: formData.phone,
          city: formData.city,
          department: formData.department,
          role_id: citizenRole.id,
          is_active: true,
        })

      if (profileError) {
        throw profileError
      }

      // Update user metadata
      await supabase.auth.updateUser({
        data: {
          full_name: formData.fullName,
          document_number: formData.documentNumber,
          phone: formData.phone,
          profile_completed: true
        }
      })

      // Redirect to dashboard
      router.push('/dashboard')
    } catch (error: any) {
      console.error('Profile setup error:', error)
      
      if (error.message.includes('duplicate key')) {
        setError('Ya existe una cuenta con estos datos')
      } else {
        setError(error.message || 'Error al configurar el perfil. Intenta de nuevo.')
      }
    } finally {
      setIsLoading(false)
    }
  }

  if (!user) {
    return (
      <div className="space-y-6">
        <div className="text-center">
          <Loader2 className="mx-auto h-8 w-8 animate-spin text-chia-blue-600" />
          <p className="mt-2 text-gray-600">Verificando sesión...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-gray-900">Completar Perfil</h2>
        <p className="mt-2 text-gray-600">
          Para acceder a todos los servicios, necesitamos algunos datos adicionales
        </p>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="fullName">Nombre Completo</Label>
          <div className="relative">
            <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              id="fullName"
              name="fullName"
              type="text"
              required
              placeholder="Tu nombre completo"
              value={formData.fullName}
              onChange={handleInputChange}
              className="pl-10"
              disabled={isLoading}
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="documentType">Tipo de Documento</Label>
            <select
              id="documentType"
              name="documentType"
              value={formData.documentType}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-chia-blue-500 focus:border-transparent"
              disabled={isLoading}
            >
              <option value="cedula">Cédula de Ciudadanía</option>
              <option value="cedula_extranjeria">Cédula de Extranjería</option>
              <option value="pasaporte">Pasaporte</option>
            </select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="documentNumber">Número de Documento</Label>
            <div className="relative">
              <FileText className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                id="documentNumber"
                name="documentNumber"
                type="text"
                required
                placeholder="12345678"
                value={formData.documentNumber}
                onChange={handleInputChange}
                className="pl-10"
                disabled={isLoading}
              />
            </div>
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="phone">Teléfono</Label>
          <div className="relative">
            <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              id="phone"
              name="phone"
              type="tel"
              required
              placeholder="3001234567"
              value={formData.phone}
              onChange={handleInputChange}
              className="pl-10"
              disabled={isLoading}
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="city">Ciudad</Label>
            <div className="relative">
              <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                id="city"
                name="city"
                type="text"
                required
                value={formData.city}
                onChange={handleInputChange}
                className="pl-10"
                disabled={isLoading}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="department">Departamento</Label>
            <Input
              id="department"
              name="department"
              type="text"
              required
              value={formData.department}
              onChange={handleInputChange}
              disabled={isLoading}
            />
          </div>
        </div>

        <Button
          type="submit"
          className="w-full bg-chia-blue-600 hover:bg-chia-blue-700"
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Configurando perfil...
            </>
          ) : (
            'Completar Configuración'
          )}
        </Button>
      </form>

      <div className="p-4 bg-blue-50 rounded-lg">
        <h3 className="text-sm font-medium text-blue-900 mb-2">
          Información importante:
        </h3>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• Estos datos son necesarios para acceder a los servicios municipales</li>
          <li>• Tu información está protegida y solo será usada para trámites oficiales</li>
          <li>• Puedes actualizar estos datos desde tu perfil en cualquier momento</li>
          <li>• El número de documento debe ser único y válido</li>
        </ul>
      </div>
    </div>
  )
}
