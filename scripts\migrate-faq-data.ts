/**
 * Script para migrar datos FAQ estáticos a Supabase
 * Ejecutar con: npx tsx scripts/migrate-faq-data.ts
 */

import { createClient } from '@supabase/supabase-js'
import type { Database } from '@/lib/database.types'

// Configuración de Supabase
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Variables de entorno de Supabase no configuradas')
  process.exit(1)
}

const supabase = createClient<Database>(supabaseUrl, supabaseServiceKey)

// Datos de categorías FAQ
const faqCategories = [
  {
    name: 'Impuestos y Tributos',
    description: 'Preguntas sobre impuestos municipales, predial, industria y comercio',
    icon: 'Receipt',
    color: 'bg-blue-500',
    display_order: 1
  },
  {
    name: 'Licencias y Permisos',
    description: 'Construcción, funcionamiento, comerciales y ambientales',
    icon: 'FileCheck',
    color: 'bg-green-500',
    display_order: 2
  },
  {
    name: 'Certificados',
    description: 'Residencia, libertad y tradición, estratificación',
    icon: 'Award',
    color: 'bg-purple-500',
    display_order: 3
  },
  {
    name: 'Servicios Públicos',
    description: 'Alumbrado público, aseo, acueducto y alcantarillado',
    icon: 'Zap',
    color: 'bg-yellow-500',
    display_order: 4
  },
  {
    name: 'Trámites Generales',
    description: 'Procedimientos administrativos y documentación',
    icon: 'FileText',
    color: 'bg-indigo-500',
    display_order: 5
  },
  {
    name: 'Pagos y Facturación',
    description: 'Métodos de pago, facturación y paz y salvos',
    icon: 'CreditCard',
    color: 'bg-red-500',
    display_order: 6
  }
]

// Datos de FAQs
const faqData = [
  // Impuestos y Tributos
  {
    question: '¿Qué es el impuesto predial y cómo se calcula?',
    answer: 'El impuesto predial unificado es un tributo municipal que grava la propiedad inmueble. Se calcula de acuerdo con el avalúo catastral del predio y el Estatuto Tributario (Acuerdo 107 de 2016). El tiempo de respuesta para consultas es de 1 hora.',
    category_name: 'Impuestos y Tributos',
    tags: ['impuesto predial', 'avalúo catastral', 'tributos', 'propiedad'],
    related_procedures: ['Impuesto predial unificado'],
    popularity: 95
  },
  {
    question: '¿Cómo funciona el impuesto de industria y comercio?',
    answer: 'El impuesto de industria y comercio y su complementario de avisos y tableros se calcula de acuerdo con los ingresos obtenidos en el año inmediatamente anterior, según el Estatuto Tributario (Acuerdo 107 de 2016). El tiempo de respuesta es de 1 día.',
    category_name: 'Impuestos y Tributos',
    tags: ['industria y comercio', 'avisos y tableros', 'ingresos', 'comercio'],
    related_procedures: ['Impuesto de industria y comercio y su complementario de avisos y tableros'],
    popularity: 85
  },
  {
    question: '¿Cuánto cuesta el impuesto de alumbrado público?',
    answer: 'Las tarifas del impuesto sobre el servicio de alumbrado público están establecidas en el Acuerdo 130 de 2017. Para predios usuarios de energía eléctrica domiciliaria es 0.5 por mil sobre el valor del impuesto predial. Para predios urbanizables no urbanizados aplican tarifas específicas.',
    category_name: 'Impuestos y Tributos',
    tags: ['alumbrado público', 'tarifas', 'energía eléctrica', 'predios'],
    related_procedures: ['Impuesto sobre el servicio de alumbrado público'],
    popularity: 70
  },

  // Licencias y Permisos
  {
    question: '¿Qué requisitos necesito para obtener una licencia de construcción?',
    answer: 'Para obtener una licencia de construcción debe presentar los documentos técnicos requeridos ante la Secretaría de Planeación. El proceso tiene un costo de $419.000 y un tiempo de respuesta de 45 días hábiles. Consulte los requisitos específicos en el portal SUIT o GOV.CO.',
    category_name: 'Licencias y Permisos',
    tags: ['licencia construcción', 'planeación', 'obras civiles', 'requisitos'],
    related_procedures: ['Licencia de construcción'],
    popularity: 90
  },
  {
    question: '¿Cómo obtengo la licencia de funcionamiento para mi negocio?',
    answer: 'La licencia de funcionamiento se tramita según el tipo de actividad comercial. Debe cumplir con los requisitos sanitarios, de seguridad y urbanísticos. Consulte con la dependencia correspondiente según su actividad específica.',
    category_name: 'Licencias y Permisos',
    tags: ['licencia funcionamiento', 'negocio', 'comercio', 'actividad comercial'],
    related_procedures: [],
    popularity: 88
  },

  // Certificados
  {
    question: '¿Cómo puedo obtener un certificado de residencia?',
    answer: 'El certificado de residencia se puede solicitar en la Secretaría General. Debe presentar documento de identidad y comprobantes de residencia en el municipio. Consulte los requisitos específicos y tiempos de respuesta en las oficinas municipales.',
    category_name: 'Certificados',
    tags: ['certificado residencia', 'secretaría general', 'documento identidad'],
    related_procedures: [],
    popularity: 92
  },
  {
    question: '¿Qué es el certificado de libertad y tradición?',
    answer: 'El certificado de libertad y tradición es un documento que certifica la propiedad inmobiliaria y su historial jurídico. Se tramita en la Secretaría General, no tiene costo y el tiempo de respuesta es de 1 día hábil.',
    category_name: 'Certificados',
    tags: ['libertad y tradición', 'propiedad inmobiliaria', 'historial jurídico'],
    related_procedures: ['Certificado de libertad y tradición'],
    popularity: 80
  },

  // Servicios Públicos
  {
    question: '¿Dónde puedo consultar sobre servicios públicos?',
    answer: 'Para consultas sobre servicios públicos como acueducto, alcantarillado y aseo, puede dirigirse a la dependencia correspondiente o consultar en línea. Cada servicio tiene procedimientos específicos de facturación y atención.',
    category_name: 'Servicios Públicos',
    tags: ['servicios públicos', 'acueducto', 'alcantarillado', 'aseo'],
    related_procedures: [],
    popularity: 75
  },

  // Pagos y Facturación
  {
    question: '¿Qué métodos de pago están disponibles?',
    answer: 'El municipio acepta diversos métodos de pago para trámites y servicios: efectivo en oficinas, transferencias bancarias, PSE y otros medios electrónicos. Consulte las opciones específicas para cada trámite.',
    category_name: 'Pagos y Facturación',
    tags: ['métodos pago', 'efectivo', 'transferencias', 'PSE', 'electrónicos'],
    related_procedures: [],
    popularity: 85
  },
  {
    question: '¿Cómo obtengo un paz y salvo municipal?',
    answer: 'El paz y salvo municipal certifica que está al día con sus obligaciones tributarias. Se puede solicitar en la Secretaría de Hacienda presentando documento de identidad y comprobante de pago de impuestos al día.',
    category_name: 'Pagos y Facturación',
    tags: ['paz y salvo', 'obligaciones tributarias', 'secretaría hacienda'],
    related_procedures: [],
    popularity: 78
  }
]

async function migrateFAQData() {
  console.log('🚀 Iniciando migración de datos FAQ...')

  try {
    // 1. Insertar categorías
    console.log('📁 Insertando categorías FAQ...')
    const { data: categoriesData, error: categoriesError } = await supabase
      .from('faq_categories')
      .insert(faqCategories)
      .select()

    if (categoriesError) {
      console.error('❌ Error insertando categorías:', categoriesError)
      return
    }

    console.log(`✅ ${categoriesData.length} categorías insertadas exitosamente`)

    // 2. Crear mapa de categorías por nombre
    const categoryMap = new Map()
    categoriesData.forEach(cat => {
      categoryMap.set(cat.name, cat.id)
    })

    // 3. Preparar datos de FAQs con category_id
    const faqsToInsert = faqData.map(faq => ({
      question: faq.question,
      answer: faq.answer,
      category_id: categoryMap.get(faq.category_name),
      tags: faq.tags,
      related_procedures: faq.related_procedures,
      popularity: faq.popularity,
      view_count: 0,
      helpful_votes: 0,
      unhelpful_votes: 0
    }))

    // 4. Insertar FAQs
    console.log('❓ Insertando FAQs...')
    const { data: faqsData, error: faqsError } = await supabase
      .from('faqs')
      .insert(faqsToInsert)
      .select()

    if (faqsError) {
      console.error('❌ Error insertando FAQs:', faqsError)
      return
    }

    console.log(`✅ ${faqsData.length} FAQs insertadas exitosamente`)

    // 5. Verificar datos insertados
    const { data: totalCategories } = await supabase
      .from('faq_categories')
      .select('*', { count: 'exact' })

    const { data: totalFAQs } = await supabase
      .from('faqs')
      .select('*', { count: 'exact' })

    console.log('\n📊 Resumen de migración:')
    console.log(`   • Categorías: ${totalCategories?.length || 0}`)
    console.log(`   • FAQs: ${totalFAQs?.length || 0}`)
    console.log('\n🎉 Migración completada exitosamente!')

  } catch (error) {
    console.error('❌ Error durante la migración:', error)
  }
}

// Ejecutar migración
migrateFAQData()
