#!/usr/bin/env python3
"""
Complete FAQ insertion script - applies all remaining batches to Supabase
"""

import json
import re
import os

def clean_text(text):
    """Clean text for SQL insertion"""
    if not text:
        return ""
    # Escape single quotes for SQL
    text = text.replace("'", "''")
    # Remove extra whitespace
    text = re.sub(r'\s+', ' ', text.strip())
    return text

def generate_consolidated_sql():
    """Generate a single consolidated SQL file with all FAQ questions"""
    
    # Load the structured FAQ data
    with open('faqs_chia_estructurado.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    sql_statements = []
    question_count = 0
    
    print("Processing FAQ data for consolidated insertion...")
    
    # Process each dependency entry
    for entry in data['faqs']:
        dependencia = entry['dependencia']
        codigo_dependencia = entry['codigo_dependencia']
        subdependencia = entry.get('subdependencia', '')
        codigo_subdependencia = entry.get('codigo_subdependencia', '')
        
        # Process each theme within the dependency
        for tema_data in entry['temas']:
            tema = tema_data['tema']
            preguntas = tema_data.get('preguntas_frecuentes', [])
            
            print(f"Processing theme: {tema[:50]}... ({len(preguntas)} questions)")
            
            # Process each question within the theme
            for i, pregunta_data in enumerate(preguntas, 1):
                pregunta = clean_text(pregunta_data.get('pregunta', ''))
                respuesta = clean_text(pregunta_data.get('respuesta', ''))
                palabras_clave = pregunta_data.get('palabras_clave', [])
                
                # Convert keywords to PostgreSQL array format
                keywords_array = "ARRAY[" + ", ".join([f"'{clean_text(kw)}'" for kw in palabras_clave]) + "]"
                
                # Generate SQL INSERT statement
                sql = f"""INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('{pregunta}', '{respuesta}', 
 (SELECT id FROM faq_themes WHERE name = '{clean_text(tema)}'), 
 {keywords_array}, {i});"""
                
                sql_statements.append(sql)
                question_count += 1
    
    # Write consolidated SQL file
    with open('consolidated_faq_insertion.sql', 'w', encoding='utf-8') as f:
        f.write("-- CONSOLIDATED FAQ INSERTION SCRIPT\n")
        f.write(f"-- Total questions: {question_count}\n")
        f.write("-- Generated from faqs_chia_estructurado.json\n\n")
        
        # Write in batches of 25 for better readability
        for i in range(0, len(sql_statements), 25):
            batch = sql_statements[i:i+25]
            f.write(f"-- Batch {i//25 + 1}: Questions {i+1} to {min(i+25, len(sql_statements))}\n")
            f.write("\n".join(batch))
            f.write("\n\n")
    
    print(f"Generated consolidated_faq_insertion.sql with {question_count} questions")
    return question_count

def main():
    """Main function"""
    print("FAQ Consolidation Script")
    print("=" * 40)
    
    try:
        total_questions = generate_consolidated_sql()
        print(f"\n✅ Successfully generated consolidated SQL file")
        print(f"📊 Total questions: {total_questions}")
        print(f"📁 Output file: consolidated_faq_insertion.sql")
        
        return 0
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
