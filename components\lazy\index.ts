/**
 * Lazy-loaded components for performance optimization
 * All major components are dynamically imported to reduce initial bundle size
 */

import dynamic from 'next/dynamic'
import { PageLoading, CardSkeleton, DocumentGridSkeleton, ChatSkeleton, TableSkeleton } from '@/components/ui/loading'

// Document Portal Components
export const DocumentPortal = dynamic(
  () => import('@/components/documents/DocumentPortal').then(mod => ({ default: mod.DocumentPortal })),
  {
    loading: () => <PageLoading type="documents" />,
    ssr: false
  }
)

export const DocumentGrid = dynamic(
  () => import('@/components/documents/DocumentGrid').then(mod => ({ default: mod.DocumentGrid })),
  {
    loading: () => <DocumentGridSkeleton />,
    ssr: false
  }
)

export const DocumentList = dynamic(
  () => import('@/components/documents/DocumentList').then(mod => ({ default: mod.DocumentList })),
  {
    loading: () => <TableSkeleton />,
    ssr: false
  }
)

export const DocumentUploadZone = dynamic(
  () => import('@/components/documents/DocumentUploadZone').then(mod => ({ default: mod.DocumentUploadZone })),
  {
    loading: () => <CardSkeleton />,
    ssr: false
  }
)

export const DocumentStats = dynamic(
  () => import('@/components/documents/DocumentStats').then(mod => ({ default: mod.DocumentStats })),
  {
    loading: () => <CardSkeleton />,
    ssr: false
  }
)

// Chat Components
export const ChatInterface = dynamic(
  () => import('@/src/components/chat/ChatInterface').then(mod => ({ default: mod.ChatInterface })),
  {
    loading: () => <PageLoading type="chat" />,
    ssr: false
  }
)

export const ChatMessage = dynamic(
  () => import('@/src/components/chat/ChatMessage').then(mod => ({ default: mod.ChatMessage })),
  {
    loading: () => <ChatSkeleton />,
    ssr: false
  }
)

// Admin Components
export const AdminDashboard = dynamic(
  () => import('@/components/admin/AdminDashboard').then(mod => ({ default: mod.AdminDashboard })),
  {
    loading: () => <PageLoading type="admin" />,
    ssr: false
  }
)

export const UserManagement = dynamic(
  () => import('@/components/admin/UserManagement').then(mod => ({ default: mod.UserManagement })),
  {
    loading: () => <TableSkeleton rows={10} columns={5} />,
    ssr: false
  }
)

export const ProcedureManagement = dynamic(
  () => import('@/components/admin/ProcedureManagement').then(mod => ({ default: mod.ProcedureManagement })),
  {
    loading: () => <TableSkeleton rows={8} columns={6} />,
    ssr: false
  }
)

export const DependencyManagement = dynamic(
  () => import('@/components/admin/DependencyManagement').then(mod => ({ default: mod.DependencyManagement })),
  {
    loading: () => <TableSkeleton rows={6} columns={4} />,
    ssr: false
  }
)

export const AdminReports = dynamic(
  () => import('@/components/admin/AdminReports').then(mod => ({ default: mod.AdminReports })),
  {
    loading: () => <CardSkeleton />,
    ssr: false
  }
)

export const SystemSettings = dynamic(
  () => import('@/components/admin/SystemSettings').then(mod => ({ default: mod.SystemSettings })),
  {
    loading: () => <CardSkeleton />,
    ssr: false
  }
)

// Procedure Components
export const ProcedureSearch = dynamic(
  () => import('@/components/procedures/ProcedureSearch').then(mod => ({ default: mod.ProcedureSearch })),
  {
    loading: () => <CardSkeleton />,
    ssr: false
  }
)

export const ProcedureCard = dynamic(
  () => import('@/components/procedures/ProcedureCard').then(mod => ({ default: mod.ProcedureCard })),
  {
    loading: () => <CardSkeleton />,
    ssr: false
  }
)

export const ProcedureDetails = dynamic(
  () => import('@/components/procedures/ProcedureDetails').then(mod => ({ default: mod.ProcedureDetails })),
  {
    loading: () => <PageLoading type="procedures" />,
    ssr: false
  }
)

export const ProcedureForm = dynamic(
  () => import('@/components/procedures/ProcedureForm').then(mod => ({ default: mod.ProcedureForm })),
  {
    loading: () => <CardSkeleton />,
    ssr: false
  }
)

export const ProcedureStatus = dynamic(
  () => import('@/components/procedures/ProcedureStatus').then(mod => ({ default: mod.ProcedureStatus })),
  {
    loading: () => <CardSkeleton />,
    ssr: false
  }
)

// Dashboard Components
export const DashboardStats = dynamic(
  () => import('@/components/dashboard/DashboardStats').then(mod => ({ default: mod.DashboardStats })),
  {
    loading: () => <CardSkeleton />,
    ssr: false
  }
)

export const QuickActions = dynamic(
  () => import('@/components/dashboard/QuickActions').then(mod => ({ default: mod.QuickActions })),
  {
    loading: () => <CardSkeleton />,
    ssr: false
  }
)

export const RecentProcedures = dynamic(
  () => import('@/components/dashboard/RecentProcedures').then(mod => ({ default: mod.RecentProcedures })),
  {
    loading: () => <TableSkeleton rows={5} columns={4} />,
    ssr: false
  }
)

export const NotificationWidget = dynamic(
  () => import('@/components/dashboard/NotificationWidget').then(mod => ({ default: mod.NotificationWidget })),
  {
    loading: () => <CardSkeleton />,
    ssr: false
  }
)

// Form Components
export const FormBuilder = dynamic(
  () => import('@/components/forms/FormBuilder').then(mod => ({ default: mod.FormBuilder })),
  {
    loading: () => <CardSkeleton />,
    ssr: false
  }
)

export const FormRenderer = dynamic(
  () => import('@/components/forms/FormRenderer').then(mod => ({ default: mod.FormRenderer })),
  {
    loading: () => <CardSkeleton />,
    ssr: false
  }
)

// Chart Components (for analytics)
export const AnalyticsChart = dynamic(
  () => import('@/components/charts/AnalyticsChart').then(mod => ({ default: mod.AnalyticsChart })),
  {
    loading: () => <CardSkeleton />,
    ssr: false
  }
)

export const ProcedureChart = dynamic(
  () => import('@/components/charts/ProcedureChart').then(mod => ({ default: mod.ProcedureChart })),
  {
    loading: () => <CardSkeleton />,
    ssr: false
  }
)

// Map Components (if needed for location services)
export const LocationMap = dynamic(
  () => import('@/components/maps/LocationMap').then(mod => ({ default: mod.LocationMap })),
  {
    loading: () => <CardSkeleton />,
    ssr: false
  }
)

// Calendar Components
export const CalendarWidget = dynamic(
  () => import('@/components/calendar/CalendarWidget').then(mod => ({ default: mod.CalendarWidget })),
  {
    loading: () => <CardSkeleton />,
    ssr: false
  }
)

export const AppointmentScheduler = dynamic(
  () => import('@/components/calendar/AppointmentScheduler').then(mod => ({ default: mod.AppointmentScheduler })),
  {
    loading: () => <CardSkeleton />,
    ssr: false
  }
)

// Search Components
export const GlobalSearch = dynamic(
  () => import('@/components/search/GlobalSearch').then(mod => ({ default: mod.GlobalSearch })),
  {
    loading: () => <CardSkeleton />,
    ssr: false
  }
)

export const SearchResults = dynamic(
  () => import('@/components/search/SearchResults').then(mod => ({ default: mod.SearchResults })),
  {
    loading: () => <TableSkeleton />,
    ssr: false
  }
)

// Notification Components
export const NotificationCenter = dynamic(
  () => import('@/components/notifications/NotificationCenter').then(mod => ({ default: mod.NotificationCenter })),
  {
    loading: () => <CardSkeleton />,
    ssr: false
  }
)

export const NotificationList = dynamic(
  () => import('@/components/notifications/NotificationList').then(mod => ({ default: mod.NotificationList })),
  {
    loading: () => <TableSkeleton rows={6} columns={3} />,
    ssr: false
  }
)

// Settings Components
export const UserSettings = dynamic(
  () => import('@/components/settings/UserSettings').then(mod => ({ default: mod.UserSettings })),
  {
    loading: () => <CardSkeleton />,
    ssr: false
  }
)

export const ProfileSettings = dynamic(
  () => import('@/components/settings/ProfileSettings').then(mod => ({ default: mod.ProfileSettings })),
  {
    loading: () => <CardSkeleton />,
    ssr: false
  }
)

// Help Components
export const HelpCenter = dynamic(
  () => import('@/components/help/HelpCenter').then(mod => ({ default: mod.HelpCenter })),
  {
    loading: () => <PageLoading type="generic" title="Cargando Centro de Ayuda..." />,
    ssr: false
  }
)

export const FAQ = dynamic(
  () => import('@/components/help/FAQ').then(mod => ({ default: mod.FAQ })),
  {
    loading: () => <CardSkeleton />,
    ssr: false
  }
)

// Export all lazy components for easy importing
export const LazyComponents = {
  // Documents
  DocumentPortal,
  DocumentGrid,
  DocumentList,
  DocumentUploadZone,
  DocumentStats,
  
  // Chat
  ChatInterface,
  ChatMessage,
  
  // Admin
  AdminDashboard,
  UserManagement,
  ProcedureManagement,
  DependencyManagement,
  AdminReports,
  SystemSettings,
  
  // Procedures
  ProcedureSearch,
  ProcedureCard,
  ProcedureDetails,
  ProcedureForm,
  ProcedureStatus,
  
  // Dashboard
  DashboardStats,
  QuickActions,
  RecentProcedures,
  NotificationWidget,
  
  // Forms
  FormBuilder,
  FormRenderer,
  
  // Charts
  AnalyticsChart,
  ProcedureChart,
  
  // Maps
  LocationMap,
  
  // Calendar
  CalendarWidget,
  AppointmentScheduler,
  
  // Search
  GlobalSearch,
  SearchResults,
  
  // Notifications
  NotificationCenter,
  NotificationList,
  
  // Settings
  UserSettings,
  ProfileSettings,
  
  // Help
  HelpCenter,
  FAQ
}

export default LazyComponents
