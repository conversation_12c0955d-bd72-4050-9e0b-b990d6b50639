'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Separator } from '@/components/ui/separator'
import { 
  Settings, 
  Database, 
  Shield, 
  Bell, 
  Mail,
  Server,
  Users,
  FileText,
  AlertTriangle,
  CheckCircle,
  Info,
  Save
} from 'lucide-react'
import { createClient } from '@/lib/supabase/client'
import { usePermissions } from '@/hooks'

interface SystemConfig {
  site_name: string
  site_description: string
  contact_email: string
  contact_phone: string
  maintenance_mode: boolean
  allow_registrations: boolean
  max_file_size_mb: number
  session_timeout_hours: number
  email_notifications: boolean
  sms_notifications: boolean
}

interface SystemStats {
  total_users: number
  total_procedures: number
  total_dependencies: number
  database_size_mb: number
  last_backup: string
  system_uptime: string
}

export function SystemSettings() {
  const [config, setConfig] = useState<SystemConfig>({
    site_name: 'Sistema Municipal Chía',
    site_description: 'Plataforma digital para trámites ciudadanos',
    contact_email: '<EMAIL>',
    contact_phone: '+57 1 234 5678',
    maintenance_mode: false,
    allow_registrations: true,
    max_file_size_mb: 10,
    session_timeout_hours: 24,
    email_notifications: true,
    sms_notifications: false
  })
  
  const [stats, setStats] = useState<SystemStats>({
    total_users: 0,
    total_procedures: 0,
    total_dependencies: 0,
    database_size_mb: 0,
    last_backup: '',
    system_uptime: ''
  })
  
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const { canViewSystemSettings } = usePermissions()

  const supabase = createClient()

  useEffect(() => {
    fetchSystemStats()
    fetchSystemConfig()
  }, [])

  const fetchSystemStats = async () => {
    try {
      const [
        { count: totalUsers },
        { count: totalProcedures },
        { count: totalDependencies }
      ] = await Promise.all([
        supabase.from('profiles').select('*', { count: 'exact', head: true }),
        supabase.from('citizen_procedures').select('*', { count: 'exact', head: true }),
        supabase.from('dependencies').select('*', { count: 'exact', head: true })
      ])

      setStats({
        total_users: totalUsers || 0,
        total_procedures: totalProcedures || 0,
        total_dependencies: totalDependencies || 0,
        database_size_mb: 0, // TODO: Get actual database size
        last_backup: new Date().toISOString(), // TODO: Get actual backup date
        system_uptime: '99.9%' // TODO: Get actual uptime
      })
    } catch (error) {
      console.error('Error fetching system stats:', error)
    }
  }

  const fetchSystemConfig = async () => {
    try {
      // TODO: Implement system configuration storage
      // For now, using default values
      setLoading(false)
    } catch (error) {
      console.error('Error fetching system config:', error)
      setLoading(false)
    }
  }

  const handleSaveConfig = async () => {
    try {
      setSaving(true)
      // TODO: Implement system configuration saving
      console.log('Saving config:', config)
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      alert('Configuración guardada exitosamente')
    } catch (error) {
      console.error('Error saving config:', error)
      alert('Error al guardar la configuración')
    } finally {
      setSaving(false)
    }
  }

  const handleBackupDatabase = async () => {
    try {
      // TODO: Implement database backup
      console.log('Creating database backup...')
      alert('Backup iniciado. Recibirás una notificación cuando esté completo.')
    } catch (error) {
      console.error('Error creating backup:', error)
      alert('Error al crear el backup')
    }
  }

  const handleClearCache = async () => {
    try {
      // TODO: Implement cache clearing
      console.log('Clearing cache...')
      alert('Cache limpiado exitosamente')
    } catch (error) {
      console.error('Error clearing cache:', error)
      alert('Error al limpiar el cache')
    }
  }

  if (!canViewSystemSettings()) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center">
            <Shield className="h-12 w-12 mx-auto text-gray-400 mb-4" />
            <p className="text-gray-500">No tienes permisos para ver esta sección</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* System Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Usuarios</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total_users}</div>
            <p className="text-xs text-muted-foreground">Usuarios registrados</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Trámites</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total_procedures}</div>
            <p className="text-xs text-muted-foreground">Trámites procesados</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Dependencias</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total_dependencies}</div>
            <p className="text-xs text-muted-foreground">Dependencias activas</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Disponibilidad</CardTitle>
            <Server className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.system_uptime}</div>
            <p className="text-xs text-muted-foreground">Tiempo de actividad</p>
          </CardContent>
        </Card>
      </div>

      {/* System Configuration */}
      <Card>
        <CardHeader>
          <CardTitle>Configuración General</CardTitle>
          <CardDescription>
            Configuración básica del sistema municipal
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <Label htmlFor="site_name">Nombre del Sitio</Label>
              <Input
                id="site_name"
                value={config.site_name}
                onChange={(e) => setConfig({ ...config, site_name: e.target.value })}
              />
            </div>
            
            <div>
              <Label htmlFor="contact_email">Email de Contacto</Label>
              <Input
                id="contact_email"
                type="email"
                value={config.contact_email}
                onChange={(e) => setConfig({ ...config, contact_email: e.target.value })}
              />
            </div>
          </div>

          <div>
            <Label htmlFor="site_description">Descripción del Sitio</Label>
            <Textarea
              id="site_description"
              value={config.site_description}
              onChange={(e) => setConfig({ ...config, site_description: e.target.value })}
              rows={3}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <Label htmlFor="contact_phone">Teléfono de Contacto</Label>
              <Input
                id="contact_phone"
                value={config.contact_phone}
                onChange={(e) => setConfig({ ...config, contact_phone: e.target.value })}
              />
            </div>
            
            <div>
              <Label htmlFor="max_file_size">Tamaño Máximo de Archivo (MB)</Label>
              <Input
                id="max_file_size"
                type="number"
                value={config.max_file_size_mb}
                onChange={(e) => setConfig({ ...config, max_file_size_mb: parseInt(e.target.value) })}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Security Settings */}
      <Card>
        <CardHeader>
          <CardTitle>Configuración de Seguridad</CardTitle>
          <CardDescription>
            Configuración de seguridad y acceso al sistema
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Modo de Mantenimiento</Label>
              <p className="text-sm text-muted-foreground">
                Desactiva el acceso público al sistema
              </p>
            </div>
            <Switch
              checked={config.maintenance_mode}
              onCheckedChange={(checked) => setConfig({ ...config, maintenance_mode: checked })}
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Permitir Registros</Label>
              <p className="text-sm text-muted-foreground">
                Permite que nuevos usuarios se registren
              </p>
            </div>
            <Switch
              checked={config.allow_registrations}
              onCheckedChange={(checked) => setConfig({ ...config, allow_registrations: checked })}
            />
          </div>

          <Separator />

          <div>
            <Label htmlFor="session_timeout">Tiempo de Sesión (horas)</Label>
            <Input
              id="session_timeout"
              type="number"
              value={config.session_timeout_hours}
              onChange={(e) => setConfig({ ...config, session_timeout_hours: parseInt(e.target.value) })}
              className="w-32"
            />
          </div>
        </CardContent>
      </Card>

      {/* Notification Settings */}
      <Card>
        <CardHeader>
          <CardTitle>Configuración de Notificaciones</CardTitle>
          <CardDescription>
            Configuración de notificaciones del sistema
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Notificaciones por Email</Label>
              <p className="text-sm text-muted-foreground">
                Enviar notificaciones por correo electrónico
              </p>
            </div>
            <Switch
              checked={config.email_notifications}
              onCheckedChange={(checked) => setConfig({ ...config, email_notifications: checked })}
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Notificaciones por SMS</Label>
              <p className="text-sm text-muted-foreground">
                Enviar notificaciones por mensaje de texto
              </p>
            </div>
            <Switch
              checked={config.sms_notifications}
              onCheckedChange={(checked) => setConfig({ ...config, sms_notifications: checked })}
            />
          </div>
        </CardContent>
      </Card>

      {/* System Maintenance */}
      <Card>
        <CardHeader>
          <CardTitle>Mantenimiento del Sistema</CardTitle>
          <CardDescription>
            Herramientas de mantenimiento y administración
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div className="flex items-center space-x-3">
              <Database className="h-5 w-5 text-blue-500" />
              <div>
                <p className="font-medium">Backup de Base de Datos</p>
                <p className="text-sm text-muted-foreground">
                  Último backup: {new Date(stats.last_backup).toLocaleDateString('es-CO')}
                </p>
              </div>
            </div>
            <Button onClick={handleBackupDatabase} variant="outline">
              Crear Backup
            </Button>
          </div>

          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div className="flex items-center space-x-3">
              <Server className="h-5 w-5 text-green-500" />
              <div>
                <p className="font-medium">Limpiar Cache</p>
                <p className="text-sm text-muted-foreground">
                  Limpia el cache del sistema para mejorar el rendimiento
                </p>
              </div>
            </div>
            <Button onClick={handleClearCache} variant="outline">
              Limpiar Cache
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Save Configuration */}
      <div className="flex justify-end">
        <Button onClick={handleSaveConfig} disabled={saving}>
          <Save className="h-4 w-4 mr-2" />
          {saving ? 'Guardando...' : 'Guardar Configuración'}
        </Button>
      </div>
    </div>
  )
}
