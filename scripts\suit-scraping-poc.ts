/**
 * Prueba de Concepto: Web Scraping SUIT
 * Demostración técnica de viabilidad para extracción de datos
 */

import puppeteer from 'puppeteer'
import * as fs from 'fs'
import * as path from 'path'

interface SuitScrapingResult {
  fichaId: string
  url: string
  success: boolean
  data?: {
    titulo?: string
    descripcion?: string
    requisitos?: string[]
    pasos?: string[]
    entidad?: string
    tiempoRespuesta?: string
    costo?: string
  }
  error?: string
  scrapedAt: Date
}

class SuitScrapingPOC {
  private browser: puppeteer.Browser | null = null
  private page: puppeteer.Page | null = null

  async initialize(): Promise<void> {
    console.log('🚀 Inicializando navegador para scraping SUIT...')
    
    this.browser = await puppeteer.launch({
      headless: false, // Visible para debugging
      slowMo: 1000,    // Pausa entre acciones
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu'
      ]
    })

    this.page = await this.browser.newPage()
    
    // Configurar User-Agent identificativo
    await this.page.setUserAgent(
      'ChiaTramitesBot/1.0 (Municipio de Chía - Análisis de Procedimientos)'
    )

    // Configurar viewport
    await this.page.setViewport({ width: 1366, height: 768 })

    console.log('✅ Navegador inicializado correctamente')
  }

  async scrapeSuitPage(fichaId: string): Promise<SuitScrapingResult> {
    if (!this.page) {
      throw new Error('Navegador no inicializado')
    }

    const url = `https://visorsuit.funcionpublica.gov.co/auth/visor?fi=${fichaId}`
    console.log(`\n🔍 Analizando ficha SUIT: ${fichaId}`)
    console.log(`🌐 URL: ${url}`)

    const result: SuitScrapingResult = {
      fichaId,
      url,
      success: false,
      scrapedAt: new Date()
    }

    try {
      // Navegar a la página
      console.log('📡 Navegando a la página...')
      await this.page.goto(url, { 
        waitUntil: 'networkidle2',
        timeout: 30000 
      })

      // Esperar a que la página cargue
      await this.page.waitForTimeout(3000)

      // Tomar screenshot para análisis
      const screenshotPath = path.join(__dirname, '..', 'temp', `suit-${fichaId}.png`)
      await this.page.screenshot({ path: screenshotPath, fullPage: true })
      console.log(`📸 Screenshot guardado: ${screenshotPath}`)

      // Verificar si requiere autenticación
      const pageContent = await this.page.content()
      
      if (pageContent.includes('login') || pageContent.includes('auth') || pageContent.includes('AuthApp')) {
        console.log('🔐 Página requiere autenticación')
        result.error = 'Requiere autenticación'
        return result
      }

      // Intentar extraer información básica
      console.log('📋 Extrayendo información de la página...')
      
      const extractedData = await this.page.evaluate(() => {
        const data: any = {}

        // Buscar título
        const titleSelectors = [
          'h1', 'h2', '.title', '.titulo', '[class*="title"]', '[class*="titulo"]'
        ]
        
        for (const selector of titleSelectors) {
          const element = document.querySelector(selector)
          if (element && element.textContent?.trim()) {
            data.titulo = element.textContent.trim()
            break
          }
        }

        // Buscar descripción
        const descSelectors = [
          '.descripcion', '.description', '[class*="desc"]', 'p'
        ]
        
        for (const selector of descSelectors) {
          const elements = document.querySelectorAll(selector)
          for (const element of elements) {
            const text = element.textContent?.trim()
            if (text && text.length > 50) {
              data.descripcion = text
              break
            }
          }
          if (data.descripcion) break
        }

        // Buscar información de entidad
        const entidadSelectors = [
          '.entidad', '.entity', '[class*="entidad"]', '[class*="entity"]'
        ]
        
        for (const selector of entidadSelectors) {
          const element = document.querySelector(selector)
          if (element && element.textContent?.trim()) {
            data.entidad = element.textContent.trim()
            break
          }
        }

        // Extraer todo el texto visible para análisis
        data.fullText = document.body.innerText

        return data
      })

      if (extractedData.titulo || extractedData.descripcion) {
        result.success = true
        result.data = extractedData
        console.log('✅ Información extraída exitosamente')
        
        if (extractedData.titulo) {
          console.log(`📝 Título: ${extractedData.titulo.substring(0, 100)}...`)
        }
        if (extractedData.descripcion) {
          console.log(`📄 Descripción: ${extractedData.descripcion.substring(0, 150)}...`)
        }
      } else {
        console.log('⚠️ No se pudo extraer información estructurada')
        result.error = 'No se encontró información estructurada'
        
        // Guardar contenido HTML para análisis
        const htmlPath = path.join(__dirname, '..', 'temp', `suit-${fichaId}.html`)
        fs.writeFileSync(htmlPath, pageContent)
        console.log(`💾 HTML guardado para análisis: ${htmlPath}`)
      }

    } catch (error) {
      console.log(`❌ Error durante el scraping: ${error}`)
      result.error = error instanceof Error ? error.message : 'Error desconocido'
    }

    return result
  }

  async runPOC(): Promise<void> {
    console.log('🧪 INICIANDO PRUEBA DE CONCEPTO - WEB SCRAPING SUIT')
    console.log('=' .repeat(60))

    // Crear directorio temporal si no existe
    const tempDir = path.join(__dirname, '..', 'temp')
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true })
    }

    try {
      await this.initialize()

      // Lista de fichas para probar (basada en análisis previo)
      const fichasTest = [
        '24215', // Descripción muy corta (35 chars)
        '76446', // Descripción muy corta (45 chars)
        '11049', // Descripción corta (50 chars)
        '16553', // Ejemplo de cesantías
        '2042'   // Ejemplo de registro de firmas
      ]

      const results: SuitScrapingResult[] = []

      for (const fichaId of fichasTest) {
        const result = await this.scrapeSuitPage(fichaId)
        results.push(result)

        // Rate limiting: esperar entre requests
        console.log('⏱️ Esperando 5 segundos antes del siguiente request...')
        await new Promise(resolve => setTimeout(resolve, 5000))
      }

      // Generar reporte de resultados
      this.generateReport(results)

    } catch (error) {
      console.error('💥 Error fatal durante la POC:', error)
    } finally {
      await this.cleanup()
    }
  }

  private generateReport(results: SuitScrapingResult[]): void {
    console.log('\n📊 REPORTE DE RESULTADOS')
    console.log('=' .repeat(60))

    const successful = results.filter(r => r.success).length
    const failed = results.filter(r => !r.success).length
    const authRequired = results.filter(r => r.error?.includes('autenticación')).length

    console.log(`📈 Total de fichas analizadas: ${results.length}`)
    console.log(`✅ Extracciones exitosas: ${successful}`)
    console.log(`❌ Extracciones fallidas: ${failed}`)
    console.log(`🔐 Requieren autenticación: ${authRequired}`)

    console.log('\n📋 DETALLE POR FICHA:')
    results.forEach(result => {
      console.log(`\n🔸 Ficha ${result.fichaId}:`)
      console.log(`   Estado: ${result.success ? '✅ Éxito' : '❌ Fallo'}`)
      if (result.error) {
        console.log(`   Error: ${result.error}`)
      }
      if (result.data?.titulo) {
        console.log(`   Título: ${result.data.titulo.substring(0, 80)}...`)
      }
      if (result.data?.descripcion) {
        console.log(`   Descripción: ${result.data.descripcion.substring(0, 100)}...`)
      }
    })

    // Guardar reporte en archivo
    const reportPath = path.join(__dirname, '..', 'temp', 'suit-scraping-report.json')
    fs.writeFileSync(reportPath, JSON.stringify(results, null, 2))
    console.log(`\n💾 Reporte completo guardado: ${reportPath}`)

    console.log('\n💡 CONCLUSIONES:')
    if (authRequired === results.length) {
      console.log('🔐 TODAS las fichas requieren autenticación')
      console.log('   Recomendación: Implementar automatización con Selenium/Puppeteer')
      console.log('   Alternativa: Contactar Función Pública para API oficial')
    } else if (successful > 0) {
      console.log('✅ Scraping directo es VIABLE para algunas fichas')
      console.log('   Recomendación: Implementar scraping selectivo')
    } else {
      console.log('❌ Scraping directo NO es viable')
      console.log('   Recomendación: Buscar alternativas (API, contacto oficial)')
    }

    console.log('\n🎯 PRÓXIMOS PASOS RECOMENDADOS:')
    console.log('1. Contactar a Función Pública sobre API oficial')
    console.log('2. Solicitar autorización formal para scraping')
    console.log('3. Desarrollar sistema de autenticación automatizada')
    console.log('4. Implementar rate limiting estricto (5-10 seg entre requests)')
    console.log('5. Crear sistema de monitoreo y alertas')
  }

  async cleanup(): Promise<void> {
    if (this.browser) {
      await this.browser.close()
      console.log('🧹 Navegador cerrado correctamente')
    }
  }
}

// Ejecutar POC si se llama directamente
if (require.main === module) {
  const poc = new SuitScrapingPOC()
  
  poc.runPOC()
    .then(() => {
      console.log('\n🎉 Prueba de concepto completada')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Error en la prueba de concepto:', error)
      process.exit(1)
    })
}

export { SuitScrapingPOC }
