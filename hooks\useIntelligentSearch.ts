'use client'

import { useState, useEffect, useCallback, useMemo } from 'react'
import { debounce } from 'lodash'
import { FilterState } from '@/components/search/SmartFilters'

interface Dependency {
  id: string
  name: string
  acronym?: string
}

interface Subdependency {
  id: string
  name: string
}

interface Procedure {
  id: string
  name: string
  description?: string
  cost?: number
  response_time?: string
  requirements?: string[]
  documents_required?: string[]
  suit_url?: string
  gov_url?: string
  online_available?: boolean
  dependency?: Dependency
  subdependency?: Subdependency
  procedure_type?: 'TRAMITE' | 'OPA'
  has_cost?: boolean
  cost_description?: string
  tags?: string[]
}

interface SearchOptions {
  debounceMs?: number
  minSearchLength?: number
  maxResults?: number
}

interface UseIntelligentSearchProps {
  procedures: Procedure[]
  dependencies: Dependency[]
  subdependencies: Subdependency[]
  options?: SearchOptions
}

interface SearchResult extends Procedure {
  matchScore: number
  highlightedName?: string
}

export function useIntelligentSearch({
  procedures,
  dependencies,
  subdependencies,
  options = {}
}: UseIntelligentSearchProps) {
  const {
    debounceMs = 300,
    minSearchLength = 0,
    maxResults = 50
  } = options

  const [searchTerm, setSearchTerm] = useState('')
  const [filters, setFilters] = useState<FilterState>({})
  const [isSearching, setIsSearching] = useState(false)
  const [searchHistory, setSearchHistory] = useState<string[]>([])

  // Función para calcular el score de coincidencia
  const calculateMatchScore = useCallback((procedure: Procedure, term: string): number => {
    if (!term.trim()) return 1 // Si no hay término, todos coinciden

    const searchTerm = term.toLowerCase()
    let score = 0

    // Coincidencia exacta en nombre (peso alto)
    if (procedure.name.toLowerCase() === searchTerm) {
      score += 100
    } else if (procedure.name.toLowerCase().includes(searchTerm)) {
      score += 50
    }

    // Coincidencia en descripción
    if (procedure.description?.toLowerCase().includes(searchTerm)) {
      score += 30
    }

    // Coincidencia en dependencia
    if (procedure.dependency?.name.toLowerCase().includes(searchTerm)) {
      score += 25
    }

    // Coincidencia en subdependencia
    if (procedure.subdependency?.name.toLowerCase().includes(searchTerm)) {
      score += 20
    }

    // Coincidencia en requisitos
    const requirementMatches = procedure.requirements?.filter(req =>
      req.toLowerCase().includes(searchTerm)
    ).length || 0
    score += requirementMatches * 10

    // Coincidencia en tags
    const tagMatches = procedure.tags?.filter(tag =>
      tag.toLowerCase().includes(searchTerm)
    ).length || 0
    score += tagMatches * 15

    // Búsqueda semántica básica (sinónimos comunes)
    const synonyms: Record<string, string[]> = {
      'licencia': ['permiso', 'autorización'],
      'certificado': ['constancia', 'documento'],
      'construcción': ['obra', 'edificación', 'construcción'],
      'residencia': ['domicilio', 'vivienda'],
      'empresa': ['negocio', 'comercio', 'establecimiento'],
      'impuesto': ['tributo', 'tasa'],
      'registro': ['inscripción', 'matrícula']
    }

    Object.entries(synonyms).forEach(([key, values]) => {
      if (searchTerm.includes(key)) {
        values.forEach(synonym => {
          if (procedure.name.toLowerCase().includes(synonym) ||
              procedure.description?.toLowerCase().includes(synonym)) {
            score += 15
          }
        })
      }
    })

    return score
  }, [])

  // Función para aplicar filtros
  const applyFilters = useCallback((procedure: Procedure, filters: FilterState): boolean => {
    // Filtro por dependencia
    if (filters.dependency && procedure.dependency?.id !== filters.dependency) {
      // También intentar comparar por código si el ID no coincide
      if (procedure.dependency?.code !== filters.dependency) {
        return false
      }
    }

    // Filtro por subdependencia
    if (filters.subdependency && procedure.subdependency?.id !== filters.subdependency) {
      // También intentar comparar por código si el ID no coincide
      if (procedure.subdependency?.code !== filters.subdependency) {
        return false
      }
    }

    // Filtro por modalidad
    if (filters.modality) {
      const isOnline = procedure.online_available
      switch (filters.modality) {
        case 'virtual':
          if (isOnline !== true) return false
          break
        case 'presencial':
          if (isOnline !== false) return false
          break
        case 'mixto':
          if (isOnline !== undefined) return false
          break
      }
    }

    // Filtro por tiempo de respuesta
    if (filters.responseTime && procedure.response_time) {
      const responseTime = procedure.response_time.toLowerCase()
      switch (filters.responseTime) {
        case 'inmediato':
          if (!responseTime.includes('inmediato') && !responseTime.includes('mismo día')) {
            return false
          }
          break
        case '1-5-dias':
          if (!responseTime.match(/[1-5]\s*(día|day)/i)) {
            return false
          }
          break
        case 'mas-5-dias':
          if (responseTime.includes('inmediato') || responseTime.match(/[1-5]\s*(día|day)/i)) {
            return false
          }
          break
      }
    }

    // Filtro por costo
    if (filters.cost) {
      const isFree = !procedure.has_cost || procedure.cost === 0
      switch (filters.cost) {
        case 'gratuito':
          if (!isFree) return false
          break
        case 'con-costo':
          if (isFree) return false
          break
      }
    }

    // Filtro por tipo de procedimiento
    if (filters.procedureType) {
      const procedureType = procedure.procedure_type?.toUpperCase()
      const filterType = filters.procedureType.toUpperCase()
      if (procedureType !== filterType) {
        return false
      }
    }

    return true
  }, [])

  // Función de búsqueda principal
  const performSearch = useCallback((term: string, currentFilters: FilterState): SearchResult[] => {
    if (term.length < minSearchLength && Object.keys(currentFilters).length === 0) {
      return procedures.slice(0, maxResults).map(proc => ({ ...proc, matchScore: 1 }))
    }

    let results = procedures
      .map(procedure => ({
        ...procedure,
        matchScore: calculateMatchScore(procedure, term)
      }))
      .filter(result => result.matchScore > 0)
      .filter(result => applyFilters(result, currentFilters))
      .sort((a, b) => b.matchScore - a.matchScore)
      .slice(0, maxResults)

    return results
  }, [procedures, calculateMatchScore, applyFilters, minSearchLength, maxResults])

  // Búsqueda con debounce
  const debouncedSearch = useMemo(
    () => debounce((term: string, currentFilters: FilterState) => {
      setIsSearching(true)
      const results = performSearch(term, currentFilters)
      setSearchResults(results)
      setIsSearching(false)

      // Agregar al historial si es una búsqueda válida
      if (term.trim() && term.length >= 2) {
        setSearchHistory(prev => {
          const newHistory = [term, ...prev.filter(h => h !== term)].slice(0, 10)
          return newHistory
        })
      }
    }, debounceMs),
    [performSearch, debounceMs]
  )

  const [searchResults, setSearchResults] = useState<SearchResult[]>([])

  // Efecto para inicializar resultados con todos los procedimientos
  useEffect(() => {
    if (procedures.length > 0 && searchResults.length === 0 && !searchTerm && Object.keys(filters).length === 0) {
      const initialResults = procedures.slice(0, maxResults).map(proc => ({ ...proc, matchScore: 1 }))
      setSearchResults(initialResults)
    }
  }, [procedures, searchResults.length, searchTerm, filters, maxResults])

  // Efecto para realizar búsqueda cuando cambian los términos o filtros
  useEffect(() => {
    debouncedSearch(searchTerm, filters)
    return () => {
      debouncedSearch.cancel()
    }
  }, [searchTerm, filters, debouncedSearch])

  // Función para actualizar término de búsqueda
  const updateSearchTerm = useCallback((term: string) => {
    setSearchTerm(term)
  }, [])

  // Función para actualizar filtros
  const updateFilters = useCallback((newFilters: FilterState) => {
    setFilters(newFilters)
  }, [])

  // Función para limpiar búsqueda
  const clearSearch = useCallback(() => {
    setSearchTerm('')
    setFilters({})
    debouncedSearch.cancel()
    setSearchResults([])
  }, [debouncedSearch])

  // Función para obtener sugerencias
  const getSuggestions = useCallback((term: string): string[] => {
    if (!term || term.length < 2) return []

    const suggestions = new Set<string>()
    const searchTerm = term.toLowerCase()

    // Sugerencias de nombres de procedimientos
    procedures.forEach(proc => {
      if (proc.name.toLowerCase().includes(searchTerm)) {
        suggestions.add(proc.name)
      }
    })

    // Sugerencias de dependencias
    dependencies.forEach(dep => {
      if (dep.name.toLowerCase().includes(searchTerm)) {
        suggestions.add(dep.name)
      }
    })

    // Sugerencias del historial
    searchHistory.forEach(historyItem => {
      if (historyItem.toLowerCase().includes(searchTerm)) {
        suggestions.add(historyItem)
      }
    })

    return Array.from(suggestions).slice(0, 8)
  }, [procedures, dependencies, searchHistory])

  // Estadísticas de búsqueda
  const searchStats = useMemo(() => ({
    totalProcedures: procedures.length,
    filteredCount: searchResults.length,
    hasActiveFilters: Object.keys(filters).some(key => filters[key as keyof FilterState]),
    hasSearchTerm: searchTerm.trim().length > 0
  }), [procedures.length, searchResults.length, filters, searchTerm])

  return {
    // Estado
    searchTerm,
    filters,
    searchResults,
    isSearching,
    searchHistory,
    searchStats,

    // Acciones
    updateSearchTerm,
    updateFilters,
    clearSearch,
    getSuggestions,

    // Utilidades
    performSearch: (term: string, filterState: FilterState) => performSearch(term, filterState)
  }
}
