import { supabase } from '@/lib/supabase/client'
import type { Database } from '@/lib/database.types'
import faqAnalytics from './faqAnalytics'

// Tipos de base de datos
type MunicipalFAQRow = Database['public']['Tables']['municipal_faqs']['Row']
type FAQThemeRow = Database['public']['Tables']['faq_themes']['Row']

/**
 * Interfaz para una pregunta frecuente municipal
 */
export interface FAQItem {
  id: string
  question: string
  answer: string
  theme: string
  themeId: string
  keywords: string[]
  relatedProcedures: string[]
  displayOrder: number | null
  popularityScore: number
  viewCount: number
  helpfulVotes: number
  unhelpfulVotes: number
  lastUpdated: Date
}

/**
 * Interfaz para temas de FAQ municipales
 */
export interface FAQTheme {
  id: string
  name: string
  description: string | null
  displayOrder: number | null
  dependencyId: string | null
  subdependencyId: string | null
  count: number
}

/**
 * Opciones de búsqueda para FAQ municipales
 */
export interface FAQSearchOptions {
  theme?: string
  dependencyId?: string
  subdependencyId?: string
  limit?: number
  includeKeywords?: boolean
}

/**
 * Servicio para gestionar preguntas frecuentes municipales
 * Conectado con Supabase usando búsqueda de texto completo en español
 */
class FAQService {
  private static instance: FAQService
  private themesCache: Map<string, FAQTheme> = new Map()
  private cacheExpiry = 5 * 60 * 1000 // 5 minutos
  private lastCacheUpdate = 0

  private constructor() {}

  static getInstance(): FAQService {
    if (!FAQService.instance) {
      FAQService.instance = new FAQService()
    }
    return FAQService.instance
  }

  /**
   * Convertir datos de base de datos a interfaz FAQItem
   */
  private mapFAQFromDB(faqRow: MunicipalFAQRow, themeName?: string): FAQItem {
    return {
      id: faqRow.id,
      question: faqRow.question,
      answer: faqRow.answer,
      theme: themeName || '',
      themeId: faqRow.theme_id || '',
      keywords: faqRow.keywords || [],
      relatedProcedures: faqRow.related_procedures || [],
      displayOrder: null, // faqs table doesn't have display_order
      popularityScore: faqRow.popularity_score || 0,
      viewCount: faqRow.view_count || 0,
      helpfulVotes: faqRow.helpful_votes || 0,
      unhelpfulVotes: faqRow.unhelpful_votes || 0,
      lastUpdated: new Date(faqRow.updated_at || faqRow.created_at || '')
    }
  }

  /**
   * Convertir datos de base de datos a interfaz FAQTheme
   */
  private mapThemeFromDB(themeRow: FAQThemeRow, count: number = 0): FAQTheme {
    return {
      id: themeRow.id,
      name: themeRow.name,
      description: themeRow.description,
      displayOrder: themeRow.display_order,
      dependencyId: themeRow.dependency_id,
      subdependencyId: themeRow.subdependency_id,
      count
    }
  }

  /**
   * Obtener todos los temas desde Supabase
   */
  async getThemes(): Promise<FAQTheme[]> {
    try {
      // Verificar cache
      const now = Date.now()
      if (now - this.lastCacheUpdate < this.cacheExpiry && this.themesCache.size > 0) {
        return Array.from(this.themesCache.values()).sort((a, b) => (a.displayOrder || 0) - (b.displayOrder || 0))
      }

      // Obtener temas activos
      const { data: themesData, error: themesError } = await supabase
        .from('faq_themes')
        .select('*')
        .eq('is_active', true)
        .order('display_order')

      if (themesError) {
        console.error('Error fetching FAQ themes:', themesError)
        return []
      }

      // Obtener conteo real de FAQs por tema
      const { data: faqCounts, error: countError } = await supabase
        .from('municipal_faqs')
        .select('theme_id')
        .eq('is_active', true)

      if (countError) {
        console.error('Error fetching FAQ counts:', countError)
      }

      // Crear mapa de conteos
      const countMap = new Map<string, number>()
      faqCounts?.forEach(faq => {
        if (faq.theme_id) {
          countMap.set(faq.theme_id, (countMap.get(faq.theme_id) || 0) + 1)
        }
      })

      // Mapear y cachear temas
      const themes = themesData?.map(theme =>
        this.mapThemeFromDB(theme, countMap.get(theme.id) || 0)
      ) || []

      // Actualizar cache
      this.themesCache.clear()
      themes.forEach(theme => this.themesCache.set(theme.id, theme))
      this.lastCacheUpdate = now

      return themes.sort((a, b) => (a.displayOrder || 0) - (b.displayOrder || 0))
    } catch (error) {
      console.error('Error in getThemes:', error)
      return []
    }
  }

  /**
   * Obtener FAQs por tema desde Supabase
   */
  async getFAQsByTheme(themeId: string, limit?: number): Promise<FAQItem[]> {
    try {
      let query = supabase
        .from('municipal_faqs')
        .select(`
          *,
          faq_themes!inner(name)
        `)
        .eq('theme_id', themeId)
        .eq('is_active', true)
        .order('popularity_score', { ascending: false })

      if (limit) {
        query = query.limit(limit)
      }

      const { data, error } = await query

      if (error) {
        console.error('Error fetching FAQs by theme:', error)
        return []
      }

      return data?.map(faq =>
        this.mapFAQFromDB(faq, faq.faq_themes?.name)
      ) || []
    } catch (error) {
      console.error('Error in getFAQsByTheme:', error)
      return []
    }
  }

  /**
   * Buscar FAQs usando búsqueda de texto completo en español
   */
  async searchFAQs(query: string, options: FAQSearchOptions = {}): Promise<FAQItem[]> {
    const startTime = Date.now()
    const { theme, dependencyId, subdependencyId, limit = 10, includeKeywords = true } = options

    if (!query.trim()) {
      return []
    }

    try {
      const searchTerm = query.trim()

      // Construir query base con búsqueda de texto completo
      let supabaseQuery = supabase
        .from('municipal_faqs')
        .select(`
          *,
          faq_themes!inner(name)
        `)
        .eq('is_active', true)

      // Filtrar por tema si se especifica
      if (theme) {
        supabaseQuery = supabaseQuery.eq('theme_id', theme)
      }

      // Para búsqueda simple usando ILIKE (fallback)
      const { data, error } = await supabaseQuery
        .or(`question.ilike.%${searchTerm}%,answer.ilike.%${searchTerm}%`)
        .order('popularity_score', { ascending: false })
        .limit(limit)

      if (error) {
        console.error('Error searching FAQs:', error)
        // Fallback a búsqueda simple si falla la búsqueda de texto completo
        return this.fallbackSearch(searchTerm, options)
      }

      // Mapear resultados
      const results = data?.map(faq =>
        this.mapFAQFromDB(faq, faq.faq_themes?.name)
      ) || []

      const responseTime = Date.now() - startTime

      // Registrar analytics
      if (results.length === 0) {
        faqAnalytics.trackNoResults(query, theme)
      } else {
        faqAnalytics.trackSearch(query, results.length, responseTime, theme)
      }

      return results
    } catch (error) {
      console.error('Error in searchFAQs:', error)
      return this.fallbackSearch(query, options)
    }
  }

  /**
   * Búsqueda de respaldo usando ILIKE
   */
  private async fallbackSearch(query: string, options: FAQSearchOptions): Promise<FAQItem[]> {
    try {
      const { theme, limit = 10 } = options
      const searchTerm = query.toLowerCase().trim()

      let supabaseQuery = supabase
        .from('municipal_faqs')
        .select(`
          *,
          faq_themes!inner(name)
        `)
        .eq('is_active', true)

      if (theme) {
        supabaseQuery = supabaseQuery.eq('theme_id', theme)
      }

      const { data, error } = await supabaseQuery
        .or(`question.ilike.%${searchTerm}%,answer.ilike.%${searchTerm}%`)
        .order('popularity_score', { ascending: false })
        .limit(limit)

      if (error) {
        console.error('Error in fallback search:', error)
        return []
      }

      return data?.map(faq =>
        this.mapFAQFromDB(faq, faq.faq_themes?.name)
      ) || []
    } catch (error) {
      console.error('Error in fallbackSearch:', error)
      return []
    }
  }

  /**
   * Calcular puntuación de relevancia para FAQs municipales
   */
  private calculateRelevanceScore(faq: FAQItem, searchTerm: string): number {
    let score = 0
    const term = searchTerm.toLowerCase()

    // Coincidencia exacta en pregunta (peso alto)
    if (faq.question.toLowerCase().includes(term)) {
      score += 100
    }

    // Coincidencia en respuesta (peso medio)
    if (faq.answer.toLowerCase().includes(term)) {
      score += 50
    }

    // Coincidencia en keywords (peso medio)
    faq.keywords.forEach(keyword => {
      if (keyword.toLowerCase().includes(term)) {
        score += 30
      }
    })

    // Popularidad (peso bajo)
    score += faq.popularityScore * 0.1

    return score
  }

  /**
   * Obtener FAQs más populares desde Supabase
   */
  async getPopularFAQs(limit: number = 5): Promise<FAQItem[]> {
    try {
      const { data, error } = await supabase
        .from('municipal_faqs')
        .select(`
          *,
          faq_themes!inner(name)
        `)
        .eq('is_active', true)
        .order('popularity_score', { ascending: false })
        .limit(limit)

      if (error) {
        console.error('Error fetching popular FAQs:', error)
        return []
      }

      return data?.map(faq =>
        this.mapFAQFromDB(faq, faq.faq_themes?.name)
      ) || []
    } catch (error) {
      console.error('Error in getPopularFAQs:', error)
      return []
    }
  }

  /**
   * Obtener FAQ por ID desde Supabase
   */
  async getFAQById(id: string): Promise<FAQItem | null> {
    try {
      const { data, error } = await supabase
        .from('municipal_faqs')
        .select(`
          *,
          faq_themes!inner(name)
        `)
        .eq('id', id)
        .eq('is_active', true)
        .single()

      if (error) {
        console.error('Error fetching FAQ by ID:', error)
        return null
      }

      const faq = this.mapFAQFromDB(data, data.faq_themes?.name)

      // Registrar visualización y actualizar contador
      faqAnalytics.trackFAQView(faq.id, faq.question)

      // Incrementar view_count en la base de datos
      await supabase
        .from('municipal_faqs')
        .update({ view_count: (data.view_count || 0) + 1 })
        .eq('id', id)

      return faq
    } catch (error) {
      console.error('Error in getFAQById:', error)
      return null
    }
  }

  /**
   * Obtener estadísticas del FAQ municipal desde Supabase
   */
  async getFAQStats(): Promise<{
    totalFAQs: number
    totalThemes: number
    averagePopularity: number
    mostPopularTheme: string
  }> {
    try {
      // Obtener estadísticas de FAQs municipales
      const { data: faqStats, error: faqError } = await supabase
        .from('municipal_faqs')
        .select('popularity_score, theme_id')
        .eq('is_active', true)

      if (faqError) {
        console.error('Error fetching FAQ stats:', faqError)
        return { totalFAQs: 0, totalThemes: 0, averagePopularity: 0, mostPopularTheme: '' }
      }

      // Obtener estadísticas de temas directamente
      const { data: themeStats, error: themeError } = await supabase
        .from('faq_themes')
        .select('id, name')
        .eq('is_active', true)

      if (themeError) {
        console.error('Error fetching theme stats:', themeError)
      }

      // Calcular estadísticas
      const totalFAQs = faqStats?.length || 0
      const totalThemes = themeStats?.length || 0
      const averagePopularity = totalFAQs > 0
        ? Math.round(faqStats.reduce((sum, faq) => sum + (faq.popularity_score || 0), 0) / totalFAQs)
        : 0

      // Calcular tema más popular
      const themeCount = new Map<string, number>()
      faqStats?.forEach(faq => {
        if (faq.theme_id) {
          themeCount.set(faq.theme_id, (themeCount.get(faq.theme_id) || 0) + 1)
        }
      })

      let mostPopularTheme = ''
      let maxCount = 0
      themeCount.forEach((count, themeId) => {
        if (count > maxCount) {
          maxCount = count
          const theme = themeStats?.find(theme => theme.id === themeId)
          mostPopularTheme = theme?.name || ''
        }
      })

      return {
        totalFAQs,
        totalThemes,
        averagePopularity,
        mostPopularTheme
      }
    } catch (error) {
      console.error('Error in getFAQStats:', error)
      return { totalFAQs: 0, totalThemes: 0, averagePopularity: 0, mostPopularTheme: '' }
    }
  }

  /**
   * Método de compatibilidad para getCategories (alias para getThemes)
   */
  async getCategories(): Promise<FAQTheme[]> {
    return this.getThemes()
  }

  /**
   * Método de compatibilidad para getFAQsByCategory (alias para getFAQsByTheme)
   */
  async getFAQsByCategory(categoryId: string, limit?: number): Promise<FAQItem[]> {
    return this.getFAQsByTheme(categoryId, limit)
  }
}

export default FAQService.getInstance()
