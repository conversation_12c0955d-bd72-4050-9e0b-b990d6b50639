'use client'

import React from 'react'
import {
  Eye,
  Clock,
  DollarSign,
  Building2,
  Monitor,
  FileText,
  ExternalLink,
  Heart,
  CheckCircle,
  AlertCircle,
  Sparkles,
  Info
} from 'lucide-react'
import { Card, CardContent } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'
import {
  SuitEnhancedProcedure,
  enhanceProcedureWithSuit,
  getSuitEnhancementStats,
  formatEnhancedResponseTime,
  formatEnhancedCost
} from '@/types/suit-enhanced-procedure'

interface Dependency {
  id: string
  name: string
  acronym?: string
}

interface Subdependency {
  id: string
  name: string
}

interface ProcedureCardEnhancedProps {
  procedure: SuitEnhancedProcedure
  onViewDetails: (procedure: SuitEnhancedProcedure) => void
  onToggleFavorite?: (procedureId: string) => void
  isFavorite?: boolean
  layout?: 'compact' | 'detailed'
  className?: string
  showPreview?: boolean
}

export function ProcedureCardEnhanced({
  procedure,
  onViewDetails,
  onToggleFavorite,
  isFavorite = false,
  layout = 'compact',
  className,
  showPreview = true
}: ProcedureCardEnhancedProps) {

  // Obtener estadísticas de mejora SUIT
  const suitStats = getSuitEnhancementStats(procedure)

  const getModalityInfo = (bestModality?: string) => {
    if (bestModality === 'Virtual') return { label: 'Virtual', color: 'bg-green-100 text-green-800', icon: Monitor }
    if (bestModality === 'Presencial') return { label: 'Presencial', color: 'bg-chia-blue-100 text-chia-blue-800', icon: Building2 }
    return { label: 'Mixta', color: 'bg-purple-100 text-purple-800', icon: FileText }
  }

  const getProcedureTypeInfo = (type?: string) => {
    if (type === 'TRAMITE') return { label: 'Trámite', color: 'bg-primary/10 text-primary' }
    if (type === 'OPA') return { label: 'OPA', color: 'bg-chia-green-100 text-chia-green-800' }
    return { label: 'Procedimiento', color: 'bg-gray-100 text-gray-800' }
  }

  const getUrgencyLevel = (responseTime?: string) => {
    if (!responseTime) return null
    const time = responseTime.toLowerCase()
    if (time.includes('inmediato') || time.includes('mismo día')) {
      return { level: 'high', color: 'text-red-600', icon: AlertCircle }
    }
    if (time.includes('1') || time.includes('2') || time.includes('3')) {
      return { level: 'medium', color: 'text-yellow-600', icon: Clock }
    }
    return { level: 'low', color: 'text-green-600', icon: CheckCircle }
  }

  const modalityInfo = getModalityInfo(procedure.best_modality)
  const typeInfo = getProcedureTypeInfo(procedure.procedure_type)
  const urgencyInfo = getUrgencyLevel(procedure.best_response_time)
  const ModalityIcon = modalityInfo.icon

  // Usar los mejores requisitos disponibles (SUIT o originales)
  const topRequirements = procedure.best_requirements?.slice(0, 3) || []

  return (
    <Card className={cn(
      "hover:shadow-lg transition-all duration-300 border-l-4 border-l-transparent hover:border-l-primary",
      "group cursor-pointer",
      className
    )}>
      <CardContent className="p-6">
        <div className="space-y-4">
          {/* Header */}
          <div className="flex justify-between items-start">
            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between mb-2">
                <div className="flex-1">
                  {/* Código del procedimiento */}
                  {(procedure.codigo_tramite || procedure.code) && (
                    <div className="mb-1">
                      <Badge variant="outline" className="text-xs font-mono bg-primary/5 text-primary border-primary/20">
                        {procedure.codigo_tramite || procedure.code}
                      </Badge>
                    </div>
                  )}
                  <div className="flex items-start gap-2">
                    <h3 className="font-semibold text-gray-900 line-clamp-2 group-hover:text-primary transition-colors flex-1">
                      {procedure.name}
                    </h3>
                    {/* Indicador de mejora SUIT */}
                    {suitStats.hasEnhancement && (
                      <div className="flex items-center gap-1 mt-0.5">
                        <Sparkles className="h-3 w-3 text-amber-500" />
                        <span className="text-xs text-amber-600 font-medium">
                          +{suitStats.enhancementCount}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
                {onToggleFavorite && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="ml-2 p-1 h-8 w-8 opacity-0 group-hover:opacity-100 transition-opacity"
                    onClick={(e) => {
                      e.stopPropagation()
                      onToggleFavorite(procedure.id)
                    }}
                  >
                    <Heart className={cn(
                      "h-4 w-4",
                      isFavorite ? "fill-red-500 text-red-500" : "text-gray-400 hover:text-red-500"
                    )} />
                  </Button>
                )}
              </div>

              {/* Dependency Info */}
              {procedure.dependency && (
                <div className="flex items-center text-sm text-gray-600 mb-2">
                  <Building2 className="h-4 w-4 mr-1" />
                  <span className="truncate">{procedure.dependency.name}</span>
                  {procedure.subdependency && (
                    <span className="ml-2 text-gray-500 truncate">
                      • {procedure.subdependency.name}
                    </span>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Badges */}
          <div className="flex flex-wrap gap-2">
            <Badge className={typeInfo.color}>
              {typeInfo.label}
            </Badge>
            <Badge className={modalityInfo.color}>
              <ModalityIcon className="h-3 w-3 mr-1" />
              {modalityInfo.label}
            </Badge>
            {urgencyInfo && (
              <Badge variant="outline" className={urgencyInfo.color}>
                <urgencyInfo.icon className="h-3 w-3 mr-1" />
                Urgente
              </Badge>
            )}
          </div>

          {/* Key Information */}
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex items-center text-gray-600">
              <Clock className="h-4 w-4 mr-2 text-gray-400" />
              <div>
                <div className="text-xs text-gray-500">Tiempo</div>
                <div className="font-medium">{formatEnhancedResponseTime(procedure)}</div>
              </div>
            </div>
            <div className="flex items-center text-gray-600">
              <DollarSign className="h-4 w-4 mr-2 text-gray-400" />
              <div>
                <div className="text-xs text-gray-500">Costo</div>
                <div className="font-medium">{formatEnhancedCost(procedure)}</div>
              </div>
            </div>
          </div>

          {/* Description Preview */}
          {showPreview && procedure.best_description && layout === 'detailed' && (
            <div className="relative">
              <p className="text-sm text-gray-600 line-clamp-2">
                {procedure.best_description}
              </p>
              {/* Indicador de mejora SUIT en descripción */}
              {procedure.suit_data?.descripcion_detallada &&
               procedure.best_description === procedure.suit_data.descripcion_detallada && (
                <div className="absolute top-0 right-0">
                  <Info className="h-3 w-3 text-amber-500" title="Información mejorada con SUIT" />
                </div>
              )}
            </div>
          )}

          {/* Top Requirements Preview */}
          {showPreview && topRequirements.length > 0 && layout === 'detailed' && (
            <div>
              <div className="flex items-center gap-1 text-xs font-medium text-gray-700 mb-1">
                <span>Requisitos principales:</span>
                {/* Indicador de mejora SUIT en requisitos */}
                {procedure.suit_data?.requisitos &&
                 procedure.best_requirements === procedure.suit_data.requisitos && (
                  <Sparkles className="h-3 w-3 text-amber-500" title="Requisitos mejorados con SUIT" />
                )}
              </div>
              <ul className="text-xs text-gray-600 space-y-1">
                {topRequirements.map((req, index) => (
                  <li key={index} className="flex items-start">
                    <span className="inline-block w-1 h-1 bg-gray-400 rounded-full mt-2 mr-2 flex-shrink-0" />
                    <span className="line-clamp-1">{req}</span>
                  </li>
                ))}
                {(procedure.best_requirements?.length || 0) > 3 && (
                  <li className="text-primary font-medium">
                    +{(procedure.best_requirements?.length || 0) - 3} requisitos más
                  </li>
                )}
              </ul>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex items-center justify-between pt-2 border-t border-gray-100">
            <div className="flex space-x-2">
              {procedure.suit_url && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-xs p-2 h-8"
                  onClick={(e) => {
                    e.stopPropagation()
                    window.open(procedure.suit_url, '_blank')
                  }}
                >
                  <ExternalLink className="h-3 w-3 mr-1" />
                  SUIT
                </Button>
              )}
              {procedure.gov_url && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-xs p-2 h-8"
                  onClick={(e) => {
                    e.stopPropagation()
                    window.open(procedure.gov_url, '_blank')
                  }}
                >
                  <ExternalLink className="h-3 w-3 mr-1" />
                  Gov.co
                </Button>
              )}
            </div>

            <Button
              size="sm"
              variant="outline"
              className="hover:bg-primary/5 hover:border-primary/30"
              onClick={(e) => {
                e.stopPropagation()
                onViewDetails(procedure)
              }}
            >
              <Eye className="h-4 w-4 mr-2" />
              Ver detalles
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
