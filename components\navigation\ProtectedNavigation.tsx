'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname, useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Home,
  FileText,
  MessageSquare,
  Bell,
  User,
  Settings,
  LogOut,
  Menu,
  X,
  Shield,
  Building,
  BarChart3,
  Users,
  ChevronDown,
  Folder,
  Search
} from 'lucide-react'
import { useAuth, useRole, usePermissions } from '@/hooks'
import { ChiaLogoIcon } from '@/components/ui/chia-logo'
import { 
  ShowForAdmin, 
  ShowForSuperAdmin, 
  ShowForCitizen,
  UserRoleDisplay 
} from '@/components/auth'
import { createClient } from '@/lib/supabase/client'

interface NavigationItem {
  name: string
  href: string
  icon: React.ComponentType<{ className?: string }>
  roles?: string[]
  permissions?: string[]
  badge?: number
}

export function ProtectedNavigation() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const pathname = usePathname()
  const router = useRouter()
  const { user, profile } = useAuth()
  const { role, isAdmin, isSuperAdmin, isCitizen } = useRole()
  const { hasPermission } = usePermissions()
  const supabase = createClient()

  // Navigation items based on roles
  const navigationItems: NavigationItem[] = [
    {
      name: 'Dashboard',
      href: '/dashboard',
      icon: Home,
      roles: ['ciudadano', 'admin', 'super_admin']
    },
    {
      name: 'Mis Trámites',
      href: '/tramites',
      icon: FileText,
      roles: ['ciudadano']
    },
    {
      name: 'Documentos',
      href: '/documentos',
      icon: Folder,
      roles: ['ciudadano']
    },
    {
      name: 'Consulta Trámites',
      href: '/consulta-tramites',
      icon: Search,
      roles: ['ciudadano', 'admin', 'super_admin']
    },
    {
      name: 'Radicar PQRS',
      href: 'https://pacochia.gov.co/realice-sus-peticiones-quejas-reclamos-sugerencias-y-denuncias/',
      icon: MessageSquare,
      roles: ['ciudadano', 'admin', 'super_admin']
    },
    {
      name: 'Administración',
      href: '/admin',
      icon: Shield,
      roles: ['admin', 'super_admin']
    },
    {
      name: 'Notificaciones',
      href: '/notificaciones',
      icon: Bell,
      roles: ['ciudadano', 'admin', 'super_admin'],
      badge: 3 // TODO: Get actual notification count
    }
  ]

  // Admin quick actions
  const adminActions = [
    {
      name: 'Gestionar Usuarios',
      href: '/admin?tab=users',
      icon: Users,
      roles: ['admin', 'super_admin']
    },
    {
      name: 'Ver Reportes',
      href: '/admin?tab=reports',
      icon: BarChart3,
      roles: ['admin', 'super_admin']
    },
    {
      name: 'Dependencias',
      href: '/admin?tab=dependencies',
      icon: Building,
      roles: ['super_admin']
    }
  ]

  const handleSignOut = async () => {
    try {
      await supabase.auth.signOut()
      router.push('/auth/login')
    } catch (error) {
      console.error('Error signing out:', error)
    }
  }

  const isActiveRoute = (href: string) => {
    if (href === '/dashboard') {
      return pathname === '/dashboard'
    }
    return pathname.startsWith(href)
  }

  const canAccessRoute = (item: NavigationItem) => {
    // Check role access
    if (item.roles && !item.roles.includes(role?.name || '')) {
      return false
    }
    
    // Check permission access
    if (item.permissions && !item.permissions.some(permission => hasPermission(permission))) {
      return false
    }
    
    return true
  }

  if (!user || !profile) {
    return null
  }

  return (
    <nav className="bg-white shadow-sm border-b sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo and Brand */}
          <div className="flex items-center space-x-3">
            <Link href="/dashboard" className="flex items-center space-x-3">
              <ChiaLogoIcon size="sm" />
              <div className="hidden sm:block">
                <h1 className="text-lg font-bold text-chia-blue-900">Municipio de Chía</h1>
                <p className="text-xs text-chia-blue-600">Sistema de Atención Ciudadana</p>
              </div>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-1">
            {navigationItems
              .filter(canAccessRoute)
              .map((item) => {
                const isExternal = item.href.startsWith('http')
                return (
                  <Link
                    key={item.name}
                    href={item.href}
                    target={isExternal ? '_blank' : undefined}
                    rel={isExternal ? 'noopener noreferrer' : undefined}
                    className={`nav-item px-3 py-2 rounded-md text-sm font-medium flex items-center space-x-2 ${
                      isActiveRoute(item.href)
                        ? 'bg-primary/10 text-primary'
                        : 'text-gray-600 hover:text-primary hover:bg-gray-50'
                    }`}
                  >
                    <item.icon className="h-4 w-4" />
                    <span>{item.name}</span>
                    {item.badge && item.badge > 0 && (
                      <Badge variant="destructive" className="ml-1 text-xs">
                        {item.badge}
                      </Badge>
                    )}
                  </Link>
                )
              })}
          </div>

          {/* User Menu */}
          <div className="flex items-center space-x-4">
            {/* Admin Quick Actions */}
            {(isAdmin || isSuperAdmin) && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="hidden lg:flex">
                    <Shield className="h-4 w-4 mr-2" />
                    Admin
                    <ChevronDown className="h-4 w-4 ml-2" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <DropdownMenuLabel>Acciones Administrativas</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  {adminActions
                    .filter(action => !action.roles || action.roles.includes(role?.name || ''))
                    .map((action) => (
                      <DropdownMenuItem key={action.name} asChild>
                        <Link href={action.href} className="flex items-center">
                          <action.icon className="h-4 w-4 mr-2" />
                          {action.name}
                        </Link>
                      </DropdownMenuItem>
                    ))}
                </DropdownMenuContent>
              </DropdownMenu>
            )}

            {/* User Profile Menu */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="flex items-center space-x-2">
                  <div className="w-8 h-8 bg-chia-blue-600 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-medium">
                      {profile.full_name?.charAt(0).toUpperCase() || 'U'}
                    </span>
                  </div>
                  <div className="hidden sm:block text-left">
                    <p className="text-sm font-medium text-gray-900">
                      {profile.full_name}
                    </p>
                    <UserRoleDisplay className="text-xs" />
                  </div>
                  <ChevronDown className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuLabel>
                  <div>
                    <p className="font-medium">{profile.full_name}</p>
                    <p className="text-sm text-muted-foreground">{user.email}</p>
                    <UserRoleDisplay className="text-xs mt-1" />
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href="/perfil" className="flex items-center">
                    <User className="h-4 w-4 mr-2" />
                    Mi Perfil
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/configuracion" className="flex items-center">
                    <Settings className="h-4 w-4 mr-2" />
                    Configuración
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleSignOut} className="text-red-600">
                  <LogOut className="h-4 w-4 mr-2" />
                  Cerrar Sesión
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            {/* Mobile Menu Button */}
            <Button
              variant="ghost"
              size="sm"
              className="md:hidden"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            >
              {isMobileMenuOpen ? (
                <X className="h-5 w-5" />
              ) : (
                <Menu className="h-5 w-5" />
              )}
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMobileMenuOpen && (
          <div className="md:hidden border-t bg-white">
            <div className="px-2 pt-2 pb-3 space-y-1">
              {navigationItems
                .filter(canAccessRoute)
                .map((item) => {
                  const isExternal = item.href.startsWith('http')
                  return (
                    <Link
                      key={item.name}
                      href={item.href}
                      target={isExternal ? '_blank' : undefined}
                      rel={isExternal ? 'noopener noreferrer' : undefined}
                      className={`block px-3 py-2 rounded-md text-base font-medium flex items-center space-x-2 ${
                        isActiveRoute(item.href)
                          ? 'bg-primary/10 text-primary'
                          : 'text-gray-600 hover:text-primary hover:bg-gray-50'
                      }`}
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      <item.icon className="h-5 w-5" />
                      <span>{item.name}</span>
                      {item.badge && item.badge > 0 && (
                        <Badge variant="destructive" className="ml-auto text-xs">
                          {item.badge}
                        </Badge>
                      )}
                    </Link>
                  )
                })}
              
              {/* Mobile Admin Actions */}
              {(isAdmin || isSuperAdmin) && (
                <>
                  <div className="border-t pt-2 mt-2">
                    <p className="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider">
                      Administración
                    </p>
                    {adminActions
                      .filter(action => !action.roles || action.roles.includes(role?.name || ''))
                      .map((action) => (
                        <Link
                          key={action.name}
                          href={action.href}
                          className="block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-chia-blue-600 hover:bg-gray-50 flex items-center space-x-2"
                          onClick={() => setIsMobileMenuOpen(false)}
                        >
                          <action.icon className="h-5 w-5" />
                          <span>{action.name}</span>
                        </Link>
                      ))}
                  </div>
                </>
              )}
            </div>
          </div>
        )}
      </div>
    </nav>
  )
}
