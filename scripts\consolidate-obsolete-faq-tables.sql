-- =============================================
-- CONSOLIDACIÓN DE TABLAS FAQ OBSOLETAS
-- Sistema Municipal de Chía
-- =============================================
-- Fecha: 2025-07-02
-- Propósito: Migrar datos únicos y eliminar tablas obsoletas
-- CRÍTICO: Ejecutar en orden secuencial

-- =============================================
-- FASE 0: BACKUP Y VALIDACIÓN PRE-MIGRACIÓN
-- =============================================

-- Crear backups de seguridad
CREATE TABLE faqs_backup AS SELECT * FROM faqs;
CREATE TABLE faq_categories_backup AS SELECT * FROM faq_categories;
CREATE TABLE faq_analytics_backup AS SELECT * FROM faq_analytics;

-- Validar estado inicial
DO $$
DECLARE
    faqs_count INTEGER;
    categories_count INTEGER;
    analytics_count INTEGER;
    municipal_faqs_count INTEGER;
    themes_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO faqs_count FROM faqs WHERE is_active = true;
    SELECT COUNT(*) INTO categories_count FROM faq_categories WHERE is_active = true;
    SELECT COUNT(*) INTO analytics_count FROM faq_analytics;
    SELECT COUNT(*) INTO municipal_faqs_count FROM municipal_faqs WHERE is_active = true;
    SELECT COUNT(*) INTO themes_count FROM faq_themes WHERE is_active = true;
    
    RAISE NOTICE '=== ESTADO PRE-MIGRACIÓN ===';
    RAISE NOTICE 'FAQs obsoletas activas: %', faqs_count;
    RAISE NOTICE 'Categorías obsoletas activas: %', categories_count;
    RAISE NOTICE 'Registros analytics: %', analytics_count;
    RAISE NOTICE 'FAQs municipales actuales: %', municipal_faqs_count;
    RAISE NOTICE 'Temas actuales: %', themes_count;
    
    IF faqs_count != 10 THEN
        RAISE EXCEPTION 'ERROR: Se esperaban 10 FAQs obsoletas, encontradas: %', faqs_count;
    END IF;
    
    IF categories_count != 6 THEN
        RAISE EXCEPTION 'ERROR: Se esperaban 6 categorías obsoletas, encontradas: %', categories_count;
    END IF;
    
    IF analytics_count != 0 THEN
        RAISE EXCEPTION 'ERROR: Se esperaba tabla analytics vacía, encontrados: % registros', analytics_count;
    END IF;
END $$;

-- =============================================
-- FASE 1: MIGRACIÓN DE DATOS ÚNICOS
-- =============================================

-- Crear mapeo temporal de categorías obsoletas a temas actuales
CREATE TEMP TABLE category_theme_mapping AS
WITH obsolete_categories AS (
    SELECT id, name, description FROM faq_categories WHERE is_active = true
),
theme_matches AS (
    SELECT 
        oc.id as old_category_id,
        oc.name as old_category_name,
        CASE 
            WHEN oc.name ILIKE '%impuesto%' OR oc.name ILIKE '%tributo%' THEN 
                (SELECT id FROM faq_themes WHERE name ILIKE '%impuesto%' OR name ILIKE '%tributo%' LIMIT 1)
            WHEN oc.name ILIKE '%licencia%' OR oc.name ILIKE '%permiso%' THEN 
                (SELECT id FROM faq_themes WHERE name ILIKE '%licencia%' LIMIT 1)
            WHEN oc.name ILIKE '%certificado%' THEN 
                (SELECT id FROM faq_themes WHERE name ILIKE '%certificad%' LIMIT 1)
            WHEN oc.name ILIKE '%servicio%' THEN 
                (SELECT id FROM faq_themes WHERE name ILIKE '%servicio%' LIMIT 1)
            WHEN oc.name ILIKE '%trámite%' THEN 
                (SELECT id FROM faq_themes WHERE name ILIKE '%trámite%' LIMIT 1)
            WHEN oc.name ILIKE '%pago%' OR oc.name ILIKE '%facturación%' THEN 
                (SELECT id FROM faq_themes WHERE name ILIKE '%pago%' LIMIT 1)
            ELSE 
                -- Fallback: usar el primer tema disponible
                (SELECT id FROM faq_themes WHERE is_active = true ORDER BY display_order LIMIT 1)
        END as new_theme_id
    FROM obsolete_categories oc
)
SELECT * FROM theme_matches WHERE new_theme_id IS NOT NULL;

-- Verificar mapeo
DO $$
DECLARE
    mapped_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO mapped_count FROM category_theme_mapping;
    RAISE NOTICE '=== MAPEO DE CATEGORÍAS ===';
    RAISE NOTICE 'Categorías mapeadas exitosamente: %', mapped_count;
    
    IF mapped_count != 6 THEN
        RAISE EXCEPTION 'ERROR: Falló el mapeo de categorías. Esperadas: 6, Mapeadas: %', mapped_count;
    END IF;
END $$;

-- Migrar FAQs únicas de tabla obsoleta a tabla actual
INSERT INTO municipal_faqs (
    question, 
    answer, 
    theme_id, 
    keywords, 
    related_procedures,
    popularity_score, 
    view_count, 
    helpful_votes, 
    unhelpful_votes,
    is_active, 
    created_at, 
    updated_at,
    search_vector
)
SELECT 
    f.question,
    f.answer,
    COALESCE(ctm.new_theme_id, (SELECT id FROM faq_themes WHERE is_active = true LIMIT 1)) as theme_id,
    f.tags as keywords,
    f.related_procedures,
    f.popularity as popularity_score,
    f.view_count,
    f.helpful_votes,
    f.unhelpful_votes,
    f.is_active,
    f.created_at,
    f.updated_at,
    to_tsvector('spanish', f.question || ' ' || f.answer) as search_vector
FROM faqs f
LEFT JOIN category_theme_mapping ctm ON f.category_id = ctm.old_category_id
WHERE f.is_active = true;

-- Validar migración
DO $$
DECLARE
    migrated_count INTEGER;
    total_municipal_faqs INTEGER;
BEGIN
    SELECT COUNT(*) INTO migrated_count 
    FROM municipal_faqs 
    WHERE created_at >= (SELECT MIN(created_at) FROM faqs);
    
    SELECT COUNT(*) INTO total_municipal_faqs 
    FROM municipal_faqs 
    WHERE is_active = true;
    
    RAISE NOTICE '=== MIGRACIÓN DE FAQs ===';
    RAISE NOTICE 'FAQs migradas: %', migrated_count;
    RAISE NOTICE 'Total FAQs municipales: %', total_municipal_faqs;
    
    IF migrated_count < 10 THEN
        RAISE EXCEPTION 'ERROR: Migración incompleta. Esperadas: 10, Migradas: %', migrated_count;
    END IF;
END $$;

-- =============================================
-- FASE 2: ACTUALIZACIÓN DE DEPENDENCIAS
-- =============================================

-- Actualizar tabla faq_analytics para usar tablas actuales
-- Paso 1: Eliminar constraints obsoletos
ALTER TABLE faq_analytics DROP CONSTRAINT IF EXISTS faq_analytics_faq_id_fkey;
ALTER TABLE faq_analytics DROP CONSTRAINT IF EXISTS faq_analytics_category_id_fkey;

-- Paso 2: Renombrar columna para claridad
ALTER TABLE faq_analytics RENAME COLUMN category_id TO theme_id;

-- Paso 3: Crear nuevos constraints hacia tablas actuales
ALTER TABLE faq_analytics 
    ADD CONSTRAINT faq_analytics_faq_id_fkey 
    FOREIGN KEY (faq_id) REFERENCES municipal_faqs(id) ON DELETE SET NULL;

ALTER TABLE faq_analytics 
    ADD CONSTRAINT faq_analytics_theme_id_fkey 
    FOREIGN KEY (theme_id) REFERENCES faq_themes(id) ON DELETE SET NULL;

-- Eliminar triggers de tablas obsoletas
DROP TRIGGER IF EXISTS update_faqs_updated_at ON faqs;
DROP TRIGGER IF EXISTS update_faq_categories_updated_at ON faq_categories;

RAISE NOTICE '=== DEPENDENCIAS ACTUALIZADAS ===';
RAISE NOTICE 'Constraints de faq_analytics actualizados hacia tablas actuales';
RAISE NOTICE 'Triggers obsoletos eliminados';

-- =============================================
-- FASE 3: ELIMINACIÓN SEGURA DE TABLAS OBSOLETAS
-- =============================================

-- Eliminar tablas obsoletas (CASCADE para eliminar dependencias restantes)
DROP TABLE IF EXISTS faqs CASCADE;
DROP TABLE IF EXISTS faq_categories CASCADE;

RAISE NOTICE '=== TABLAS OBSOLETAS ELIMINADAS ===';
RAISE NOTICE 'Tabla "faqs" eliminada';
RAISE NOTICE 'Tabla "faq_categories" eliminada';

-- =============================================
-- FASE 4: VALIDACIÓN POST-CONSOLIDACIÓN
-- =============================================

DO $$
DECLARE
    final_municipal_faqs INTEGER;
    final_themes INTEGER;
    analytics_constraints INTEGER;
BEGIN
    -- Contar registros finales
    SELECT COUNT(*) INTO final_municipal_faqs FROM municipal_faqs WHERE is_active = true;
    SELECT COUNT(*) INTO final_themes FROM faq_themes WHERE is_active = true;
    
    -- Verificar constraints de analytics
    SELECT COUNT(*) INTO analytics_constraints 
    FROM information_schema.table_constraints 
    WHERE table_name = 'faq_analytics' 
    AND constraint_type = 'FOREIGN KEY'
    AND constraint_name IN ('faq_analytics_faq_id_fkey', 'faq_analytics_theme_id_fkey');
    
    RAISE NOTICE '=== VALIDACIÓN FINAL ===';
    RAISE NOTICE 'FAQs municipales totales: %', final_municipal_faqs;
    RAISE NOTICE 'Temas activos: %', final_themes;
    RAISE NOTICE 'Constraints analytics actualizados: %', analytics_constraints;
    
    -- Verificar que no existen las tablas obsoletas
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name IN ('faqs', 'faq_categories')) THEN
        RAISE EXCEPTION 'ERROR: Tablas obsoletas aún existen';
    END IF;
    
    -- Verificar que tenemos al menos 393 FAQs (383 originales + 10 migradas)
    IF final_municipal_faqs < 393 THEN
        RAISE EXCEPTION 'ERROR: Faltan FAQs. Esperadas: >=393, Encontradas: %', final_municipal_faqs;
    END IF;
    
    -- Verificar constraints de analytics
    IF analytics_constraints != 2 THEN
        RAISE EXCEPTION 'ERROR: Constraints de analytics incorrectos. Esperados: 2, Encontrados: %', analytics_constraints;
    END IF;
    
    RAISE NOTICE '✅ CONSOLIDACIÓN COMPLETADA EXITOSAMENTE';
    RAISE NOTICE '✅ Sistema FAQ funcionando con tablas actuales únicamente';
    RAISE NOTICE '✅ Datos únicos migrados correctamente';
    RAISE NOTICE '✅ Dependencias actualizadas';
END $$;

-- Limpiar tablas temporales
DROP TABLE IF EXISTS category_theme_mapping;

-- =============================================
-- INFORMACIÓN FINAL
-- =============================================

RAISE NOTICE '==========================================';
RAISE NOTICE 'CONSOLIDACIÓN DE TABLAS FAQ COMPLETADA';
RAISE NOTICE '==========================================';
RAISE NOTICE 'Tablas eliminadas: faqs, faq_categories';
RAISE NOTICE 'Tablas activas: municipal_faqs, faq_themes';
RAISE NOTICE 'Dependencias actualizadas: faq_analytics';
RAISE NOTICE 'Backups disponibles: *_backup tables';
RAISE NOTICE '==========================================';
