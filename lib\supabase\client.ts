import { createClient } from '@supabase/supabase-js'
import { Database } from '@/lib/database.types'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
  },
  realtime: {
    params: {
      eventsPerSecond: 10,
    },
  },
})

// Helper types for better type inference
export type SupabaseClient = typeof supabase

// Database table types
export type Profile = Database['public']['Tables']['profiles']['Row']
export type ProfileInsert = Database['public']['Tables']['profiles']['Insert']
export type ProfileUpdate = Database['public']['Tables']['profiles']['Update']

export type Role = Database['public']['Tables']['roles']['Row']
export type Dependency = Database['public']['Tables']['dependencies']['Row']
export type Subdependency = Database['public']['Tables']['subdependencies']['Row']

export type Procedure = Database['public']['Tables']['procedures']['Row']
export type ProcedureInsert = Database['public']['Tables']['procedures']['Insert']
export type ProcedureUpdate = Database['public']['Tables']['procedures']['Update']

export type CitizenProcedure = Database['public']['Tables']['citizen_procedures']['Row']
export type CitizenProcedureInsert = Database['public']['Tables']['citizen_procedures']['Insert']
export type CitizenProcedureUpdate = Database['public']['Tables']['citizen_procedures']['Update']

export type ProcedureStatus = Database['public']['Tables']['procedure_statuses']['Row']

export type OPA = Database['public']['Tables']['opas']['Row']
export type OPAInsert = Database['public']['Tables']['opas']['Insert']
export type OPAUpdate = Database['public']['Tables']['opas']['Update']

export type KnowledgeBase = Database['public']['Tables']['knowledge_base']['Row']
export type KnowledgeBaseInsert = Database['public']['Tables']['knowledge_base']['Insert']
export type KnowledgeBaseUpdate = Database['public']['Tables']['knowledge_base']['Update']

export type ChatConversation = Database['public']['Tables']['chat_conversations']['Row']
export type ChatConversationInsert = Database['public']['Tables']['chat_conversations']['Insert']
export type ChatConversationUpdate = Database['public']['Tables']['chat_conversations']['Update']

export type ChatMessage = Database['public']['Tables']['chat_messages']['Row']
export type ChatMessageInsert = Database['public']['Tables']['chat_messages']['Insert']
export type ChatMessageUpdate = Database['public']['Tables']['chat_messages']['Update']

export type Notification = Database['public']['Tables']['notifications']['Row']
export type NotificationInsert = Database['public']['Tables']['notifications']['Insert']
export type NotificationUpdate = Database['public']['Tables']['notifications']['Update']

export type AuditLog = Database['public']['Tables']['audit_logs']['Row']
export type AuditLogInsert = Database['public']['Tables']['audit_logs']['Insert']

// Enum types
export type UserRole = Database['public']['Enums']['user_role']
export type DocumentType = Database['public']['Enums']['document_type']
export type ProcedureStatusEnum = Database['public']['Enums']['procedure_status']
export type TramiteStatus = Database['public']['Enums']['tramite_status']

// View types
export type AllProceduresView = Database['public']['Views']['all_procedures']['Row']
export type VistaProcedimientosCompleta = Database['public']['Views']['vista_procedimientos_completa']['Row']

// Helper functions for common queries
export const getProfile = async (userId: string) => {
  const { data, error } = await supabase
    .from('profiles')
    .select(`
      *,
      role:roles(*),
      dependency:dependencies(*),
      subdependency:subdependencies(*)
    `)
    .eq('id', userId)
    .single()

  return { data, error }
}

export const getDependencies = async () => {
  const { data, error } = await supabase
    .from('dependencies')
    .select(`
      *,
      subdependencies(*)
    `)
    .eq('is_active', true)
    .order('name')

  return { data, error }
}

export const getProcedures = async (filters?: {
  dependency_id?: string
  subdependency_id?: string
  category?: string
  is_active?: boolean
}) => {
  let query = supabase
    .from('procedures')
    .select(`
      *,
      dependency:dependencies(*),
      subdependency:subdependencies(*)
    `)

  if (filters?.dependency_id) {
    query = query.eq('dependency_id', filters.dependency_id)
  }
  
  if (filters?.subdependency_id) {
    query = query.eq('subdependency_id', filters.subdependency_id)
  }
  
  if (filters?.category) {
    query = query.eq('category', filters.category)
  }
  
  if (filters?.is_active !== undefined) {
    query = query.eq('is_active', filters.is_active)
  }

  const { data, error } = await query.order('name')
  return { data, error }
}

export const getOPAs = async (filters?: {
  dependency_id?: string
  subdependency_id?: string
  is_active?: boolean
}) => {
  let query = supabase
    .from('opas')
    .select(`
      *,
      dependency:dependencies(*),
      subdependency:subdependencies(*)
    `)

  if (filters?.dependency_id) {
    query = query.eq('dependency_id', filters.dependency_id)
  }
  
  if (filters?.subdependency_id) {
    query = query.eq('subdependency_id', filters.subdependency_id)
  }
  
  if (filters?.is_active !== undefined) {
    query = query.eq('is_active', filters.is_active)
  }

  const { data, error } = await query.order('name')
  return { data, error }
}

export const getCitizenProcedures = async (citizenId: string) => {
  const { data, error } = await supabase
    .from('citizen_procedures')
    .select(`
      *,
      procedure:procedures(*),
      status:procedure_statuses(*),
      assigned_to:profiles(*)
    `)
    .eq('citizen_id', citizenId)
    .order('created_at', { ascending: false })

  return { data, error }
}

export const searchKnowledgeBase = async (query: string, limit = 10) => {
  // This would use vector similarity search in a real implementation
  const { data, error } = await supabase
    .from('knowledge_base')
    .select('*')
    .textSearch('content', query)
    .eq('is_active', true)
    .limit(limit)

  return { data, error }
}

// Real-time subscriptions
export const subscribeToUserNotifications = (userId: string, callback: (payload: any) => void) => {
  return supabase
    .channel('user-notifications')
    .on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'notifications',
        filter: `user_id=eq.${userId}`,
      },
      callback
    )
    .subscribe()
}

export const subscribeToProcedureUpdates = (procedureId: string, callback: (payload: any) => void) => {
  return supabase
    .channel('procedure-updates')
    .on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'citizen_procedures',
        filter: `procedure_id=eq.${procedureId}`,
      },
      callback
    )
    .subscribe()
}

// Auth helpers
export const getCurrentUser = async () => {
  const { data: { user }, error } = await supabase.auth.getUser()
  return { user, error }
}

export const signOut = async () => {
  const { error } = await supabase.auth.signOut()
  return { error }
}

export default supabase
