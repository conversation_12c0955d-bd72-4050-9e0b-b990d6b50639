#!/usr/bin/env python3
"""
Generate corrected FAQ SQL with proper theme mapping
"""

import json
import re

def clean_text(text):
    """Clean text for SQL insertion"""
    if not text:
        return ""
    # Escape single quotes for SQL
    text = text.replace("'", "''")
    # Remove extra whitespace
    text = re.sub(r'\s+', ' ', text.strip())
    return text

def get_theme_mapping():
    """Get the correct theme mapping"""
    return {
        "INTERNET GRATUITO Y ACCESO A EQUIPOS DE CÓMPUTO": "INTERNET GRATUITO Y ACCESO A EQUIPOS DE CÓMPUTO",
        "PROGRAMA DE CAPACITACIÓN DIGITAL": "INTERNET GRATUITO Y ACCESO A EQUIPOS DE CÓMPUTO",
        "Espacio Público": "ORGANIZACIONES COMUNITARIAS",
        "FORTALECIMIENTO DEL BUEN GOBIERNO PARA EL RESPETO Y GARANTÍA DE LOS DERECHOS HUMANOS": "ORGANIZACIONES COMUNITARIAS",
        "La Unidad de Gestión del Riesgo de Desastres del municipio de Chía, ha caracterizado sus servicios bajo tres criterios:": "ORGANIZACIONES COMUNITARIAS",
        "La Unidad de Gestión del Riesgo de Desastres del municipio de Chía, ha caracterizado sus servicios bajo tres criterios enfocados en reducir, mitigar, adelantar, contribuir y dar respuesta:": "ORGANIZACIONES COMUNITARIAS",
        "Fortalecimiento de la convivencia y la seguridad ciudadana": "ORGANIZACIONES COMUNITARIAS",
        "Sistema penitenciario y carcelario en el marco de los derechos Humanos": "ORGANIZACIONES COMUNITARIAS",
        "PISCC": "ORGANIZACIONES COMUNITARIAS",
        "GENERALIDADES MUNICIPIO DE CHÍA": "ORGANIZACIONES COMUNITARIAS",
        "Cuáles son las acciones para atender la seguridad además del servicio que brinda la policía en el Municipio de Chía": "ORGANIZACIONES COMUNITARIAS",
        "CONTROL PARENTAL Y USO ADECUADO DE TECNOLOGÍA Y REDES SOCIALES": "PRIMERA INFANCIA",
        "PREVENCIÓN DEL CONSUMO DE SUSTANCIAS PSICOACTIVAS –SPA- EN ADOLESCENTES. \"ESTRATEGIAS PARA PADRES\"": "PRIMERA INFANCIA",
        "PAUTAS DE CRIANZA": "PRIMERA INFANCIA",
        "EQUIDAD DE GÉNERO Y PREVENCIÓN DE LA VIOLENCIA INTRAFAMILIAR": "PRIMERA INFANCIA",
        "ACUERDOS DE PAGO - SPAC IMPUESTO PREDIAL": "IMPUESTO PREDIAL UNIFICADO",
        "PROGRAMA COLOMBIA MAYOR": "ADULTO MAYOR",
        "PROGRAMA PLATAFORMA DE JUVENTUDES": "CASA DE LA JUVENTUD",
        "PROGRAMA CONCEJO MUNICIPAL DE JUVENTUDES": "CASA DE LA JUVENTUD",
        "PROGRAMA PROMOCIÓN DE LECTURA, ESCRITURA Y ORALIDAD – LEO": "BIBLIOTECA HOQABIGA",
        "PROGRAMA  FORMA TIC RED DE BIBLIOTECAS": "BIBLIOTECA HOQABIGA",
        "PROGRAMA  SERVICIO SOCIAL PARA ESTUDIANTES DE GRADO 10 Y 11, EN LAS BIBLIOTECAS PÚBLICAS DEL MUNICIPIO DE CHÍA.": "CASA DE LA JUVENTUD",
        "PROGRAMA EFAC- ESCUELA DE FORMACION ARTISTICA DE CHIA": "ESCUELA DE FORMACIÓN ARTÍSTICA Y CULTURAL",
        "PROGRAMA BEPS (BENEFICIOS ECONOMICOS PERIODICOS)": "ADULTO MAYOR",
        "PROGRAMA PORTAFOLIO MUNICIPAL DE ESTIMULOS (PME)": "CASA DE LA CULTURA",
        "PROGRAMA  FOES- FOMENTO DE LA EDUCACIÓN SUPERIOR": "CASA DE LA JUVENTUD",
        "AFILIACIÓN AL SISTEMA GENERAL DE SEGURIDAD SOCIAL EN SALUD": "ORGANIZACIONES COMUNITARIAS",
        "GESTIÓN DE PETICIONES, QUEJAS, RECLAMOS, SUGERENCIAS (PQRS) RELACIONADAS CON EL SGSSS": "ORGANIZACIONES COMUNITARIAS",
        "EMPRENDIMIENTO": "APOYO AL EMPRENDIMIENTO",
        "ASISTENCIA TÉCNICA AGROPECUARIA": "CAPACITACIÓN EMPRESARIAL",
        "EMPLEO": "APOYO AL EMPRENDIMIENTO",
        "Informacion Plaza de Mercado": "ORGANIZACIONES COMUNITARIAS",
        "FESTIVAL GASTRONÓMICO": "EVENTOS CULTURALES",
        "CONSEJO DE TURISMO": "REGISTRO NACIONAL DE TURISMO",
        "SIT CHÍA": "REGISTRO NACIONAL DE TURISMO",
        "PROGRAMA PRESUPUESTO PARTICIPATIVO": "ORGANIZACIONES COMUNITARIAS",
        "PROGRAMA MESA MUNICIPAL DE NIÑOS, NIÑAS Y ADOLESCENTES.": "PRIMERA INFANCIA"
    }

def generate_corrected_sql():
    """Generate corrected SQL with proper theme mapping"""
    
    # Load the structured FAQ data
    with open('faqs_chia_estructurado.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    theme_mapping = get_theme_mapping()
    sql_statements = []
    question_count = 0
    
    print("Generating corrected FAQ SQL with proper theme mapping...")
    
    # Process each dependency entry
    for entry in data['faqs']:
        dependencia = entry['dependencia']
        codigo_dependencia = entry['codigo_dependencia']
        subdependencia = entry.get('subdependencia', '')
        codigo_subdependencia = entry.get('codigo_subdependencia', '')
        
        # Process each theme within the dependency
        for tema_data in entry['temas']:
            tema = tema_data['tema']
            preguntas = tema_data.get('preguntas_frecuentes', [])
            
            # Map to correct database theme
            db_theme = theme_mapping.get(tema, "ORGANIZACIONES COMUNITARIAS")  # Default fallback
            
            print(f"Processing theme: {tema[:50]}... -> {db_theme} ({len(preguntas)} questions)")
            
            # Process each question within the theme
            for i, pregunta_data in enumerate(preguntas, 1):
                pregunta = clean_text(pregunta_data.get('pregunta', ''))
                respuesta = clean_text(pregunta_data.get('respuesta', ''))
                palabras_clave = pregunta_data.get('palabras_clave', [])
                
                # Convert keywords to PostgreSQL array format
                keywords_array = "ARRAY[" + ", ".join([f"'{clean_text(kw)}'" for kw in palabras_clave]) + "]"
                
                # Generate SQL INSERT statement with correct theme mapping
                sql = f"""INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('{pregunta}', '{respuesta}', 
 (SELECT id FROM faq_themes WHERE name = '{db_theme}'), 
 {keywords_array}, {i});"""
                
                sql_statements.append(sql)
                question_count += 1
    
    # Write corrected SQL file
    with open('corrected_faq_insertion.sql', 'w', encoding='utf-8') as f:
        f.write("-- CORRECTED FAQ INSERTION SCRIPT\n")
        f.write(f"-- Total questions: {question_count}\n")
        f.write("-- Generated with proper theme mapping\n\n")
        
        # Write in batches of 25 for better readability
        for i in range(0, len(sql_statements), 25):
            batch = sql_statements[i:i+25]
            f.write(f"-- Batch {i//25 + 1}: Questions {i+1} to {min(i+25, len(sql_statements))}\n")
            f.write("\n".join(batch))
            f.write("\n\n")
    
    print(f"Generated corrected_faq_insertion.sql with {question_count} questions")
    return question_count

def main():
    """Main function"""
    print("Corrected FAQ SQL Generator")
    print("=" * 40)
    
    try:
        total_questions = generate_corrected_sql()
        print(f"\n✅ Successfully generated corrected SQL file")
        print(f"📊 Total questions: {total_questions}")
        print(f"📁 Output file: corrected_faq_insertion.sql")
        
        return 0
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
