'use client'

import { useMemo } from 'react'
import { useAuth } from './useAuth'
import { Database } from '@/lib/database.types'

type RoleName = 'ciudadano' | 'admin' | 'super_admin'
type Profile = Database['public']['Tables']['profiles']['Row']

interface RolePermissions {
  // General permissions
  canViewDashboard: boolean
  canViewProfile: boolean
  canEditProfile: boolean
  
  // Citizen permissions
  canCreateProcedures: boolean
  canViewOwnProcedures: boolean
  canChatWithAI: boolean
  canReceiveNotifications: boolean
  
  // Admin permissions
  canViewDependencyProcedures: boolean
  canManageDependencyProcedures: boolean
  canViewDependencyUsers: boolean
  canManageDependencyUsers: boolean
  canViewDependencyReports: boolean
  canManageKnowledgeBase: boolean
  
  // Super admin permissions
  canViewAllProcedures: boolean
  canManageAllProcedures: boolean
  canViewAllUsers: boolean
  canManageAllUsers: boolean
  canViewAllReports: boolean
  canManageSystem: boolean
  canManageDependencies: boolean
  canManageRoles: boolean
  canViewAuditLogs: boolean
}

interface RoleUtils {
  roleName: RoleName | null
  isLoading: boolean
  isCitizen: boolean
  isAdmin: boolean
  isSuperAdmin: boolean
  isDependencyAdmin: (dependencyId?: string) => boolean
  hasRole: (role: RoleName) => boolean
  hasAnyRole: (roles: RoleName[]) => boolean
  permissions: RolePermissions
  canAccess: (resource: keyof RolePermissions) => boolean
}

export function useRole(): RoleUtils {
  const { profile, role, isLoading, isAuthenticated } = useAuth()

  const roleName = useMemo((): RoleName | null => {
    if (!role?.name) return null
    return role.name as RoleName
  }, [role])

  const isCitizen = useMemo(() => roleName === 'ciudadano', [roleName])
  const isAdmin = useMemo(() => roleName === 'admin', [roleName])
  const isSuperAdmin = useMemo(() => roleName === 'super_admin', [roleName])

  const hasRole = useMemo(() => (targetRole: RoleName): boolean => {
    return roleName === targetRole
  }, [roleName])

  const hasAnyRole = useMemo(() => (roles: RoleName[]): boolean => {
    return roleName ? roles.includes(roleName) : false
  }, [roleName])

  const isDependencyAdmin = useMemo(() => (dependencyId?: string): boolean => {
    if (!isAuthenticated || !profile) return false
    
    // Super admin can manage any dependency
    if (isSuperAdmin) return true
    
    // Regular admin can only manage their own dependency
    if (isAdmin && dependencyId) {
      return profile.dependency_id === dependencyId
    }
    
    return false
  }, [isAuthenticated, profile, isSuperAdmin, isAdmin])

  const permissions = useMemo((): RolePermissions => {
    const basePermissions: RolePermissions = {
      // General permissions
      canViewDashboard: false,
      canViewProfile: false,
      canEditProfile: false,
      
      // Citizen permissions
      canCreateProcedures: false,
      canViewOwnProcedures: false,
      canChatWithAI: false,
      canReceiveNotifications: false,
      
      // Admin permissions
      canViewDependencyProcedures: false,
      canManageDependencyProcedures: false,
      canViewDependencyUsers: false,
      canManageDependencyUsers: false,
      canViewDependencyReports: false,
      canManageKnowledgeBase: false,
      
      // Super admin permissions
      canViewAllProcedures: false,
      canManageAllProcedures: false,
      canViewAllUsers: false,
      canManageAllUsers: false,
      canViewAllReports: false,
      canManageSystem: false,
      canManageDependencies: false,
      canManageRoles: false,
      canViewAuditLogs: false,
    }

    if (!isAuthenticated || !roleName) {
      return basePermissions
    }

    // Citizen permissions
    if (isCitizen) {
      return {
        ...basePermissions,
        canViewDashboard: true,
        canViewProfile: true,
        canEditProfile: true,
        canCreateProcedures: true,
        canViewOwnProcedures: true,
        canChatWithAI: true,
        canReceiveNotifications: true,
      }
    }

    // Admin permissions (includes all citizen permissions)
    if (isAdmin) {
      return {
        ...basePermissions,
        // General permissions
        canViewDashboard: true,
        canViewProfile: true,
        canEditProfile: true,
        
        // Citizen permissions
        canCreateProcedures: true,
        canViewOwnProcedures: true,
        canChatWithAI: true,
        canReceiveNotifications: true,
        
        // Admin permissions
        canViewDependencyProcedures: true,
        canManageDependencyProcedures: true,
        canViewDependencyUsers: true,
        canManageDependencyUsers: true,
        canViewDependencyReports: true,
        canManageKnowledgeBase: true,
      }
    }

    // Super admin permissions (includes all permissions)
    if (isSuperAdmin) {
      return {
        ...basePermissions,
        // General permissions
        canViewDashboard: true,
        canViewProfile: true,
        canEditProfile: true,
        
        // Citizen permissions
        canCreateProcedures: true,
        canViewOwnProcedures: true,
        canChatWithAI: true,
        canReceiveNotifications: true,
        
        // Admin permissions
        canViewDependencyProcedures: true,
        canManageDependencyProcedures: true,
        canViewDependencyUsers: true,
        canManageDependencyUsers: true,
        canViewDependencyReports: true,
        canManageKnowledgeBase: true,
        
        // Super admin permissions
        canViewAllProcedures: true,
        canManageAllProcedures: true,
        canViewAllUsers: true,
        canManageAllUsers: true,
        canViewAllReports: true,
        canManageSystem: true,
        canManageDependencies: true,
        canManageRoles: true,
        canViewAuditLogs: true,
      }
    }

    return basePermissions
  }, [isAuthenticated, roleName, isCitizen, isAdmin, isSuperAdmin])

  const canAccess = useMemo(() => (resource: keyof RolePermissions): boolean => {
    return permissions[resource]
  }, [permissions])

  return {
    roleName,
    isLoading,
    isCitizen,
    isAdmin,
    isSuperAdmin,
    isDependencyAdmin,
    hasRole,
    hasAnyRole,
    permissions,
    canAccess,
  }
}
