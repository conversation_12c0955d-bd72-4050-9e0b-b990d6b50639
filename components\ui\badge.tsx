import * as React from 'react'
import { cva, type VariantProps } from 'class-variance-authority'
import { cn } from '@/lib/utils'

const badgeVariants = cva(
  'inline-flex items-center rounded-full border px-3 py-1 text-xs font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 shadow-sm',
  {
    variants: {
      variant: {
        default:
          'border-transparent bg-primary/10 text-primary hover:bg-primary/20 shadow-primary/10',
        secondary:
          'border-transparent bg-gray-100 text-gray-700 hover:bg-gray-200 shadow-gray-100',
        destructive:
          'border-transparent bg-red-100 text-red-800 hover:bg-red-200 shadow-red-100',
        outline: 'text-gray-700 border-gray-300 bg-white hover:bg-gray-50 hover:border-gray-400',
        success:
          'border-transparent bg-chia-green-100 text-chia-green-800 hover:bg-chia-green-200 shadow-chia-green-100',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  }
)

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, ...props }: BadgeProps) {
  return (
    <div className={cn(badgeVariants({ variant }), className)} {...props} />
  )
}

export { Badge, badgeVariants }
