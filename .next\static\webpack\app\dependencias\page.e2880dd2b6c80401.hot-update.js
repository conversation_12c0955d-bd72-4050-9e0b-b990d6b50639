"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dependencias/page",{

/***/ "(app-pages-browser)/./components/ui/loading-states.tsx":
/*!******************************************!*\
  !*** ./components/ui/loading-states.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DependencyGridLoading: function() { return /* binding */ DependencyGridLoading; },\n/* harmony export */   InlineLoading: function() { return /* binding */ InlineLoading; },\n/* harmony export */   LoadingSpinner: function() { return /* binding */ LoadingSpinner; },\n/* harmony export */   ModalLoading: function() { return /* binding */ ModalLoading; },\n/* harmony export */   ProgressLoading: function() { return /* binding */ ProgressLoading; },\n/* harmony export */   SearchLoading: function() { return /* binding */ SearchLoading; },\n/* harmony export */   SearchResultsLoading: function() { return /* binding */ SearchResultsLoading; },\n/* harmony export */   TabContentLoading: function() { return /* binding */ TabContentLoading; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_FileText_Loader2_Search_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,FileText,Loader2,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_FileText_Loader2_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,FileText,Loader2,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_FileText_Loader2_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,FileText,Loader2,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_FileText_Loader2_Search_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,FileText,Loader2,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_FileText_Loader2_Search_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,FileText,Loader2,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ LoadingSpinner,SearchLoading,ModalLoading,DependencyGridLoading,SearchResultsLoading,InlineLoading,ProgressLoading,TabContentLoading auto */ \n\n\n\nfunction LoadingSpinner(param) {\n    let { size = \"md\", className, color = \"blue\" } = param;\n    const sizeClasses = {\n        sm: \"h-4 w-4\",\n        md: \"h-6 w-6\",\n        lg: \"h-8 w-8\"\n    };\n    const colorClasses = {\n        blue: \"text-primary\",\n        green: \"text-chia-green-600\",\n        gray: \"text-gray-600\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_FileText_Loader2_Search_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"animate-spin\", sizeClasses[size], colorClasses[color], className)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\loading-states.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n_c = LoadingSpinner;\nfunction SearchLoading(param) {\n    let { message = \"Buscando...\", className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center justify-center space-x-2 py-8\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_FileText_Loader2_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"h-5 w-5 text-primary animate-pulse\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\loading-states.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingSpinner, {\n                size: \"sm\",\n                color: \"blue\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\loading-states.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm text-gray-600\",\n                children: message\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\loading-states.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\loading-states.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\n_c1 = SearchLoading;\nfunction ModalLoading(param) {\n    let { type = \"general\", message, className } = param;\n    const getIcon = ()=>{\n        switch(type){\n            case \"dependency\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_FileText_Loader2_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-8 w-8 text-chia-blue-600 animate-pulse\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\loading-states.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 16\n                }, this);\n            case \"procedure\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_FileText_Loader2_Search_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-8 w-8 text-chia-green-600 animate-pulse\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\loading-states.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_FileText_Loader2_Search_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-8 w-8 text-gray-600 animate-pulse\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\loading-states.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getDefaultMessage = ()=>{\n        switch(type){\n            case \"dependency\":\n                return \"Cargando informaci\\xf3n de la dependencia...\";\n            case \"procedure\":\n                return \"Cargando detalles del procedimiento...\";\n            default:\n                return \"Cargando...\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col items-center justify-center space-y-4 py-12\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [\n                    getIcon(),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingSpinner, {\n                        size: \"md\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\loading-states.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\loading-states.tsx\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-sm text-gray-600 text-center\",\n                children: message || getDefaultMessage()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\loading-states.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\loading-states.tsx\",\n        lineNumber: 100,\n        columnNumber: 5\n    }, this);\n}\n_c2 = ModalLoading;\n// Loading para grid de dependencias\nfunction DependencyGridLoading(param) {\n    let { className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\", className),\n        children: Array.from({\n            length: 6\n        }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg border border-gray-200 p-6 animate-pulse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-12 h-12 bg-gray-200 rounded-lg\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\loading-states.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-gray-200 rounded w-3/4 mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\loading-states.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-3 bg-gray-200 rounded w-1/2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\loading-states.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\loading-states.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\loading-states.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-3 bg-gray-200 rounded w-full\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\loading-states.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-3 bg-gray-200 rounded w-2/3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\loading-states.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\loading-states.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mt-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-6 bg-gray-200 rounded w-16\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\loading-states.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-6 bg-gray-200 rounded w-16\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\loading-states.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\loading-states.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, index, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\loading-states.tsx\",\n                lineNumber: 120,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\loading-states.tsx\",\n        lineNumber: 118,\n        columnNumber: 5\n    }, this);\n}\n_c3 = DependencyGridLoading;\n// Loading para resultados de búsqueda\nfunction SearchResultsLoading(param) {\n    let { className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"space-y-2\", className),\n        children: Array.from({\n            length: 5\n        }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-3 p-3 border border-gray-200 rounded-lg animate-pulse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 bg-gray-200 rounded\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\loading-states.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-4 bg-gray-200 rounded w-3/4 mb-1\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\loading-states.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-3 bg-gray-200 rounded w-1/2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\loading-states.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\loading-states.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-6 bg-gray-200 rounded\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\loading-states.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, index, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\loading-states.tsx\",\n                lineNumber: 150,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\loading-states.tsx\",\n        lineNumber: 148,\n        columnNumber: 5\n    }, this);\n}\n_c4 = SearchResultsLoading;\nfunction InlineLoading(param) {\n    let { message = \"Cargando...\", size = \"sm\", className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center space-x-2\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingSpinner, {\n                size: size\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\loading-states.tsx\",\n                lineNumber: 180,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-gray-600\", size === \"sm\" ? \"text-xs\" : \"text-sm\"),\n                children: message\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\loading-states.tsx\",\n                lineNumber: 181,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\loading-states.tsx\",\n        lineNumber: 179,\n        columnNumber: 5\n    }, this);\n}\n_c5 = InlineLoading;\nfunction ProgressLoading(param) {\n    let { progress = 0, message = \"Procesando...\", className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"space-y-3\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingSpinner, {\n                        size: \"sm\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\loading-states.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm text-gray-600\",\n                        children: message\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\loading-states.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\loading-states.tsx\",\n                lineNumber: 205,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full bg-gray-200 rounded-full h-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-chia-blue-600 h-2 rounded-full transition-all duration-300 ease-out\",\n                    style: {\n                        width: \"\".concat(Math.min(100, Math.max(0, progress)), \"%\")\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\loading-states.tsx\",\n                    lineNumber: 210,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\loading-states.tsx\",\n                lineNumber: 209,\n                columnNumber: 7\n            }, this),\n            progress > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-xs text-gray-500 text-center\",\n                children: [\n                    Math.round(progress),\n                    \"% completado\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\loading-states.tsx\",\n                lineNumber: 216,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\loading-states.tsx\",\n        lineNumber: 204,\n        columnNumber: 5\n    }, this);\n}\n_c6 = ProgressLoading;\n// Loading para contenido de pestañas\nfunction TabContentLoading(param) {\n    let { className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"space-y-4 py-4\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-200 rounded w-1/4 animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\loading-states.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-3 bg-gray-200 rounded w-full animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\loading-states.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-3 bg-gray-200 rounded w-3/4 animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\loading-states.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\loading-states.tsx\",\n                lineNumber: 228,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-200 rounded w-1/3 animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\loading-states.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-3 bg-gray-200 rounded w-full animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\loading-states.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-3 bg-gray-200 rounded w-2/3 animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\loading-states.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\loading-states.tsx\",\n                lineNumber: 233,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\ui\\\\loading-states.tsx\",\n        lineNumber: 227,\n        columnNumber: 5\n    }, this);\n}\n_c7 = TabContentLoading;\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7;\n$RefreshReg$(_c, \"LoadingSpinner\");\n$RefreshReg$(_c1, \"SearchLoading\");\n$RefreshReg$(_c2, \"ModalLoading\");\n$RefreshReg$(_c3, \"DependencyGridLoading\");\n$RefreshReg$(_c4, \"SearchResultsLoading\");\n$RefreshReg$(_c5, \"InlineLoading\");\n$RefreshReg$(_c6, \"ProgressLoading\");\n$RefreshReg$(_c7, \"TabContentLoading\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ui/loading-states.tsx\n"));

/***/ })

});