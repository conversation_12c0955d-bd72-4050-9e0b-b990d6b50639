#!/usr/bin/env python3
"""
Fix theme mapping between JSON file and database
"""

import json
import re

def create_theme_mapping():
    """Create mapping between JSON themes and database themes"""
    
    # Load the structured FAQ data
    with open('faqs_chia_estructurado.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # Database themes (from the query result)
    db_themes = [
        "ADULTO MAYOR", "APOYO AL EMPRENDIMIENTO", "BIBLIOTECA HOQABIGA", 
        "CAPACITACIÓN EMPRESARIAL", "CASA DE LA CULTURA", "CASA DE LA JUVENTUD",
        "CERTIFICACIÓN DE VÍCTIMAS", "CERTIFICADO DE DEFUNCIÓN", "CERTIFICADO DE ESTRATIFICACIÓN",
        "CERTIFICADO DE NACIDO VIVO", "CERTIFICADO DE NOMENCLATURA", "CERTIFICADO DE PAZ Y SALVO",
        "CERTIFICADO DE USO DEL SUELO", "COMPARENDOS", "CONCEPTO SANITARIO",
        "CONCILIACIÓN EN DERECHO", "DESCUENTO POR PRONTO PAGO", "DISCAPACIDAD",
        "ESCUELA DE FORMACIÓN ARTÍSTICA Y CULTURAL", "ESPECTÁCULOS PÚBLICOS", "EVENTOS CULTURALES",
        "FERIA EMPRESARIAL", "FORTALECIMIENTO EMPRESARIAL", "IMPUESTO DE INDUSTRIA Y COMERCIO",
        "IMPUESTO PREDIAL UNIFICADO", "INTERNET GRATUITO Y ACCESO A EQUIPOS DE CÓMPUTO",
        "JUNTAS DE ACCIÓN COMUNAL", "LICENCIA DE CONDUCCIÓN", "LICENCIA DE FUNCIONAMIENTO",
        "LICENCIA DE RIFAS Y SORTEOS", "LICENCIA PARA VENTA DE BEBIDAS ALCOHÓLICAS",
        "MUSEO ARQUEOLÓGICO", "ORGANIZACIONES COMUNITARIAS", "PERMISO DE AGLOMERACIÓN DE PÚBLICO",
        "PRIMERA INFANCIA", "REGISTRO NACIONAL DE TURISMO", "VEEDURÍAS CIUDADANAS"
    ]
    
    # Extract JSON themes
    json_themes = []
    for entry in data['faqs']:
        for tema_data in entry['temas']:
            tema = tema_data['tema']
            if tema not in json_themes:
                json_themes.append(tema)
    
    print("JSON Themes found:")
    for i, theme in enumerate(json_themes, 1):
        print(f"{i:2d}. {theme}")
    
    print(f"\nTotal JSON themes: {len(json_themes)}")
    print(f"Total DB themes: {len(db_themes)}")
    
    # Create manual mapping based on content analysis
    theme_mapping = {
        "INTERNET GRATUITO Y ACCESO A EQUIPOS DE CÓMPUTO": "INTERNET GRATUITO Y ACCESO A EQUIPOS DE CÓMPUTO",
        "PROGRAMA DE CAPACITACIÓN DIGITAL": "INTERNET GRATUITO Y ACCESO A EQUIPOS DE CÓMPUTO",  # Related to digital services
        "Espacio Público": "ORGANIZACIONES COMUNITARIAS",  # Public space management
        "FORTALECIMIENTO DEL BUEN GOBIERNO PARA EL RESPETO DE LOS DERECHOS HUMANOS": "ORGANIZACIONES COMUNITARIAS",
        "La Unidad de Gestión del Riesgo de Desastres del municipio de Chía, ha caracterizado sus servicios bajo tres criterios enfocados en reducir, mitigar, adelantar, contribuir y dar respuesta:": "ORGANIZACIONES COMUNITARIAS",
        "Fortalecimiento de la convivencia y la seguridad ciudadana": "ORGANIZACIONES COMUNITARIAS",
        "Sistema penitenciario y carcelario en el marco de la política criminal del Estado": "ORGANIZACIONES COMUNITARIAS",
        "PISCC": "ORGANIZACIONES COMUNITARIAS",
        "GENERALIDADES MUNICIPIO DE CHÍA": "ORGANIZACIONES COMUNITARIAS",
        "Cuáles son las acciones para atender la seguridad ciudadana": "ORGANIZACIONES COMUNITARIAS",
        "CONTROL PARENTAL Y USO ADECUADO DE TECNOLOGÍA Y REDES SOCIALES": "PRIMERA INFANCIA",
        "PREVENCIÓN DEL CONSUMO DE SUSTANCIAS PSICOACTIVAS Y ALCOHOL": "PRIMERA INFANCIA",
        "PAUTAS DE CRIANZA": "PRIMERA INFANCIA",
        "EQUIDAD DE GÉNERO Y PREVENCIÓN DE LA VIOLENCIA INTRAFAMILIAR": "PRIMERA INFANCIA",
        "ACUERDOS DE PAGO - SPAC IMPUESTO PREDIAL": "IMPUESTO PREDIAL UNIFICADO",
        "PROGRAMA COLOMBIA MAYOR": "ADULTO MAYOR",
        "PROGRAMA PLATAFORMA DE JUVENTUDES": "CASA DE LA JUVENTUD",
        "PROGRAMA CONCEJO MUNICIPAL DE JUVENTUDES": "CASA DE LA JUVENTUD",
        "PROGRAMA PROMOCIÓN DE LECTURA, ESCRITURA Y ORALIDAD": "BIBLIOTECA HOQABIGA",
        "PROGRAMA  FORMA TIC RED DE BIBLIOTECAS": "BIBLIOTECA HOQABIGA",
        "PROGRAMA  SERVICIO SOCIAL PARA ESTUDIANTES DE GRADO 10 Y 11": "CASA DE LA JUVENTUD",
        "PROGRAMA EFAC- ESCUELA DE FORMACION ARTISTICA DE CHIA": "ESCUELA DE FORMACIÓN ARTÍSTICA Y CULTURAL",
        "PROGRAMA BEPS (BENEFICIOS ECONOMICOS PERIODICOS)": "ADULTO MAYOR",
        "PROGRAMA PORTAFOLIO MUNICIPAL DE ESTIMULOS (PME)": "CASA DE LA CULTURA",
        "PROGRAMA  FOES- FOMENTO DE LA EDUCACIÓN SUPERIOR": "CASA DE LA JUVENTUD",
        "AFILIACIÓN AL SISTEMA GENERAL DE SEGURIDAD SOCIAL EN SALUD": "ORGANIZACIONES COMUNITARIAS",
        "GESTIÓN DE PETICIONES, QUEJAS, RECLAMOS, SUGERENCIAS Y DENUNCIAS": "ORGANIZACIONES COMUNITARIAS",
        "EMPRENDIMIENTO": "APOYO AL EMPRENDIMIENTO",
        "ASISTENCIA TÉCNICA AGROPECUARIA": "CAPACITACIÓN EMPRESARIAL",
        "EMPLEO": "APOYO AL EMPRENDIMIENTO",
        "Informacion Plaza de Mercado": "ORGANIZACIONES COMUNITARIAS",
        "FESTIVAL GASTRONÓMICO": "EVENTOS CULTURALES",
        "CONSEJO DE TURISMO": "REGISTRO NACIONAL DE TURISMO",
        "SIT CHÍA": "REGISTRO NACIONAL DE TURISMO",
        "PROGRAMA PRESUPUESTO PARTICIPATIVO": "ORGANIZACIONES COMUNITARIAS",
        "PROGRAMA MESA MUNICIPAL DE NIÑOS, NIÑAS Y ADOLESCENTES": "PRIMERA INFANCIA"
    }
    
    return theme_mapping

def main():
    """Main function"""
    print("Theme Mapping Analysis")
    print("=" * 30)
    
    mapping = create_theme_mapping()
    
    print(f"\nTheme Mapping Created:")
    for json_theme, db_theme in mapping.items():
        print(f"'{json_theme[:50]}...' -> '{db_theme}'")
    
    return 0

if __name__ == "__main__":
    exit(main())
