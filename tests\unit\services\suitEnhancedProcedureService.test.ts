import { getAllProceduresWithSuit, getProceduresByDependencyWithSuit } from '@/lib/services/suitEnhancedProcedureService'
import { createClient } from '@/lib/supabase/client'

// Mock Supabase client
jest.mock('@/lib/supabase/client', () => ({
  createClient: jest.fn()
}))

const mockSupabaseClient = {
  from: jest.fn()
}

const mockSelect = jest.fn()
const mockEq = jest.fn()

describe('SuitEnhancedProcedureService', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    ;(createClient as any).mockReturnValue(mockSupabaseClient)
    mockSupabaseClient.from.mockReturnValue({
      select: mockSelect
    })
    mockSelect.mockReturnValue({
      eq: mockEq
    })
    mockEq.mockReturnValue({
      data: [],
      error: null
    })
  })

  describe('getAllProceduresWithSuit', () => {
    it('should fetch and enhance procedures with SUIT data', async () => {
      const mockTramiteData = {
        id: '1',
        name: 'Test Tramite',
        description: 'Original description',
        requirements: ['Original req 1'],
        suit_scraped_data: {
          ficha_id: 'T001',
          descripcion_detallada: 'Enhanced SUIT description',
          requisitos: ['Enhanced req 1', 'Enhanced req 2'],
          pasos: ['Step 1', 'Step 2'],
          tiempo_respuesta: '5 días hábiles',
          costo_detallado: 'Gratuito según SUIT',
          success: true,
          scraped_at: '2025-01-01T00:00:00Z'
        }
      }

      const mockOpaData = {
        id: '2',
        name: 'Test OPA',
        description: 'Original OPA description',
        requirements: ['Original OPA req'],
        suit_scraped_data: null
      }

      // Mock tramites response
      mockSelect.mockReturnValueOnce({
        data: [mockTramiteData],
        error: null
      })

      // Mock opas response
      mockSelect.mockReturnValueOnce({
        data: [mockOpaData],
        error: null
      })

      const result = await getAllProceduresWithSuit()

      expect(result).toHaveLength(2)
      
      // Test enhanced tramite
      const enhancedTramite = result.find(p => p.id === '1')
      expect(enhancedTramite).toBeDefined()
      expect(enhancedTramite?.best_description).toBe('Enhanced SUIT description')
      expect(enhancedTramite?.best_requirements).toEqual(['Enhanced req 1', 'Enhanced req 2'])
      expect(enhancedTramite?.has_suit_enhancement).toBe(true)

      // Test non-enhanced OPA
      const nonEnhancedOpa = result.find(p => p.id === '2')
      expect(nonEnhancedOpa).toBeDefined()
      expect(nonEnhancedOpa?.best_description).toBe('Original OPA description')
      expect(nonEnhancedOpa?.best_requirements).toEqual(['Original OPA req'])
      expect(nonEnhancedOpa?.has_suit_enhancement).toBe(false)
    })

    it('should handle errors gracefully', async () => {
      // Mock error response
      mockSelect.mockReturnValueOnce({
        data: null,
        error: { message: 'Database error' }
      })

      mockSelect.mockReturnValueOnce({
        data: null,
        error: { message: 'Database error' }
      })

      const result = await getAllProceduresWithSuit()

      expect(result).toEqual([])
    })
  })

  describe('getProceduresByDependencyWithSuit', () => {
    it('should fetch procedures filtered by dependency', async () => {
      const dependencyId = 'dep-123'
      
      mockEq.mockReturnValueOnce({
        data: [{
          id: '1',
          name: 'Dependency Tramite',
          dependency_id: dependencyId,
          suit_scraped_data: {
            ficha_id: 'T001',
            descripcion_detallada: 'SUIT enhanced description',
            success: true,
            scraped_at: '2025-01-01T00:00:00Z'
          }
        }],
        error: null
      })

      mockEq.mockReturnValueOnce({
        data: [],
        error: null
      })

      const result = await getProceduresByDependencyWithSuit(dependencyId)

      expect(mockEq).toHaveBeenCalledWith('dependency_id', dependencyId)
      expect(result).toHaveLength(1)
      expect(result[0].best_description).toBe('SUIT enhanced description')
    })
  })
})
