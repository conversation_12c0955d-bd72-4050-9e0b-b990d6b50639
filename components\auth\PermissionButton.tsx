'use client'

import { ReactNode, forwardRef } from 'react'
import { Button, ButtonProps } from '@/components/ui/button'
import { useAuth } from '@/hooks/useAuth'
import { useRole } from '@/hooks/useRole'
import { usePermissions } from '@/hooks/usePermissions'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { Lock } from 'lucide-react'

type RoleName = 'ciudadano' | 'admin' | 'super_admin'

interface PermissionButtonProps extends ButtonProps {
  children: ReactNode
  requiredRoles?: RoleName[]
  requiredPermissions?: string[]
  dependencyId?: string
  requireAll?: boolean
  showTooltip?: boolean
  tooltipContent?: string
  hideWhenDisabled?: boolean
  disabledIcon?: ReactNode
}

export const PermissionButton = forwardRef<HTMLButtonElement, PermissionButtonProps>(
  ({
    children,
    requiredRoles = [],
    requiredPermissions = [],
    dependencyId,
    requireAll = false,
    showTooltip = true,
    tooltipContent,
    hideWhenDisabled = false,
    disabledIcon = <Lock className="h-4 w-4" />,
    disabled,
    ...props
  }, ref) => {
    const { isAuthenticated } = useAuth()
    const { hasRole, hasAnyRole } = useRole()
    const permissionChecks = usePermissions()

    // Check if user has required permissions
    const hasPermission = (): boolean => {
      if (!isAuthenticated) return false

      // Check role requirements
      if (requiredRoles.length > 0) {
        const hasRequiredRoles = requireAll
          ? requiredRoles.every(role => hasRole(role))
          : hasAnyRole(requiredRoles)

        if (!hasRequiredRoles) return false
      }

      // Check permission requirements
      if (requiredPermissions.length > 0) {
        const checkPermission = (permission: string): boolean => {
          const permissionMethod = permissionChecks[permission as keyof typeof permissionChecks]
          if (typeof permissionMethod === 'function') {
            // If dependencyId is provided, pass it to the permission check
            if (dependencyId && permissionMethod.length > 0) {
              return permissionMethod(dependencyId)
            }
            return permissionMethod()
          }
          return false
        }

        const hasRequiredPermissions = requireAll
          ? requiredPermissions.every(checkPermission)
          : requiredPermissions.some(checkPermission)

        if (!hasRequiredPermissions) return false
      }

      return true
    }

    const canAccess = hasPermission()
    const isDisabled = disabled || !canAccess

    // Hide button if user doesn't have permission and hideWhenDisabled is true
    if (hideWhenDisabled && !canAccess) {
      return null
    }

    // Generate tooltip content
    const getTooltipContent = (): string => {
      if (tooltipContent) return tooltipContent

      if (!isAuthenticated) {
        return 'Debes iniciar sesión para realizar esta acción'
      }

      if (!canAccess) {
        const missingRoles = requiredRoles.filter(role => !hasRole(role))
        const missingPermissions = requiredPermissions.filter(permission => {
          const permissionMethod = permissionChecks[permission as keyof typeof permissionChecks]
          if (typeof permissionMethod === 'function') {
            return !permissionMethod()
          }
          return true
        })

        const messages: string[] = []
        
        if (missingRoles.length > 0) {
          messages.push(`Rol requerido: ${missingRoles.join(', ')}`)
        }
        
        if (missingPermissions.length > 0) {
          messages.push(`Permisos requeridos: ${missingPermissions.join(', ')}`)
        }

        return messages.length > 0 
          ? messages.join('\n') 
          : 'No tienes permisos para realizar esta acción'
      }

      return ''
    }

    const button = (
      <Button
        ref={ref}
        disabled={isDisabled}
        {...props}
        className={`${props.className || ''} ${
          !canAccess ? 'opacity-50 cursor-not-allowed' : ''
        }`}
      >
        {!canAccess && disabledIcon && (
          <span className="mr-2">{disabledIcon}</span>
        )}
        {children}
      </Button>
    )

    // Wrap with tooltip if needed
    if (showTooltip && (!canAccess || tooltipContent)) {
      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              {button}
            </TooltipTrigger>
            <TooltipContent>
              <p className="max-w-xs text-sm whitespace-pre-line">
                {getTooltipContent()}
              </p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )
    }

    return button
  }
)

PermissionButton.displayName = 'PermissionButton'

// Convenience components for common use cases
interface ConvenientButtonProps extends Omit<PermissionButtonProps, 'requiredRoles'> {
  children: ReactNode
}

export const AdminButton = forwardRef<HTMLButtonElement, ConvenientButtonProps>(
  ({ children, ...props }, ref) => (
    <PermissionButton
      ref={ref}
      requiredRoles={['admin', 'super_admin']}
      {...props}
    >
      {children}
    </PermissionButton>
  )
)

AdminButton.displayName = 'AdminButton'

export const SuperAdminButton = forwardRef<HTMLButtonElement, ConvenientButtonProps>(
  ({ children, ...props }, ref) => (
    <PermissionButton
      ref={ref}
      requiredRoles={['super_admin']}
      {...props}
    >
      {children}
    </PermissionButton>
  )
)

SuperAdminButton.displayName = 'SuperAdminButton'

export const CitizenButton = forwardRef<HTMLButtonElement, ConvenientButtonProps>(
  ({ children, ...props }, ref) => (
    <PermissionButton
      ref={ref}
      requiredRoles={['ciudadano', 'admin', 'super_admin']}
      {...props}
    >
      {children}
    </PermissionButton>
  )
)

CitizenButton.displayName = 'CitizenButton'
