#!/usr/bin/env node

/**
 * Script para verificar el cumplimiento WCAG 2.1 AA de los colores del portal municipal de Chía
 * Verifica específicamente las combinaciones de colores usadas después de la actualización
 * del esquema de colores corporativo.
 */

// Colores del sistema Chía
const chiaColors = {
  'chia-blue-900': '#1e3a8a',
  'chia-blue-800': '#1e40af', 
  'chia-blue-600': '#2563eb',
  'chia-green-600': '#059669', // Verde oficial de Chía
  'chia-green-700': '#15803d', // Color primario - Cumple WCAG 2.1 AA
  'white': '#ffffff',
  'gray-50': '#f9fafb',
  'gray-100': '#f3f4f6',
  'gray-600': '#4b5563',
  'gray-900': '#111827'
}

// Función para calcular luminancia
function getLuminance(hex) {
  const rgb = parseInt(hex.slice(1), 16)
  const r = (rgb >> 16) & 0xff
  const g = (rgb >> 8) & 0xff
  const b = (rgb >> 0) & 0xff

  const [rs, gs, bs] = [r, g, b].map(c => {
    c = c / 255
    return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4)
  })

  return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs
}

// Función para calcular contraste
function calculateContrast(color1, color2) {
  const lum1 = getLuminance(color1)
  const lum2 = getLuminance(color2)
  const brightest = Math.max(lum1, lum2)
  const darkest = Math.min(lum1, lum2)

  return (brightest + 0.05) / (darkest + 0.05)
}

// Combinaciones críticas después de la actualización
const criticalCombinations = [
  { 
    fg: chiaColors['chia-green-700'], 
    bg: chiaColors.white, 
    name: 'PRIMARY (chia-green-700) sobre blanco',
    usage: 'Botones primarios, enlaces, texto de acción'
  },
  { 
    fg: chiaColors.white, 
    bg: chiaColors['chia-green-700'], 
    name: 'Texto blanco sobre PRIMARY',
    usage: 'Texto en botones primarios, badges'
  },
  { 
    fg: chiaColors['chia-green-700'], 
    bg: chiaColors['gray-50'], 
    name: 'PRIMARY sobre fondo gris claro',
    usage: 'Texto en cards, modales'
  },
  { 
    fg: chiaColors['chia-green-600'], 
    bg: chiaColors.white, 
    name: 'Verde oficial sobre blanco',
    usage: 'Elementos decorativos, iconos'
  },
  { 
    fg: chiaColors.white, 
    bg: chiaColors['chia-green-600'], 
    name: 'Texto blanco sobre verde oficial',
    usage: 'Headers, gradientes'
  }
]

console.log('🎨 VERIFICACIÓN WCAG 2.1 AA - PORTAL MUNICIPAL DE CHÍA')
console.log('=' .repeat(60))
console.log()

let passCount = 0
let failCount = 0

criticalCombinations.forEach((combo, index) => {
  const ratio = calculateContrast(combo.fg, combo.bg)
  const passesAA = ratio >= 4.5
  const passesAAA = ratio >= 7
  
  const status = passesAAA ? '✅ AAA' : passesAA ? '✅ AA' : '❌ FAIL'
  const statusColor = passesAA ? '\x1b[32m' : '\x1b[31m' // Green or Red
  const resetColor = '\x1b[0m'
  
  console.log(`${index + 1}. ${combo.name}`)
  console.log(`   Uso: ${combo.usage}`)
  console.log(`   Contraste: ${statusColor}${ratio.toFixed(2)}:1 ${status}${resetColor}`)
  console.log(`   Foreground: ${combo.fg}`)
  console.log(`   Background: ${combo.bg}`)
  console.log()
  
  if (passesAA) {
    passCount++
  } else {
    failCount++
  }
})

console.log('📊 RESUMEN')
console.log('=' .repeat(30))
console.log(`✅ Combinaciones que pasan WCAG 2.1 AA: ${passCount}`)
console.log(`❌ Combinaciones que fallan: ${failCount}`)
console.log(`📈 Porcentaje de cumplimiento: ${((passCount / criticalCombinations.length) * 100).toFixed(1)}%`)
console.log()

if (failCount === 0) {
  console.log('🎉 ¡EXCELENTE! Todas las combinaciones de colores cumplen con WCAG 2.1 AA')
  console.log('   El esquema de colores corporativo es accesible.')
} else {
  console.log('⚠️  ATENCIÓN: Algunas combinaciones no cumplen con WCAG 2.1 AA')
  console.log('   Se requieren ajustes en los colores para garantizar accesibilidad.')
}

console.log()
console.log('📋 NOTAS:')
console.log('- WCAG 2.1 AA requiere un contraste mínimo de 4.5:1')
console.log('- WCAG 2.1 AAA requiere un contraste mínimo de 7:1')
console.log('- Este script verifica las combinaciones más críticas del portal')
