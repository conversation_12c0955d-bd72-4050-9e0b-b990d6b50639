#!/usr/bin/env tsx

/**
 * Script de Validación Post-Consolidación FAQ
 * Sistema Municipal de Chía
 * 
 * Valida que la consolidación de tablas obsoletas se ejecutó correctamente
 */

import { createClient } from '@supabase/supabase-js'
import type { Database } from '../lib/database.types'

// Configuración de Supabase
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Error: Variables de entorno de Supabase no configuradas')
  process.exit(1)
}

const supabase = createClient<Database>(supabaseUrl, supabaseKey)

interface ValidationResult {
  test: string
  status: 'PASS' | 'FAIL' | 'WARNING'
  message: string
  details?: any
}

class FAQConsolidationValidator {
  private results: ValidationResult[] = []

  /**
   * Agregar resultado de validación
   */
  private addResult(test: string, status: 'PASS' | 'FAIL' | 'WARNING', message: string, details?: any) {
    this.results.push({ test, status, message, details })
  }

  /**
   * Validar que las tablas obsoletas no existen
   */
  async validateObsoleteTablesRemoved(): Promise<void> {
    console.log('🗑️  Validando eliminación de tablas obsoletas...')

    try {
      // Intentar consultar tablas obsoletas (debería fallar)
      const obsoleteTables = ['faqs', 'faq_categories']
      
      for (const table of obsoleteTables) {
        try {
          const { error } = await supabase.from(table as any).select('id').limit(1)
          
          if (error && error.message.includes('does not exist')) {
            this.addResult(
              `OBSOLETE_TABLE_${table.toUpperCase()}`,
              'PASS',
              `Tabla obsoleta "${table}" eliminada correctamente`
            )
          } else {
            this.addResult(
              `OBSOLETE_TABLE_${table.toUpperCase()}`,
              'FAIL',
              `Tabla obsoleta "${table}" aún existe`
            )
          }
        } catch (err) {
          this.addResult(
            `OBSOLETE_TABLE_${table.toUpperCase()}`,
            'PASS',
            `Tabla obsoleta "${table}" no accesible (eliminada)`
          )
        }
      }
    } catch (error) {
      this.addResult('OBSOLETE_TABLES', 'FAIL', `Error validando tablas obsoletas: ${error}`)
    }
  }

  /**
   * Validar que las tablas actuales funcionan correctamente
   */
  async validateCurrentTablesWorking(): Promise<void> {
    console.log('✅ Validando funcionamiento de tablas actuales...')

    try {
      // Validar municipal_faqs
      const { data: faqs, error: faqsError } = await supabase
        .from('municipal_faqs')
        .select('id, question, theme_id')
        .eq('is_active', true)
        .limit(5)

      if (faqsError) {
        this.addResult('MUNICIPAL_FAQS', 'FAIL', `Error consultando municipal_faqs: ${faqsError.message}`)
      } else {
        this.addResult(
          'MUNICIPAL_FAQS',
          'PASS',
          `Tabla municipal_faqs funcional con ${faqs?.length || 0} registros consultados`
        )
      }

      // Validar faq_themes
      const { data: themes, error: themesError } = await supabase
        .from('faq_themes')
        .select('id, name')
        .eq('is_active', true)
        .limit(5)

      if (themesError) {
        this.addResult('FAQ_THEMES', 'FAIL', `Error consultando faq_themes: ${themesError.message}`)
      } else {
        this.addResult(
          'FAQ_THEMES',
          'PASS',
          `Tabla faq_themes funcional con ${themes?.length || 0} registros consultados`
        )
      }

      // Validar relación entre tablas
      const { data: joinData, error: joinError } = await supabase
        .from('municipal_faqs')
        .select(`
          id,
          question,
          faq_themes!inner(name)
        `)
        .eq('is_active', true)
        .limit(3)

      if (joinError) {
        this.addResult('TABLE_RELATIONS', 'FAIL', `Error en JOIN municipal_faqs-faq_themes: ${joinError.message}`)
      } else {
        this.addResult(
          'TABLE_RELATIONS',
          'PASS',
          `Relación municipal_faqs-faq_themes funcional con ${joinData?.length || 0} registros`
        )
      }

    } catch (error) {
      this.addResult('CURRENT_TABLES', 'FAIL', `Error validando tablas actuales: ${error}`)
    }
  }

  /**
   * Validar conteos de datos post-migración
   */
  async validateDataCounts(): Promise<void> {
    console.log('📊 Validando conteos de datos...')

    try {
      // Contar FAQs municipales
      const { count: faqCount, error: faqCountError } = await supabase
        .from('municipal_faqs')
        .select('*', { count: 'exact', head: true })
        .eq('is_active', true)

      if (faqCountError) {
        this.addResult('FAQ_COUNT', 'FAIL', `Error contando FAQs: ${faqCountError.message}`)
      } else {
        const expectedMinimum = 393 // 383 originales + 10 migradas
        if ((faqCount || 0) >= expectedMinimum) {
          this.addResult(
            'FAQ_COUNT',
            'PASS',
            `Conteo de FAQs correcto: ${faqCount} (esperado: >=${expectedMinimum})`
          )
        } else {
          this.addResult(
            'FAQ_COUNT',
            'FAIL',
            `Conteo de FAQs insuficiente: ${faqCount} (esperado: >=${expectedMinimum})`
          )
        }
      }

      // Contar temas
      const { count: themeCount, error: themeCountError } = await supabase
        .from('faq_themes')
        .select('*', { count: 'exact', head: true })
        .eq('is_active', true)

      if (themeCountError) {
        this.addResult('THEME_COUNT', 'FAIL', `Error contando temas: ${themeCountError.message}`)
      } else {
        const expectedThemes = 37
        if (themeCount === expectedThemes) {
          this.addResult(
            'THEME_COUNT',
            'PASS',
            `Conteo de temas correcto: ${themeCount} (esperado: ${expectedThemes})`
          )
        } else {
          this.addResult(
            'THEME_COUNT',
            'WARNING',
            `Conteo de temas diferente: ${themeCount} (esperado: ${expectedThemes})`
          )
        }
      }

    } catch (error) {
      this.addResult('DATA_COUNTS', 'FAIL', `Error validando conteos: ${error}`)
    }
  }

  /**
   * Validar que faq_analytics fue actualizada correctamente
   */
  async validateAnalyticsTable(): Promise<void> {
    console.log('📈 Validando tabla faq_analytics...')

    try {
      // Verificar estructura de la tabla
      const { data: analyticsData, error: analyticsError } = await supabase
        .from('faq_analytics')
        .select('id, faq_id, theme_id')
        .limit(1)

      if (analyticsError && !analyticsError.message.includes('theme_id')) {
        this.addResult('ANALYTICS_STRUCTURE', 'FAIL', `Error en estructura de faq_analytics: ${analyticsError.message}`)
      } else {
        this.addResult(
          'ANALYTICS_STRUCTURE',
          'PASS',
          'Tabla faq_analytics accesible con nueva estructura (theme_id)'
        )
      }

      // Intentar insertar un registro de prueba para validar constraints
      const testRecord = {
        session_id: 'test-consolidation-validation',
        event_type: 'validation_test',
        faq_id: null,
        theme_id: null,
        search_query: 'test query',
        results_count: 0,
        response_time: 100,
        context: 'consolidation_validation'
      }

      const { error: insertError } = await supabase
        .from('faq_analytics')
        .insert(testRecord)

      if (insertError) {
        this.addResult('ANALYTICS_INSERT', 'WARNING', `Advertencia en inserción de prueba: ${insertError.message}`)
      } else {
        this.addResult('ANALYTICS_INSERT', 'PASS', 'Inserción de prueba en faq_analytics exitosa')
        
        // Limpiar registro de prueba
        await supabase
          .from('faq_analytics')
          .delete()
          .eq('session_id', 'test-consolidation-validation')
      }

    } catch (error) {
      this.addResult('ANALYTICS_TABLE', 'FAIL', `Error validando faq_analytics: ${error}`)
    }
  }

  /**
   * Validar funcionalidad del sistema FAQ
   */
  async validateFAQSystemFunctionality(): Promise<void> {
    console.log('🔍 Validando funcionalidad del sistema FAQ...')

    try {
      // Test de búsqueda
      const { data: searchResults, error: searchError } = await supabase
        .from('municipal_faqs')
        .select(`
          id,
          question,
          answer,
          faq_themes!inner(name)
        `)
        .eq('is_active', true)
        .ilike('question', '%impuesto%')
        .limit(3)

      if (searchError) {
        this.addResult('FAQ_SEARCH', 'FAIL', `Error en búsqueda FAQ: ${searchError.message}`)
      } else {
        this.addResult(
          'FAQ_SEARCH',
          'PASS',
          `Búsqueda FAQ funcional: ${searchResults?.length || 0} resultados encontrados`
        )
      }

      // Test de filtrado por tema
      const { data: themeResults, error: themeError } = await supabase
        .from('municipal_faqs')
        .select(`
          id,
          question,
          faq_themes!inner(name)
        `)
        .eq('is_active', true)
        .limit(5)

      if (themeError) {
        this.addResult('FAQ_THEME_FILTER', 'FAIL', `Error en filtrado por tema: ${themeError.message}`)
      } else {
        this.addResult(
          'FAQ_THEME_FILTER',
          'PASS',
          `Filtrado por tema funcional: ${themeResults?.length || 0} resultados`
        )
      }

    } catch (error) {
      this.addResult('FAQ_FUNCTIONALITY', 'FAIL', `Error validando funcionalidad FAQ: ${error}`)
    }
  }

  /**
   * Imprimir resultados de validación
   */
  printResults(): void {
    console.log('\n' + '='.repeat(70))
    console.log('📋 RESULTADOS DE VALIDACIÓN POST-CONSOLIDACIÓN')
    console.log('='.repeat(70))

    const passed = this.results.filter(r => r.status === 'PASS')
    const failed = this.results.filter(r => r.status === 'FAIL')
    const warnings = this.results.filter(r => r.status === 'WARNING')

    // Mostrar resultados por categoría
    this.results.forEach(result => {
      const icon = result.status === 'PASS' ? '✅' : result.status === 'FAIL' ? '❌' : '⚠️'
      console.log(`${icon} ${result.test}: ${result.message}`)
      if (result.details) {
        console.log(`   Detalles: ${JSON.stringify(result.details, null, 2)}`)
      }
    })

    console.log('\n' + '='.repeat(70))
    console.log(`📊 RESUMEN: ${passed.length} exitosos | ${failed.length} fallidos | ${warnings.length} advertencias`)

    if (failed.length === 0) {
      console.log('\n🎉 ¡CONSOLIDACIÓN VALIDADA EXITOSAMENTE!')
      console.log('✅ Sistema FAQ funcionando correctamente con tablas actuales')
      console.log('✅ Tablas obsoletas eliminadas')
      console.log('✅ Datos migrados correctamente')
    } else {
      console.log('\n🚨 CONSOLIDACIÓN REQUIERE ATENCIÓN')
      console.log('❌ Se encontraron errores que deben ser corregidos')
    }

    if (warnings.length > 0) {
      console.log('\n⚠️  Revisar advertencias para asegurar funcionamiento óptimo')
    }
  }

  /**
   * Ejecutar todas las validaciones
   */
  async runAllValidations(): Promise<void> {
    console.log('🔍 Iniciando validación post-consolidación FAQ...\n')

    await this.validateObsoleteTablesRemoved()
    await this.validateCurrentTablesWorking()
    await this.validateDataCounts()
    await this.validateAnalyticsTable()
    await this.validateFAQSystemFunctionality()

    this.printResults()
  }
}

// Ejecutar validación si el script se ejecuta directamente
if (require.main === module) {
  const validator = new FAQConsolidationValidator()
  validator.runAllValidations()
    .then(() => {
      console.log('\n✅ Validación completada')
      process.exit(0)
    })
    .catch((error) => {
      console.error('\n❌ Error durante la validación:', error)
      process.exit(1)
    })
}

export default FAQConsolidationValidator
