import { describe, it, expect } from '@jest/globals'

describe('FAQService - Unit Tests', () => {
  describe('Service Structure', () => {
    it('should be able to import FAQService', async () => {
      const FAQService = await import('@/lib/services/faqService')
      expect(FAQService.default).toBeDefined()
      expect(typeof FAQService.default).toBe('object')
    })

    it('should have required methods', async () => {
      const FAQService = await import('@/lib/services/faqService')
      const service = FAQService.default
      
      expect(typeof service.getThemes).toBe('function')
      expect(typeof service.searchFAQs).toBe('function')
      expect(typeof service.getFAQsByTheme).toBe('function')
      expect(typeof service.getPopularFAQs).toBe('function')
      expect(typeof service.getFAQById).toBe('function')
      expect(typeof service.getFAQStats).toBe('function')
      
      // Compatibility methods
      expect(typeof service.getCategories).toBe('function')
      expect(typeof service.getFAQsByCategory).toBe('function')
    })
  })

  describe('Input Validation', () => {
    it('should handle empty search queries', async () => {
      const FAQService = await import('@/lib/services/faqService')
      const service = FAQService.default
      
      const results = await service.searchFAQs('')
      expect(Array.isArray(results)).toBe(true)
      expect(results.length).toBe(0)
    })

    it('should handle whitespace-only search queries', async () => {
      const FAQService = await import('@/lib/services/faqService')
      const service = FAQService.default
      
      const results = await service.searchFAQs('   ')
      expect(Array.isArray(results)).toBe(true)
      expect(results.length).toBe(0)
    })
  })

  describe('Interface Compatibility', () => {
    it('should maintain FAQItem interface structure', () => {
      // Test que las interfaces están correctamente definidas
      const mockFAQItem = {
        id: 'test-id',
        question: 'Test question',
        answer: 'Test answer',
        theme: 'Test theme',
        themeId: 'theme-id',
        keywords: ['test', 'keyword'],
        displayOrder: 1,
        popularityScore: 10,
        viewCount: 5,
        helpfulVotes: 3,
        unhelpfulVotes: 1,
        lastUpdated: new Date()
      }
      
      expect(mockFAQItem.id).toBeDefined()
      expect(mockFAQItem.question).toBeDefined()
      expect(mockFAQItem.answer).toBeDefined()
      expect(mockFAQItem.theme).toBeDefined()
      expect(mockFAQItem.themeId).toBeDefined()
      expect(Array.isArray(mockFAQItem.keywords)).toBe(true)
    })

    it('should maintain FAQTheme interface structure', () => {
      const mockFAQTheme = {
        id: 'theme-id',
        name: 'Test Theme',
        description: 'Test description',
        displayOrder: 1,
        dependencyId: 'dep-id',
        subdependencyId: 'subdep-id',
        count: 10
      }
      
      expect(mockFAQTheme.id).toBeDefined()
      expect(mockFAQTheme.name).toBeDefined()
      expect(typeof mockFAQTheme.count).toBe('number')
    })
  })

  describe('Search Options', () => {
    it('should accept valid search options', async () => {
      const FAQService = await import('@/lib/services/faqService')
      const service = FAQService.default
      
      const options = {
        theme: 'test-theme-id',
        dependencyId: 'test-dep-id',
        subdependencyId: 'test-subdep-id',
        limit: 5,
        includeKeywords: true
      }
      
      // Should not throw error with valid options
      expect(() => service.searchFAQs('test', options)).not.toThrow()
    })
  })

  describe('Error Handling', () => {
    it('should handle invalid FAQ ID gracefully', async () => {
      const FAQService = await import('@/lib/services/faqService')
      const service = FAQService.default
      
      // Should not throw error, should return null or handle gracefully
      const result = await service.getFAQById('invalid-id')
      expect(result === null || typeof result === 'object').toBe(true)
    })

    it('should handle invalid theme ID gracefully', async () => {
      const FAQService = await import('@/lib/services/faqService')
      const service = FAQService.default
      
      // Should not throw error, should return empty array
      const results = await service.getFAQsByTheme('invalid-theme-id')
      expect(Array.isArray(results)).toBe(true)
    })
  })

  describe('Method Return Types', () => {
    it('getThemes should return array', async () => {
      const FAQService = await import('@/lib/services/faqService')
      const service = FAQService.default
      
      const themes = await service.getThemes()
      expect(Array.isArray(themes)).toBe(true)
    })

    it('searchFAQs should return array', async () => {
      const FAQService = await import('@/lib/services/faqService')
      const service = FAQService.default
      
      const results = await service.searchFAQs('test')
      expect(Array.isArray(results)).toBe(true)
    })

    it('getFAQStats should return object with correct structure', async () => {
      const FAQService = await import('@/lib/services/faqService')
      const service = FAQService.default
      
      const stats = await service.getFAQStats()
      expect(typeof stats).toBe('object')
      expect(stats).toHaveProperty('totalFAQs')
      expect(stats).toHaveProperty('totalThemes')
      expect(stats).toHaveProperty('averagePopularity')
      expect(stats).toHaveProperty('mostPopularTheme')
      
      expect(typeof stats.totalFAQs).toBe('number')
      expect(typeof stats.totalThemes).toBe('number')
      expect(typeof stats.averagePopularity).toBe('number')
      expect(typeof stats.mostPopularTheme).toBe('string')
    })
  })

  describe('Compatibility Methods', () => {
    it('getCategories should be alias for getThemes', async () => {
      const FAQService = await import('@/lib/services/faqService')
      const service = FAQService.default
      
      // Both methods should exist and be functions
      expect(typeof service.getCategories).toBe('function')
      expect(typeof service.getThemes).toBe('function')
    })

    it('getFAQsByCategory should be alias for getFAQsByTheme', async () => {
      const FAQService = await import('@/lib/services/faqService')
      const service = FAQService.default
      
      // Both methods should exist and be functions
      expect(typeof service.getFAQsByCategory).toBe('function')
      expect(typeof service.getFAQsByTheme).toBe('function')
    })
  })
})
