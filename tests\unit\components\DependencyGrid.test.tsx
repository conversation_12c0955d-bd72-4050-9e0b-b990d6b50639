import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { DependencyGrid } from '@/components/dependencies/DependencyGrid'
import DependencyService from '@/lib/services/dependencyService'

// Mock del DependencyService
jest.mock('@/lib/services/dependencyService', () => ({
  __esModule: true,
  default: {
    getAllDependencies: jest.fn(),
    getDependencyStats: jest.fn(),
    searchDependencies: jest.fn(),
    getProceduresByDependency: jest.fn(),
    getTopDependencies: jest.fn()
  }
}))

const mockDependencies = [
  {
    id: '040',
    name: 'Secretaría de Hacienda',
    sigla: 'SH',
    tramitesCount: 25,
    opasCount: 15,
    totalProcedures: 40,
    description: 'Administración financiera y tributaria municipal',
    icon: 'banknote',
    color: 'bg-primary/10 border-primary/30 hover:bg-primary/20'
  },
  {
    id: '010',
    name: 'Secretaría de Planeación',
    sigla: 'SP',
    tramitesCount: 18,
    opasCount: 12,
    totalProcedures: 30,
    description: 'Ordenamiento territorial y desarrollo urbano',
    icon: 'map',
    color: 'bg-chia-green-100 border-chia-green-300 hover:bg-chia-green-200'
  }
]

const mockStats = {
  totalDependencies: 2,
  totalTramites: 43,
  totalOPAs: 27,
  totalProcedures: 70,
  averageProceduresPerDependency: 35
}

describe('DependencyGrid', () => {
  const mockOnDependencySelect = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
    ;(DependencyService.getAllDependencies as jest.Mock).mockResolvedValue(mockDependencies)
    ;(DependencyService.getDependencyStats as jest.Mock).mockResolvedValue(mockStats)
    ;(DependencyService.searchDependencies as jest.Mock).mockResolvedValue(mockDependencies)
  })

  describe('Renderizado inicial', () => {
    it('debería renderizar correctamente', async () => {
      render(<DependencyGrid />)
      
      await waitFor(() => {
        expect(screen.getByText('Secretaría de Hacienda')).toBeInTheDocument()
        expect(screen.getByText('Secretaría de Planeación')).toBeInTheDocument()
      })
    })

    it('debería mostrar estado de carga inicialmente', () => {
      render(<DependencyGrid />)
      
      // Verificar elementos de skeleton loading
      const skeletonElements = document.querySelectorAll('.animate-pulse')
      expect(skeletonElements.length).toBeGreaterThan(0)
    })

    it('debería mostrar estadísticas cuando showStats es true', async () => {
      render(<DependencyGrid showStats={true} />)
      
      await waitFor(() => {
        expect(screen.getByText('Dependencias')).toBeInTheDocument()
        expect(screen.getByText('2')).toBeInTheDocument()
        expect(screen.getByText('Trámites')).toBeInTheDocument()
        expect(screen.getByText('43')).toBeInTheDocument()
      })
    })

    it('debería ocultar estadísticas cuando showStats es false', async () => {
      render(<DependencyGrid showStats={false} />)
      
      await waitFor(() => {
        expect(screen.queryByText('Dependencias')).not.toBeInTheDocument()
      })
    })

    it('debería mostrar barra de búsqueda cuando showSearch es true', async () => {
      render(<DependencyGrid showSearch={true} />)
      
      await waitFor(() => {
        expect(screen.getByPlaceholderText('Buscar dependencias...')).toBeInTheDocument()
      })
    })

    it('debería ocultar barra de búsqueda cuando showSearch es false', async () => {
      render(<DependencyGrid showSearch={false} />)
      
      await waitFor(() => {
        expect(screen.queryByPlaceholderText('Buscar dependencias...')).not.toBeInTheDocument()
      })
    })
  })

  describe('Interacciones', () => {
    it('debería llamar onDependencySelect al hacer clic en una dependencia', async () => {
      render(<DependencyGrid onDependencySelect={mockOnDependencySelect} />)
      
      await waitFor(() => {
        expect(screen.getByText('Secretaría de Hacienda')).toBeInTheDocument()
      })

      fireEvent.click(screen.getByText('Secretaría de Hacienda'))
      
      expect(mockOnDependencySelect).toHaveBeenCalledWith(mockDependencies[0])
    })

    it('debería realizar búsqueda al escribir en el campo de búsqueda', async () => {
      const user = userEvent.setup()
      render(<DependencyGrid showSearch={true} />)
      
      await waitFor(() => {
        expect(screen.getByPlaceholderText('Buscar dependencias...')).toBeInTheDocument()
      })

      const searchInput = screen.getByPlaceholderText('Buscar dependencias...')
      await user.type(searchInput, 'Hacienda')
      
      await waitFor(() => {
        expect(DependencyService.searchDependencies).toHaveBeenCalledWith('Hacienda')
      })
    })

    it('debería limpiar búsqueda al hacer clic en "Limpiar búsqueda"', async () => {
      ;(DependencyService.searchDependencies as jest.Mock).mockResolvedValue([])
      
      const user = userEvent.setup()
      render(<DependencyGrid showSearch={true} />)
      
      await waitFor(() => {
        expect(screen.getByPlaceholderText('Buscar dependencias...')).toBeInTheDocument()
      })

      const searchInput = screen.getByPlaceholderText('Buscar dependencias...')
      await user.type(searchInput, 'NoExiste')
      
      await waitFor(() => {
        expect(screen.getByText('No se encontraron dependencias')).toBeInTheDocument()
      })

      const clearButton = screen.getByText('Limpiar búsqueda')
      fireEvent.click(clearButton)
      
      expect(searchInput).toHaveValue('')
    })
  })

  describe('Visualización de datos', () => {
    it('debería mostrar información completa de cada dependencia', async () => {
      render(<DependencyGrid />)
      
      await waitFor(() => {
        // Verificar nombre y sigla
        expect(screen.getByText('Secretaría de Hacienda')).toBeInTheDocument()
        expect(screen.getByText('SH')).toBeInTheDocument()
        
        // Verificar descripción
        expect(screen.getByText('Administración financiera y tributaria municipal')).toBeInTheDocument()
        
        // Verificar contadores
        expect(screen.getByText('25 trámites')).toBeInTheDocument()
        expect(screen.getByText('15 OPAs')).toBeInTheDocument()
        expect(screen.getByText('40')).toBeInTheDocument()
      })
    })

    it('debería respetar el límite maxItems', async () => {
      render(<DependencyGrid maxItems={1} />)
      
      await waitFor(() => {
        expect(screen.getByText('Secretaría de Hacienda')).toBeInTheDocument()
        expect(screen.queryByText('Secretaría de Planeación')).not.toBeInTheDocument()
      })
    })

    it('debería mostrar mensaje cuando no hay resultados', async () => {
      ;(DependencyService.getAllDependencies as jest.Mock).mockResolvedValue([])
      
      render(<DependencyGrid />)
      
      await waitFor(() => {
        expect(screen.getByText('No se encontraron dependencias')).toBeInTheDocument()
      })
    })
  })

  describe('Manejo de errores', () => {
    it('debería mostrar mensaje de error cuando falla la carga', async () => {
      ;(DependencyService.getAllDependencies as jest.Mock).mockRejectedValue(new Error('Error de red'))
      
      render(<DependencyGrid />)
      
      await waitFor(() => {
        expect(screen.getByText(/Error al cargar las dependencias/)).toBeInTheDocument()
      })
    })

    it('debería permitir reintentar después de un error', async () => {
      ;(DependencyService.getAllDependencies as jest.Mock).mockRejectedValueOnce(new Error('Error'))
        .mockResolvedValueOnce(mockDependencies)
      
      render(<DependencyGrid />)
      
      await waitFor(() => {
        expect(screen.getByText('Reintentar')).toBeInTheDocument()
      })

      fireEvent.click(screen.getByText('Reintentar'))
      
      await waitFor(() => {
        expect(screen.getByText('Secretaría de Hacienda')).toBeInTheDocument()
      })
    })
  })

  describe('Accesibilidad', () => {
    it('debería tener elementos clickeables accesibles', async () => {
      render(<DependencyGrid onDependencySelect={mockOnDependencySelect} />)
      
      await waitFor(() => {
        const dependencyCards = screen.getAllByRole('button', { name: /Secretaría/ })
        expect(dependencyCards.length).toBeGreaterThan(0)
      })
    })

    it('debería permitir navegación por teclado', async () => {
      const user = userEvent.setup()
      render(<DependencyGrid showSearch={true} />)
      
      await waitFor(() => {
        expect(screen.getByPlaceholderText('Buscar dependencias...')).toBeInTheDocument()
      })

      const searchInput = screen.getByPlaceholderText('Buscar dependencias...')
      
      // Verificar que el input es focuseable
      await user.tab()
      expect(searchInput).toHaveFocus()
    })
  })

  describe('Responsive design', () => {
    it('debería aplicar clases CSS responsivas', async () => {
      render(<DependencyGrid />)
      
      await waitFor(() => {
        const gridContainer = document.querySelector('.grid')
        expect(gridContainer).toHaveClass('grid-cols-1', 'md:grid-cols-2', 'lg:grid-cols-3')
      })
    })
  })

  describe('Props personalizadas', () => {
    it('debería aplicar className personalizada', () => {
      render(<DependencyGrid className="custom-class" />)
      
      const container = document.querySelector('.custom-class')
      expect(container).toBeInTheDocument()
    })

    it('debería manejar props undefined correctamente', async () => {
      render(<DependencyGrid onDependencySelect={undefined} />)
      
      await waitFor(() => {
        expect(screen.getByText('Secretaría de Hacienda')).toBeInTheDocument()
      })

      // No debería lanzar error al hacer clic
      fireEvent.click(screen.getByText('Secretaría de Hacienda'))
    })
  })
})
