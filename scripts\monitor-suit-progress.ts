/**
 * Monitor SUIT Scraping Progress
 * Script para monitorear el progreso del scraping SUIT
 */

import { createClient } from '@supabase/supabase-js'
import * as dotenv from 'dotenv'
import * as path from 'path'

// Cargar variables de entorno
dotenv.config({ path: path.join(__dirname, '..', '.env.local') })

const supabaseUrl = process.env['NEXT_PUBLIC_SUPABASE_URL']!
const supabaseKey = process.env['SUPABASE_SERVICE_ROLE_KEY']!
const supabase = createClient(supabaseUrl, supabaseKey)

interface ProgressStats {
  totalProcedures: number
  proceduresWithSuitLinks: number
  scrapedProcedures: number
  scrapedSuccessfully: number
  scrapedFailed: number
  pendingScraping: number
  successPercentage: number
}

interface RecentSuccess {
  fichaId: string
  procedureName: string
  titulo: string
  entidadResponsable: string
  costoDetallado: string
  scrapedAt: string
}

class SuitProgressMonitor {
  async getProgressStats(): Promise<ProgressStats> {
    const { data, error } = await supabase.rpc('get_suit_scraping_stats')
    
    if (error) {
      console.error('Error obteniendo estadísticas:', error)
      throw error
    }

    return data[0] as ProgressStats
  }

  async getRecentSuccesses(limit: number = 10): Promise<RecentSuccess[]> {
    const { data, error } = await supabase
      .from('suit_scraped_data')
      .select(`
        ficha_id,
        titulo,
        entidad_responsable,
        costo_detallado,
        scraped_at,
        procedures!inner(name)
      `)
      .eq('scraping_status', 'success')
      .order('scraped_at', { ascending: false })
      .limit(limit)

    if (error) {
      console.error('Error obteniendo éxitos recientes:', error)
      throw error
    }

    return data.map(item => ({
      fichaId: item.ficha_id,
      procedureName: item.procedures.name,
      titulo: item.titulo,
      entidadResponsable: item.entidad_responsable,
      costoDetallado: item.costo_detallado,
      scrapedAt: item.scraped_at
    }))
  }

  async getFailedProcedures(): Promise<any[]> {
    const { data, error } = await supabase
      .from('suit_scraped_data')
      .select(`
        ficha_id,
        error_message,
        scraped_at,
        procedures!inner(name, suit_link)
      `)
      .eq('scraping_status', 'failed')
      .order('scraped_at', { ascending: false })

    if (error) {
      console.error('Error obteniendo fallos:', error)
      throw error
    }

    return data
  }

  async displayProgress(): Promise<void> {
    console.log('🔍 MONITOREO DE PROGRESO SUIT SCRAPING')
    console.log('=' .repeat(60))

    try {
      // Obtener estadísticas generales
      const stats = await this.getProgressStats()
      
      console.log('\n📊 ESTADÍSTICAS GENERALES')
      console.log('-' .repeat(40))
      console.log(`📋 Total de procedimientos: ${stats.totalProcedures}`)
      console.log(`🔗 Con enlaces SUIT: ${stats.proceduresWithSuitLinks}`)
      console.log(`🤖 Procesados: ${stats.scrapedProcedures}`)
      console.log(`✅ Exitosos: ${stats.scrapedSuccessfully}`)
      console.log(`❌ Fallidos: ${stats.scrapedFailed}`)
      console.log(`⏳ Pendientes: ${stats.pendingScraping}`)
      console.log(`📈 Tasa de éxito: ${stats.successPercentage}%`)

      // Calcular progreso hacia el objetivo del 80%
      const targetSuccessRate = 80
      const currentSuccessRate = stats.successPercentage
      const progressToTarget = (currentSuccessRate / targetSuccessRate * 100).toFixed(1)
      
      console.log(`\n🎯 PROGRESO HACIA OBJETIVO (80% de éxito)`)
      console.log('-' .repeat(40))
      console.log(`Progreso actual: ${progressToTarget}% del objetivo`)
      
      if (currentSuccessRate >= targetSuccessRate) {
        console.log('🎉 ¡OBJETIVO ALCANZADO!')
      } else {
        const proceduresNeeded = Math.ceil((targetSuccessRate * stats.proceduresWithSuitLinks / 100) - stats.scrapedSuccessfully)
        console.log(`Procedimientos adicionales necesarios: ${proceduresNeeded}`)
      }

      // Mostrar éxitos recientes
      console.log('\n✅ ÉXITOS RECIENTES (últimos 5)')
      console.log('-' .repeat(40))
      const recentSuccesses = await this.getRecentSuccesses(5)
      
      if (recentSuccesses.length === 0) {
        console.log('No hay éxitos recientes')
      } else {
        recentSuccesses.forEach((success, index) => {
          console.log(`${index + 1}. ${success.titulo}`)
          console.log(`   Entidad: ${success.entidadResponsable}`)
          console.log(`   Costo: ${success.costoDetallado}`)
          console.log(`   Fecha: ${new Date(success.scrapedAt).toLocaleString()}`)
          console.log('')
        })
      }

      // Mostrar fallos si los hay
      const failures = await this.getFailedProcedures()
      if (failures.length > 0) {
        console.log('\n❌ FALLOS RECIENTES (últimos 3)')
        console.log('-' .repeat(40))
        failures.slice(0, 3).forEach((failure, index) => {
          console.log(`${index + 1}. ${failure.procedures.name}`)
          console.log(`   Ficha ID: ${failure.ficha_id}`)
          console.log(`   Error: ${failure.error_message}`)
          console.log(`   Fecha: ${new Date(failure.scraped_at).toLocaleString()}`)
          console.log('')
        })
      }

      // Recomendaciones
      console.log('\n💡 RECOMENDACIONES')
      console.log('-' .repeat(40))
      
      if (stats.pendingScraping > 0) {
        console.log(`• Continuar scraping de ${stats.pendingScraping} procedimientos pendientes`)
      }
      
      if (stats.scrapedFailed > 0) {
        console.log(`• Revisar y reintentar ${stats.scrapedFailed} procedimientos fallidos`)
      }
      
      if (currentSuccessRate >= targetSuccessRate) {
        console.log('• ¡Proceder con la implementación del sistema de monitoreo!')
        console.log('• Validar la integración frontend con los datos extraídos')
      }

    } catch (error) {
      console.error('💥 Error monitoreando progreso:', error)
    }
  }

  async monitorContinuously(intervalSeconds: number = 30): Promise<void> {
    console.log(`🔄 Iniciando monitoreo continuo (cada ${intervalSeconds} segundos)`)
    console.log('Presiona Ctrl+C para detener\n')

    const monitor = async () => {
      console.clear()
      await this.displayProgress()
      console.log(`\n⏰ Próxima actualización en ${intervalSeconds} segundos...`)
    }

    // Mostrar inmediatamente
    await monitor()

    // Configurar intervalo
    const interval = setInterval(monitor, intervalSeconds * 1000)

    // Manejar interrupción
    process.on('SIGINT', () => {
      clearInterval(interval)
      console.log('\n👋 Monitoreo detenido')
      process.exit(0)
    })
  }
}

// Ejecutar si es el módulo principal
if (require.main === module) {
  const monitor = new SuitProgressMonitor()
  
  const args = process.argv.slice(2)
  const continuous = args.includes('--continuous') || args.includes('-c')
  const interval = parseInt(args.find(arg => arg.startsWith('--interval='))?.split('=')[1] || '30')

  if (continuous) {
    monitor.monitorContinuously(interval)
      .catch(error => {
        console.error('💥 Error en monitoreo continuo:', error)
        process.exit(1)
      })
  } else {
    monitor.displayProgress()
      .then(() => {
        console.log('\n✅ Monitoreo completado')
        process.exit(0)
      })
      .catch(error => {
        console.error('💥 Error en monitoreo:', error)
        process.exit(1)
      })
  }
}

export { SuitProgressMonitor }
