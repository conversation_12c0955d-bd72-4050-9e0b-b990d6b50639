/**
 * OpenAI Client Configuration
 * Configuración del cliente OpenAI para el sistema de IA conversacional
 */

import OpenAI from 'openai';

// Verificar que la API key esté configurada
if (!process.env.OPENAI_API_KEY) {
  throw new Error('OPENAI_API_KEY is required but not found in environment variables');
}

// Configurar el cliente OpenAI
export const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Configuración de modelos
export const AI_MODELS = {
  CHAT: 'gpt-4o',
  EMBEDDINGS: 'text-embedding-3-small',
  EMBEDDINGS_DIMENSIONS: 1536,
} as const;

// Configuración de parámetros de chat
export const CHAT_CONFIG = {
  temperature: 0.7,
  max_tokens: 2000,
  top_p: 1,
  frequency_penalty: 0,
  presence_penalty: 0,
} as const;

// Configuración de embeddings
export const EMBEDDINGS_CONFIG = {
  model: AI_MODELS.EMBEDDINGS,
  dimensions: AI_MODELS.EMBEDDINGS_DIMENSIONS,
} as const;

/**
 * Generar embeddings para texto
 */
export async function generateEmbedding(text: string): Promise<number[]> {
  try {
    const response = await openai.embeddings.create({
      model: EMBEDDINGS_CONFIG.model,
      input: text,
      dimensions: EMBEDDINGS_CONFIG.dimensions,
    });

    const embedding = response.data[0]?.embedding;
    if (!embedding) {
      throw new Error('No embedding generated');
    }

    return embedding;
  } catch (error) {
    console.error('Error generating embedding:', error);
    throw new Error('Failed to generate embedding');
  }
}

/**
 * Generar embeddings para múltiples textos
 */
export async function generateEmbeddings(texts: string[]): Promise<number[][]> {
  try {
    const response = await openai.embeddings.create({
      model: EMBEDDINGS_CONFIG.model,
      input: texts,
      dimensions: EMBEDDINGS_CONFIG.dimensions,
    });

    return response.data.map(item => item.embedding);
  } catch (error) {
    console.error('Error generating embeddings:', error);
    throw new Error('Failed to generate embeddings');
  }
}

/**
 * Crear chat completion
 */
export async function createChatCompletion(
  messages: OpenAI.Chat.Completions.ChatCompletionMessageParam[],
  options?: Partial<typeof CHAT_CONFIG>
) {
  try {
    const response = await openai.chat.completions.create({
      model: AI_MODELS.CHAT,
      messages,
      ...CHAT_CONFIG,
      ...options,
    });

    return response;
  } catch (error) {
    console.error('Error creating chat completion:', error);
    throw new Error('Failed to create chat completion');
  }
}

/**
 * Crear chat completion con streaming
 */
export async function createStreamingChatCompletion(
  messages: OpenAI.Chat.Completions.ChatCompletionMessageParam[],
  options?: Partial<typeof CHAT_CONFIG>
) {
  try {
    const stream = await openai.chat.completions.create({
      model: AI_MODELS.CHAT,
      messages,
      stream: true,
      ...CHAT_CONFIG,
      ...options,
    });

    return stream;
  } catch (error) {
    console.error('Error creating streaming chat completion:', error);
    throw new Error('Failed to create streaming chat completion');
  }
}

/**
 * Tipos para el sistema de IA
 */
export type ChatMessage = OpenAI.Chat.Completions.ChatCompletionMessageParam;
export type ChatCompletion = OpenAI.Chat.Completions.ChatCompletion;
export type ChatCompletionChunk = OpenAI.Chat.Completions.ChatCompletionChunk;

/**
 * Función para validar y limpiar texto antes de generar embeddings
 */
export function sanitizeTextForEmbedding(text: string): string {
  return text
    .trim()
    .replace(/\s+/g, ' ') // Normalizar espacios
    .replace(/[^\w\s\-.,;:!?()]/g, '') // Remover caracteres especiales
    .substring(0, 8000); // Limitar longitud para evitar errores de API
}

/**
 * Función para combinar texto de múltiples campos para embeddings
 */
export function combineTextForEmbedding(fields: Record<string, string | null | undefined>): string {
  return Object.entries(fields)
    .filter(([_, value]) => value && value.trim())
    .map(([key, value]) => `${key}: ${value}`)
    .join('\n');
}
