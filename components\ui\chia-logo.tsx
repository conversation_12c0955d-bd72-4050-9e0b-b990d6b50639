'use client'

import React from 'react'
import { cn } from '@/lib/utils'

interface ChiaLogoProps {
  className?: string
  size?: 'sm' | 'md' | 'lg' | 'xl'
  variant?: 'full' | 'icon' | 'text'
  showText?: boolean
}

const sizeClasses = {
  sm: 'w-8 h-8',
  md: 'w-10 h-10', 
  lg: 'w-12 h-12',
  xl: 'w-16 h-16'
}

const textSizeClasses = {
  sm: 'text-sm',
  md: 'text-base',
  lg: 'text-lg', 
  xl: 'text-xl'
}

export function ChiaLogo({ 
  className, 
  size = 'md', 
  variant = 'full',
  showText = true 
}: ChiaLogoProps) {
  
  // Escudo/Corona SVG optimizado basado en el logo oficial
  const ShieldIcon = () => (
    <svg 
      viewBox="0 0 100 120" 
      className={cn(sizeClasses[size], className)}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      {/* Corona */}
      <path
        d="M15 25 L85 25 L80 15 L75 20 L70 10 L65 20 L60 10 L55 20 L50 10 L45 20 L40 10 L35 20 L30 10 L25 20 L20 15 Z"
        fill="#FFD700"
        stroke="#B8860B"
        strokeWidth="1"
      />
      
      {/* Escudo principal */}
      <path
        d="M20 30 L80 30 L80 80 C80 90 70 100 50 110 C30 100 20 90 20 80 Z"
        fill="#1E40AF"
        stroke="#1E3A8A"
        strokeWidth="2"
      />
      
      {/* Sección superior azul con estrellas */}
      <rect x="25" y="35" width="50" height="20" fill="#3B82F6" />
      <circle cx="35" cy="42" r="2" fill="#FFD700" />
      <circle cx="45" cy="42" r="2" fill="#FFD700" />
      <circle cx="55" cy="42" r="2" fill="#FFD700" />
      <circle cx="65" cy="42" r="2" fill="#FFD700" />
      
      {/* Sección roja */}
      <rect x="25" y="55" width="25" height="20" fill="#DC2626" />
      
      {/* Sección verde */}
      <rect x="50" y="55" width="25" height="20" fill="#059669" />
      
      {/* Ondas en la parte inferior */}
      <path
        d="M25 75 Q35 80 45 75 T65 75 T75 75 L75 85 Q65 90 55 85 T35 85 T25 85 Z"
        fill="#3B82F6"
      />
      
      {/* Figura central (representando elementos municipales) */}
      <circle cx="37" cy="65" r="4" fill="#FFD700" />
      <rect x="55" y="60" width="8" height="10" fill="#FFD700" />
    </svg>
  )

  // Logo de texto CHÍA
  const ChiaText = () => (
    <div className={cn("font-bold text-chia-green-600", textSizeClasses[size])}>
      <span className="text-chia-green-600">CHÍA</span>
    </div>
  )

  if (variant === 'icon') {
    return <ShieldIcon />
  }

  if (variant === 'text') {
    return <ChiaText />
  }

  // Variant 'full' - Logo completo
  return (
    <div className={cn("flex items-center space-x-2", className)}>
      <ShieldIcon />
      {showText && (
        <div className="flex flex-col">
          <div className={cn("font-bold text-chia-blue-900 leading-tight", textSizeClasses[size])}>
            ALCALDÍA
          </div>
          <div className={cn("font-bold text-chia-green-600 leading-tight", textSizeClasses[size])}>
            DE CHÍA
          </div>
        </div>
      )}
    </div>
  )
}

// Componente específico para el header/navbar
export function ChiaLogoHeader({ className }: { className?: string }) {
  return (
    <ChiaLogo 
      size="md"
      variant="full"
      showText={true}
      className={className}
    />
  )
}

// Componente específico para el footer
export function ChiaLogoFooter({ className }: { className?: string }) {
  return (
    <ChiaLogo 
      size="md"
      variant="full" 
      showText={true}
      className={className}
    />
  )
}

// Componente solo icono para espacios pequeños
export function ChiaLogoIcon({ size = 'md', className }: { size?: 'sm' | 'md' | 'lg' | 'xl', className?: string }) {
  return (
    <ChiaLogo 
      size={size}
      variant="icon"
      showText={false}
      className={className}
    />
  )
}
