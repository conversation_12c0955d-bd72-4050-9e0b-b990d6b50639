-- =============================================
-- SISTEMA DE CÓDIGOS CONSECUTIVOS PARA TRÁMITES Y OPAS
-- =============================================

-- 1. Agregar campo codigo_tramite a la tabla procedures
ALTER TABLE procedures ADD COLUMN IF NOT EXISTS codigo_tramite TEXT UNIQUE;

-- 2. Crear índice para optimizar búsquedas por código
CREATE INDEX IF NOT EXISTS idx_procedures_codigo_tramite ON procedures(codigo_tramite);
CREATE INDEX IF NOT EXISTS idx_opas_code ON opas(code);

-- 3. Función para generar códigos consecutivos por subdependencia
CREATE OR REPLACE FUNCTION generate_consecutive_code(
  p_subdependency_id UUID,
  p_table_name TEXT DEFAULT 'procedures'
) RETURNS TEXT AS $$
DECLARE
  v_subdep_code TEXT;
  v_next_consecutive INTEGER;
  v_new_code TEXT;
BEGIN
  -- Obtener el código de la subdependencia
  SELECT s.code INTO v_subdep_code
  FROM subdependencies s
  WHERE s.id = p_subdependency_id;
  
  IF v_subdep_code IS NULL THEN
    RAISE EXCEPTION 'Subdependencia no encontrada: %', p_subdependency_id;
  END IF;
  
  -- Calcular el siguiente consecutivo para esta subdependencia
  IF p_table_name = 'procedures' THEN
    SELECT COALESCE(MAX(
      CASE 
        WHEN codigo_tramite ~ ('^' || v_subdep_code || '-[0-9]+$') 
        THEN CAST(SPLIT_PART(codigo_tramite, '-', 2) AS INTEGER)
        ELSE 0
      END
    ), 0) + 1 INTO v_next_consecutive
    FROM procedures p
    JOIN subdependencies s ON p.subdependency_id = s.id
    WHERE s.code = v_subdep_code;
  ELSE
    -- Para OPAs, usar el formato existente pero normalizado
    SELECT COALESCE(MAX(
      CASE 
        WHEN code ~ ('^[0-9]+-' || v_subdep_code || '-[0-9]+$') 
        THEN CAST(SPLIT_PART(code, '-', 3) AS INTEGER)
        ELSE 0
      END
    ), 0) + 1 INTO v_next_consecutive
    FROM opas o
    JOIN subdependencies s ON o.subdependency_id = s.id
    WHERE s.code = v_subdep_code;
  END IF;
  
  -- Generar el nuevo código
  v_new_code := v_subdep_code || '-' || LPAD(v_next_consecutive::TEXT, 3, '0');
  
  RETURN v_new_code;
END;
$$ LANGUAGE plpgsql;

-- 4. Función para normalizar códigos de OPAs al formato estándar
CREATE OR REPLACE FUNCTION normalize_opa_codes() RETURNS VOID AS $$
DECLARE
  opa_record RECORD;
  v_dep_code TEXT;
  v_subdep_code TEXT;
  v_consecutive TEXT;
  v_new_code TEXT;
BEGIN
  -- Iterar sobre todos los OPAs que necesitan normalización
  FOR opa_record IN 
    SELECT o.id, o.code, s.code as subdep_code, d.code as dep_code
    FROM opas o
    JOIN subdependencies s ON o.subdependency_id = s.id
    JOIN dependencies d ON s.dependency_id = d.id
    WHERE o.code ~ '^[0-9]+-[0-9]+-[0-9]+$' -- Formato actual: 000-001-1
  LOOP
    -- Extraer el consecutivo del código actual (último número)
    v_consecutive := SPLIT_PART(opa_record.code, '-', 3);
    
    -- Crear nuevo código en formato [CODIGO_SUBDEP]-[CONSECUTIVO]
    v_new_code := opa_record.subdep_code || '-' || LPAD(v_consecutive, 3, '0');
    
    -- Actualizar el registro
    UPDATE opas 
    SET code = v_new_code 
    WHERE id = opa_record.id;
    
    RAISE NOTICE 'OPA normalizado: % -> %', opa_record.code, v_new_code;
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- 5. Función trigger para auto-generar códigos en INSERT
CREATE OR REPLACE FUNCTION auto_generate_codigo_tramite() RETURNS TRIGGER AS $$
BEGIN
  IF NEW.codigo_tramite IS NULL AND NEW.subdependency_id IS NOT NULL THEN
    NEW.codigo_tramite := generate_consecutive_code(NEW.subdependency_id, 'procedures');
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 6. Crear trigger para procedures
DROP TRIGGER IF EXISTS trigger_auto_generate_codigo_tramite ON procedures;
CREATE TRIGGER trigger_auto_generate_codigo_tramite
  BEFORE INSERT ON procedures
  FOR EACH ROW
  EXECUTE FUNCTION auto_generate_codigo_tramite();

-- 7. Generar códigos para todos los procedures existentes
DO $$
DECLARE
  proc_record RECORD;
  v_new_code TEXT;
BEGIN
  FOR proc_record IN 
    SELECT id, subdependency_id 
    FROM procedures 
    WHERE codigo_tramite IS NULL AND subdependency_id IS NOT NULL
    ORDER BY created_at
  LOOP
    v_new_code := generate_consecutive_code(proc_record.subdependency_id, 'procedures');
    
    UPDATE procedures 
    SET codigo_tramite = v_new_code 
    WHERE id = proc_record.id;
    
    RAISE NOTICE 'Código generado para procedure %: %', proc_record.id, v_new_code;
  END LOOP;
END;
$$;

-- 8. Ejecutar normalización de códigos de OPAs
SELECT normalize_opa_codes();

-- 9. Agregar constraint para asegurar formato correcto
ALTER TABLE procedures ADD CONSTRAINT check_codigo_tramite_format 
  CHECK (codigo_tramite ~ '^[0-9]{3}-[0-9]{3}$');

-- 10. Actualizar constraint para OPAs
ALTER TABLE opas DROP CONSTRAINT IF EXISTS check_opa_code_format;
ALTER TABLE opas ADD CONSTRAINT check_opa_code_format 
  CHECK (code ~ '^[0-9]{3}-[0-9]{3}$');

-- 11. Crear vista unificada para códigos
CREATE OR REPLACE VIEW vista_codigos_procedimientos AS
SELECT 
  'TRAMITE' as tipo,
  p.id,
  p.name,
  p.codigo_tramite as codigo,
  s.code as subdep_code,
  s.name as subdep_name,
  d.code as dep_code,
  d.name as dep_name
FROM procedures p
LEFT JOIN subdependencies s ON p.subdependency_id = s.id
LEFT JOIN dependencies d ON s.dependency_id = d.id
WHERE p.codigo_tramite IS NOT NULL

UNION ALL

SELECT 
  'OPA' as tipo,
  o.id,
  o.name,
  o.code as codigo,
  s.code as subdep_code,
  s.name as subdep_name,
  d.code as dep_code,
  d.name as dep_name
FROM opas o
LEFT JOIN subdependencies s ON o.subdependency_id = s.id
LEFT JOIN dependencies d ON s.dependency_id = d.id
WHERE o.code IS NOT NULL;

-- 12. Comentarios para documentación
COMMENT ON COLUMN procedures.codigo_tramite IS 'Código único consecutivo por subdependencia en formato [CODIGO_SUBDEP]-[CONSECUTIVO]';
COMMENT ON FUNCTION generate_consecutive_code IS 'Genera códigos consecutivos únicos por subdependencia para trámites y OPAs';
COMMENT ON VIEW vista_codigos_procedimientos IS 'Vista unificada de todos los códigos de procedimientos (trámites y OPAs)';
