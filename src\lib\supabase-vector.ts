/**
 * Supabase Vector Database Operations
 * Operaciones de base de datos vectorial con pgvector
 */

import { createClient } from '@supabase/supabase-js';
import { Database } from '@/types/supabase';

// Cliente Supabase para operaciones vectoriales
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

export const supabaseVector = createClient<Database>(supabaseUrl, supabaseServiceKey);

/**
 * Tipos para operaciones vectoriales
 */
export interface VectorSearchResult {
  id: string;
  content: string;
  metadata: Record<string, any>;
  similarity: number;
}

export interface ProcedureSearchResult extends VectorSearchResult {
  procedure_id: string;
}

export interface OPASearchResult extends VectorSearchResult {
  opa_id: string;
}

export interface KnowledgeBaseSearchResult extends VectorSearchResult {
  title: string;
  source_type: string;
  source_id?: string;
}

export interface AllContentSearchResult extends VectorSearchResult {
  content_type: string;
  source_id: string;
}

export interface EmbeddingRecord {
  id?: string;
  content: string;
  embedding: number[];
  metadata: Record<string, any>;
  created_at?: string;
  updated_at?: string;
}

/**
 * Configuración de búsqueda vectorial
 */
export const VECTOR_SEARCH_CONFIG = {
  DEFAULT_MATCH_THRESHOLD: 0.78,
  DEFAULT_MATCH_COUNT: 10,
  MAX_MATCH_COUNT: 50,
} as const;

/**
 * Función para buscar documentos similares usando embeddings
 */
export async function searchSimilarDocuments(
  queryEmbedding: number[],
  options: {
    matchThreshold?: number;
    matchCount?: number;
    table?: string;
    filter?: Record<string, any>;
  } = {}
): Promise<VectorSearchResult[]> {
  const {
    matchThreshold = VECTOR_SEARCH_CONFIG.DEFAULT_MATCH_THRESHOLD,
    matchCount = VECTOR_SEARCH_CONFIG.DEFAULT_MATCH_COUNT,
    table = 'knowledge_base',
    filter = {},
  } = options;

  try {
    // Construir la consulta RPC para búsqueda vectorial
    let query = supabaseVector.rpc('match_documents', {
      query_embedding: queryEmbedding,
      match_threshold: matchThreshold,
      match_count: Math.min(matchCount, VECTOR_SEARCH_CONFIG.MAX_MATCH_COUNT),
    });

    // Aplicar filtros adicionales si se proporcionan
    if (Object.keys(filter).length > 0) {
      Object.entries(filter).forEach(([key, value]) => {
        query = query.eq(key, value);
      });
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error searching similar documents:', error);
      throw new Error(`Vector search failed: ${error.message}`);
    }

    return data || [];
  } catch (error) {
    console.error('Error in searchSimilarDocuments:', error);
    throw error;
  }
}

/**
 * Función para insertar o actualizar embeddings en la base de datos
 */
export async function upsertEmbedding(
  record: EmbeddingRecord,
  table: string = 'knowledge_base'
): Promise<void> {
  try {
    const { error } = await supabaseVector
      .from(table)
      .upsert({
        id: record.id,
        content: record.content,
        embedding: record.embedding,
        metadata: record.metadata,
        updated_at: new Date().toISOString(),
      });

    if (error) {
      console.error('Error upserting embedding:', error);
      throw new Error(`Failed to upsert embedding: ${error.message}`);
    }
  } catch (error) {
    console.error('Error in upsertEmbedding:', error);
    throw error;
  }
}

/**
 * Función para insertar múltiples embeddings
 */
export async function upsertEmbeddings(
  records: EmbeddingRecord[],
  table: string = 'knowledge_base'
): Promise<void> {
  try {
    const recordsWithTimestamp = records.map(record => ({
      ...record,
      updated_at: new Date().toISOString(),
    }));

    const { error } = await supabaseVector
      .from(table)
      .upsert(recordsWithTimestamp);

    if (error) {
      console.error('Error upserting embeddings:', error);
      throw new Error(`Failed to upsert embeddings: ${error.message}`);
    }
  } catch (error) {
    console.error('Error in upsertEmbeddings:', error);
    throw error;
  }
}

/**
 * Función para buscar trámites similares
 */
export async function searchSimilarTramites(
  queryEmbedding: number[],
  options: {
    matchThreshold?: number;
    matchCount?: number;
    categoria?: string;
    dependencia?: string;
  } = {}
): Promise<VectorSearchResult[]> {
  const filter: Record<string, any> = {};
  
  if (options.categoria) {
    filter['metadata->categoria'] = options.categoria;
  }
  
  if (options.dependencia) {
    filter['metadata->dependencia'] = options.dependencia;
  }

  return searchSimilarDocuments(queryEmbedding, {
    ...options,
    table: 'tramites_embeddings',
    filter,
  });
}

/**
 * Función para buscar OPAs similares
 */
export async function searchSimilarOPAs(
  queryEmbedding: number[],
  options: {
    matchThreshold?: number;
    matchCount?: number;
    categoria?: string;
    dependencia?: string;
  } = {}
): Promise<VectorSearchResult[]> {
  const filter: Record<string, any> = {};
  
  if (options.categoria) {
    filter['metadata->categoria'] = options.categoria;
  }
  
  if (options.dependencia) {
    filter['metadata->dependencia'] = options.dependencia;
  }

  return searchSimilarDocuments(queryEmbedding, {
    ...options,
    table: 'opas_embeddings',
    filter,
  });
}

/**
 * Función para obtener estadísticas de la base de datos vectorial
 */
export async function getVectorDatabaseStats(table: string = 'knowledge_base') {
  try {
    const { count, error } = await supabaseVector
      .from(table)
      .select('*', { count: 'exact', head: true });

    if (error) {
      console.error('Error getting vector database stats:', error);
      throw new Error(`Failed to get stats: ${error.message}`);
    }

    return {
      totalDocuments: count || 0,
      table,
    };
  } catch (error) {
    console.error('Error in getVectorDatabaseStats:', error);
    throw error;
  }
}

/**
 * Función para eliminar embeddings
 */
export async function deleteEmbedding(
  id: string,
  table: string = 'knowledge_base'
): Promise<void> {
  try {
    const { error } = await supabaseVector
      .from(table)
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting embedding:', error);
      throw new Error(`Failed to delete embedding: ${error.message}`);
    }
  } catch (error) {
    console.error('Error in deleteEmbedding:', error);
    throw error;
  }
}

/**
 * Función para buscar procedures usando la función RPC específica
 */
export async function searchSimilarProcedures(
  queryEmbedding: number[],
  options: {
    matchThreshold?: number;
    matchCount?: number;
  } = {}
): Promise<ProcedureSearchResult[]> {
  const {
    matchThreshold = VECTOR_SEARCH_CONFIG.DEFAULT_MATCH_THRESHOLD,
    matchCount = VECTOR_SEARCH_CONFIG.DEFAULT_MATCH_COUNT
  } = options;

  try {
    const { data, error } = await supabaseVector.rpc('match_procedures', {
      query_embedding: queryEmbedding,
      match_threshold: matchThreshold,
      match_count: matchCount
    });

    if (error) {
      console.error('Error searching similar procedures:', error);
      throw new Error(`Procedure search failed: ${error.message}`);
    }

    return data || [];
  } catch (error) {
    console.error('Error in searchSimilarProcedures:', error);
    throw error;
  }
}

/**
 * Función para buscar OPAs usando la función RPC específica
 */
export async function searchSimilarOPAsRPC(
  queryEmbedding: number[],
  options: {
    matchThreshold?: number;
    matchCount?: number;
  } = {}
): Promise<OPASearchResult[]> {
  const {
    matchThreshold = VECTOR_SEARCH_CONFIG.DEFAULT_MATCH_THRESHOLD,
    matchCount = VECTOR_SEARCH_CONFIG.DEFAULT_MATCH_COUNT
  } = options;

  try {
    const { data, error } = await supabaseVector.rpc('match_opas', {
      query_embedding: queryEmbedding,
      match_threshold: matchThreshold,
      match_count: matchCount
    });

    if (error) {
      console.error('Error searching similar OPAs:', error);
      throw new Error(`OPA search failed: ${error.message}`);
    }

    return data || [];
  } catch (error) {
    console.error('Error in searchSimilarOPAsRPC:', error);
    throw error;
  }
}

/**
 * Función para buscar en knowledge base usando la función RPC específica
 */
export async function searchKnowledgeBase(
  queryEmbedding: number[],
  options: {
    matchThreshold?: number;
    matchCount?: number;
  } = {}
): Promise<KnowledgeBaseSearchResult[]> {
  const {
    matchThreshold = VECTOR_SEARCH_CONFIG.DEFAULT_MATCH_THRESHOLD,
    matchCount = VECTOR_SEARCH_CONFIG.DEFAULT_MATCH_COUNT
  } = options;

  try {
    const { data, error } = await supabaseVector.rpc('match_knowledge_base', {
      query_embedding: queryEmbedding,
      match_threshold: matchThreshold,
      match_count: matchCount
    });

    if (error) {
      console.error('Error searching knowledge base:', error);
      throw new Error(`Knowledge base search failed: ${error.message}`);
    }

    return data || [];
  } catch (error) {
    console.error('Error in searchKnowledgeBase:', error);
    throw error;
  }
}

/**
 * Función para buscar en todo el contenido usando la función RPC combinada
 */
export async function searchAllContent(
  queryEmbedding: number[],
  options: {
    matchThreshold?: number;
    matchCount?: number;
  } = {}
): Promise<AllContentSearchResult[]> {
  const {
    matchThreshold = VECTOR_SEARCH_CONFIG.DEFAULT_MATCH_THRESHOLD,
    matchCount = VECTOR_SEARCH_CONFIG.DEFAULT_MATCH_COUNT
  } = options;

  try {
    const { data, error } = await supabaseVector.rpc('match_all_content', {
      query_embedding: queryEmbedding,
      match_threshold: matchThreshold,
      match_count: matchCount
    });

    if (error) {
      console.error('Error searching all content:', error);
      throw new Error(`All content search failed: ${error.message}`);
    }

    return data || [];
  } catch (error) {
    console.error('Error in searchAllContent:', error);
    throw error;
  }
}
