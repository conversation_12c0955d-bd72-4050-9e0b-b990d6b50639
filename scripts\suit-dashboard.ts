/**
 * SUIT Dashboard - Sistema de Monitoreo y Reportes
 * Dashboard para visualizar el progreso y estadísticas del scraping SUIT
 */

import { createClient } from '@supabase/supabase-js'
import * as dotenv from 'dotenv'
import * as path from 'path'
import * as fs from 'fs'

// Cargar variables de entorno
dotenv.config({ path: path.join(__dirname, '..', '.env.local') })

const supabaseUrl = process.env['NEXT_PUBLIC_SUPABASE_URL']!
const supabaseKey = process.env['SUPABASE_SERVICE_ROLE_KEY']!
const supabase = createClient(supabaseUrl, supabaseKey)

interface DashboardStats {
  totalProcedures: number
  proceduresWithSuitLinks: number
  scrapedProcedures: number
  scrapedSuccessfully: number
  scrapedFailed: number
  pendingScraping: number
  successPercentage: number
}

interface QualityMetrics {
  proceduresWithTitles: number
  proceduresWithDescriptions: number
  proceduresWithCosts: number
  proceduresWithEntities: number
  proceduresWithTimes: number
  averageContentLength: number
}

interface RecentActivity {
  fichaId: string
  procedureName: string
  titulo: string
  status: string
  scrapedAt: string
  processingTime?: number
}

class SuitDashboard {
  private logFile: string

  constructor() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    this.logFile = path.join(__dirname, '..', 'logs', `suit-dashboard-${timestamp}.log`)
  }

  private log(message: string): void {
    const timestamp = new Date().toISOString()
    const logEntry = `[${timestamp}] ${message}`
    console.log(logEntry)
    fs.appendFileSync(this.logFile, `${logEntry}\n`)
  }

  async getStats(): Promise<DashboardStats> {
    const { data, error } = await supabase.rpc('get_suit_scraping_stats')
    
    if (error) {
      throw new Error(`Error obteniendo estadísticas: ${error.message}`)
    }

    return data[0] as DashboardStats
  }

  async getQualityMetrics(): Promise<QualityMetrics> {
    const { data, error } = await supabase
      .from('suit_scraped_data')
      .select(`
        titulo,
        descripcion_detallada,
        costo_detallado,
        entidad_responsable,
        tiempo_respuesta,
        raw_text
      `)
      .eq('scraping_status', 'success')

    if (error) {
      throw new Error(`Error obteniendo métricas de calidad: ${error.message}`)
    }

    const metrics: QualityMetrics = {
      proceduresWithTitles: 0,
      proceduresWithDescriptions: 0,
      proceduresWithCosts: 0,
      proceduresWithEntities: 0,
      proceduresWithTimes: 0,
      averageContentLength: 0
    }

    let totalContentLength = 0

    data.forEach(item => {
      if (item.titulo && item.titulo.length > 5) {
        metrics.proceduresWithTitles++
      }
      if (item.descripcion_detallada && item.descripcion_detallada.length > 20) {
        metrics.proceduresWithDescriptions++
      }
      if (item.costo_detallado && item.costo_detallado.length > 3) {
        metrics.proceduresWithCosts++
      }
      if (item.entidad_responsable && item.entidad_responsable.length > 3) {
        metrics.proceduresWithEntities++
      }
      if (item.tiempo_respuesta && item.tiempo_respuesta.length > 3) {
        metrics.proceduresWithTimes++
      }
      if (item.raw_text) {
        totalContentLength += item.raw_text.length
      }
    })

    metrics.averageContentLength = data.length > 0 ? Math.round(totalContentLength / data.length) : 0

    return metrics
  }

  async getRecentActivity(limit: number = 10): Promise<RecentActivity[]> {
    const { data, error } = await supabase
      .from('suit_scraped_data')
      .select(`
        ficha_id,
        titulo,
        scraping_status,
        scraped_at,
        procedures!inner(name)
      `)
      .order('scraped_at', { ascending: false })
      .limit(limit)

    if (error) {
      throw new Error(`Error obteniendo actividad reciente: ${error.message}`)
    }

    return data.map(item => ({
      fichaId: item.ficha_id,
      procedureName: item.procedures.name,
      titulo: item.titulo || 'Sin título',
      status: item.scraping_status,
      scrapedAt: item.scraped_at
    }))
  }

  async generateReport(): Promise<string> {
    try {
      const stats = await this.getStats()
      const quality = await this.getQualityMetrics()
      const activity = await this.getRecentActivity(5)

      const report = `
# 📊 REPORTE SUIT SCRAPING DASHBOARD
**Generado:** ${new Date().toLocaleString()}

## 🎯 ESTADÍSTICAS GENERALES
- **Total de procedimientos:** ${stats.totalProcedures}
- **Con enlaces SUIT:** ${stats.proceduresWithSuitLinks}
- **Procesados:** ${stats.scrapedProcedures}
- **✅ Exitosos:** ${stats.scrapedSuccessfully}
- **❌ Fallidos:** ${stats.scrapedFailed}
- **⏳ Pendientes:** ${stats.pendingScraping}
- **📈 Tasa de éxito:** ${stats.successPercentage}%

## 🏆 OBJETIVO ALCANZADO
${stats.successPercentage >= 80 ? '🎉 **¡OBJETIVO DEL 80% SUPERADO!**' : '⚠️ Objetivo del 80% aún no alcanzado'}
- Progreso hacia objetivo: ${(stats.successPercentage / 80 * 100).toFixed(1)}%

## 📋 MÉTRICAS DE CALIDAD DE DATOS
- **Procedimientos con títulos:** ${quality.proceduresWithTitles} (${(quality.proceduresWithTitles / stats.scrapedSuccessfully * 100).toFixed(1)}%)
- **Con descripciones:** ${quality.proceduresWithDescriptions} (${(quality.proceduresWithDescriptions / stats.scrapedSuccessfully * 100).toFixed(1)}%)
- **Con información de costos:** ${quality.proceduresWithCosts} (${(quality.proceduresWithCosts / stats.scrapedSuccessfully * 100).toFixed(1)}%)
- **Con entidad responsable:** ${quality.proceduresWithEntities} (${(quality.proceduresWithEntities / stats.scrapedSuccessfully * 100).toFixed(1)}%)
- **Con tiempo de respuesta:** ${quality.proceduresWithTimes} (${(quality.proceduresWithTimes / stats.scrapedSuccessfully * 100).toFixed(1)}%)
- **Longitud promedio de contenido:** ${quality.averageContentLength} caracteres

## 🕒 ACTIVIDAD RECIENTE
${activity.map((item, index) => `
${index + 1}. **${item.titulo}**
   - Estado: ${item.status === 'success' ? '✅ Exitoso' : '❌ Fallido'}
   - Fecha: ${new Date(item.scrapedAt).toLocaleString()}
`).join('')}

## 💡 RECOMENDACIONES
${this.generateRecommendations(stats, quality)}

---
*Reporte generado automáticamente por SUIT Dashboard*
`

      return report
    } catch (error) {
      throw new Error(`Error generando reporte: ${error}`)
    }
  }

  private generateRecommendations(stats: DashboardStats, quality: QualityMetrics): string {
    const recommendations = []

    if (stats.successPercentage >= 80) {
      recommendations.push('✅ Objetivo del 80% alcanzado - Proceder con la validación final del sistema')
      recommendations.push('🔄 Implementar sistema de monitoreo continuo para mantener la calidad')
    } else {
      recommendations.push(`⚠️ Necesario procesar ${Math.ceil((80 * stats.proceduresWithSuitLinks / 100) - stats.scrapedSuccessfully)} procedimientos más para alcanzar el 80%`)
    }

    if (stats.pendingScraping > 0) {
      recommendations.push(`📋 Continuar scraping de ${stats.pendingScraping} procedimientos pendientes`)
    }

    if (stats.scrapedFailed > 0) {
      recommendations.push(`🔧 Revisar y reintentar ${stats.scrapedFailed} procedimientos fallidos`)
    }

    const qualityScore = (
      (quality.proceduresWithTitles / stats.scrapedSuccessfully) +
      (quality.proceduresWithDescriptions / stats.scrapedSuccessfully) +
      (quality.proceduresWithCosts / stats.scrapedSuccessfully) +
      (quality.proceduresWithEntities / stats.scrapedSuccessfully) +
      (quality.proceduresWithTimes / stats.scrapedSuccessfully)
    ) / 5 * 100

    if (qualityScore < 90) {
      recommendations.push(`📊 Calidad de datos: ${qualityScore.toFixed(1)}% - Considerar mejoras en extracción`)
    } else {
      recommendations.push(`🌟 Excelente calidad de datos: ${qualityScore.toFixed(1)}%`)
    }

    return recommendations.map(rec => `- ${rec}`).join('\n')
  }

  async displayDashboard(): Promise<void> {
    console.clear()
    this.log('🚀 Iniciando SUIT Dashboard')

    try {
      const stats = await this.getStats()
      const quality = await this.getQualityMetrics()
      const activity = await this.getRecentActivity(5)

      console.log('📊 SUIT SCRAPING DASHBOARD')
      console.log('=' .repeat(60))
      
      // Estadísticas principales
      console.log('\n🎯 ESTADÍSTICAS PRINCIPALES')
      console.log('-' .repeat(40))
      console.log(`📋 Total de procedimientos: ${stats.totalProcedures}`)
      console.log(`🔗 Con enlaces SUIT: ${stats.proceduresWithSuitLinks}`)
      console.log(`🤖 Procesados: ${stats.scrapedProcedures}`)
      console.log(`✅ Exitosos: ${stats.scrapedSuccessfully}`)
      console.log(`❌ Fallidos: ${stats.scrapedFailed}`)
      console.log(`⏳ Pendientes: ${stats.pendingScraping}`)
      console.log(`📈 Tasa de éxito: ${stats.successPercentage}%`)

      // Estado del objetivo
      console.log('\n🏆 ESTADO DEL OBJETIVO')
      console.log('-' .repeat(40))
      if (stats.successPercentage >= 80) {
        console.log('🎉 ¡OBJETIVO DEL 80% SUPERADO!')
        console.log(`✨ Tasa actual: ${stats.successPercentage}%`)
      } else {
        console.log('⚠️ Objetivo del 80% aún no alcanzado')
        console.log(`📊 Progreso: ${(stats.successPercentage / 80 * 100).toFixed(1)}%`)
      }

      // Métricas de calidad
      console.log('\n📋 CALIDAD DE DATOS')
      console.log('-' .repeat(40))
      console.log(`📝 Con títulos: ${quality.proceduresWithTitles}/${stats.scrapedSuccessfully} (${(quality.proceduresWithTitles / stats.scrapedSuccessfully * 100).toFixed(1)}%)`)
      console.log(`📄 Con descripciones: ${quality.proceduresWithDescriptions}/${stats.scrapedSuccessfully} (${(quality.proceduresWithDescriptions / stats.scrapedSuccessfully * 100).toFixed(1)}%)`)
      console.log(`💰 Con costos: ${quality.proceduresWithCosts}/${stats.scrapedSuccessfully} (${(quality.proceduresWithCosts / stats.scrapedSuccessfully * 100).toFixed(1)}%)`)
      console.log(`🏢 Con entidades: ${quality.proceduresWithEntities}/${stats.scrapedSuccessfully} (${(quality.proceduresWithEntities / stats.scrapedSuccessfully * 100).toFixed(1)}%)`)
      console.log(`⏰ Con tiempos: ${quality.proceduresWithTimes}/${stats.scrapedSuccessfully} (${(quality.proceduresWithTimes / stats.scrapedSuccessfully * 100).toFixed(1)}%)`)

      // Actividad reciente
      console.log('\n🕒 ACTIVIDAD RECIENTE')
      console.log('-' .repeat(40))
      activity.forEach((item, index) => {
        const statusIcon = item.status === 'success' ? '✅' : '❌'
        console.log(`${index + 1}. ${statusIcon} ${item.titulo}`)
        console.log(`   📅 ${new Date(item.scrapedAt).toLocaleString()}`)
      })

      this.log('✅ Dashboard actualizado exitosamente')

    } catch (error) {
      this.log(`❌ Error en dashboard: ${error}`)
      console.error('💥 Error en dashboard:', error)
    }
  }

  async saveReport(): Promise<string> {
    try {
      const report = await this.generateReport()
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
      const reportFile = path.join(__dirname, '..', 'logs', `suit-report-${timestamp}.md`)
      
      fs.writeFileSync(reportFile, report)
      this.log(`📄 Reporte guardado en: ${reportFile}`)
      
      return reportFile
    } catch (error) {
      this.log(`❌ Error guardando reporte: ${error}`)
      throw error
    }
  }
}

// Ejecutar si es el módulo principal
if (require.main === module) {
  const dashboard = new SuitDashboard()
  
  const args = process.argv.slice(2)
  const generateReport = args.includes('--report') || args.includes('-r')

  if (generateReport) {
    dashboard.saveReport()
      .then((reportFile) => {
        console.log(`📄 Reporte generado: ${reportFile}`)
        process.exit(0)
      })
      .catch((error) => {
        console.error('💥 Error generando reporte:', error)
        process.exit(1)
      })
  } else {
    dashboard.displayDashboard()
      .then(() => {
        console.log('\n✅ Dashboard completado')
        process.exit(0)
      })
      .catch((error) => {
        console.error('💥 Error en dashboard:', error)
        process.exit(1)
      })
  }
}

export { SuitDashboard }
