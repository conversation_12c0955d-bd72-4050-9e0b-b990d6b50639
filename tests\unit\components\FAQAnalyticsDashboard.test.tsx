import faqAnalytics from '@/lib/services/faqAnalytics'

// Mock del servicio de analytics
jest.mock('@/lib/services/faqAnalytics')
const mockFaqAnalytics = faqAnalytics as jest.Mocked<typeof faqAnalytics>

// Mock de URL.createObjectURL y URL.revokeObjectURL
global.URL.createObjectURL = jest.fn(() => 'mock-url')
global.URL.revokeObjectURL = jest.fn()

// Mock de document.createElement y métodos relacionados
const mockAnchorElement = {
  href: '',
  download: '',
  click: jest.fn(),
}

Object.defineProperty(document, 'createElement', {
  value: jest.fn(() => mockAnchorElement),
})

Object.defineProperty(document.body, 'appendChild', {
  value: jest.fn(),
})

Object.defineProperty(document.body, 'removeChild', {
  value: jest.fn(),
})

// Datos mock para métricas
const mockMetrics = {
  totalSearches: 1250,
  totalViews: 3400,
  averageResponseTime: 150,
  topSearchTerms: [
    { term: 'impuesto predial', count: 45 },
    { term: 'certificado residencia', count: 38 },
    { term: 'licencia construcción', count: 32 }
  ],
  topFAQs: [
    { faqId: 'impuesto-predial-que-es', question: '¿Qué es el impuesto predial?', views: 156 },
    { faqId: 'certificado-residencia-como', question: '¿Cómo obtengo un certificado de residencia?', views: 134 }
  ],
  topCategories: [
    { category: 'impuestos', count: 89 },
    { category: 'certificados', count: 67 },
    { category: 'licencias', count: 45 }
  ],
  searchSuccessRate: 0.87,
  contextUsage: [
    { context: 'home', count: 45 },
    { context: 'procedures', count: 32 },
    { context: 'management', count: 23 }
  ],
  timeRange: {
    start: new Date('2024-01-01'),
    end: new Date('2024-01-31')
  }
}

describe('FAQAnalyticsDashboard', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    mockFaqAnalytics.getMetrics.mockResolvedValue(mockMetrics)
  })

  describe('Integración con servicio de analytics', () => {
    it('debería llamar al servicio de analytics al cargar', () => {
      expect(mockFaqAnalytics.getMetrics).toBeDefined()
      expect(typeof mockFaqAnalytics.getMetrics).toBe('function')
    })

    it('debería manejar métricas correctamente', async () => {
      const metrics = await mockFaqAnalytics.getMetrics()

      expect(metrics).toHaveProperty('totalSearches', 1250)
      expect(metrics).toHaveProperty('totalViews', 3400)
      expect(metrics).toHaveProperty('averageResponseTime', 150)
      expect(metrics).toHaveProperty('searchSuccessRate', 0.87)
      expect(metrics.topSearchTerms).toHaveLength(3)
      expect(metrics.topFAQs).toHaveLength(2)
      expect(metrics.topCategories).toHaveLength(3)
      expect(metrics.contextUsage).toHaveLength(3)
    })

    it('debería manejar errores del servicio', async () => {
      mockFaqAnalytics.getMetrics.mockRejectedValue(new Error('Network error'))

      try {
        await mockFaqAnalytics.getMetrics()
      } catch (error) {
        expect(error).toBeInstanceOf(Error)
        expect((error as Error).message).toBe('Network error')
      }
    })

    it('debería aceptar parámetros de rango temporal', async () => {
      const startDate = new Date('2024-01-01')
      const endDate = new Date('2024-01-31')

      await mockFaqAnalytics.getMetrics({ start: startDate, end: endDate })

      expect(mockFaqAnalytics.getMetrics).toHaveBeenCalledWith({
        start: startDate,
        end: endDate
      })
    })
  })

  describe('Funcionalidad de rangos temporales', () => {
    it('debería calcular fechas correctamente para 7 días', () => {
      const endDate = new Date()
      const startDate = new Date()
      startDate.setDate(endDate.getDate() - 7)

      expect(startDate.getTime()).toBeLessThan(endDate.getTime())
      expect(endDate.getTime() - startDate.getTime()).toBe(7 * 24 * 60 * 60 * 1000)
    })

    it('debería calcular fechas correctamente para 30 días', () => {
      const endDate = new Date()
      const startDate = new Date()
      startDate.setDate(endDate.getDate() - 30)

      expect(startDate.getTime()).toBeLessThan(endDate.getTime())
      expect(endDate.getTime() - startDate.getTime()).toBe(30 * 24 * 60 * 60 * 1000)
    })

    it('debería calcular fechas correctamente para 90 días', () => {
      const endDate = new Date()
      const startDate = new Date()
      startDate.setDate(endDate.getDate() - 90)

      expect(startDate.getTime()).toBeLessThan(endDate.getTime())
      expect(endDate.getTime() - startDate.getTime()).toBe(90 * 24 * 60 * 60 * 1000)
    })
  })

  describe('Procesamiento de datos', () => {
    it('debería procesar términos de búsqueda correctamente', () => {
      const { topSearchTerms } = mockMetrics

      expect(topSearchTerms).toHaveLength(3)
      expect(topSearchTerms[0]).toEqual({ term: 'impuesto predial', count: 45 })
      expect(topSearchTerms[1]).toEqual({ term: 'certificado residencia', count: 38 })
      expect(topSearchTerms[2]).toEqual({ term: 'licencia construcción', count: 32 })
    })

    it('debería procesar FAQs más consultadas correctamente', () => {
      const { topFAQs } = mockMetrics

      expect(topFAQs).toHaveLength(2)
      expect(topFAQs[0]).toEqual({
        faqId: 'impuesto-predial-que-es',
        question: '¿Qué es el impuesto predial?',
        views: 156
      })
      expect(topFAQs[1]).toEqual({
        faqId: 'certificado-residencia-como',
        question: '¿Cómo obtengo un certificado de residencia?',
        views: 134
      })
    })

    it('debería procesar categorías correctamente', () => {
      const { topCategories } = mockMetrics

      expect(topCategories).toHaveLength(3)
      expect(topCategories[0]).toEqual({ category: 'impuestos', count: 89 })
      expect(topCategories[1]).toEqual({ category: 'certificados', count: 67 })
      expect(topCategories[2]).toEqual({ category: 'licencias', count: 45 })
    })

    it('debería procesar uso por contexto correctamente', () => {
      const { contextUsage } = mockMetrics

      expect(contextUsage).toHaveLength(3)
      expect(contextUsage[0]).toEqual({ context: 'home', count: 45 })
      expect(contextUsage[1]).toEqual({ context: 'procedures', count: 32 })
      expect(contextUsage[2]).toEqual({ context: 'management', count: 23 })
    })
  })

  describe('Funcionalidad de exportación', () => {
    it('debería crear estructura de datos correcta para exportación', () => {
      const exportData = {
        metrics: mockMetrics,
        exportDate: new Date().toISOString(),
        timeRange: '30d'
      }

      expect(exportData).toHaveProperty('metrics')
      expect(exportData).toHaveProperty('exportDate')
      expect(exportData).toHaveProperty('timeRange')
      expect(exportData.metrics).toEqual(mockMetrics)
      expect(exportData.timeRange).toBe('30d')
    })

    it('debería generar nombre de archivo correcto', () => {
      const today = new Date().toISOString().split('T')[0]
      const expectedFilename = `faq-analytics-30d-${today}.json`

      expect(expectedFilename).toMatch(/^faq-analytics-30d-\d{4}-\d{2}-\d{2}\.json$/)
    })

    it('debería serializar datos correctamente', () => {
      const data = {
        metrics: mockMetrics,
        exportDate: new Date().toISOString(),
        timeRange: '30d'
      }

      const serialized = JSON.stringify(data, null, 2)
      const parsed = JSON.parse(serialized)

      expect(parsed.metrics.totalSearches).toBe(1250)
      expect(parsed.metrics.totalViews).toBe(3400)
      expect(parsed.timeRange).toBe('30d')
    })
  })

  describe('Formateo de datos', () => {
    it('debería formatear números correctamente', () => {
      expect((1250).toLocaleString()).toBe('1,250')
      expect((3400).toLocaleString()).toBe('3,400')
      expect((150).toString() + 'ms').toBe('150ms')
      expect((0.87 * 100).toFixed(1) + '%').toBe('87.0%')
    })

    it('debería formatear fechas correctamente', () => {
      const date = new Date('2024-01-15T10:30:00Z')
      const formatted = date.toLocaleString()

      expect(formatted).toContain('2024')
      expect(typeof formatted).toBe('string')
    })

    it('debería capitalizar contextos correctamente', () => {
      const contexts = ['home', 'procedures', 'management']
      const capitalized = contexts.map(ctx => ctx.charAt(0).toUpperCase() + ctx.slice(1))

      expect(capitalized).toEqual(['Home', 'Procedures', 'Management'])
    })
  })

  describe('Validación de datos', () => {
    it('debería validar estructura de métricas', () => {
      const requiredFields = [
        'totalSearches',
        'totalViews',
        'averageResponseTime',
        'topSearchTerms',
        'topFAQs',
        'topCategories',
        'searchSuccessRate',
        'contextUsage',
        'timeRange'
      ]

      requiredFields.forEach(field => {
        expect(mockMetrics).toHaveProperty(field)
      })
    })

    it('debería validar tipos de datos', () => {
      expect(typeof mockMetrics.totalSearches).toBe('number')
      expect(typeof mockMetrics.totalViews).toBe('number')
      expect(typeof mockMetrics.averageResponseTime).toBe('number')
      expect(typeof mockMetrics.searchSuccessRate).toBe('number')
      expect(Array.isArray(mockMetrics.topSearchTerms)).toBe(true)
      expect(Array.isArray(mockMetrics.topFAQs)).toBe(true)
      expect(Array.isArray(mockMetrics.topCategories)).toBe(true)
      expect(Array.isArray(mockMetrics.contextUsage)).toBe(true)
    })
  })
})
