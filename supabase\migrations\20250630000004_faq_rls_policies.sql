-- =============================================
-- RLS POLICIES FOR FAQ SYSTEM
-- =============================================

-- Enable RLS on FAQ tables
ALTER TABLE faq_themes ENABLE ROW LEVEL SECURITY;
ALTER TABLE municipal_faqs ENABLE ROW LEVEL SECURITY;

-- =============================================
-- FAQ THEMES POLICIES
-- =============================================

-- Public read access to active FAQ themes
CREATE POLICY "Public can view active FAQ themes" ON faq_themes
    FOR SELECT USING (is_active = true);

-- Authenticated users can view all FAQ themes
CREATE POLICY "Authenticated users can view all FAQ themes" ON faq_themes
    FOR SELECT TO authenticated USING (true);

-- Only admins can modify FAQ themes
CREATE POLICY "Admins can manage FAQ themes" ON faq_themes
    FOR ALL TO authenticated 
    USING (
        EXISTS (
            SELECT 1 FROM profiles p
            JOIN roles r ON p.role_id = r.id
            WHERE p.id = auth.uid()
            AND r.name IN ('admin', 'super_admin')
        )
    );

-- =============================================
-- MUNICIPAL FAQS POLICIES
-- =============================================

-- Public read access to active municipal FAQs
CREATE POLICY "Public can view active municipal FAQs" ON municipal_faqs
    FOR SELECT USING (is_active = true);

-- Authenticated users can view all municipal FAQs
CREATE POLICY "Authenticated users can view all municipal FAQs" ON municipal_faqs
    FOR SELECT TO authenticated USING (true);

-- Only admins can modify municipal FAQs
CREATE POLICY "Admins can manage municipal FAQs" ON municipal_faqs
    FOR ALL TO authenticated 
    USING (
        EXISTS (
            SELECT 1 FROM profiles p
            JOIN roles r ON p.role_id = r.id
            WHERE p.id = auth.uid()
            AND r.name IN ('admin', 'super_admin')
        )
    );

-- Allow authenticated users to update view counts and votes
CREATE POLICY "Users can update FAQ engagement metrics" ON municipal_faqs
    FOR UPDATE TO authenticated 
    USING (true)
    WITH CHECK (
        -- Only allow updates to engagement fields
        OLD.question = NEW.question AND
        OLD.answer = NEW.answer AND
        OLD.theme_id = NEW.theme_id AND
        OLD.keywords = NEW.keywords AND
        OLD.related_procedures = NEW.related_procedures AND
        OLD.display_order = NEW.display_order AND
        OLD.is_active = NEW.is_active
    );

-- =============================================
-- GRANT PERMISSIONS
-- =============================================

-- Grant usage on sequences
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;

-- Grant select permissions for public access
GRANT SELECT ON faq_themes TO anon, authenticated;
GRANT SELECT ON municipal_faqs TO anon, authenticated;

-- Grant update permissions for engagement metrics
GRANT UPDATE (view_count, helpful_votes, unhelpful_votes) ON municipal_faqs TO authenticated;
