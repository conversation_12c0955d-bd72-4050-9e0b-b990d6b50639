import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import { Button } from '@/components/ui/button'

describe('Button Component', () => {
  it('renders button with text', () => {
    render(<Button>Click me</Button>)
    
    const button = screen.getByRole('button', { name: 'Click me' })
    expect(button).toBeInTheDocument()
  })

  it('handles click events', () => {
    const handleClick = jest.fn()
    render(<Button onClick={handleClick}>Click me</Button>)
    
    const button = screen.getByRole('button', { name: 'Click me' })
    fireEvent.click(button)
    
    expect(handleClick).toHaveBeenCalledTimes(1)
  })

  it('applies variant classes correctly', () => {
    const { rerender } = render(<Button variant="default">Default</Button>)
    
    let button = screen.getByRole('button')
    expect(button).toHaveClass('bg-primary')
    
    rerender(<Button variant="destructive">Destructive</Button>)
    button = screen.getByRole('button')
    expect(button).toHaveClass('bg-red-500')
    
    rerender(<Button variant="outline">Outline</Button>)
    button = screen.getByRole('button')
    expect(button).toHaveClass('border-2')
    
    rerender(<Button variant="secondary">Secondary</Button>)
    button = screen.getByRole('button')
    expect(button).toHaveClass('bg-chia-green-100')
    
    rerender(<Button variant="ghost">Ghost</Button>)
    button = screen.getByRole('button')
    expect(button).toHaveClass('hover:bg-gray-100')
    
    rerender(<Button variant="link">Link</Button>)
    button = screen.getByRole('button')
    expect(button).toHaveClass('text-primary')
  })

  it('applies size classes correctly', () => {
    const { rerender } = render(<Button size="default">Default</Button>)
    
    let button = screen.getByRole('button')
    expect(button).toHaveClass('h-11')
    
    rerender(<Button size="sm">Small</Button>)
    button = screen.getByRole('button')
    expect(button).toHaveClass('h-9')
    
    rerender(<Button size="lg">Large</Button>)
    button = screen.getByRole('button')
    expect(button).toHaveClass('h-12')
    
    rerender(<Button size="icon">Icon</Button>)
    button = screen.getByRole('button')
    expect(button).toHaveClass('h-11', 'w-11')
  })

  it('handles disabled state', () => {
    render(<Button disabled>Disabled</Button>)
    
    const button = screen.getByRole('button')
    expect(button).toBeDisabled()
    expect(button).toHaveClass('disabled:pointer-events-none', 'disabled:opacity-50')
  })

  it('supports custom className', () => {
    render(<Button className="custom-class">Custom</Button>)
    
    const button = screen.getByRole('button')
    expect(button).toHaveClass('custom-class')
  })

  it('forwards ref correctly', () => {
    const ref = React.createRef<HTMLButtonElement>()
    render(<Button ref={ref}>Ref test</Button>)
    
    expect(ref.current).toBeInstanceOf(HTMLButtonElement)
  })

  it('supports asChild prop with Slot', () => {
    // This would require @radix-ui/react-slot to be properly mocked
    // For now, we'll test the basic functionality
    render(<Button asChild={false}>Normal Button</Button>)
    
    const button = screen.getByRole('button')
    expect(button.tagName).toBe('BUTTON')
  })

  it('is accessible', () => {
    render(<Button aria-label="Accessible button">Button</Button>)
    
    const button = screen.getByRole('button', { name: 'Accessible button' })
    expect(button).toBeInTheDocument()
    expect(button).toHaveAttribute('aria-label', 'Accessible button')
  })

  it('supports keyboard navigation', () => {
    const handleClick = jest.fn()
    render(<Button onClick={handleClick}>Keyboard test</Button>)
    
    const button = screen.getByRole('button')
    
    // Focus the button
    button.focus()
    expect(button).toHaveFocus()
    
    // Press Enter
    fireEvent.keyDown(button, { key: 'Enter', code: 'Enter' })
    fireEvent.keyUp(button, { key: 'Enter', code: 'Enter' })
    
    // Press Space
    fireEvent.keyDown(button, { key: ' ', code: 'Space' })
    fireEvent.keyUp(button, { key: ' ', code: 'Space' })
    
    // Button should be focusable and respond to keyboard
    expect(button).toHaveFocus()
  })

  it('handles form submission', () => {
    const handleSubmit = jest.fn((e) => e.preventDefault())
    
    render(
      <form onSubmit={handleSubmit}>
        <Button type="submit">Submit</Button>
      </form>
    )
    
    const button = screen.getByRole('button', { name: 'Submit' })
    fireEvent.click(button)
    
    expect(handleSubmit).toHaveBeenCalled()
  })

  it('supports different button types', () => {
    const { rerender } = render(<Button type="button">Button</Button>)
    
    let button = screen.getByRole('button')
    expect(button).toHaveAttribute('type', 'button')
    
    rerender(<Button type="submit">Submit</Button>)
    button = screen.getByRole('button')
    expect(button).toHaveAttribute('type', 'submit')
    
    rerender(<Button type="reset">Reset</Button>)
    button = screen.getByRole('button')
    expect(button).toHaveAttribute('type', 'reset')
  })

  it('combines multiple props correctly', () => {
    const handleClick = jest.fn()
    
    render(
      <Button
        variant="secondary"
        size="lg"
        disabled={false}
        className="custom-class"
        onClick={handleClick}
        aria-label="Complex button"
      >
        Complex Button
      </Button>
    )
    
    const button = screen.getByRole('button', { name: 'Complex button' })
    expect(button).toBeInTheDocument()
    expect(button).toHaveClass('bg-chia-green-100', 'h-12', 'custom-class')
    expect(button).not.toBeDisabled()
    
    fireEvent.click(button)
    expect(handleClick).toHaveBeenCalled()
  })
})
