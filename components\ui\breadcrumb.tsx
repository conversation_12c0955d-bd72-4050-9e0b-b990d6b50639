"use client"

import * as React from "react"
import { ChevronRight, Home } from "lucide-react"
import Link from "next/link"
import { cn } from "@/lib/utils"

export interface BreadcrumbItem {
  label: string
  href?: string
  icon?: React.ComponentType<{ className?: string }>
  current?: boolean
}

interface BreadcrumbProps {
  items: BreadcrumbItem[]
  className?: string
  separator?: React.ComponentType<{ className?: string }>
  showHome?: boolean
  homeHref?: string
}

const Breadcrumb = React.forwardRef<
  HTMLElement,
  BreadcrumbProps
>(({ 
  items, 
  className, 
  separator: Separator = ChevronRight,
  showHome = true,
  homeHref = "/",
  ...props 
}, ref) => {
  const allItems = showHome 
    ? [{ label: "Inicio", href: homeHref, icon: Home }, ...items]
    : items

  return (
    <nav
      ref={ref}
      aria-label="Breadcrumb"
      className={cn("flex items-center space-x-2 text-sm text-gray-600 font-medium", className)}
      {...props}
    >
      <ol className="flex items-center space-x-1">
        {allItems.map((item, index) => {
          const isLast = index === allItems.length - 1
          const IconComponent = item.icon

          return (
            <li key={index} className="flex items-center">
              {index > 0 && (
                <Separator 
                  className="h-4 w-4 text-gray-400 mx-2" 
                  aria-hidden="true"
                />
              )}
              
              <div className="flex items-center space-x-1">
                {IconComponent && (
                  <IconComponent className="h-4 w-4" />
                )}
                
                {item.href && !isLast ? (
                  <Link
                    href={item.href}
                    className={cn(
                      "hover:text-chia-blue-600 transition-all duration-200 hover:underline hover:underline-offset-4",
                      "focus:outline-none focus:ring-2 focus:ring-chia-blue-500/20 focus:ring-offset-1 rounded-lg px-1 py-0.5",
                      item.current && "text-chia-blue-600 font-semibold"
                    )}
                    aria-current={item.current ? "page" : undefined}
                  >
                    {item.label}
                  </Link>
                ) : (
                  <span 
                    className={cn(
                      isLast
                        ? "text-gray-900 font-semibold"
                        : "text-gray-600 font-medium",
                      item.current && "text-chia-blue-600 font-semibold"
                    )}
                    aria-current={isLast ? "page" : undefined}
                  >
                    {item.label}
                  </span>
                )}
              </div>
            </li>
          )
        })}
      </ol>
    </nav>
  )
})

Breadcrumb.displayName = "Breadcrumb"

// Componente específico para navegación de dependencias
interface DependencyBreadcrumbProps {
  dependency?: {
    name: string
    id: string
  }
  procedure?: {
    name: string
    type: 'TRAMITE' | 'OPA'
  }
  searchQuery?: string
  className?: string
  onNavigate?: (level: 'home' | 'dependencies' | 'dependency') => void
}

export function DependencyBreadcrumb({
  dependency,
  procedure,
  searchQuery,
  className,
  onNavigate
}: DependencyBreadcrumbProps) {
  const items: BreadcrumbItem[] = []

  // Nivel de dependencias
  items.push({
    label: "Dependencias",
    href: onNavigate ? undefined : "#dependencias",
    current: !dependency && !procedure
  })

  // Si hay búsqueda activa
  if (searchQuery && !dependency && !procedure) {
    items.push({
      label: `Resultados: "${searchQuery}"`,
      current: true
    })
  }

  // Nivel de dependencia específica
  if (dependency) {
    items.push({
      label: dependency.name,
      href: onNavigate ? undefined : `#dependencia-${dependency.id}`,
      current: !procedure
    })
  }

  // Nivel de procedimiento específico
  if (procedure) {
    const typeLabel = procedure.type === 'TRAMITE' ? 'Trámite' : 'OPA'
    items.push({
      label: `${typeLabel}: ${procedure.name}`,
      current: true
    })
  }

  const handleItemClick = (index: number) => {
    if (!onNavigate) return

    if (index === 0) {
      onNavigate('home')
    } else if (index === 1 && !searchQuery) {
      onNavigate('dependencies')
    } else if (index === 1 && searchQuery) {
      onNavigate('dependencies')
    } else if (index === 2 && dependency) {
      onNavigate('dependency')
    }
  }

  return (
    <Breadcrumb
      items={items.map((item, index) => ({
        ...item,
        href: onNavigate ? undefined : item.href,
        onClick: onNavigate ? () => handleItemClick(index) : undefined
      }))}
      className={className}
      showHome={true}
      homeHref="/"
    />
  )
}

// Hook para gestionar el estado de navegación de breadcrumbs
export function useDependencyNavigation() {
  const [navigationState, setNavigationState] = React.useState<{
    level: 'home' | 'dependencies' | 'dependency' | 'procedure'
    dependency?: { name: string; id: string }
    procedure?: { name: string; type: 'TRAMITE' | 'OPA' }
    searchQuery?: string
  }>({
    level: 'dependencies'
  })

  const navigateToHome = React.useCallback(() => {
    setNavigationState({ level: 'home' })
  }, [])

  const navigateToDependencies = React.useCallback(() => {
    setNavigationState({ level: 'dependencies' })
  }, [])

  const navigateToDependency = React.useCallback((dependency: { name: string; id: string }) => {
    setNavigationState({ 
      level: 'dependency', 
      dependency 
    })
  }, [])

  const navigateToProcedure = React.useCallback((
    dependency: { name: string; id: string },
    procedure: { name: string; type: 'TRAMITE' | 'OPA' }
  ) => {
    setNavigationState({ 
      level: 'procedure', 
      dependency, 
      procedure 
    })
  }, [])

  const setSearchQuery = React.useCallback((query: string) => {
    setNavigationState(prev => ({ 
      ...prev, 
      searchQuery: query 
    }))
  }, [])

  return {
    navigationState,
    navigateToHome,
    navigateToDependencies,
    navigateToDependency,
    navigateToProcedure,
    setSearchQuery
  }
}

export { Breadcrumb }
