version: '3.8'

services:
  # Next.js Application
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: runner
    container_name: chia-tramites-app
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_TELEMETRY_DISABLED=1
      - HOSTNAME=0.0.0.0
      - PORT=3000
      # Supabase Configuration (to be set in production)
      - NEXT_PUBLIC_SUPABASE_URL=${NEXT_PUBLIC_SUPABASE_URL}
      - NEXT_PUBLIC_SUPABASE_ANON_KEY=${NEXT_PUBLIC_SUPABASE_ANON_KEY}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      # Application Configuration
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
      - NEXTAUTH_URL=${NEXTAUTH_URL}
    networks:
      - chia-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    labels:
      # Coolify labels for automatic deployment
      - "coolify.managed=true"
      - "coolify.name=chia-tramites"
      - "coolify.type=application"
      # Traefik labels for reverse proxy (if using Traefik)
      - "traefik.enable=true"
      - "traefik.http.routers.chia-tramites.rule=Host(`${DOMAIN_NAME}`)"
      - "traefik.http.routers.chia-tramites.tls=true"
      - "traefik.http.routers.chia-tramites.tls.certresolver=letsencrypt"
      - "traefik.http.services.chia-tramites.loadbalancer.server.port=3000"

networks:
  chia-network:
    driver: bridge

# Optional: Redis for caching (can be added later)
# redis:
#   image: redis:7-alpine
#   container_name: chia-tramites-redis
#   restart: unless-stopped
#   ports:
#     - "6379:6379"
#   volumes:
#     - redis_data:/data
#   networks:
#     - chia-network
#   command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}

# volumes:
#   redis_data:
