import { createServerClient, type CookieOptions } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { Database } from '@/lib/database.types'

export const createClient = () => {
  const cookieStore = cookies()

  return createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value
        },
        set(name: string, value: string, options: CookieOptions) {
          try {
            cookieStore.set({ name, value, ...options })
          } catch (error) {
            // The `set` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
        remove(name: string, options: CookieOptions) {
          try {
            cookieStore.set({ name, value: '', ...options })
          } catch (error) {
            // The `delete` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
      },
    }
  )
}

// Helper function to get the current user on the server
export const getCurrentUser = async () => {
  const supabase = createClient()
  const { data: { user }, error } = await supabase.auth.getUser()
  return { user, error }
}

// Helper function to get user profile with role information
export const getUserProfile = async (userId?: string) => {
  const supabase = createClient()
  
  let targetUserId = userId
  if (!targetUserId) {
    const { user } = await getCurrentUser()
    if (!user) return { data: null, error: new Error('No authenticated user') }
    targetUserId = user.id
  }

  const { data, error } = await supabase
    .from('profiles')
    .select(`
      *,
      role:roles(*),
      dependency:dependencies(*),
      subdependency:subdependencies(*)
    `)
    .eq('id', targetUserId)
    .single()

  return { data, error }
}

// Helper function to check if user has specific role
export const hasRole = async (requiredRole: string, userId?: string) => {
  const { data: profile, error } = await getUserProfile(userId)
  
  if (error || !profile) return false
  
  return profile.role?.name === requiredRole
}

// Helper function to check if user is admin of specific dependency
export const isDependencyAdmin = async (dependencyId: string, userId?: string) => {
  const { data: profile, error } = await getUserProfile(userId)
  
  if (error || !profile) return false
  
  return (
    profile.role?.name === 'admin' && 
    profile.dependency_id === dependencyId
  ) || profile.role?.name === 'super_admin'
}

// Helper function to check if user is super admin
export const isSuperAdmin = async (userId?: string) => {
  return await hasRole('super_admin', userId)
}

// Helper function to get procedures accessible to current user
export const getAccessibleProcedures = async () => {
  const supabase = createClient()
  const { data: profile } = await getUserProfile()
  
  if (!profile) {
    return { data: [], error: new Error('No user profile found') }
  }

  let query = supabase
    .from('procedures')
    .select(`
      *,
      dependency:dependencies(*),
      subdependency:subdependencies(*)
    `)
    .eq('is_active', true)

  // Filter based on user role and dependency
  if (profile.role?.name === 'admin' && profile.dependency_id) {
    query = query.eq('dependency_id', profile.dependency_id)
  }
  // Super admins can see all procedures
  // Citizens can see all active procedures

  const { data, error } = await query.order('name')
  return { data, error }
}

// Helper function to get OPAs accessible to current user
export const getAccessibleOPAs = async () => {
  const supabase = createClient()
  const { data: profile } = await getUserProfile()
  
  if (!profile) {
    return { data: [], error: new Error('No user profile found') }
  }

  let query = supabase
    .from('opas')
    .select(`
      *,
      dependency:dependencies(*),
      subdependency:subdependencies(*)
    `)
    .eq('is_active', true)

  // Filter based on user role and dependency
  if (profile.role?.name === 'admin' && profile.dependency_id) {
    query = query.eq('dependency_id', profile.dependency_id)
  }
  // Super admins can see all OPAs
  // Citizens can see all active OPAs

  const { data, error } = await query.order('name')
  return { data, error }
}

// Helper function to create audit log entry
export const createAuditLog = async (
  action: string,
  tableName?: string,
  recordId?: string,
  oldValues?: any,
  newValues?: any,
  userId?: string
) => {
  const supabase = createClient()
  
  let targetUserId = userId
  if (!targetUserId) {
    const { user } = await getCurrentUser()
    targetUserId = user?.id
  }

  const { data, error } = await supabase
    .from('audit_logs')
    .insert({
      user_id: targetUserId,
      action,
      table_name: tableName,
      record_id: recordId,
      old_values: oldValues,
      new_values: newValues,
    })

  return { data, error }
}

// Helper function to send notification
export const sendNotification = async (
  userId: string,
  title: string,
  message: string,
  type: string = 'info',
  category?: string,
  relatedId?: string
) => {
  const supabase = createClient()

  const { data, error } = await supabase
    .from('notifications')
    .insert({
      user_id: userId,
      title,
      message,
      type,
      category,
      related_id: relatedId,
      sent_at: new Date().toISOString(),
    })

  return { data, error }
}

// Helper function to get user's unread notifications
export const getUnreadNotifications = async (userId?: string) => {
  const supabase = createClient()
  
  let targetUserId = userId
  if (!targetUserId) {
    const { user } = await getCurrentUser()
    if (!user) return { data: [], error: new Error('No authenticated user') }
    targetUserId = user.id
  }

  const { data, error } = await supabase
    .from('notifications')
    .select('*')
    .eq('user_id', targetUserId)
    .eq('is_read', false)
    .order('created_at', { ascending: false })

  return { data, error }
}

// Helper function to mark notification as read
export const markNotificationAsRead = async (notificationId: string) => {
  const supabase = createClient()

  const { data, error } = await supabase
    .from('notifications')
    .update({ is_read: true })
    .eq('id', notificationId)

  return { data, error }
}

export default createClient
