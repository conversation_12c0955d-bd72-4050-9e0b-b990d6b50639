import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { PublicProcedureSearchInterface } from '../../../../components/procedures/PublicProcedureSearchInterface'

// Mock the modal component
jest.mock('../../../../components/procedures/PublicProcedureDetailModal', () => ({
  PublicProcedureDetailModal: ({ isOpen, onClose, procedure }: any) => 
    isOpen ? (
      <div data-testid="procedure-modal">
        <h2>{procedure.name}</h2>
        <button onClick={onClose}>Close</button>
      </div>
    ) : null
}))

// Mock lodash debounce
jest.mock('lodash', () => ({
  debounce: (fn: any) => {
    fn.cancel = jest.fn()
    return fn
  }
}))

const mockProcedures = [
  {
    id: '1',
    name: 'Licencia de Construcción',
    description: 'Permiso para construir edificaciones',
    cost: 150000,
    response_time: '15 días hábiles',
    procedure_type: 'TRAMITE' as const,
    has_cost: true,
    online_available: false,
    dependency: { id: 'dep1', name: 'Secretaría de Planeación' },
    subdependency: { id: 'sub1', name: 'Oficina de Licencias' },
    requirements: ['Planos arquitectónicos', 'Certificado de tradición'],
    suit_url: 'https://suit.gov.co/licencia',
    gov_url: 'https://gov.co/licencia'
  },
  {
    id: '2',
    name: 'Certificado de Residencia',
    description: 'Documento que certifica el lugar de residencia',
    cost: 0,
    response_time: 'Inmediato',
    procedure_type: 'OPA' as const,
    has_cost: false,
    online_available: true,
    dependency: { id: 'dep2', name: 'Secretaría de Gobierno' },
    subdependency: { id: 'sub2', name: 'Oficina de Atención al Ciudadano' },
    requirements: ['Cédula de ciudadanía', 'Recibo de servicios públicos']
  },
  {
    id: '3',
    name: 'Registro de Empresa',
    description: 'Inscripción de nueva empresa',
    cost: 75000,
    response_time: '5 días hábiles',
    procedure_type: 'TRAMITE' as const,
    has_cost: true,
    online_available: true,
    dependency: { id: 'dep3', name: 'Secretaría de Desarrollo Económico' },
    subdependency: { id: 'sub3', name: 'Oficina de Registro Empresarial' },
    requirements: ['Formulario de registro', 'Documento de identidad']
  }
]

const mockDependencies = [
  { id: 'dep1', name: 'Secretaría de Planeación' },
  { id: 'dep2', name: 'Secretaría de Gobierno' },
  { id: 'dep3', name: 'Secretaría de Desarrollo Económico' }
]

const mockSubdependencies = [
  { id: 'sub1', name: 'Oficina de Licencias' },
  { id: 'sub2', name: 'Oficina de Atención al Ciudadano' },
  { id: 'sub3', name: 'Oficina de Registro Empresarial' }
]

const mockCategories = ['Construcción', 'Gobierno', 'Empresarial']

const defaultProps = {
  procedures: mockProcedures,
  dependencies: mockDependencies,
  subdependencies: mockSubdependencies,
  categories: mockCategories
}

describe('PublicProcedureSearchInterface Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should render the enhanced search interface', () => {
    render(<PublicProcedureSearchInterface {...defaultProps} />)

    expect(screen.getByText('Búsqueda Inteligente de Trámites y OPAs')).toBeInTheDocument()
    expect(screen.getByPlaceholderText(/¿Qué trámite necesitas?/)).toBeInTheDocument()
    expect(screen.getByText('Filtros Inteligentes')).toBeInTheDocument()
  })

  it('should show all procedures initially', () => {
    render(<PublicProcedureSearchInterface {...defaultProps} />)

    expect(screen.getByText('Licencia de Construcción')).toBeInTheDocument()
    expect(screen.getByText('Certificado de Residencia')).toBeInTheDocument()
    expect(screen.getByText('Registro de Empresa')).toBeInTheDocument()
    expect(screen.getByText('Mostrando 3 de 3 procedimientos')).toBeInTheDocument()
  })

  it('should filter procedures by search term', async () => {
    const user = userEvent.setup()
    render(<PublicProcedureSearchInterface {...defaultProps} />)

    const searchInput = screen.getByPlaceholderText(/¿Qué trámite necesitas?/)
    await user.type(searchInput, 'construcción')

    await waitFor(() => {
      expect(screen.getByText('Licencia de Construcción')).toBeInTheDocument()
      expect(screen.queryByText('Certificado de Residencia')).not.toBeInTheDocument()
      expect(screen.queryByText('Registro de Empresa')).not.toBeInTheDocument()
    })
  })

  it('should show search suggestions', async () => {
    const user = userEvent.setup()
    render(<PublicProcedureSearchInterface {...defaultProps} />)

    const searchInput = screen.getByPlaceholderText(/¿Qué trámite necesitas?/)
    await user.click(searchInput)
    await user.type(searchInput, 'lic')

    await waitFor(() => {
      expect(screen.getByText('Sugerencias')).toBeInTheDocument()
      expect(screen.getByText('Licencia de Construcción')).toBeInTheDocument()
    })
  })

  it('should filter by dependency', async () => {
    const user = userEvent.setup()
    render(<PublicProcedureSearchInterface {...defaultProps} />)

    // Open dependency filter
    const dependencyButton = screen.getByRole('button', { name: /todas las dependencias/i })
    await user.click(dependencyButton)

    // Select a dependency
    const option = screen.getByText('Secretaría de Gobierno')
    await user.click(option)

    await waitFor(() => {
      expect(screen.getByText('Certificado de Residencia')).toBeInTheDocument()
      expect(screen.queryByText('Licencia de Construcción')).not.toBeInTheDocument()
      expect(screen.queryByText('Registro de Empresa')).not.toBeInTheDocument()
    })
  })

  it('should filter by modality', async () => {
    const user = userEvent.setup()
    render(<PublicProcedureSearchInterface {...defaultProps} />)

    // Click virtual modality filter
    const virtualButton = screen.getByRole('button', { name: /virtual/i })
    await user.click(virtualButton)

    await waitFor(() => {
      expect(screen.getByText('Certificado de Residencia')).toBeInTheDocument()
      expect(screen.getByText('Registro de Empresa')).toBeInTheDocument()
      expect(screen.queryByText('Licencia de Construcción')).not.toBeInTheDocument()
    })
  })

  it('should filter by cost', async () => {
    const user = userEvent.setup()
    render(<PublicProcedureSearchInterface {...defaultProps} />)

    // Click free cost filter
    const freeButton = screen.getByRole('button', { name: /gratuito/i })
    await user.click(freeButton)

    await waitFor(() => {
      expect(screen.getByText('Certificado de Residencia')).toBeInTheDocument()
      expect(screen.queryByText('Licencia de Construcción')).not.toBeInTheDocument()
      expect(screen.queryByText('Registro de Empresa')).not.toBeInTheDocument()
    })
  })

  it('should combine search and filters', async () => {
    const user = userEvent.setup()
    render(<PublicProcedureSearchInterface {...defaultProps} />)

    // Search for "certificado"
    const searchInput = screen.getByPlaceholderText(/¿Qué trámite necesitas?/)
    await user.type(searchInput, 'certificado')

    // Apply free cost filter
    const freeButton = screen.getByRole('button', { name: /gratuito/i })
    await user.click(freeButton)

    await waitFor(() => {
      expect(screen.getByText('Certificado de Residencia')).toBeInTheDocument()
      expect(screen.queryByText('Licencia de Construcción')).not.toBeInTheDocument()
      expect(screen.queryByText('Registro de Empresa')).not.toBeInTheDocument()
    })
  })

  it('should show active filter badges', async () => {
    const user = userEvent.setup()
    render(<PublicProcedureSearchInterface {...defaultProps} />)

    // Apply search term
    const searchInput = screen.getByPlaceholderText(/¿Qué trámite necesitas?/)
    await user.type(searchInput, 'licencia')

    await waitFor(() => {
      expect(screen.getByText('Búsqueda: "licencia"')).toBeInTheDocument()
    })
  })

  it('should clear all filters', async () => {
    const user = userEvent.setup()
    render(<PublicProcedureSearchInterface {...defaultProps} />)

    // Apply filters
    const searchInput = screen.getByPlaceholderText(/¿Qué trámite necesitas?/)
    await user.type(searchInput, 'test')

    const freeButton = screen.getByRole('button', { name: /gratuito/i })
    await user.click(freeButton)

    // Clear all
    const clearButton = screen.getByRole('button', { name: /limpiar todo/i })
    await user.click(clearButton)

    await waitFor(() => {
      expect(screen.getByText('Mostrando 3 de 3 procedimientos')).toBeInTheDocument()
    })
  })

  it('should toggle view mode', async () => {
    const user = userEvent.setup()
    render(<PublicProcedureSearchInterface {...defaultProps} />)

    const viewToggle = screen.getByRole('button', { name: /cambiar vista/i })
    await user.click(viewToggle)

    // Should change from grid to list view
    expect(viewToggle.querySelector('svg')).toHaveClass('h-4')
  })

  it('should open procedure detail modal', async () => {
    const user = userEvent.setup()
    render(<PublicProcedureSearchInterface {...defaultProps} />)

    const viewButton = screen.getAllByRole('button', { name: /ver detalles/i })[0]
    await user.click(viewButton)

    await waitFor(() => {
      expect(screen.getByTestId('procedure-modal')).toBeInTheDocument()
      expect(screen.getByText('Licencia de Construcción')).toBeInTheDocument()
    })
  })

  it('should handle empty search results', async () => {
    const user = userEvent.setup()
    render(<PublicProcedureSearchInterface {...defaultProps} />)

    const searchInput = screen.getByPlaceholderText(/¿Qué trámite necesitas?/)
    await user.type(searchInput, 'nonexistent')

    await waitFor(() => {
      expect(screen.getByText('No se encontraron procedimientos')).toBeInTheDocument()
      expect(screen.getByText('Intenta ajustar tus criterios de búsqueda o filtros')).toBeInTheDocument()
    })
  })

  it('should show loading state during search', async () => {
    const user = userEvent.setup()
    render(<PublicProcedureSearchInterface {...defaultProps} />)

    const searchInput = screen.getByPlaceholderText(/¿Qué trámite necesitas?/)
    await user.type(searchInput, 'test')

    // Should show loading indicator
    expect(screen.getByText('Buscando...')).toBeInTheDocument()
  })

  it('should be accessible with keyboard navigation', async () => {
    const user = userEvent.setup()
    render(<PublicProcedureSearchInterface {...defaultProps} />)

    const searchInput = screen.getByPlaceholderText(/¿Qué trámite necesitas?/)
    
    // Focus search input
    searchInput.focus()
    expect(searchInput).toHaveFocus()

    // Tab to next focusable element
    await user.tab()
    
    // Should move focus to next interactive element
    expect(document.activeElement).not.toBe(searchInput)
  })
})
