import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { ProtectedNavigation } from '@/components/navigation/ProtectedNavigation'
import { mockUsers, setupMockAuthSuccess, cleanupMocks } from '@/tests/utils/test-helpers'

// Mock hooks
jest.mock('@/hooks/useAuth', () => ({
  useAuth: () => ({
    user: mockUsers.ciudadano,
    profile: mockUsers.ciudadano.profile,
    loading: false,
    signOut: jest.fn(),
  }),
}))

jest.mock('@/hooks/useRole', () => ({
  useRole: () => ({
    role: 'ciudadano',
    isLoading: false,
    hasRole: jest.fn((role) => role === 'ciudadano'),
    hasAnyRole: jest.fn((roles) => roles.includes('ciudadano')),
  }),
}))

jest.mock('@/hooks/usePermissions', () => ({
  usePermissions: () => ({
    canViewDashboard: true,
    canManageProcedures: false,
    canViewReports: false,
  }),
}))

// Mock Next.js navigation
const mockPush = jest.fn()
const mockReplace = jest.fn()

jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
    replace: mockReplace,
    refresh: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
  }),
  usePathname: () => '/dashboard',
  useSearchParams: () => new URLSearchParams(),
}))

// Mock Supabase client
jest.mock('@/lib/supabase/client', () => ({
  createClient: () => ({
    auth: {
      signOut: jest.fn(() => Promise.resolve({ error: null })),
    },
  }),
}))

describe('ProtectedNavigation', () => {
  beforeEach(() => {
    setupMockAuthSuccess('ciudadano')
    jest.clearAllMocks()
  })

  afterEach(() => {
    cleanupMocks()
  })

  it('renders navigation with user information', async () => {
    render(<ProtectedNavigation />)

    // Check if navigation is rendered
    const nav = screen.getByRole('navigation')
    expect(nav).toBeInTheDocument()

    // Check for user menu trigger
    const userMenuTrigger = screen.getByRole('button', { name: /abrir menú de usuario/i })
    expect(userMenuTrigger).toBeInTheDocument()
  })

  it('displays role-based navigation items for citizen', async () => {
    render(<ProtectedNavigation />)

    // Check for citizen-specific navigation items
    expect(screen.getByText('Dashboard')).toBeInTheDocument()
    expect(screen.getByText('Mis Trámites')).toBeInTheDocument()
    expect(screen.getByText('Documentos')).toBeInTheDocument()
    expect(screen.getByText('Chat IA')).toBeInTheDocument()
  })

  it('opens and closes mobile menu correctly', async () => {
    // Mock mobile viewport
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 375,
    })

    render(<ProtectedNavigation />)

    // Find mobile menu button
    const mobileMenuButton = screen.getByLabelText('Abrir menú')
    expect(mobileMenuButton).toBeInTheDocument()

    // Click to open menu
    fireEvent.click(mobileMenuButton)

    // Check if menu is open
    await waitFor(() => {
      expect(screen.getByLabelText('Cerrar menú')).toBeInTheDocument()
    })

    // Click to close menu
    const closeButton = screen.getByLabelText('Cerrar menú')
    fireEvent.click(closeButton)

    // Check if menu is closed
    await waitFor(() => {
      expect(screen.getByLabelText('Abrir menú')).toBeInTheDocument()
    })
  })

  it('handles user menu interactions', async () => {
    render(<ProtectedNavigation />)

    // Open user menu
    const userMenuTrigger = screen.getByRole('button', { name: /abrir menú de usuario/i })
    fireEvent.click(userMenuTrigger)

    // Check for user menu items
    await waitFor(() => {
      expect(screen.getByText('Mi Perfil')).toBeInTheDocument()
      expect(screen.getByText('Configuración')).toBeInTheDocument()
      expect(screen.getByText('Cerrar Sesión')).toBeInTheDocument()
    })
  })

  it('handles logout correctly', async () => {
    const mockSignOut = jest.fn(() => Promise.resolve({ error: null }))
    
    // Mock the createClient to return our mock
    jest.doMock('@/lib/supabase/client', () => ({
      createClient: () => ({
        auth: {
          signOut: mockSignOut,
        },
      }),
    }))

    render(<ProtectedNavigation />)

    // Open user menu
    const userMenuTrigger = screen.getByRole('button', { name: /abrir menú de usuario/i })
    fireEvent.click(userMenuTrigger)

    // Click logout
    await waitFor(() => {
      const logoutButton = screen.getByText('Cerrar Sesión')
      fireEvent.click(logoutButton)
    })

    // Verify signOut was called
    await waitFor(() => {
      expect(mockSignOut).toHaveBeenCalled()
    })
  })

  it('navigates correctly when menu items are clicked', async () => {
    render(<ProtectedNavigation />)

    // Click on a navigation item
    const dashboardLink = screen.getByText('Dashboard')
    fireEvent.click(dashboardLink)

    // Verify navigation
    expect(mockPush).toHaveBeenCalledWith('/dashboard')
  })

  it('shows notifications indicator', async () => {
    render(<ProtectedNavigation />)

    // Check for notifications button
    const notificationsButton = screen.getByLabelText('Notificaciones')
    expect(notificationsButton).toBeInTheDocument()
  })

  it('displays user role badge correctly', async () => {
    render(<ProtectedNavigation />)

    // Check for role badge
    expect(screen.getByText('Ciudadano')).toBeInTheDocument()
  })

  it('is accessible', () => {
    const { container } = render(<ProtectedNavigation />)

    // Check for proper navigation landmark
    const nav = screen.getByRole('navigation')
    expect(nav).toBeInTheDocument()

    // Check for proper button roles and labels
    const buttons = screen.getAllByRole('button')
    buttons.forEach(button => {
      expect(
        button.textContent || 
        button.getAttribute('aria-label') || 
        button.getAttribute('title')
      ).toBeTruthy()
    })

    // Check for proper link roles
    const links = screen.getAllByRole('link')
    links.forEach(link => {
      expect(
        link.textContent || 
        link.getAttribute('aria-label') || 
        link.getAttribute('title')
      ).toBeTruthy()
    })
  })

  it('supports keyboard navigation', async () => {
    render(<ProtectedNavigation />)

    // Tab to first interactive element
    const firstButton = screen.getAllByRole('button')[0]
    firstButton.focus()
    expect(firstButton).toHaveFocus()

    // Test Enter key activation
    fireEvent.keyDown(firstButton, { key: 'Enter', code: 'Enter' })
    
    // Should trigger the button action
    expect(firstButton).toHaveFocus()
  })

  it('handles responsive design correctly', () => {
    // Test desktop view
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 1024,
    })

    const { rerender } = render(<ProtectedNavigation />)

    // Should show desktop navigation
    expect(screen.getByText('Dashboard')).toBeInTheDocument()

    // Test mobile view
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 375,
    })

    rerender(<ProtectedNavigation />)

    // Should show mobile menu button
    expect(screen.getByLabelText('Abrir menú')).toBeInTheDocument()
  })

  it('shows correct navigation items based on current path', () => {
    // Mock different pathname
    jest.doMock('next/navigation', () => ({
      useRouter: () => ({
        push: mockPush,
        replace: mockReplace,
        refresh: jest.fn(),
        back: jest.fn(),
        forward: jest.fn(),
      }),
      usePathname: () => '/documentos',
      useSearchParams: () => new URLSearchParams(),
    }))

    render(<ProtectedNavigation />)

    // Should highlight current page
    const documentsLink = screen.getByText('Documentos')
    expect(documentsLink.closest('a')).toHaveClass('active')
  })

  it('handles loading state correctly', () => {
    // Mock loading state
    jest.doMock('@/hooks/useAuth', () => ({
      useAuth: () => ({
        user: null,
        profile: null,
        loading: true,
        signOut: jest.fn(),
      }),
    }))

    render(<ProtectedNavigation />)

    // Should show loading state or skeleton
    expect(screen.getByTestId('navigation-loading')).toBeInTheDocument()
  })

  it('handles error state gracefully', () => {
    // Mock error state
    jest.doMock('@/hooks/useAuth', () => ({
      useAuth: () => ({
        user: null,
        profile: null,
        loading: false,
        error: 'Authentication error',
        signOut: jest.fn(),
      }),
    }))

    render(<ProtectedNavigation />)

    // Should still render basic navigation structure
    const nav = screen.getByRole('navigation')
    expect(nav).toBeInTheDocument()
  })
})
