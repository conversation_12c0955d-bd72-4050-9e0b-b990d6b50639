'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { 
  X,
  Clock, 
  DollarSign, 
  Building, 
  FileText,
  Star,
  Users,
  Phone,
  Mail,
  MapPin,
  ExternalLink,
  Plus,
  CheckCircle,
  AlertCircle,
  Info,
  Download,
  Calendar,
  Scale
} from 'lucide-react'

interface Procedure {
  id: string
  name: string
  description?: string
  cost?: number
  response_time?: string
  category?: string
  difficulty_level?: number
  popularity_score?: number
  requirements?: string[]
  documents_required?: string[]
  process_steps?: string[]
  legal_framework?: string
  contact_info?: any
  online_available?: boolean
  tags?: string[]
  dependency?: {
    id: string
    name: string
    acronym?: string
    description?: string
    contact_email?: string
    contact_phone?: string
    address?: string
  }
}

interface ProcedureDetailModalProps {
  procedure: Procedure
  onClose: () => void
  userStatus?: any
}

export function ProcedureDetailModal({ procedure, onClose, userStatus }: ProcedureDetailModalProps) {
  const [activeTab, setActiveTab] = useState<'overview' | 'requirements' | 'process' | 'contact'>('overview')

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex justify-between items-start p-6 border-b">
          <div className="flex-1">
            <div className="flex items-center space-x-3 mb-2">
              <h2 className="text-2xl font-bold text-gray-900">
                {procedure.name}
              </h2>
              {userStatus && (
                <Badge variant="outline">
                  {userStatus.display_name}
                </Badge>
              )}
            </div>
            <p className="text-gray-600">
              {procedure.dependency?.name}
            </p>
            <div className="flex items-center space-x-4 mt-3">
              {procedure.cost !== undefined && (
                <div className="flex items-center text-sm">
                  <DollarSign className="h-4 w-4 text-green-600 mr-1" />
                  <span className="font-medium">
                    {procedure.cost > 0 ? `$${procedure.cost.toLocaleString()}` : 'Gratuito'}
                  </span>
                </div>
              )}
              {procedure.response_time && (
                <div className="flex items-center text-sm">
                  <Clock className="h-4 w-4 text-blue-600 mr-1" />
                  <span>{procedure.response_time}</span>
                </div>
              )}
              {procedure.difficulty_level && (
                <div className="flex items-center text-sm">
                  <Star className="h-4 w-4 text-yellow-500 mr-1" />
                  <span>Nivel {procedure.difficulty_level}</span>
                </div>
              )}
              {procedure.online_available && (
                <Badge className="bg-green-100 text-green-800">
                  Disponible en línea
                </Badge>
              )}
            </div>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Tabs */}
        <div className="border-b">
          <div className="flex space-x-8 px-6">
            {[
              { id: 'overview', label: 'Información General', icon: Info },
              { id: 'requirements', label: 'Requisitos', icon: CheckCircle },
              { id: 'process', label: 'Proceso', icon: FileText },
              { id: 'contact', label: 'Contacto', icon: Phone }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center space-x-2 py-4 border-b-2 transition-colors ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                <tab.icon className="h-4 w-4" />
                <span className="font-medium">{tab.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Content */}
        <div className="p-6 max-h-[60vh] overflow-y-auto">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              {/* Description */}
              {procedure.description && (
                <div>
                  <h3 className="text-lg font-semibold mb-3">Descripción</h3>
                  <p className="text-gray-700 leading-relaxed">
                    {procedure.description}
                  </p>
                </div>
              )}

              {/* Key Information */}
              <div>
                <h3 className="text-lg font-semibold mb-3">Información Clave</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center space-x-3">
                        <DollarSign className="h-8 w-8 text-green-600" />
                        <div>
                          <p className="text-sm text-gray-500">Costo</p>
                          <p className="font-semibold">
                            {procedure.cost ? `$${procedure.cost.toLocaleString()}` : 'Gratuito'}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center space-x-3">
                        <Clock className="h-8 w-8 text-blue-600" />
                        <div>
                          <p className="text-sm text-gray-500">Tiempo de Respuesta</p>
                          <p className="font-semibold">
                            {procedure.response_time || 'No especificado'}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center space-x-3">
                        <Star className="h-8 w-8 text-yellow-500" />
                        <div>
                          <p className="text-sm text-gray-500">Nivel de Dificultad</p>
                          <p className="font-semibold">
                            Nivel {procedure.difficulty_level || 1}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center space-x-3">
                        <Building className="h-8 w-8 text-purple-600" />
                        <div>
                          <p className="text-sm text-gray-500">Dependencia</p>
                          <p className="font-semibold">
                            {procedure.dependency?.name || 'N/A'}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>

              {/* Tags */}
              {procedure.tags && procedure.tags.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold mb-3">Etiquetas</h3>
                  <div className="flex flex-wrap gap-2">
                    {procedure.tags.map((tag, index) => (
                      <Badge key={index} variant="secondary">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* Legal Framework */}
              {procedure.legal_framework && (
                <div>
                  <h3 className="text-lg font-semibold mb-3 flex items-center">
                    <Scale className="h-5 w-5 mr-2" />
                    Marco Legal
                  </h3>
                  <p className="text-gray-700 bg-gray-50 p-4 rounded-lg">
                    {procedure.legal_framework}
                  </p>
                </div>
              )}
            </div>
          )}

          {activeTab === 'requirements' && (
            <div className="space-y-6">
              {/* Requirements */}
              {procedure.requirements && procedure.requirements.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold mb-3 flex items-center">
                    <CheckCircle className="h-5 w-5 mr-2 text-green-600" />
                    Requisitos
                  </h3>
                  <ul className="space-y-2">
                    {procedure.requirements.map((requirement, index) => (
                      <li key={index} className="flex items-start space-x-3">
                        <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-700">{requirement}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Required Documents */}
              {procedure.documents_required && procedure.documents_required.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold mb-3 flex items-center">
                    <FileText className="h-5 w-5 mr-2 text-blue-600" />
                    Documentos Requeridos
                  </h3>
                  <ul className="space-y-2">
                    {procedure.documents_required.map((document, index) => (
                      <li key={index} className="flex items-start space-x-3">
                        <FileText className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-700">{document}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {(!procedure.requirements || procedure.requirements.length === 0) &&
               (!procedure.documents_required || procedure.documents_required.length === 0) && (
                <div className="text-center py-8">
                  <AlertCircle className="mx-auto h-12 w-12 text-gray-300 mb-4" />
                  <p className="text-gray-500">
                    No hay información de requisitos disponible para este trámite.
                  </p>
                </div>
              )}
            </div>
          )}

          {activeTab === 'process' && (
            <div className="space-y-6">
              {/* Process Steps */}
              {procedure.process_steps && procedure.process_steps.length > 0 ? (
                <div>
                  <h3 className="text-lg font-semibold mb-3 flex items-center">
                    <FileText className="h-5 w-5 mr-2 text-purple-600" />
                    Pasos del Proceso
                  </h3>
                  <div className="space-y-4">
                    {procedure.process_steps.map((step, index) => (
                      <div key={index} className="flex items-start space-x-4">
                        <div className="flex-shrink-0 w-8 h-8 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center font-semibold text-sm">
                          {index + 1}
                        </div>
                        <div className="flex-1">
                          <p className="text-gray-700">{step}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <FileText className="mx-auto h-12 w-12 text-gray-300 mb-4" />
                  <p className="text-gray-500">
                    No hay información del proceso disponible para este trámite.
                  </p>
                </div>
              )}
            </div>
          )}

          {activeTab === 'contact' && (
            <div className="space-y-6">
              {/* Dependency Contact */}
              {procedure.dependency && (
                <div>
                  <h3 className="text-lg font-semibold mb-3 flex items-center">
                    <Building className="h-5 w-5 mr-2 text-blue-600" />
                    Información de la Dependencia
                  </h3>
                  <Card>
                    <CardContent className="p-4">
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-semibold text-gray-900">
                            {procedure.dependency.name}
                          </h4>
                          {procedure.dependency.acronym && (
                            <p className="text-sm text-gray-500">
                              ({procedure.dependency.acronym})
                            </p>
                          )}
                          {procedure.dependency.description && (
                            <p className="text-gray-700 mt-2">
                              {procedure.dependency.description}
                            </p>
                          )}
                        </div>

                        <Separator />

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {procedure.dependency.contact_phone && (
                            <div className="flex items-center space-x-3">
                              <Phone className="h-5 w-5 text-green-600" />
                              <div>
                                <p className="text-sm text-gray-500">Teléfono</p>
                                <p className="font-medium">{procedure.dependency.contact_phone}</p>
                              </div>
                            </div>
                          )}

                          {procedure.dependency.contact_email && (
                            <div className="flex items-center space-x-3">
                              <Mail className="h-5 w-5 text-blue-600" />
                              <div>
                                <p className="text-sm text-gray-500">Email</p>
                                <p className="font-medium">{procedure.dependency.contact_email}</p>
                              </div>
                            </div>
                          )}

                          {procedure.dependency.address && (
                            <div className="flex items-start space-x-3 md:col-span-2">
                              <MapPin className="h-5 w-5 text-red-600 mt-1" />
                              <div>
                                <p className="text-sm text-gray-500">Dirección</p>
                                <p className="font-medium">{procedure.dependency.address}</p>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}

              {/* Procedure Specific Contact */}
              {procedure.contact_info && (
                <div>
                  <h3 className="text-lg font-semibold mb-3 flex items-center">
                    <Phone className="h-5 w-5 mr-2 text-green-600" />
                    Contacto Específico del Trámite
                  </h3>
                  <Card>
                    <CardContent className="p-4">
                      <pre className="text-sm text-gray-700 whitespace-pre-wrap">
                        {JSON.stringify(procedure.contact_info, null, 2)}
                      </pre>
                    </CardContent>
                  </Card>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="border-t p-6">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-4">
              {procedure.category && (
                <Badge variant="outline">
                  {procedure.category}
                </Badge>
              )}
              {procedure.popularity_score && (
                <div className="flex items-center text-sm text-gray-500">
                  <Users className="h-4 w-4 mr-1" />
                  Popularidad: {procedure.popularity_score}
                </div>
              )}
            </div>
            <div className="flex space-x-3">
              <Button variant="outline" onClick={onClose}>
                Cerrar
              </Button>
              {!userStatus && (
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Iniciar Trámite
                </Button>
              )}
              {userStatus && (
                <Button variant="outline">
                  <Eye className="h-4 w-4 mr-2" />
                  Ver Mi Trámite
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
