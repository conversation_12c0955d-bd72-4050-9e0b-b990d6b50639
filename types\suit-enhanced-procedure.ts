/**
 * Tipos extendidos para procedimientos con información SUIT
 */

export interface SuitData {
  ficha_id: string
  titulo?: string
  descripcion_detallada?: string
  requisitos?: string[]
  pasos?: string[]
  documentos_necesarios?: string[]
  entidad_responsable?: string
  tiempo_respuesta?: string
  costo_detallado?: string
  base_juridica?: string
  scraping_status: 'pending' | 'success' | 'failed' | 'auth_required'
  scraped_at?: string
  updated_at?: string
}

export interface Dependency {
  id: string
  name: string
  code?: string
}

export interface Subdependency {
  id: string
  name: string
  code?: string
  dependency_id: string
}

export interface SuitEnhancedProcedure {
  // Campos básicos del procedimiento
  id: string
  name: string
  codigo_tramite?: string
  code?: string // Para OPAs
  description?: string
  cost?: number
  response_time?: string
  requirements?: string[]
  documents_required?: string[]
  process_steps?: string[]
  legal_framework?: string
  contact_info?: any
  online_available?: boolean
  tags?: string[]
  category?: string
  difficulty_level?: number
  popularity_score?: number

  // Información de dependencias
  dependency?: Dependency
  subdependency?: Subdependency

  // Tipo de procedimiento
  procedure_type?: string

  // Enlaces oficiales
  suit_url?: string
  suit_link?: string // Alias para compatibilidad
  gov_url?: string
  govco_link?: string // Alias para compatibilidad

  // Información de costos
  has_cost?: boolean
  cost_description?: string

  // Datos SUIT enriquecidos
  suit_data?: SuitData

  // Campos calculados para mostrar la mejor información disponible
  best_description?: string
  best_requirements?: string[]
  best_process_steps?: string[]
  best_response_time?: string
  best_cost_info?: string
  best_modality?: string
  has_suit_enhancement?: boolean
}

/**
 * Función para combinar datos del procedimiento base con información SUIT
 */
export function enhanceProcedureWithSuit(
  baseProcedure: any,
  suitData?: SuitData
): SuitEnhancedProcedure {
  const enhanced: SuitEnhancedProcedure = {
    ...baseProcedure,
    suit_data: suitData,
    has_suit_enhancement: !!suitData && (
      !!suitData.descripcion_detallada ||
      !!suitData.requisitos?.length ||
      !!suitData.pasos?.length ||
      !!suitData.tiempo_respuesta ||
      !!suitData.costo_detallado
    )
  }

  // Calcular la mejor descripción disponible
  if (suitData?.descripcion_detallada && suitData.descripcion_detallada.length > (baseProcedure.description?.length || 0)) {
    enhanced.best_description = suitData.descripcion_detallada
  } else {
    enhanced.best_description = baseProcedure.description
  }

  // Calcular los mejores requisitos
  if (suitData?.requisitos && suitData.requisitos.length > 0) {
    enhanced.best_requirements = suitData.requisitos
  } else {
    enhanced.best_requirements = baseProcedure.requirements || []
  }

  // Calcular los mejores pasos del proceso
  if (suitData?.pasos && suitData.pasos.length > 0) {
    enhanced.best_process_steps = suitData.pasos
  } else {
    enhanced.best_process_steps = baseProcedure.process_steps || []
  }

  // Calcular el mejor tiempo de respuesta
  if (suitData?.tiempo_respuesta) {
    enhanced.best_response_time = suitData.tiempo_respuesta
  } else {
    enhanced.best_response_time = baseProcedure.response_time
  }

  // Calcular la mejor información de costo
  if (suitData?.costo_detallado) {
    enhanced.best_cost_info = suitData.costo_detallado
  } else if (baseProcedure.cost_description) {
    enhanced.best_cost_info = baseProcedure.cost_description
  } else if (baseProcedure.cost && baseProcedure.cost > 0) {
    enhanced.best_cost_info = `$${baseProcedure.cost.toLocaleString('es-CO')}`
  } else if (baseProcedure.has_cost === false) {
    enhanced.best_cost_info = 'Gratuito'
  } else {
    enhanced.best_cost_info = 'Consultar en entidad'
  }

  // Calcular la mejor modalidad
  if (baseProcedure.online_available === true) {
    enhanced.best_modality = 'Virtual'
  } else if (baseProcedure.online_available === false) {
    enhanced.best_modality = 'Presencial'
  } else {
    enhanced.best_modality = 'Mixta'
  }

  return enhanced
}

/**
 * Función para obtener estadísticas de mejora SUIT
 */
export function getSuitEnhancementStats(procedure: SuitEnhancedProcedure) {
  if (!procedure.suit_data || procedure.suit_data.scraping_status !== 'success') {
    return {
      hasEnhancement: false,
      improvements: []
    }
  }

  const improvements: string[] = []
  
  // Verificar mejoras en descripción
  if (procedure.suit_data.descripcion_detallada && 
      procedure.suit_data.descripcion_detallada.length > (procedure.description?.length || 0)) {
    improvements.push('Descripción más detallada')
  }

  // Verificar mejoras en requisitos
  if (procedure.suit_data.requisitos && 
      procedure.suit_data.requisitos.length > (procedure.requirements?.length || 0)) {
    improvements.push('Requisitos más completos')
  }

  // Verificar mejoras en pasos
  if (procedure.suit_data.pasos && 
      procedure.suit_data.pasos.length > (procedure.process_steps?.length || 0)) {
    improvements.push('Pasos del proceso detallados')
  }

  // Verificar información adicional
  if (procedure.suit_data.entidad_responsable) {
    improvements.push('Entidad responsable')
  }

  if (procedure.suit_data.base_juridica) {
    improvements.push('Base jurídica')
  }

  if (procedure.suit_data.documentos_necesarios && procedure.suit_data.documentos_necesarios.length > 0) {
    improvements.push('Documentos necesarios')
  }

  return {
    hasEnhancement: improvements.length > 0,
    improvements,
    enhancementCount: improvements.length
  }
}

/**
 * Función para formatear información de tiempo de respuesta
 */
export function formatEnhancedResponseTime(procedure: SuitEnhancedProcedure): string {
  const time = procedure.best_response_time
  if (!time) return 'No especificado'
  
  // Si viene de SUIT, puede estar más detallado
  if (procedure.suit_data?.tiempo_respuesta && time === procedure.suit_data.tiempo_respuesta) {
    return time // Ya está formateado desde SUIT
  }
  
  // Formateo estándar para datos originales
  return time
}

/**
 * Función para formatear información de costo enriquecida
 */
export function formatEnhancedCost(procedure: SuitEnhancedProcedure): string {
  // Priorizar información de SUIT si está disponible
  if (procedure.suit_data?.costo_detallado) {
    return procedure.suit_data.costo_detallado
  }
  
  // Fallback a información original
  if (procedure.has_cost === false) {
    return 'Gratuito'
  }
  
  if (procedure.cost_description) {
    return procedure.cost_description
  }
  
  if (procedure.cost && procedure.cost > 0) {
    return `$${procedure.cost.toLocaleString('es-CO')}`
  }
  
  return 'Consultar en entidad'
}

export default SuitEnhancedProcedure
