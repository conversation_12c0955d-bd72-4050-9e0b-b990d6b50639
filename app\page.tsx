'use client'

import React from 'react'
import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { IntelligentSearchBar } from '@/components/search/IntelligentSearchBar'
import { FAQSection } from '@/components/faq/FAQSection'
import { ChiaLogoHeader, ChiaLogoFooter } from '@/components/ui/chia-logo'
import {
  Search,
  Clock,
  Shield,
  MessageCircle,
  Building2,
  MapPin,
  Phone,
  Mail,
  ExternalLink,
  ArrowRight,
  CheckCircle,
  Star,
  TrendingUp,
  Edit3,
  Clipboard,
  Headphones,
  FolderOpen,
  Calendar,
  Bot
} from 'lucide-react'

export default function HomePage() {
  const handleSearch = (query: string, results: any[]) => {
    // Only redirect if there are no results to show or if explicitly requested
    // This prevents automatic redirection and allows the dropdown to show first
    console.log('Search performed:', query, 'Results:', results.length)
  }

  const handleResultSelect = (result: any) => {
    // Handle result selection
    if (result.type === 'procedure') {
      window.location.href = `/consulta-tramites?id=${result.id}`
    } else if (result.type === 'opa') {
      window.location.href = `/consulta-tramites?opa=${result.id}`
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-chia-blue-50 via-white to-chia-green-50">
      {/* Navigation Header */}
      <nav className="bg-white/90 backdrop-blur-sm shadow-sm border-b sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo and Brand */}
            <div className="flex items-center space-x-3">
              <ChiaLogoHeader />
              <div className="hidden sm:block">
                <p className="text-xs text-primary">Sistema de Atención Ciudadana</p>
              </div>
            </div>

            {/* Navigation Links */}
            <div className="hidden md:flex items-center space-x-6">
              <Link href="/dependencias" className="text-gray-700 hover:text-primary font-medium">
                Dependencias
              </Link>
              <Link href="/consulta-tramites" className="text-gray-700 hover:text-primary font-medium">
                Trámites
              </Link>
              <Link
                href="https://pacochia.gov.co/realice-sus-peticiones-quejas-reclamos-sugerencias-y-denuncias/"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-700 hover:text-primary font-medium"
              >
                Radicar PQRS
              </Link>
              <Link href="/auth/login">
                <Button variant="outline" className="border-primary text-primary hover:bg-primary/5">
                  Iniciar Sesión
                </Button>
              </Link>
              <Link href="/auth/register">
                <Button>
                  Registrarse
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center">
          {/* Main Heading */}
          <div className="space-y-6 mb-12">
            <Badge variant="default" className="px-4 py-2">
              <Star className="h-4 w-4 mr-2" />
              Portal Ciudadano Digital
            </Badge>

            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 leading-tight">
              Servicios municipales
              <span className="block text-primary">al alcance de todos</span>
            </h1>

            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Accede a más de <strong>800 trámites y servicios</strong> del Municipio de Chía
              de forma rápida, segura y completamente digital. Tu gobierno más cerca que nunca.
            </p>
          </div>

          {/* Main Search Bar */}
          <div className="max-w-4xl mx-auto mb-12">
            <IntelligentSearchBar
              onSearch={handleSearch}
              onResultSelect={handleResultSelect}
              placeholder="¿Qué trámite necesitas? Ej: licencia construcción, certificado residencia..."
              className="w-full"
              showFilters={true}
              maxResults={6}
            />
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
            <div className="text-center">
              <div className="text-3xl font-bold text-primary">108</div>
              <div className="text-sm text-gray-600">Trámites</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-primary">721</div>
              <div className="text-sm text-gray-600">OPAs</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-primary">14</div>
              <div className="text-sm text-gray-600">Dependencias</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-primary">24/7</div>
              <div className="text-sm text-gray-600">Disponible</div>
            </div>
          </div>
        </div>
      </section>

      {/* Quick Services Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Servicios más solicitados
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Accede rápidamente a los trámites y servicios que más utilizan los ciudadanos de Chía
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Card 1: Radique peticiones en línea */}
            <Card className="group hover:shadow-lg transition-all duration-300 bg-gradient-to-br from-yellow-50 to-yellow-100 border-0 shadow-md">
              <CardContent className="p-8 text-center">
                <div className="w-16 h-16 bg-yellow-200 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <Edit3 className="h-8 w-8 text-yellow-700" />
                </div>
                <CardTitle className="text-xl font-bold text-gray-900 mb-3">
                  Radique aquí sus peticiones en línea
                </CardTitle>
                <CardDescription className="text-gray-700 mb-6 leading-relaxed">
                  Radique aquí sus peticiones en línea y demás detalles para acceder a los diferentes trámites de la Alcaldía Municipal de Chía.
                </CardDescription>
                <Link href="/peticiones">
                  <Button className="bg-yellow-600 hover:bg-yellow-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                    Acceder
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                </Link>
              </CardContent>
            </Card>

            {/* Card 2: Trámites y OPA */}
            <Card className="group hover:shadow-lg transition-all duration-300 bg-gradient-to-br from-gray-50 to-gray-100 border-0 shadow-md">
              <CardContent className="p-8 text-center">
                <div className="w-16 h-16 bg-gray-200 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <Clipboard className="h-8 w-8 text-gray-700" />
                </div>
                <CardTitle className="text-xl font-bold text-gray-900 mb-3">
                  Trámites y otros procesos administrativos- OPA
                </CardTitle>
                <CardDescription className="text-gray-700 mb-6 leading-relaxed">
                  Consulte aquí los requisitos, formularios, costos y demás detalles para acceder a los diferentes trámites de la Alcaldía Municipal de Chía.
                </CardDescription>
                <Link href="/consulta-tramites">
                  <Button className="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                    Acceder
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                </Link>
              </CardContent>
            </Card>

            {/* Card 3: Canales de atención */}
            <Card className="group hover:shadow-lg transition-all duration-300 bg-gradient-to-br from-blue-50 to-blue-100 border-0 shadow-md">
              <CardContent className="p-8 text-center">
                <div className="w-16 h-16 bg-blue-200 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <Headphones className="h-8 w-8 text-blue-700" />
                </div>
                <CardTitle className="text-xl font-bold text-gray-900 mb-3">
                  Canales de atención al ciudadano
                </CardTitle>
                <CardDescription className="text-gray-700 mb-6 leading-relaxed">
                  Consulta todos los canales de atención disponibles para tener a la Alcaldía de Chía cada vez más cerca.
                </CardDescription>
                <Link href="/atencion-ciudadano">
                  <Button className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                    Acceder
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                </Link>
              </CardContent>
            </Card>

            {/* Card 4: Documentos relacionados */}
            <Card className="group hover:shadow-lg transition-all duration-300 bg-gradient-to-br from-green-50 to-green-100 border-0 shadow-md">
              <CardContent className="p-8 text-center">
                <div className="w-16 h-16 bg-green-200 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <FolderOpen className="h-8 w-8 text-green-700" />
                </div>
                <CardTitle className="text-xl font-bold text-gray-900 mb-3">
                  Documentos relacionados
                </CardTitle>
                <CardDescription className="text-gray-700 mb-6 leading-relaxed">
                  Consulte aquí los documentos relacionados con la Atención al Ciudadano.
                </CardDescription>
                <Link href="/documentos">
                  <Button className="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                    Acceder
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                </Link>
              </CardContent>
            </Card>

            {/* Card 5: Agendamiento especializado */}
            <Card className="group hover:shadow-lg transition-all duration-300 bg-gradient-to-br from-purple-50 to-purple-100 border-0 shadow-md">
              <CardContent className="p-8 text-center">
                <div className="w-16 h-16 bg-purple-200 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <Calendar className="h-8 w-8 text-purple-700" />
                </div>
                <CardTitle className="text-xl font-bold text-gray-900 mb-3">
                  Agendamiento especializado en línea
                </CardTitle>
                <CardDescription className="text-gray-700 mb-6 leading-relaxed">
                  Próximamente podrá consultar el listado de servicios disponibles para el agendamiento de una asesoría especializada por parte de funcionarios de las diferentes dependencias de la administración central.
                </CardDescription>
                <Link href="/agendamiento">
                  <Button className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                    Acceder
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                </Link>
              </CardContent>
            </Card>

            {/* Card 6: Asistente IA Inteligente */}
            <Card className="group hover:shadow-lg transition-all duration-300 bg-gradient-to-br from-indigo-50 to-indigo-100 border-0 shadow-md">
              <CardContent className="p-8 text-center">
                <div className="w-16 h-16 bg-indigo-200 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <Bot className="h-8 w-8 text-indigo-700" />
                </div>
                <CardTitle className="text-xl font-bold text-gray-900 mb-3">
                  Asistente IA Inteligente
                </CardTitle>
                <CardDescription className="text-gray-700 mb-6 leading-relaxed">
                  Chatbot con inteligencia artificial que te guía paso a paso en tus trámites, responde preguntas frecuentes y te ayuda a encontrar la información que necesitas de manera rápida y eficiente.
                </CardDescription>
                <Link href="/chat">
                  <Button className="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                    Acceder
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-chia-blue-50 to-chia-green-50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              ¿Por qué elegir nuestro portal digital?
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Modernizamos la gestión municipal para ofrecerte la mejor experiencia ciudadana
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <Search className="h-8 w-8 text-primary" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Búsqueda Inteligente</h3>
              <p className="text-gray-600">
                Encuentra cualquier trámite o servicio con nuestra IA avanzada que entiende tu lenguaje natural
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-chia-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Clock className="h-8 w-8 text-chia-green-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Disponible 24/7</h3>
              <p className="text-gray-600">
                Accede a todos los servicios las 24 horas del día, los 7 días de la semana, desde cualquier dispositivo
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Shield className="h-8 w-8 text-purple-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Seguro y Confiable</h3>
              <p className="text-gray-600">
                Tus datos están protegidos con los más altos estándares de seguridad y privacidad
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Quick Access Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Acceso rápido por dependencias
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Navega directamente a la secretaría o dependencia que necesitas
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Link href="/dependencias?dep=alcaldia" className="group">
              <Card className="text-center hover:shadow-lg transition-all duration-300 group-hover:scale-105">
                <CardContent className="p-6">
                  <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <Building2 className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="font-semibold text-gray-900">Alcaldía</h3>
                  <p className="text-sm text-gray-600 mt-1">15 servicios</p>
                </CardContent>
              </Card>
            </Link>

            <Link href="/dependencias?dep=planeacion" className="group">
              <Card className="text-center hover:shadow-lg transition-all duration-300 group-hover:scale-105">
                <CardContent className="p-6">
                  <div className="w-12 h-12 bg-chia-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <MapPin className="h-6 w-6 text-chia-green-600" />
                  </div>
                  <h3 className="font-semibold text-gray-900">Planeación</h3>
                  <p className="text-sm text-gray-600 mt-1">42 servicios</p>
                </CardContent>
              </Card>
            </Link>

            <Link href="/dependencias?dep=hacienda" className="group">
              <Card className="text-center hover:shadow-lg transition-all duration-300 group-hover:scale-105">
                <CardContent className="p-6">
                  <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <TrendingUp className="h-6 w-6 text-yellow-600" />
                  </div>
                  <h3 className="font-semibold text-gray-900">Hacienda</h3>
                  <p className="text-sm text-gray-600 mt-1">28 servicios</p>
                </CardContent>
              </Card>
            </Link>

            <Link href="/dependencias" className="group">
              <Card className="text-center hover:shadow-lg transition-all duration-300 group-hover:scale-105 border-2 border-dashed border-gray-300">
                <CardContent className="p-6">
                  <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <ExternalLink className="h-6 w-6 text-gray-600" />
                  </div>
                  <h3 className="font-semibold text-gray-900">Ver todas</h3>
                  <p className="text-sm text-gray-600 mt-1">14 dependencias</p>
                </CardContent>
              </Card>
            </Link>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <FAQSection
            title="Preguntas Frecuentes"
            description="Encuentra respuestas rápidas a las consultas más comunes de los ciudadanos"
            showSearch={true}
            showStats={true}
            className="max-w-4xl mx-auto"
          />
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-chia-blue-900 text-white">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
            <div>
              <h2 className="text-3xl font-bold mb-6">
                ¿Necesitas ayuda adicional?
              </h2>
              <p className="text-chia-blue-100 text-lg mb-8">
                Nuestro equipo está disponible para ayudarte con cualquier consulta o trámite que necesites realizar.
              </p>

              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <Phone className="h-5 w-5 text-chia-blue-300" />
                  <span className="text-chia-blue-100">(*************</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Mail className="h-5 w-5 text-chia-blue-300" />
                  <span className="text-chia-blue-100"><EMAIL></span>
                </div>
                <div className="flex items-center space-x-3">
                  <MapPin className="h-5 w-5 text-chia-blue-300" />
                  <span className="text-chia-blue-100">Carrera 11 # 17-25, Chía, Cundinamarca</span>
                </div>
              </div>
            </div>

            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8">
              <h3 className="text-xl font-semibold mb-4">Horarios de Atención</h3>
              <div className="space-y-3 text-chia-blue-100">
                <div className="flex justify-between">
                  <span>Lunes - Viernes:</span>
                  <span>8:00 AM - 5:00 PM</span>
                </div>
                <div className="flex justify-between">
                  <span>Sábados:</span>
                  <span>8:00 AM - 12:00 PM</span>
                </div>
                <div className="flex justify-between">
                  <span>Portal Digital:</span>
                  <span className="text-chia-green-300 font-semibold">24/7</span>
                </div>
              </div>

              <div className="mt-6 pt-6 border-t border-white/20">
                <Link href="/chat">
                  <Button className="w-full bg-chia-green-600 hover:bg-chia-green-700 text-white">
                    <MessageCircle className="h-4 w-4 mr-2" />
                    Chatear con Asistente IA
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="col-span-1 md:col-span-2">
              <div className="flex items-center space-x-3 mb-4">
                <ChiaLogoFooter />
              </div>
              <p className="text-gray-400 mb-4">
                Transformando la gestión municipal a través de la tecnología para brindar
                mejores servicios a nuestros ciudadanos.
              </p>
              <div className="flex items-center space-x-2 text-sm text-gray-400">
                <CheckCircle className="h-4 w-4 text-chia-green-500" />
                <span>Gobierno Digital Certificado</span>
              </div>
            </div>

            <div>
              <h4 className="font-semibold mb-4">Enlaces Rápidos</h4>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="/consulta-tramites" className="hover:text-white">Consultar Trámites</Link></li>
                <li><Link href="/dependencias" className="hover:text-white">Dependencias</Link></li>
                <li><Link href="https://pacochia.gov.co/realice-sus-peticiones-quejas-reclamos-sugerencias-y-denuncias/" target="_blank" rel="noopener noreferrer" className="hover:text-white">Radicar PQRS</Link></li>
                <li><Link href="/auth/register" className="hover:text-white">Crear Cuenta</Link></li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-4">Soporte</h4>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="/ayuda" className="hover:text-white">Centro de Ayuda</Link></li>
                <li><Link href="/contacto" className="hover:text-white">Contacto</Link></li>
                <li><Link href="/terminos" className="hover:text-white">Términos de Uso</Link></li>
                <li><Link href="/privacidad" className="hover:text-white">Política de Privacidad</Link></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 Municipio de Chía. Todos los derechos reservados.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}