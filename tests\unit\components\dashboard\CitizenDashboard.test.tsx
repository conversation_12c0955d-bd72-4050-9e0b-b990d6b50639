import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { DashboardClient } from '@/components/dashboard/DashboardClient'
import { mockUsers, setupMockAuthSuccess, cleanupMocks } from '@/tests/utils/test-helpers'

// Mock the hooks
jest.mock('@/hooks/useAuth', () => ({
  useAuth: () => ({
    user: mockUsers.ciudadano,
    profile: mockUsers.ciudadano.profile,
    loading: false,
    signOut: jest.fn(),
  }),
}))

jest.mock('@/hooks/useRole', () => ({
  useRole: () => ({
    role: 'ciudadano',
    isLoading: false,
    hasRole: jest.fn((role) => role === 'ciudadano'),
  }),
}))

// Mock Supabase client
const mockSupabase = {
  from: jest.fn(() => ({
    select: jest.fn(() => ({
      eq: jest.fn(() => ({
        order: jest.fn(() => ({
          limit: jest.fn(() => Promise.resolve({
            data: [
              {
                id: 1,
                procedure_name: 'Certificado de Residencia',
                status: 'en_proceso',
                created_at: '2024-01-01T00:00:00Z',
                updated_at: '2024-01-01T00:00:00Z',
              },
              {
                id: 2,
                procedure_name: 'Licencia de Construcción',
                status: 'completado',
                created_at: '2024-01-02T00:00:00Z',
                updated_at: '2024-01-02T00:00:00Z',
              },
            ],
            error: null,
          })),
        })),
      })),
    })),
  })),
}

jest.mock('@/lib/supabase/client', () => ({
  supabase: mockSupabase,
}))

describe('DashboardClient', () => {
  beforeEach(() => {
    setupMockAuthSuccess('ciudadano')
  })

  afterEach(() => {
    cleanupMocks()
  })

  it('renders dashboard with user information', async () => {
    render(<DashboardClient />)

    // Check if dashboard content is displayed
    expect(screen.getByText('Portal Ciudadano')).toBeInTheDocument()

    // Check if role display is shown
    expect(screen.getByText('Rol actual:')).toBeInTheDocument()
  })

  it('displays statistics cards', async () => {
    render(<CitizenDashboard />)

    await waitFor(() => {
      // Check for statistics cards
      expect(screen.getByText('Trámites Activos')).toBeInTheDocument()
      expect(screen.getByText('Trámites Completados')).toBeInTheDocument()
      expect(screen.getByText('Documentos Subidos')).toBeInTheDocument()
      expect(screen.getByText('Notificaciones')).toBeInTheDocument()
    })
  })

  it('displays quick actions panel', async () => {
    render(<CitizenDashboard />)

    // Check for quick action buttons
    expect(screen.getByText('Nuevo Trámite')).toBeInTheDocument()
    expect(screen.getByText('Consultar Estado')).toBeInTheDocument()
    expect(screen.getByText('Subir Documentos')).toBeInTheDocument()
    expect(screen.getByText('Chat con IA')).toBeInTheDocument()
  })

  it('displays recent procedures', async () => {
    render(<CitizenDashboard />)

    await waitFor(() => {
      expect(screen.getByText('Certificado de Residencia')).toBeInTheDocument()
      expect(screen.getByText('Licencia de Construcción')).toBeInTheDocument()
    })
  })

  it('handles quick action clicks', async () => {
    const mockPush = jest.fn()
    jest.doMock('next/navigation', () => ({
      useRouter: () => ({
        push: mockPush,
      }),
    }))

    render(<CitizenDashboard />)

    // Click on "Nuevo Trámite" button
    const newProcedureButton = screen.getByText('Nuevo Trámite')
    fireEvent.click(newProcedureButton)

    // Verify navigation was called
    await waitFor(() => {
      expect(mockPush).toHaveBeenCalledWith('/gestion-tramites')
    })
  })

  it('displays notifications widget', async () => {
    render(<CitizenDashboard />)

    // Check for notifications section
    expect(screen.getByText('Notificaciones Recientes')).toBeInTheDocument()
  })

  it('displays upcoming deadlines', async () => {
    render(<CitizenDashboard />)

    // Check for deadlines section
    expect(screen.getByText('Próximos Vencimientos')).toBeInTheDocument()
  })

  it('is accessible', async () => {
    const { container } = render(<CitizenDashboard />)

    // Check for proper heading structure
    const mainHeading = screen.getByRole('heading', { level: 1 })
    expect(mainHeading).toBeInTheDocument()

    // Check for proper button roles
    const buttons = screen.getAllByRole('button')
    expect(buttons.length).toBeGreaterThan(0)

    // Check for proper link roles
    const links = screen.getAllByRole('link')
    expect(links.length).toBeGreaterThan(0)

    // Check for proper landmark regions
    const main = container.querySelector('main')
    expect(main).toBeInTheDocument()
  })

  it('handles loading state', async () => {
    // Mock loading state
    jest.doMock('@/hooks/useAuth', () => ({
      useAuth: () => ({
        user: null,
        profile: null,
        loading: true,
        signOut: jest.fn(),
      }),
    }))

    render(<CitizenDashboard />)

    // Check for loading indicators
    expect(screen.getByTestId('dashboard-loading')).toBeInTheDocument()
  })

  it('handles error state', async () => {
    // Mock error in data fetching
    mockSupabase.from.mockImplementation(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          order: jest.fn(() => ({
            limit: jest.fn(() => Promise.resolve({
              data: null,
              error: { message: 'Database error' },
            })),
          })),
        })),
      })),
    }))

    render(<CitizenDashboard />)

    await waitFor(() => {
      // Should still render basic dashboard structure
      expect(screen.getByText('Ciudadano Test')).toBeInTheDocument()
    })
  })

  it('displays progress tracking', async () => {
    render(<CitizenDashboard />)

    // Check for progress indicators
    expect(screen.getByText('Tu Progreso')).toBeInTheDocument()
  })

  it('displays popular procedures recommendations', async () => {
    render(<CitizenDashboard />)

    // Check for recommendations section
    expect(screen.getByText('Trámites Populares')).toBeInTheDocument()
  })

  it('handles responsive design', async () => {
    // Mock mobile viewport
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 375,
    })

    render(<CitizenDashboard />)

    // Check that mobile-specific elements are present
    const container = screen.getByTestId('dashboard-container')
    expect(container).toHaveClass('mobile-responsive')
  })

  it('updates data in real-time', async () => {
    render(<CitizenDashboard />)

    // Simulate data update
    await waitFor(() => {
      expect(screen.getByText('Certificado de Residencia')).toBeInTheDocument()
    })

    // Mock updated data
    mockSupabase.from.mockImplementation(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          order: jest.fn(() => ({
            limit: jest.fn(() => Promise.resolve({
              data: [
                {
                  id: 1,
                  procedure_name: 'Certificado de Residencia',
                  status: 'completado',
                  created_at: '2024-01-01T00:00:00Z',
                  updated_at: '2024-01-03T00:00:00Z',
                },
              ],
              error: null,
            })),
          })),
        })),
      })),
    }))

    // Trigger refresh
    const refreshButton = screen.getByLabelText('Actualizar datos')
    fireEvent.click(refreshButton)

    await waitFor(() => {
      // Check that status was updated
      expect(screen.getByText('completado')).toBeInTheDocument()
    })
  })
})
