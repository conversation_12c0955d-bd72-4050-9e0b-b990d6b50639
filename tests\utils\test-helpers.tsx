import React from 'react'
import { createClient } from '@supabase/supabase-js'

// Test utilities for authentication testing

export const mockSupabaseClient = {
  auth: {
    getUser: jest.fn(),
    signInWithPassword: jest.fn(),
    signUp: jest.fn(),
    signOut: jest.fn(),
    resetPasswordForEmail: jest.fn(),
    updateUser: jest.fn(),
    onAuthStateChange: jest.fn(() => ({
      data: { subscription: { unsubscribe: jest.fn() } },
    })),
  },
  from: jest.fn(() => ({
    select: jest.fn(() => ({
      eq: jest.fn(() => ({
        single: jest.fn(),
        limit: jest.fn(() => ({
          single: jest.fn(),
        })),
      })),
      limit: jest.fn(() => ({
        single: jest.fn(),
      })),
    })),
    insert: jest.fn(() => ({
      select: jest.fn(),
    })),
    update: jest.fn(() => ({
      eq: jest.fn(() => ({
        select: jest.fn(),
      })),
    })),
    delete: jest.fn(() => ({
      eq: jest.fn(),
    })),
  })),
}

// Mock user data for testing
export const mockUsers = {
  ciudadano: {
    id: 'ciudadano-123',
    email: '<EMAIL>',
    email_confirmed_at: '2023-01-01T00:00:00Z',
    profile: {
      id: 'ciudadano-123',
      full_name: 'Ciudadano Test',
      role: 'ciudadano',
      document_type: 'CC',
      document_number: '12345678',
      phone: '**********',
      address: 'Calle 123 #45-67',
      created_at: '2023-01-01T00:00:00Z',
      updated_at: '2023-01-01T00:00:00Z',
    },
  },
  admin: {
    id: 'admin-123',
    email: '<EMAIL>',
    email_confirmed_at: '2023-01-01T00:00:00Z',
    profile: {
      id: 'admin-123',
      full_name: 'Admin Test',
      role: 'admin',
      document_type: 'CC',
      document_number: '87654321',
      phone: '3007654321',
      department: 'Sistemas',
      position: 'Administrador',
      created_at: '2023-01-01T00:00:00Z',
      updated_at: '2023-01-01T00:00:00Z',
    },
  },
  superAdmin: {
    id: 'super-admin-123',
    email: '<EMAIL>',
    email_confirmed_at: '2023-01-01T00:00:00Z',
    profile: {
      id: 'super-admin-123',
      full_name: 'Super Admin Test',
      role: 'super_admin',
      document_type: 'CC',
      document_number: '11111111',
      phone: '3001111111',
      department: 'Dirección',
      position: 'Super Administrador',
      created_at: '2023-01-01T00:00:00Z',
      updated_at: '2023-01-01T00:00:00Z',
    },
  },
}

// Helper functions for setting up mock responses
export const setupMockAuthSuccess = (userType: keyof typeof mockUsers) => {
  const user = mockUsers[userType]

  if (!user) {
    throw new Error(`Mock user not found for type: ${userType}`)
  }

  mockSupabaseClient.auth.getUser.mockResolvedValue({
    data: { user },
    error: null,
  })

  if (user.profile) {
    mockSupabaseClient.from().select().eq().single.mockResolvedValue({
      data: user.profile,
      error: null,
    })
  }

  return user
}

export const setupMockAuthFailure = (errorMessage: string = 'Authentication failed') => {
  mockSupabaseClient.auth.getUser.mockResolvedValue({
    data: { user: null },
    error: { message: errorMessage },
  })
  
  mockSupabaseClient.from().select().eq().single.mockResolvedValue({
    data: null,
    error: { message: 'User not found' },
  })
}

export const setupMockLoginSuccess = (userType: keyof typeof mockUsers) => {
  const user = mockUsers[userType]
  
  mockSupabaseClient.auth.signInWithPassword.mockResolvedValue({
    data: { user },
    error: null,
  })
  
  return user
}

export const setupMockLoginFailure = (errorMessage: string = 'Invalid credentials') => {
  mockSupabaseClient.auth.signInWithPassword.mockResolvedValue({
    data: { user: null },
    error: { message: errorMessage },
  })
}

export const setupMockSignupSuccess = (email: string, fullName: string) => {
  const newUser = {
    id: `new-user-${Date.now()}`,
    email,
    email_confirmed_at: null,
  }
  
  mockSupabaseClient.auth.signUp.mockResolvedValue({
    data: { user: newUser },
    error: null,
  })
  
  return newUser
}

export const setupMockSignupFailure = (errorMessage: string = 'User already exists') => {
  mockSupabaseClient.auth.signUp.mockResolvedValue({
    data: { user: null },
    error: { message: errorMessage },
  })
}

// Helper for testing role-based access
export const testRoleAccess = (userRole: string, allowedRoles: string[]) => {
  const roleHierarchy: Record<string, string[]> = {
    ciudadano: ['ciudadano'],
    admin: ['ciudadano', 'admin'],
    super_admin: ['ciudadano', 'admin', 'super_admin'],
  }
  
  const userRoles = roleHierarchy[userRole] || []
  return allowedRoles.some(role => userRoles.includes(role))
}

// Helper for creating test components with providers
export const createTestWrapper = (initialAuth?: any) => {
  return ({ children }: { children: React.ReactNode }) => {
    // This would wrap children with necessary providers
    // For now, just return children
    return <>{children}</>
  }
}

// Helper for waiting in tests
export const waitFor = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

// Helper for generating test data
export const generateTestUser = (overrides: Partial<typeof mockUsers.ciudadano> = {}) => {
  const timestamp = Date.now()
  return {
    id: `test-user-${timestamp}`,
    email: `test-${timestamp}@example.com`,
    email_confirmed_at: '2023-01-01T00:00:00Z',
    profile: {
      id: `test-user-${timestamp}`,
      full_name: `Test User ${timestamp}`,
      role: 'ciudadano' as const,
      document_type: 'CC',
      document_number: `${timestamp}`.slice(-8),
      phone: '**********',
      address: 'Test Address',
      created_at: '2023-01-01T00:00:00Z',
      updated_at: '2023-01-01T00:00:00Z',
      ...overrides,
    },
  }
}

// Helper for cleaning up after tests
export const cleanupMocks = () => {
  jest.clearAllMocks()
  
  // Reset all mock implementations
  Object.values(mockSupabaseClient.auth).forEach(mock => {
    if (jest.isMockFunction(mock)) {
      mock.mockReset()
    }
  })
}

// Helper for testing form validation
export const testFormValidation = async (
  form: HTMLFormElement,
  requiredFields: string[],
  getByTestId: (testId: string) => HTMLElement
) => {
  // Submit form without filling required fields
  form.submit()
  
  // Check that validation errors appear
  for (const field of requiredFields) {
    const errorElement = getByTestId(`${field}-error`)
    expect(errorElement).toBeInTheDocument()
  }
}

// Helper for testing accessibility
export const testAccessibility = async (container: HTMLElement) => {
  // This would run accessibility tests
  // For now, just check basic requirements
  const headings = container.querySelectorAll('h1, h2, h3, h4, h5, h6')
  const buttons = container.querySelectorAll('button')
  const inputs = container.querySelectorAll('input, textarea, select')
  
  // Check that buttons have accessible names
  buttons.forEach(button => {
    expect(
      button.textContent || 
      button.getAttribute('aria-label') || 
      button.getAttribute('title')
    ).toBeTruthy()
  })
  
  // Check that inputs have labels
  inputs.forEach(input => {
    const id = input.getAttribute('id')
    if (id) {
      const label = container.querySelector(`label[for="${id}"]`)
      expect(label || input.getAttribute('aria-label')).toBeTruthy()
    }
  })
}
