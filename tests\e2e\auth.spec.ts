import { test, expect } from '@playwright/test'

// Test data
const testUser = {
  email: '<EMAIL>',
  password: 'TestPassword123!',
  fullName: 'Test User',
  document: '12345678',
}

const adminUser = {
  email: '<EMAIL>',
  password: 'AdminPassword123!',
  fullName: 'Admin User',
}

test.describe('Authentication Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('/')
  })

  test.describe('Login Flow', () => {
    test('should successfully log in with valid credentials', async ({ page }) => {
      // Navigate to login page
      await page.click('text=Iniciar Sesión')
      await expect(page).toHaveURL('/auth/login')

      // Fill in login form
      await page.fill('[data-testid="email-input"]', testUser.email)
      await page.fill('[data-testid="password-input"]', testUser.password)
      
      // Submit form
      await page.click('[data-testid="login-submit"]')

      // Should redirect to dashboard
      await expect(page).toHaveURL('/dashboard')
      
      // Should show user name in navigation
      await expect(page.locator('[data-testid="user-name"]')).toContainText(testUser.fullName)
    })

    test('should show error for invalid credentials', async ({ page }) => {
      await page.click('text=Iniciar Sesión')
      
      await page.fill('[data-testid="email-input"]', testUser.email)
      await page.fill('[data-testid="password-input"]', 'wrongpassword')
      
      await page.click('[data-testid="login-submit"]')

      // Should show error message
      await expect(page.locator('[data-testid="error-message"]')).toContainText('Credenciales inválidas')
      
      // Should remain on login page
      await expect(page).toHaveURL('/auth/login')
    })

    test('should validate required fields', async ({ page }) => {
      await page.click('text=Iniciar Sesión')
      
      // Try to submit without filling fields
      await page.click('[data-testid="login-submit"]')

      // Should show validation errors
      await expect(page.locator('[data-testid="email-error"]')).toContainText('El correo electrónico es requerido')
      await expect(page.locator('[data-testid="password-error"]')).toContainText('La contraseña es requerida')
    })
  })

  test.describe('Registration Flow', () => {
    test('should successfully register a new user', async ({ page }) => {
      await page.click('text=Registrarse')
      await expect(page).toHaveURL('/auth/register')

      // Fill in registration form
      await page.fill('[data-testid="fullname-input"]', testUser.fullName)
      await page.fill('[data-testid="email-input"]', `new-${Date.now()}@example.com`)
      await page.fill('[data-testid="document-input"]', testUser.document)
      await page.fill('[data-testid="password-input"]', testUser.password)
      await page.fill('[data-testid="confirm-password-input"]', testUser.password)
      
      await page.click('[data-testid="register-submit"]')

      // Should show success message or redirect to verification page
      await expect(page.locator('[data-testid="success-message"]')).toContainText('Registro exitoso')
    })

    test('should validate password confirmation', async ({ page }) => {
      await page.click('text=Registrarse')
      
      await page.fill('[data-testid="fullname-input"]', testUser.fullName)
      await page.fill('[data-testid="email-input"]', '<EMAIL>')
      await page.fill('[data-testid="document-input"]', testUser.document)
      await page.fill('[data-testid="password-input"]', testUser.password)
      await page.fill('[data-testid="confirm-password-input"]', 'differentpassword')
      
      await page.click('[data-testid="register-submit"]')

      await expect(page.locator('[data-testid="confirm-password-error"]')).toContainText('Las contraseñas no coinciden')
    })
  })

  test.describe('Protected Routes', () => {
    test('should redirect to login when accessing protected route without authentication', async ({ page }) => {
      // Try to access dashboard directly
      await page.goto('/dashboard')
      
      // Should redirect to login
      await expect(page).toHaveURL('/auth/login')
    })

    test('should allow access to dashboard after login', async ({ page }) => {
      // Login first
      await page.goto('/auth/login')
      await page.fill('[data-testid="email-input"]', testUser.email)
      await page.fill('[data-testid="password-input"]', testUser.password)
      await page.click('[data-testid="login-submit"]')

      // Should be on dashboard
      await expect(page).toHaveURL('/dashboard')
      await expect(page.locator('h1')).toContainText('Dashboard')
    })

    test('should deny access to admin routes for regular users', async ({ page }) => {
      // Login as regular user
      await page.goto('/auth/login')
      await page.fill('[data-testid="email-input"]', testUser.email)
      await page.fill('[data-testid="password-input"]', testUser.password)
      await page.click('[data-testid="login-submit"]')

      // Try to access admin page
      await page.goto('/admin')
      
      // Should show access denied
      await expect(page.locator('[data-testid="access-denied"]')).toContainText('Acceso Denegado')
    })

    test('should allow admin access to admin routes', async ({ page }) => {
      // Login as admin
      await page.goto('/auth/login')
      await page.fill('[data-testid="email-input"]', adminUser.email)
      await page.fill('[data-testid="password-input"]', adminUser.password)
      await page.click('[data-testid="login-submit"]')

      // Access admin page
      await page.goto('/admin')
      
      // Should show admin dashboard
      await expect(page.locator('h1')).toContainText('Panel Administrativo')
    })
  })

  test.describe('Navigation', () => {
    test('should show role-appropriate navigation items', async ({ page }) => {
      // Login as regular user
      await page.goto('/auth/login')
      await page.fill('[data-testid="email-input"]', testUser.email)
      await page.fill('[data-testid="password-input"]', testUser.password)
      await page.click('[data-testid="login-submit"]')

      // Should show citizen navigation items
      await expect(page.locator('[data-testid="nav-dashboard"]')).toBeVisible()
      await expect(page.locator('[data-testid="nav-tramites"]')).toBeVisible()
      await expect(page.locator('[data-testid="nav-chat"]')).toBeVisible()
      
      // Should not show admin navigation
      await expect(page.locator('[data-testid="nav-admin"]')).not.toBeVisible()
    })

    test('should show admin navigation for admin users', async ({ page }) => {
      // Login as admin
      await page.goto('/auth/login')
      await page.fill('[data-testid="email-input"]', adminUser.email)
      await page.fill('[data-testid="password-input"]', adminUser.password)
      await page.click('[data-testid="login-submit"]')

      // Should show admin navigation items
      await expect(page.locator('[data-testid="nav-dashboard"]')).toBeVisible()
      await expect(page.locator('[data-testid="nav-admin"]')).toBeVisible()
      await expect(page.locator('[data-testid="nav-chat"]')).toBeVisible()
    })
  })

  test.describe('User Profile', () => {
    test('should display user profile information', async ({ page }) => {
      // Login
      await page.goto('/auth/login')
      await page.fill('[data-testid="email-input"]', testUser.email)
      await page.fill('[data-testid="password-input"]', testUser.password)
      await page.click('[data-testid="login-submit"]')

      // Click on user profile
      await page.click('[data-testid="user-profile-button"]')
      
      // Should show user menu
      await expect(page.locator('[data-testid="user-menu"]')).toBeVisible()
      await expect(page.locator('[data-testid="user-email"]')).toContainText(testUser.email)
    })

    test('should navigate to profile page', async ({ page }) => {
      // Login
      await page.goto('/auth/login')
      await page.fill('[data-testid="email-input"]', testUser.email)
      await page.fill('[data-testid="password-input"]', testUser.password)
      await page.click('[data-testid="login-submit"]')

      // Click on user profile and then profile link
      await page.click('[data-testid="user-profile-button"]')
      await page.click('[data-testid="profile-link"]')
      
      // Should navigate to profile page
      await expect(page).toHaveURL('/perfil')
      await expect(page.locator('h1')).toContainText('Mi Perfil')
    })
  })

  test.describe('Logout', () => {
    test('should successfully log out user', async ({ page }) => {
      // Login first
      await page.goto('/auth/login')
      await page.fill('[data-testid="email-input"]', testUser.email)
      await page.fill('[data-testid="password-input"]', testUser.password)
      await page.click('[data-testid="login-submit"]')

      // Logout
      await page.click('[data-testid="user-profile-button"]')
      await page.click('[data-testid="logout-button"]')
      
      // Should redirect to login page
      await expect(page).toHaveURL('/auth/login')
      
      // Should not be able to access protected routes
      await page.goto('/dashboard')
      await expect(page).toHaveURL('/auth/login')
    })
  })

  test.describe('Session Persistence', () => {
    test('should maintain session across page reloads', async ({ page }) => {
      // Login
      await page.goto('/auth/login')
      await page.fill('[data-testid="email-input"]', testUser.email)
      await page.fill('[data-testid="password-input"]', testUser.password)
      await page.click('[data-testid="login-submit"]')

      // Reload page
      await page.reload()
      
      // Should still be logged in
      await expect(page).toHaveURL('/dashboard')
      await expect(page.locator('[data-testid="user-name"]')).toContainText(testUser.fullName)
    })

    test('should handle session expiration', async ({ page }) => {
      // This test would require mocking session expiration
      // or waiting for actual session timeout
      test.skip('Session expiration test requires backend setup')
    })
  })

  test.describe('Mobile Navigation', () => {
    test('should work on mobile devices', async ({ page }) => {
      // Set mobile viewport
      await page.setViewportSize({ width: 375, height: 667 })
      
      // Login
      await page.goto('/auth/login')
      await page.fill('[data-testid="email-input"]', testUser.email)
      await page.fill('[data-testid="password-input"]', testUser.password)
      await page.click('[data-testid="login-submit"]')

      // Should show mobile menu button
      await expect(page.locator('[data-testid="mobile-menu-button"]')).toBeVisible()
      
      // Click mobile menu
      await page.click('[data-testid="mobile-menu-button"]')
      
      // Should show mobile navigation
      await expect(page.locator('[data-testid="mobile-menu"]')).toBeVisible()
    })
  })
})
