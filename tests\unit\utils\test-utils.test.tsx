import { mockUsers, setupMockAuthSuccess, cleanupMocks } from '../../utils/test-helpers'

describe('Test Utilities', () => {
  afterEach(() => {
    cleanupMocks()
  })

  describe('Mock Users', () => {
    it('should have ciudadano mock user', () => {
      expect(mockUsers.ciudadano).toBeDefined()
      expect(mockUsers.ciudadano.id).toBe('ciudadano-123')
      expect(mockUsers.ciudadano.email).toBe('<EMAIL>')
      expect(mockUsers.ciudadano.profile.role).toBe('ciudadano')
    })

    it('should have admin mock user', () => {
      expect(mockUsers.admin).toBeDefined()
      expect(mockUsers.admin.id).toBe('admin-123')
      expect(mockUsers.admin.email).toBe('<EMAIL>')
      expect(mockUsers.admin.profile.role).toBe('admin')
    })

    it('should have super admin mock user', () => {
      expect(mockUsers.superAdmin).toBeDefined()
      expect(mockUsers.superAdmin.id).toBe('super-admin-123')
      expect(mockUsers.superAdmin.email).toBe('<EMAIL>')
      expect(mockUsers.superAdmin.profile.role).toBe('super_admin')
    })
  })

  describe('Auth Setup', () => {
    it('should setup mock auth for ciudadano', () => {
      const result = setupMockAuthSuccess('ciudadano')
      expect(result).toBeDefined()
    })

    it('should setup mock auth for admin', () => {
      const result = setupMockAuthSuccess('admin')
      expect(result).toBeDefined()
    })

    it('should setup mock auth for superAdmin', () => {
      const result = setupMockAuthSuccess('superAdmin')
      expect(result).toBeDefined()
    })
  })

  describe('Cleanup', () => {
    it('should cleanup mocks without errors', () => {
      expect(() => cleanupMocks()).not.toThrow()
    })
  })
})
