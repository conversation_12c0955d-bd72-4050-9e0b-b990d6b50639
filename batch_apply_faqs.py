#!/usr/bin/env python3
"""
Batch apply FAQ chunks efficiently
"""

import os
import re

def combine_chunks(start_chunk, end_chunk):
    """Combine multiple chunks into a single SQL file"""
    
    combined_sql = []
    total_questions = 0
    
    for chunk_num in range(start_chunk, end_chunk + 1):
        filename = f"corrected_faq_chunk_{chunk_num:02d}.sql"
        
        if not os.path.exists(filename):
            print(f"Warning: {filename} not found, skipping...")
            continue
        
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Extract INSERT statements
        insert_statements = re.findall(r'INSERT INTO municipal_faqs.*?;', content, re.DOTALL)
        combined_sql.extend(insert_statements)
        total_questions += len(insert_statements)
        
        print(f"Added {len(insert_statements)} questions from {filename}")
    
    # Write combined file
    output_filename = f"combined_chunks_{start_chunk:02d}_to_{end_chunk:02d}.sql"
    
    with open(output_filename, 'w', encoding='utf-8') as f:
        f.write(f"-- Combined FAQ Chunks {start_chunk} to {end_chunk}\n")
        f.write(f"-- Total questions: {total_questions}\n\n")
        f.write("\n".join(combined_sql))
        f.write("\n")
    
    print(f"Created {output_filename} with {total_questions} questions")
    return output_filename, total_questions

def create_batch_files():
    """Create batch files for efficient application"""
    
    print("Creating batch files for efficient FAQ application...")
    
    # Create batches of 5 chunks each (75 questions per batch)
    batches = [
        (3, 7),   # Chunks 3-7 (75 questions)
        (8, 12),  # Chunks 8-12 (75 questions)
        (13, 17), # Chunks 13-17 (75 questions)
        (18, 22), # Chunks 18-22 (75 questions)
        (23, 26)  # Chunks 23-26 (remaining questions)
    ]
    
    batch_files = []
    
    for i, (start, end) in enumerate(batches, 1):
        print(f"\nCreating batch {i}: chunks {start}-{end}")
        filename, count = combine_chunks(start, end)
        batch_files.append((filename, count))
    
    return batch_files

def main():
    """Main function"""
    print("FAQ Batch Combiner")
    print("=" * 25)
    
    try:
        batch_files = create_batch_files()
        
        print(f"\n✅ Successfully created {len(batch_files)} batch files:")
        total_questions = 0
        
        for i, (filename, count) in enumerate(batch_files, 1):
            print(f"   {i}. {filename} ({count} questions)")
            total_questions += count
        
        print(f"\n📊 Total questions in batches: {total_questions}")
        print(f"📋 Ready for database application!")
        
        return 0
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
