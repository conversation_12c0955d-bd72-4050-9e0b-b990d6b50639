import { renderHook, act } from '@testing-library/react'
import { useDebounce } from '@/hooks/useDebounce'

// Mock de timers para controlar el tiempo en los tests
jest.useFakeTimers()

describe('useDebounce', () => {
  afterEach(() => {
    jest.clearAllTimers()
  })

  it('debería retornar el valor inicial inmediatamente', () => {
    const { result } = renderHook(() => useDebounce('initial', 500))
    
    expect(result.current).toBe('initial')
  })

  it('debería debounce el valor después del delay especificado', () => {
    const { result, rerender } = renderHook(
      ({ value, delay }) => useDebounce(value, delay),
      {
        initialProps: { value: 'initial', delay: 500 }
      }
    )

    expect(result.current).toBe('initial')

    // Cambiar el valor
    rerender({ value: 'updated', delay: 500 })

    // El valor no debería cambiar inmediatamente
    expect(result.current).toBe('initial')

    // Avanzar el tiempo pero no completamente
    act(() => {
      jest.advanceTimersByTime(300)
    })

    // Aún no debería haber cambiado
    expect(result.current).toBe('initial')

    // Completar el delay
    act(() => {
      jest.advanceTimersByTime(200)
    })

    // Ahora debería haber cambiado
    expect(result.current).toBe('updated')
  })

  it('debería cancelar el timer anterior cuando el valor cambia rápidamente', () => {
    const { result, rerender } = renderHook(
      ({ value, delay }) => useDebounce(value, delay),
      {
        initialProps: { value: 'initial', delay: 500 }
      }
    )

    // Cambiar el valor múltiples veces rápidamente
    rerender({ value: 'first', delay: 500 })
    
    act(() => {
      jest.advanceTimersByTime(200)
    })
    
    rerender({ value: 'second', delay: 500 })
    
    act(() => {
      jest.advanceTimersByTime(200)
    })
    
    rerender({ value: 'final', delay: 500 })

    // El valor aún debería ser el inicial
    expect(result.current).toBe('initial')

    // Completar el delay
    act(() => {
      jest.advanceTimersByTime(500)
    })

    // Debería mostrar solo el último valor
    expect(result.current).toBe('final')
  })

  it('debería manejar diferentes tipos de datos', () => {
    // Test con números
    const { result: numberResult, rerender: numberRerender } = renderHook(
      ({ value, delay }) => useDebounce(value, delay),
      {
        initialProps: { value: 0, delay: 300 }
      }
    )

    numberRerender({ value: 42, delay: 300 })
    
    act(() => {
      jest.advanceTimersByTime(300)
    })

    expect(numberResult.current).toBe(42)

    // Test con objetos
    const initialObj = { name: 'initial' }
    const updatedObj = { name: 'updated' }

    const { result: objectResult, rerender: objectRerender } = renderHook(
      ({ value, delay }) => useDebounce(value, delay),
      {
        initialProps: { value: initialObj, delay: 300 }
      }
    )

    objectRerender({ value: updatedObj, delay: 300 })
    
    act(() => {
      jest.advanceTimersByTime(300)
    })

    expect(objectResult.current).toBe(updatedObj)
  })

  it('debería manejar delay de 0', () => {
    const { result, rerender } = renderHook(
      ({ value, delay }) => useDebounce(value, delay),
      {
        initialProps: { value: 'initial', delay: 0 }
      }
    )

    rerender({ value: 'updated', delay: 0 })

    // Con delay 0, debería cambiar inmediatamente después del próximo tick
    act(() => {
      jest.advanceTimersByTime(0)
    })

    expect(result.current).toBe('updated')
  })

  it('debería limpiar el timeout al desmontar', () => {
    const clearTimeoutSpy = jest.spyOn(global, 'clearTimeout')
    
    const { unmount } = renderHook(() => useDebounce('test', 500))

    unmount()

    expect(clearTimeoutSpy).toHaveBeenCalled()
    
    clearTimeoutSpy.mockRestore()
  })

  it('debería manejar cambios en el delay', () => {
    const { result, rerender } = renderHook(
      ({ value, delay }) => useDebounce(value, delay),
      {
        initialProps: { value: 'initial', delay: 500 }
      }
    )

    // Cambiar valor y delay
    rerender({ value: 'updated', delay: 200 })

    // Avanzar solo el nuevo delay
    act(() => {
      jest.advanceTimersByTime(200)
    })

    expect(result.current).toBe('updated')
  })

  it('debería funcionar correctamente en un caso de uso real de búsqueda', () => {
    const { result, rerender } = renderHook(
      ({ searchTerm }) => useDebounce(searchTerm, 300),
      {
        initialProps: { searchTerm: '' }
      }
    )

    // Simular escritura rápida
    rerender({ searchTerm: 'l' })
    rerender({ searchTerm: 'li' })
    rerender({ searchTerm: 'lic' })
    rerender({ searchTerm: 'lice' })
    rerender({ searchTerm: 'licen' })
    rerender({ searchTerm: 'licencia' })

    // No debería haber cambiado aún
    expect(result.current).toBe('')

    // Completar el delay
    act(() => {
      jest.advanceTimersByTime(300)
    })

    // Ahora debería mostrar el término completo
    expect(result.current).toBe('licencia')
  })
})
