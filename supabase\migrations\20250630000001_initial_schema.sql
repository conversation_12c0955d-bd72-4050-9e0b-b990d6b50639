-- Initial schema for Sistema de Atención Ciudadana - Chía
-- Based on PLAN_IMPLEMENTACION_SISTEMA_ATENCION_CIUDADANA.md

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "vector";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Create custom types
CREATE TYPE user_role AS ENUM ('ciudadano', 'admin', 'super_admin');
CREATE TYPE procedure_status AS ENUM ('draft', 'active', 'inactive', 'archived');
CREATE TYPE tramite_status AS ENUM ('pending', 'in_progress', 'completed', 'rejected', 'cancelled');
CREATE TYPE document_type AS ENUM ('cedula', 'pasaporte', 'cedula_extranjeria', 'nit');

-- =============================================
-- ROLES AND PERMISSIONS SYSTEM
-- =============================================

-- Roles table
CREATE TABLE roles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT UNIQUE NOT NULL,
  description TEXT,
  permissions JSONB DEFAULT '{}',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default roles
INSERT INTO roles (name, description, permissions) VALUES
('ciudadano', 'Ciudadano del municipio', '{"can_view_own_data": true, "can_create_tramites": true, "can_chat": true}'),
('admin', 'Administrador de dependencia', '{"can_manage_dependency": true, "can_view_reports": true, "can_assign_tramites": true}'),
('super_admin', 'Super administrador del sistema', '{"can_manage_all": true, "can_configure_system": true, "can_view_all_data": true}');

-- Extended profiles table
CREATE TABLE profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  full_name TEXT,
  document_type document_type,
  document_number TEXT UNIQUE,
  phone TEXT,
  address TEXT,
  role_id UUID REFERENCES roles(id) DEFAULT (SELECT id FROM roles WHERE name = 'ciudadano'),
  dependency_id UUID, -- Will reference dependencies table
  metadata JSONB DEFAULT '{}',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- ORGANIZATIONAL STRUCTURE
-- =============================================

-- Dependencies (Dependencias gubernamentales)
CREATE TABLE dependencies (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  code TEXT UNIQUE NOT NULL,
  name TEXT NOT NULL,
  acronym TEXT,
  description TEXT,
  parent_id UUID REFERENCES dependencies(id),
  contact_email TEXT,
  contact_phone TEXT,
  address TEXT,
  is_active BOOLEAN DEFAULT true,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Subdependencies
CREATE TABLE subdependencies (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  dependency_id UUID REFERENCES dependencies(id) NOT NULL,
  code TEXT NOT NULL,
  name TEXT NOT NULL,
  acronym TEXT,
  description TEXT,
  contact_email TEXT,
  contact_phone TEXT,
  is_active BOOLEAN DEFAULT true,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(dependency_id, code)
);

-- =============================================
-- PROCEDURES AND TRAMITES SYSTEM
-- =============================================

-- Procedures (Trámites)
CREATE TABLE procedures (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  dependency_id UUID REFERENCES dependencies(id),
  subdependency_id UUID REFERENCES subdependencies(id),
  response_time TEXT,
  has_cost BOOLEAN DEFAULT false,
  cost_description TEXT,
  requirements JSONB DEFAULT '[]',
  documents_required JSONB DEFAULT '[]',
  suit_url TEXT,
  gov_url TEXT,
  form_required BOOLEAN DEFAULT false,
  status procedure_status DEFAULT 'draft',
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Administrative Procedures (OPA)
CREATE TABLE administrative_procedures (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  dependency_id UUID REFERENCES dependencies(id),
  subdependency_id UUID REFERENCES subdependencies(id),
  code TEXT NOT NULL,
  description TEXT NOT NULL,
  status procedure_status DEFAULT 'active',
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- TRAMITES INSTANCES (User-initiated procedures)
-- =============================================

-- Tramite instances (when a user starts a procedure)
CREATE TABLE tramite_instances (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  procedure_id UUID REFERENCES procedures(id),
  user_id UUID REFERENCES profiles(id) NOT NULL,
  status tramite_status DEFAULT 'pending',
  form_data JSONB DEFAULT '{}',
  assigned_to UUID REFERENCES profiles(id),
  notes TEXT,
  internal_notes TEXT, -- Only visible to admins
  estimated_completion_date DATE,
  completed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tramite status history
CREATE TABLE tramite_status_history (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tramite_id UUID REFERENCES tramite_instances(id) NOT NULL,
  previous_status tramite_status,
  new_status tramite_status NOT NULL,
  changed_by UUID REFERENCES profiles(id),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- KNOWLEDGE BASE AND AI SYSTEM
-- =============================================

-- Knowledge base documents
CREATE TABLE knowledge_base (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  summary TEXT,
  category TEXT,
  tags TEXT[],
  source_url TEXT,
  embedding vector(1536), -- OpenAI embedding dimension
  metadata JSONB DEFAULT '{}',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Chat conversations
CREATE TABLE chat_conversations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES profiles(id),
  session_id TEXT,
  title TEXT,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Chat messages
CREATE TABLE chat_messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  conversation_id UUID REFERENCES chat_conversations(id) NOT NULL,
  role TEXT NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
  content TEXT NOT NULL,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- FILE MANAGEMENT
-- =============================================

-- Document attachments
CREATE TABLE document_attachments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tramite_id UUID REFERENCES tramite_instances(id),
  filename TEXT NOT NULL,
  original_filename TEXT NOT NULL,
  file_path TEXT NOT NULL,
  file_size INTEGER,
  mime_type TEXT,
  uploaded_by UUID REFERENCES profiles(id) NOT NULL,
  is_public BOOLEAN DEFAULT false,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- AUDIT AND LOGGING
-- =============================================

-- Audit log
CREATE TABLE audit_log (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  table_name TEXT NOT NULL,
  record_id UUID,
  action TEXT NOT NULL CHECK (action IN ('INSERT', 'UPDATE', 'DELETE')),
  old_values JSONB,
  new_values JSONB,
  user_id UUID REFERENCES profiles(id),
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- INDEXES FOR PERFORMANCE
-- =============================================

-- Profiles indexes
CREATE INDEX idx_profiles_email ON profiles(email);
CREATE INDEX idx_profiles_document ON profiles(document_type, document_number);
CREATE INDEX idx_profiles_role ON profiles(role_id);
CREATE INDEX idx_profiles_dependency ON profiles(dependency_id);

-- Procedures indexes
CREATE INDEX idx_procedures_dependency ON procedures(dependency_id);
CREATE INDEX idx_procedures_subdependency ON procedures(subdependency_id);
CREATE INDEX idx_procedures_status ON procedures(status);

-- Tramite instances indexes
CREATE INDEX idx_tramite_instances_user ON tramite_instances(user_id);
CREATE INDEX idx_tramite_instances_procedure ON tramite_instances(procedure_id);
CREATE INDEX idx_tramite_instances_status ON tramite_instances(status);
CREATE INDEX idx_tramite_instances_assigned ON tramite_instances(assigned_to);

-- Knowledge base indexes
CREATE INDEX idx_knowledge_base_category ON knowledge_base(category);
CREATE INDEX idx_knowledge_base_tags ON knowledge_base USING GIN(tags);
CREATE INDEX idx_knowledge_base_content_search ON knowledge_base USING GIN(to_tsvector('spanish', content));

-- Vector similarity search index (HNSW)
CREATE INDEX idx_knowledge_base_embedding ON knowledge_base USING hnsw (embedding vector_cosine_ops);

-- Chat indexes
CREATE INDEX idx_chat_conversations_user ON chat_conversations(user_id);
CREATE INDEX idx_chat_messages_conversation ON chat_messages(conversation_id);

-- Audit log indexes
CREATE INDEX idx_audit_log_table_record ON audit_log(table_name, record_id);
CREATE INDEX idx_audit_log_user ON audit_log(user_id);
CREATE INDEX idx_audit_log_created ON audit_log(created_at);

-- =============================================
-- TRIGGERS FOR AUDIT AND TIMESTAMPS
-- =============================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers to relevant tables
CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_dependencies_updated_at BEFORE UPDATE ON dependencies FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_subdependencies_updated_at BEFORE UPDATE ON subdependencies FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_procedures_updated_at BEFORE UPDATE ON procedures FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_administrative_procedures_updated_at BEFORE UPDATE ON administrative_procedures FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_tramite_instances_updated_at BEFORE UPDATE ON tramite_instances FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_knowledge_base_updated_at BEFORE UPDATE ON knowledge_base FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_chat_conversations_updated_at BEFORE UPDATE ON chat_conversations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
