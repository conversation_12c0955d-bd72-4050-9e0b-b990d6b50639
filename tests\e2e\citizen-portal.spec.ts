import { test, expect } from '@playwright/test'

// Test data
const testUser = {
  email: '<EMAIL>',
  password: 'TestPassword123!',
  fullName: 'Ciudadano Test E2E',
  documentNumber: '12345678',
  phone: '3001234567',
  address: 'Calle Test 123',
}

test.describe('Citizen Portal E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('/')
    
    // Mock authentication for testing
    await page.addInitScript(() => {
      // Mock localStorage for auth state
      localStorage.setItem('supabase.auth.token', JSON.stringify({
        access_token: 'mock-token',
        refresh_token: 'mock-refresh',
        user: {
          id: 'test-user-id',
          email: '<EMAIL>',
          email_confirmed_at: '2024-01-01T00:00:00Z',
        }
      }))
    })
  })

  test.describe('Dashboard Flow', () => {
    test('should display citizen dashboard with user information', async ({ page }) => {
      await page.goto('/dashboard')

      // Wait for dashboard to load
      await expect(page.getByText('Bienvenido')).toBeVisible()
      
      // Check user information is displayed
      await expect(page.getByText('Ciudadano Test')).toBeVisible()
      
      // Check statistics cards
      await expect(page.getByText('Trámites Activos')).toBeVisible()
      await expect(page.getByText('Trámites Completados')).toBeVisible()
      await expect(page.getByText('Documentos Subidos')).toBeVisible()
      await expect(page.getByText('Notificaciones')).toBeVisible()
      
      // Check quick actions
      await expect(page.getByText('Nuevo Trámite')).toBeVisible()
      await expect(page.getByText('Consultar Estado')).toBeVisible()
      await expect(page.getByText('Subir Documentos')).toBeVisible()
      await expect(page.getByText('Chat con IA')).toBeVisible()
    })

    test('should navigate to procedure management from quick actions', async ({ page }) => {
      await page.goto('/dashboard')
      
      // Click on "Nuevo Trámite" quick action
      await page.getByText('Nuevo Trámite').click()
      
      // Should navigate to procedure management
      await expect(page).toHaveURL('/gestion-tramites')
      await expect(page.getByText('Gestión de Trámites')).toBeVisible()
    })

    test('should display notifications and allow interaction', async ({ page }) => {
      await page.goto('/dashboard')
      
      // Check notifications section
      await expect(page.getByText('Notificaciones Recientes')).toBeVisible()
      
      // Click on a notification
      const notification = page.getByText('Documento aprobado').first()
      if (await notification.isVisible()) {
        await notification.click()
        
        // Should show notification details
        await expect(page.getByText('Detalles de la notificación')).toBeVisible()
      }
    })
  })

  test.describe('Procedure Search and Management', () => {
    test('should search and filter procedures correctly', async ({ page }) => {
      await page.goto('/consulta-tramites')
      
      // Wait for procedures to load
      await expect(page.getByText('Consulta de Trámites')).toBeVisible()
      
      // Search for specific procedure
      const searchInput = page.getByPlaceholder('Buscar trámites...')
      await searchInput.fill('certificado')
      
      // Wait for search results
      await page.waitForTimeout(1000) // Debounce delay
      
      // Should show filtered results
      await expect(page.getByText('Certificado de Residencia')).toBeVisible()
      
      // Apply category filter
      await page.getByText('Filtros').click()
      await page.getByText('Categoría').click()
      await page.getByText('Certificados').click()
      
      // Should show only certificate procedures
      await expect(page.getByText('Certificado de Residencia')).toBeVisible()
      
      // Clear filters
      await page.getByText('Limpiar Filtros').click()
      
      // Should show all procedures again
      await expect(page.getByText('Licencia de Construcción')).toBeVisible()
    })

    test('should start a new procedure', async ({ page }) => {
      await page.goto('/consulta-tramites')
      
      // Select a procedure
      await page.getByText('Certificado de Residencia').click()
      
      // Should show procedure details
      await expect(page.getByText('$15.000')).toBeVisible()
      await expect(page.getByText('5 días')).toBeVisible()
      await expect(page.getByText('Requisitos:')).toBeVisible()
      
      // Start the procedure
      await page.getByText('Iniciar Trámite').click()
      
      // Should navigate to procedure management
      await expect(page).toHaveURL('/gestion-tramites')
      await expect(page.getByText('Trámite iniciado exitosamente')).toBeVisible()
    })

    test('should manage active procedures', async ({ page }) => {
      await page.goto('/gestion-tramites')
      
      // Should show active procedures
      await expect(page.getByText('Mis Trámites')).toBeVisible()
      
      // Click on a procedure to see details
      const procedureCard = page.getByText('Certificado de Residencia').first()
      if (await procedureCard.isVisible()) {
        await procedureCard.click()
        
        // Should show procedure details
        await expect(page.getByText('Estado del trámite')).toBeVisible()
        await expect(page.getByText('Documentos requeridos')).toBeVisible()
        
        // Check document upload button
        await expect(page.getByText('Subir Documentos')).toBeVisible()
      }
    })
  })

  test.describe('Document Management', () => {
    test('should display document portal correctly', async ({ page }) => {
      await page.goto('/documentos')
      
      // Should show document portal
      await expect(page.getByText('Portal de Documentos')).toBeVisible()
      
      // Check statistics
      await expect(page.getByText('Total Documentos')).toBeVisible()
      await expect(page.getByText('Espacio Usado')).toBeVisible()
      
      // Check view toggles
      await expect(page.getByText('Cuadrícula')).toBeVisible()
      await expect(page.getByText('Lista')).toBeVisible()
      
      // Check upload button
      await expect(page.getByText('Subir Documento')).toBeVisible()
    })

    test('should upload a document successfully', async ({ page }) => {
      await page.goto('/documentos')
      
      // Click upload button
      await page.getByText('Subir Documento').click()
      
      // Should open upload modal
      await expect(page.getByText('Subir Nuevo Documento')).toBeVisible()
      
      // Create a test file
      const fileContent = 'Test document content'
      const fileName = 'test-document.pdf'
      
      // Upload file
      const fileInput = page.getByLabel('Seleccionar archivos')
      await fileInput.setInputFiles({
        name: fileName,
        mimeType: 'application/pdf',
        buffer: Buffer.from(fileContent),
      })
      
      // Should show file in upload list
      await expect(page.getByText(fileName)).toBeVisible()
      
      // Select procedure for the document
      await page.getByText('Seleccionar trámite').click()
      await page.getByText('Certificado de Residencia').click()
      
      // Submit upload
      await page.getByText('Subir Archivo').click()
      
      // Should show success message
      await expect(page.getByText('Documento subido exitosamente')).toBeVisible()
    })

    test('should filter and search documents', async ({ page }) => {
      await page.goto('/documentos')
      
      // Search for documents
      const searchInput = page.getByPlaceholder('Buscar documentos...')
      await searchInput.fill('cedula')
      
      // Should filter documents
      await expect(page.getByText('cedula.pdf')).toBeVisible()
      
      // Filter by procedure
      await page.getByText('Filtrar por trámite').click()
      await page.getByText('Certificado de Residencia').click()
      
      // Should show only documents for that procedure
      await expect(page.getByText('Certificado de Residencia')).toBeVisible()
      
      // Switch to list view
      await page.getByText('Lista').click()
      
      // Should change to list layout
      await expect(page.getByTestId('document-list')).toBeVisible()
    })
  })

  test.describe('Accessibility', () => {
    test('should be keyboard navigable', async ({ page }) => {
      await page.goto('/dashboard')
      
      // Tab through interactive elements
      await page.keyboard.press('Tab')
      
      // Should focus on first interactive element
      const focusedElement = page.locator(':focus')
      await expect(focusedElement).toBeVisible()
      
      // Continue tabbing
      await page.keyboard.press('Tab')
      await page.keyboard.press('Tab')
      
      // Should be able to activate with Enter
      await page.keyboard.press('Enter')
    })

    test('should have proper ARIA labels and roles', async ({ page }) => {
      await page.goto('/dashboard')
      
      // Check for main landmark
      await expect(page.locator('main')).toBeVisible()
      
      // Check for proper headings
      await expect(page.locator('h1')).toBeVisible()
      
      // Check for proper button labels
      const buttons = page.locator('button')
      const buttonCount = await buttons.count()
      
      for (let i = 0; i < buttonCount; i++) {
        const button = buttons.nth(i)
        const hasLabel = await button.evaluate(el => 
          el.textContent || 
          el.getAttribute('aria-label') || 
          el.getAttribute('title')
        )
        expect(hasLabel).toBeTruthy()
      }
    })
  })

  test.describe('Responsive Design', () => {
    test('should work on mobile devices', async ({ page }) => {
      // Set mobile viewport
      await page.setViewportSize({ width: 375, height: 667 })
      
      await page.goto('/dashboard')
      
      // Should show mobile-optimized layout
      await expect(page.getByText('Ciudadano Test')).toBeVisible()
      
      // Check that mobile navigation works
      const menuButton = page.getByLabel('Abrir menú')
      if (await menuButton.isVisible()) {
        await menuButton.click()
        await expect(page.getByText('Dashboard')).toBeVisible()
      }
    })

    test('should work on tablet devices', async ({ page }) => {
      // Set tablet viewport
      await page.setViewportSize({ width: 768, height: 1024 })
      
      await page.goto('/dashboard')
      
      // Should show tablet-optimized layout
      await expect(page.getByText('Trámites Activos')).toBeVisible()
      
      // Check that touch interactions work
      await page.getByText('Nuevo Trámite').tap()
      await expect(page).toHaveURL('/gestion-tramites')
    })
  })

  test.describe('Performance', () => {
    test('should load pages within acceptable time', async ({ page }) => {
      const startTime = Date.now()
      
      await page.goto('/dashboard')
      await expect(page.getByText('Ciudadano Test')).toBeVisible()
      
      const loadTime = Date.now() - startTime
      
      // Should load within 3 seconds
      expect(loadTime).toBeLessThan(3000)
    })
  })
})
