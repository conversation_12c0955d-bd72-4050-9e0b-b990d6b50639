/**
 * Script de prueba simplificado para verificar la configuración del sistema
 * Verifica la estructura básica sin requerir claves de API
 */

import { config } from 'dotenv';

// Cargar variables de entorno
config({ path: '.env.local' });

async function testSystemConfiguration() {
  console.log('🔍 Verificando configuración del sistema de chatbot...\n');

  // 1. Verificar variables de entorno
  console.log('1. Verificando variables de entorno...');
  
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  const openaiKey = process.env.OPENAI_API_KEY;
  const openaiModel = process.env.OPENAI_MODEL;
  const embeddingModel = process.env.OPENAI_EMBEDDING_MODEL;
  
  console.log(`✅ NEXT_PUBLIC_SUPABASE_URL: ${supabaseUrl ? 'Configurada' : 'No configurada'}`);
  console.log(`✅ SUPABASE_SERVICE_ROLE_KEY: ${supabaseServiceKey ? 'Configurada' : 'No configurada'}`);
  console.log(`✅ OPENAI_API_KEY: ${openaiKey ? 'Configurada' : 'No configurada'}`);
  console.log(`✅ OPENAI_MODEL: ${openaiModel || 'gpt-4-turbo-preview (default)'}`);
  console.log(`✅ OPENAI_EMBEDDING_MODEL: ${embeddingModel || 'text-embedding-3-small (default)'}`);

  // 2. Verificar estructura de archivos
  console.log('\n2. Verificando estructura de archivos...');
  
  const fs = require('fs');
  const path = require('path');
  
  const requiredFiles = [
    'src/lib/openai.ts',
    'src/lib/supabase-vector.ts',
    'src/lib/rag-system.ts',
    'src/lib/embedding-processor.ts',
    'src/components/chat/ChatInterface.tsx',
    'src/app/api/chat/route.ts',
    'src/app/(protected)/chat/page.tsx',
    'scripts/process-embeddings.ts'
  ];
  
  for (const file of requiredFiles) {
    if (fs.existsSync(file)) {
      console.log(`✅ ${file}`);
    } else {
      console.log(`❌ ${file} - No encontrado`);
    }
  }

  // 3. Verificar dependencias
  console.log('\n3. Verificando dependencias...');
  
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  const requiredDeps = [
    'openai',
    '@supabase/supabase-js',
    'tsx',
    'dotenv'
  ];
  
  for (const dep of requiredDeps) {
    if (packageJson.dependencies[dep] || packageJson.devDependencies[dep]) {
      const version = packageJson.dependencies[dep] || packageJson.devDependencies[dep];
      console.log(`✅ ${dep}: ${version}`);
    } else {
      console.log(`❌ ${dep} - No instalada`);
    }
  }

  // 4. Verificar scripts npm
  console.log('\n4. Verificando scripts npm...');
  
  const requiredScripts = [
    'process-embeddings',
    'process-embeddings:procedures',
    'process-embeddings:opas',
    'test-system'
  ];
  
  for (const script of requiredScripts) {
    if (packageJson.scripts[script]) {
      console.log(`✅ ${script}: ${packageJson.scripts[script]}`);
    } else {
      console.log(`❌ ${script} - No configurado`);
    }
  }

  // 5. Verificar configuración de TypeScript
  console.log('\n5. Verificando configuración de TypeScript...');
  
  if (fs.existsSync('tsconfig.json')) {
    console.log('✅ tsconfig.json encontrado');
    
    const tsConfig = JSON.parse(fs.readFileSync('tsconfig.json', 'utf8'));
    if (tsConfig.compilerOptions?.strict) {
      console.log('✅ Modo estricto de TypeScript habilitado');
    } else {
      console.log('⚠️ Modo estricto de TypeScript no habilitado');
    }
  } else {
    console.log('❌ tsconfig.json no encontrado');
  }

  // 6. Verificar configuración de Next.js
  console.log('\n6. Verificando configuración de Next.js...');
  
  if (fs.existsSync('next.config.js') || fs.existsSync('next.config.mjs')) {
    console.log('✅ Configuración de Next.js encontrada');
  } else {
    console.log('⚠️ Configuración de Next.js no encontrada');
  }

  // 7. Verificar middleware
  console.log('\n7. Verificando middleware...');
  
  if (fs.existsSync('middleware.ts')) {
    console.log('✅ middleware.ts encontrado');
    
    const middlewareContent = fs.readFileSync('middleware.ts', 'utf8');
    if (middlewareContent.includes('/chat')) {
      console.log('✅ Ruta /chat protegida en middleware');
    } else {
      console.log('⚠️ Ruta /chat no encontrada en middleware');
    }
  } else {
    console.log('❌ middleware.ts no encontrado');
  }

  console.log('\n🎉 Verificación del sistema completada!');
  console.log('\n📋 Estado del sistema:');
  
  const allFilesExist = requiredFiles.every(file => fs.existsSync(file));
  const allDepsInstalled = requiredDeps.every(dep => 
    packageJson.dependencies[dep] || packageJson.devDependencies[dep]
  );
  const allScriptsConfigured = requiredScripts.every(script => 
    packageJson.scripts[script]
  );
  
  if (allFilesExist && allDepsInstalled && allScriptsConfigured) {
    console.log('✅ Sistema listo para configurar claves de API y procesar embeddings');
    console.log('\n📋 Próximos pasos:');
    console.log('1. Configurar OPENAI_API_KEY en .env.local');
    console.log('2. Configurar SUPABASE_SERVICE_ROLE_KEY en .env.local');
    console.log('3. Ejecutar: npm run process-embeddings');
    console.log('4. Probar el chatbot en: http://localhost:3000/chat');
  } else {
    console.log('⚠️ Sistema parcialmente configurado - revisar elementos faltantes arriba');
  }
}

// Ejecutar verificación
testSystemConfiguration()
  .then(() => {
    console.log('\n✅ Script de verificación completado');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ Error en script de verificación:', error);
    process.exit(1);
  });
