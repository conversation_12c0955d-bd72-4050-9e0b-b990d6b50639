"use client"

import * as React from "react"
import { Loader2, Search, Building2, FileText, BarChart3, AlertCircle } from "lucide-react"
import { cn } from "@/lib/utils"

// Loading Spinner básico
interface LoadingSpinnerProps {
  size?: "sm" | "md" | "lg"
  className?: string
  color?: "blue" | "green" | "gray"
}

export function LoadingSpinner({ 
  size = "md", 
  className,
  color = "blue"
}: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: "h-4 w-4",
    md: "h-6 w-6", 
    lg: "h-8 w-8"
  }

  const colorClasses = {
    blue: "text-primary",
    green: "text-chia-green-600",
    gray: "text-gray-600"
  }

  return (
    <Loader2 
      className={cn(
        "animate-spin",
        sizeClasses[size],
        colorClasses[color],
        className
      )}
    />
  )
}

// Loading para búsqueda
interface SearchLoadingProps {
  message?: string
  className?: string
}

export function SearchLoading({ 
  message = "Buscando...", 
  className 
}: SearchLoadingProps) {
  return (
    <div className={cn(
      "flex items-center justify-center space-x-2 py-8",
      className
    )}>
      <Search className="h-5 w-5 text-primary animate-pulse" />
      <LoadingSpinner size="sm" color="blue" />
      <span className="text-sm text-gray-600">{message}</span>
    </div>
  )
}

// Loading para modales
interface ModalLoadingProps {
  type?: "dependency" | "procedure" | "general"
  message?: string
  className?: string
}

export function ModalLoading({ 
  type = "general", 
  message,
  className 
}: ModalLoadingProps) {
  const getIcon = () => {
    switch (type) {
      case "dependency":
        return <Building2 className="h-8 w-8 text-primary animate-pulse" />
      case "procedure":
        return <FileText className="h-8 w-8 text-chia-green-600 animate-pulse" />
      default:
        return <BarChart3 className="h-8 w-8 text-gray-600 animate-pulse" />
    }
  }

  const getDefaultMessage = () => {
    switch (type) {
      case "dependency":
        return "Cargando información de la dependencia..."
      case "procedure":
        return "Cargando detalles del procedimiento..."
      default:
        return "Cargando..."
    }
  }

  return (
    <div className={cn(
      "flex flex-col items-center justify-center space-y-4 py-12",
      className
    )}>
      <div className="flex items-center space-x-3">
        {getIcon()}
        <LoadingSpinner size="md" />
      </div>
      <p className="text-sm text-gray-600 text-center">
        {message || getDefaultMessage()}
      </p>
    </div>
  )
}

// Loading para grid de dependencias
export function DependencyGridLoading({ className }: { className?: string }) {
  return (
    <div className={cn("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6", className)}>
      {Array.from({ length: 6 }).map((_, index) => (
        <div
          key={index}
          className="bg-white rounded-lg border border-gray-200 p-6 animate-pulse"
        >
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-12 h-12 bg-gray-200 rounded-lg"></div>
            <div className="flex-1">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            </div>
          </div>
          <div className="space-y-2">
            <div className="h-3 bg-gray-200 rounded w-full"></div>
            <div className="h-3 bg-gray-200 rounded w-2/3"></div>
          </div>
          <div className="flex justify-between items-center mt-4">
            <div className="h-6 bg-gray-200 rounded w-16"></div>
            <div className="h-6 bg-gray-200 rounded w-16"></div>
          </div>
        </div>
      ))}
    </div>
  )
}

// Loading para resultados de búsqueda
export function SearchResultsLoading({ className }: { className?: string }) {
  return (
    <div className={cn("space-y-2", className)}>
      {Array.from({ length: 5 }).map((_, index) => (
        <div
          key={index}
          className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg animate-pulse"
        >
          <div className="w-8 h-8 bg-gray-200 rounded"></div>
          <div className="flex-1">
            <div className="h-4 bg-gray-200 rounded w-3/4 mb-1"></div>
            <div className="h-3 bg-gray-200 rounded w-1/2"></div>
          </div>
          <div className="w-16 h-6 bg-gray-200 rounded"></div>
        </div>
      ))}
    </div>
  )
}

// Loading inline para botones
interface InlineLoadingProps {
  message?: string
  size?: "sm" | "md"
  className?: string
}

export function InlineLoading({ 
  message = "Cargando...", 
  size = "sm",
  className 
}: InlineLoadingProps) {
  return (
    <div className={cn("flex items-center space-x-2", className)}>
      <LoadingSpinner size={size} />
      <span className={cn(
        "text-gray-600",
        size === "sm" ? "text-xs" : "text-sm"
      )}>
        {message}
      </span>
    </div>
  )
}

// Loading con progreso para operaciones largas
interface ProgressLoadingProps {
  progress?: number
  message?: string
  className?: string
}

export function ProgressLoading({ 
  progress = 0, 
  message = "Procesando...",
  className 
}: ProgressLoadingProps) {
  return (
    <div className={cn("space-y-3", className)}>
      <div className="flex items-center space-x-3">
        <LoadingSpinner size="sm" />
        <span className="text-sm text-gray-600">{message}</span>
      </div>
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div 
          className="bg-primary h-2 rounded-full transition-all duration-300 ease-out"
          style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
        />
      </div>
      {progress > 0 && (
        <p className="text-xs text-gray-500 text-center">
          {Math.round(progress)}% completado
        </p>
      )}
    </div>
  )
}

// Loading para contenido de pestañas
export function TabContentLoading({ className }: { className?: string }) {
  return (
    <div className={cn("space-y-4 py-4", className)}>
      <div className="space-y-2">
        <div className="h-4 bg-gray-200 rounded w-1/4 animate-pulse"></div>
        <div className="h-3 bg-gray-200 rounded w-full animate-pulse"></div>
        <div className="h-3 bg-gray-200 rounded w-3/4 animate-pulse"></div>
      </div>
      <div className="space-y-2">
        <div className="h-4 bg-gray-200 rounded w-1/3 animate-pulse"></div>
        <div className="h-3 bg-gray-200 rounded w-full animate-pulse"></div>
        <div className="h-3 bg-gray-200 rounded w-2/3 animate-pulse"></div>
      </div>
    </div>
  )
}
