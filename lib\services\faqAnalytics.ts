/**
 * Servicio de Analytics para el sistema FAQ
 * Rastrea interacciones, búsquedas y métricas de uso
 */

/**
 * Tipos de eventos de analytics FAQ
 */
export type FAQAnalyticsEvent = 
  | 'faq_search'           // Búsqueda realizada
  | 'faq_view'             // FAQ visualizada/expandida
  | 'faq_category_filter'  // Filtro por categoría aplicado
  | 'faq_helpful_vote'     // Voto de utilidad (si se implementa)
  | 'faq_section_load'     // Sección FAQ cargada
  | 'faq_no_results'       // Búsqueda sin resultados

/**
 * Datos del evento de analytics
 */
export interface FAQAnalyticsEventData {
  event: FAQAnalyticsEvent
  timestamp: Date
  sessionId?: string
  userId?: string
  context?: string        // Contexto donde ocurrió (home, procedures, etc.)
  metadata?: {
    query?: string        // Término de búsqueda
    faqId?: string       // ID de FAQ interactuada
    category?: string    // Categoría filtrada
    resultsCount?: number // Número de resultados
    responseTime?: number // Tiempo de respuesta en ms
    userAgent?: string   // Información del navegador
    page?: string        // Página donde ocurrió
    [key: string]: any
  }
}

/**
 * Métricas agregadas de FAQ
 */
export interface FAQMetrics {
  totalSearches: number
  totalViews: number
  averageResponseTime: number
  topSearchTerms: Array<{ term: string; count: number }>
  topFAQs: Array<{ faqId: string; question: string; views: number }>
  topCategories: Array<{ category: string; count: number }>
  searchSuccessRate: number
  contextUsage: Array<{ context: string; count: number }>
  timeRange: {
    start: Date
    end: Date
  }
}

/**
 * Configuración del servicio de analytics
 */
interface FAQAnalyticsConfig {
  enabled: boolean
  batchSize: number
  flushInterval: number // ms
  maxRetries: number
  storageKey: string
  apiEndpoint?: string
}

/**
 * Servicio singleton de analytics para FAQ
 */
class FAQAnalyticsService {
  private static instance: FAQAnalyticsService
  private config: FAQAnalyticsConfig
  private eventQueue: FAQAnalyticsEventData[] = []
  private flushTimer: NodeJS.Timeout | null = null
  private sessionId: string
  private isInitialized = false

  private constructor() {
    this.config = {
      enabled: process.env.NODE_ENV === 'production' || process.env.NEXT_PUBLIC_ENABLE_FAQ_ANALYTICS === 'true',
      batchSize: 10,
      flushInterval: 30000, // 30 segundos
      maxRetries: 3,
      storageKey: 'faq_analytics_queue',
      apiEndpoint: process.env.NEXT_PUBLIC_FAQ_ANALYTICS_ENDPOINT
    }
    
    this.sessionId = this.generateSessionId()
    this.initialize()
  }

  /**
   * Obtener instancia singleton
   */
  public static getInstance(): FAQAnalyticsService {
    if (!FAQAnalyticsService.instance) {
      FAQAnalyticsService.instance = new FAQAnalyticsService()
    }
    return FAQAnalyticsService.instance
  }

  /**
   * Inicializar el servicio
   */
  private initialize(): void {
    if (this.isInitialized || !this.config.enabled) return

    // Cargar eventos pendientes del localStorage
    this.loadQueueFromStorage()

    // Configurar flush automático
    this.startFlushTimer()

    // Limpiar al cerrar la página
    if (typeof window !== 'undefined') {
      window.addEventListener('beforeunload', () => {
        this.flush()
      })
    }

    this.isInitialized = true
  }

  /**
   * Generar ID de sesión único
   */
  private generateSessionId(): string {
    return `faq_session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * Registrar evento de analytics
   */
  public track(event: FAQAnalyticsEvent, metadata?: FAQAnalyticsEventData['metadata']): void {
    if (!this.config.enabled) return

    const eventData: FAQAnalyticsEventData = {
      event,
      timestamp: new Date(),
      sessionId: this.sessionId,
      userId: this.getCurrentUserId(),
      context: this.getCurrentContext(),
      metadata: {
        ...metadata,
        userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : undefined,
        page: typeof window !== 'undefined' ? window.location.pathname : undefined
      }
    }

    this.eventQueue.push(eventData)

    // Flush si alcanzamos el tamaño del batch
    if (this.eventQueue.length >= this.config.batchSize) {
      this.flush()
    }
  }

  /**
   * Métodos específicos para eventos FAQ
   */
  public trackSearch(query: string, resultsCount: number, responseTime: number, context?: string): void {
    this.track('faq_search', {
      query,
      resultsCount,
      responseTime,
      context
    })
  }

  public trackFAQView(faqId: string, question: string, context?: string): void {
    this.track('faq_view', {
      faqId,
      question,
      context
    })
  }

  public trackCategoryFilter(category: string, context?: string): void {
    this.track('faq_category_filter', {
      category,
      context
    })
  }

  public trackSectionLoad(context: string, loadTime: number): void {
    this.track('faq_section_load', {
      context,
      responseTime: loadTime
    })
  }

  public trackNoResults(query: string, context?: string): void {
    this.track('faq_no_results', {
      query,
      context
    })
  }

  /**
   * Enviar eventos al servidor
   */
  private async flush(): Promise<void> {
    if (this.eventQueue.length === 0) return

    const eventsToSend = [...this.eventQueue]
    this.eventQueue = []

    try {
      if (this.config.apiEndpoint) {
        await this.sendToAPI(eventsToSend)
      } else {
        // Fallback: guardar en localStorage para análisis posterior
        this.saveToLocalStorage(eventsToSend)
      }
    } catch (error) {
      console.warn('Error enviando analytics FAQ:', error)
      // Reencolar eventos para reintento
      this.eventQueue.unshift(...eventsToSend)
    }
  }

  /**
   * Enviar eventos a API externa
   */
  private async sendToAPI(events: FAQAnalyticsEventData[]): Promise<void> {
    if (!this.config.apiEndpoint) return

    const response = await fetch(this.config.apiEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ events })
    })

    if (!response.ok) {
      throw new Error(`Analytics API error: ${response.status}`)
    }
  }

  /**
   * Guardar en localStorage como fallback
   */
  private saveToLocalStorage(events: FAQAnalyticsEventData[]): void {
    if (typeof window === 'undefined') return

    try {
      const existing = localStorage.getItem(this.config.storageKey)
      const existingEvents = existing ? JSON.parse(existing) : []
      const allEvents = [...existingEvents, ...events]
      
      // Mantener solo los últimos 1000 eventos
      const recentEvents = allEvents.slice(-1000)
      
      localStorage.setItem(this.config.storageKey, JSON.stringify(recentEvents))
    } catch (error) {
      console.warn('Error guardando analytics en localStorage:', error)
    }
  }

  /**
   * Cargar cola desde localStorage
   */
  private loadQueueFromStorage(): void {
    if (typeof window === 'undefined') return

    try {
      const stored = localStorage.getItem(this.config.storageKey)
      if (stored) {
        const events = JSON.parse(stored)
        // Solo cargar eventos recientes (últimas 24 horas)
        const recent = events.filter((event: FAQAnalyticsEventData) => {
          const eventTime = new Date(event.timestamp)
          const dayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000)
          return eventTime > dayAgo
        })
        this.eventQueue.push(...recent)
      }
    } catch (error) {
      console.warn('Error cargando analytics desde localStorage:', error)
    }
  }

  /**
   * Configurar timer para flush automático
   */
  private startFlushTimer(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer)
    }

    this.flushTimer = setInterval(() => {
      this.flush()
    }, this.config.flushInterval)
  }

  /**
   * Obtener ID del usuario actual (si está disponible)
   */
  private getCurrentUserId(): string | undefined {
    // Implementar según el sistema de autenticación
    if (typeof window !== 'undefined') {
      // Ejemplo: obtener desde localStorage o contexto de auth
      return localStorage.getItem('user_id') || undefined
    }
    return undefined
  }

  /**
   * Obtener contexto actual basado en la URL
   */
  private getCurrentContext(): string | undefined {
    if (typeof window === 'undefined') return undefined

    const path = window.location.pathname
    if (path === '/') return 'home'
    if (path.includes('/consulta-tramites')) return 'procedures'
    if (path.includes('/gestion-tramites')) return 'management'
    if (path.includes('/tramites')) return 'citizen'
    return 'unknown'
  }

  /**
   * Obtener métricas agregadas (para dashboard admin)
   */
  public async getMetrics(timeRange?: { start: Date; end: Date }): Promise<FAQMetrics> {
    // En una implementación real, esto consultaría la base de datos
    // Por ahora, retornamos datos de ejemplo
    return {
      totalSearches: 1250,
      totalViews: 3400,
      averageResponseTime: 150,
      topSearchTerms: [
        { term: 'impuesto predial', count: 45 },
        { term: 'certificado residencia', count: 38 },
        { term: 'licencia construcción', count: 32 }
      ],
      topFAQs: [
        { faqId: 'impuesto-predial-que-es', question: '¿Qué es el impuesto predial?', views: 156 },
        { faqId: 'certificado-residencia-como', question: '¿Cómo obtengo un certificado de residencia?', views: 134 }
      ],
      topCategories: [
        { category: 'impuestos', count: 89 },
        { category: 'certificados', count: 67 },
        { category: 'licencias', count: 45 }
      ],
      searchSuccessRate: 0.87,
      contextUsage: [
        { context: 'home', count: 45 },
        { context: 'procedures', count: 32 },
        { context: 'management', count: 23 }
      ],
      timeRange: timeRange || {
        start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
        end: new Date()
      }
    }
  }

  /**
   * Limpiar datos de analytics
   */
  public clearData(): void {
    this.eventQueue = []
    if (typeof window !== 'undefined') {
      localStorage.removeItem(this.config.storageKey)
    }
  }

  /**
   * Destruir instancia (para testing)
   */
  public destroy(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer)
      this.flushTimer = null
    }
    this.flush()
    this.isInitialized = false
  }
}

// Exportar instancia singleton
const faqAnalytics = FAQAnalyticsService.getInstance()
export default faqAnalytics

// Exportar clase para testing
export { FAQAnalyticsService }
