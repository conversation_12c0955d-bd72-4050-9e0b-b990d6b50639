import React from 'react'
import { render, screen, waitFor, act } from '@testing-library/react'
import { 
  ContextualFAQSection, 
  CompactFAQSection, 
  FloatingFAQSection,
  useFAQContext,
  type FAQContext 
} from '@/components/faq/ContextualFAQSection'
import faqService from '@/lib/services/faqService'

// Mock del servicio FAQ
jest.mock('@/lib/services/faqService')
const mockFaqService = faqService as jest.Mocked<typeof faqService>

// Mock de datos FAQ para pruebas
const mockFAQs = [
  {
    id: 'test-faq-1',
    question: '¿Cómo consulto el estado de mi trámite?',
    answer: 'Puedes consultar el estado ingresando a la sección "Mis Trámites"',
    theme: 'Trámites Generales',
    themeId: 'tramites-generales',
    keywords: ['consulta', 'estado', 'tramite'],
    relatedProcedures: ['Consulta de estado'],
    popularityScore: 85,
    viewCount: 150,
    helpfulVotes: 45,
    unhelpfulVotes: 2,
    displayOrder: 1,
    lastUpdated: new Date()
  },
  {
    id: 'test-faq-2',
    question: '¿Cuánto tiempo tarda un certificado de residencia?',
    answer: 'El certificado de residencia se expide en 24 horas hábiles',
    theme: 'Certificados',
    themeId: 'certificados',
    keywords: ['certificado', 'residencia', 'tiempo'],
    relatedProcedures: ['Certificado de residencia'],
    popularityScore: 90,
    viewCount: 120,
    helpfulVotes: 38,
    unhelpfulVotes: 1,
    displayOrder: 2,
    lastUpdated: new Date()
  }
]

const mockThemes = [
  {
    id: 'tramites-generales',
    name: 'Trámites Generales',
    count: 5,
    displayOrder: 1,
    dependencyId: null,
    subdependencyId: null
  },
  {
    id: 'certificados',
    name: 'Certificados',
    count: 3,
    displayOrder: 2,
    dependencyId: null,
    subdependencyId: null
  }
]

describe('ContextualFAQSection', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    mockFaqService.getPopularFAQs.mockResolvedValue(mockFAQs)
    mockFaqService.getThemes.mockResolvedValue(mockThemes)
    mockFaqService.getFAQStats.mockResolvedValue({
      totalFAQs: 10,
      totalThemes: 6,
      averagePopularity: 75,
      mostPopularTheme: 'Impuestos y Tributos'
    })
  })

  describe('Renderizado por contexto', () => {
    it('debería renderizar contexto home correctamente', async () => {
      await act(async () => {
        render(<ContextualFAQSection context="home" />)
      })

      await waitFor(() => {
        expect(screen.getByText('Preguntas Frecuentes')).toBeInTheDocument()
        expect(screen.getByText(/consultas más comunes sobre trámites/)).toBeInTheDocument()
      })
    })

    it('debería renderizar contexto procedures correctamente', async () => {
      await act(async () => {
        render(<ContextualFAQSection context="procedures" />)
      })

      await waitFor(() => {
        expect(screen.getByText('¿Necesitas ayuda con los trámites?')).toBeInTheDocument()
        expect(screen.getByText(/consultar y gestionar tus trámites/)).toBeInTheDocument()
      })
    })

    it('debería renderizar contexto management correctamente', async () => {
      await act(async () => {
        render(<ContextualFAQSection context="management" />)
      })

      await waitFor(() => {
        expect(screen.getByText('Ayuda con la Gestión de Trámites')).toBeInTheDocument()
        expect(screen.getByText(/iniciar, seguir y completar/)).toBeInTheDocument()
      })
    })

    it('debería renderizar contexto citizen correctamente', async () => {
      await act(async () => {
        render(<ContextualFAQSection context="citizen" />)
      })

      await waitFor(() => {
        expect(screen.getByText('¿Tienes dudas sobre tus trámites?')).toBeInTheDocument()
        expect(screen.getByText(/estado, requisitos y proceso/)).toBeInTheDocument()
      })
    })

    it('debería renderizar contexto payments correctamente', async () => {
      await act(async () => {
        render(<ContextualFAQSection context="payments" />)
      })

      await waitFor(() => {
        expect(screen.getByText('Ayuda con Pagos y Facturación')).toBeInTheDocument()
        expect(screen.getByText(/impuestos, pagos en línea/)).toBeInTheDocument()
      })
    })

    it('debería renderizar contexto certificates correctamente', async () => {
      await act(async () => {
        render(<ContextualFAQSection context="certificates" />)
      })

      await waitFor(() => {
        expect(screen.getByText('Información sobre Certificados')).toBeInTheDocument()
        expect(screen.getByText(/certificados municipales/)).toBeInTheDocument()
      })
    })

    it('debería renderizar contexto licenses correctamente', async () => {
      await act(async () => {
        render(<ContextualFAQSection context="licenses" />)
      })

      await waitFor(() => {
        expect(screen.getByText('Guía de Licencias y Permisos')).toBeInTheDocument()
        expect(screen.getByText(/licencias de construcción/)).toBeInTheDocument()
      })
    })
  })

  describe('Configuración contextual', () => {
    it('debería mostrar estadísticas solo en contexto home', async () => {
      await act(async () => {
        render(<ContextualFAQSection context="home" />)
      })

      await waitFor(() => {
        expect(screen.getByText(/preguntas/)).toBeInTheDocument()
        expect(screen.getByText(/temas/)).toBeInTheDocument()
      })
    })

    it('debería ocultar estadísticas en otros contextos', async () => {
      await act(async () => {
        render(<ContextualFAQSection context="procedures" />)
      })

      await waitFor(() => {
        expect(screen.getByText('¿Necesitas ayuda con los trámites?')).toBeInTheDocument()
      })

      // No debería mostrar estadísticas
      expect(screen.queryByText(/10 preguntas/)).not.toBeInTheDocument()
    })

    it('debería mostrar filtros de tema según contexto', async () => {
      await act(async () => {
        render(<ContextualFAQSection context="home" />)
      })

      await waitFor(() => {
        expect(screen.getByText('Filtrar por tema:')).toBeInTheDocument()
      })
    })

    it('debería ocultar filtros en contexto citizen', async () => {
      await act(async () => {
        render(<ContextualFAQSection context="citizen" />)
      })

      await waitFor(() => {
        expect(screen.getByText('¿Tienes dudas sobre tus trámites?')).toBeInTheDocument()
      })

      expect(screen.queryByText('Filtrar por categoría:')).not.toBeInTheDocument()
    })
  })

  describe('Modo compacto', () => {
    it('debería aplicar configuración compacta', async () => {
      await act(async () => {
        render(<ContextualFAQSection context="home" compact={true} />)
      })

      await waitFor(() => {
        expect(screen.getByText('Preguntas Frecuentes')).toBeInTheDocument()
      })

      // En modo compacto no debería mostrar estadísticas ni filtros
      expect(screen.queryByText('Filtrar por categoría:')).not.toBeInTheDocument()
    })
  })

  describe('Clases CSS personalizadas', () => {
    it('debería aplicar clases CSS adicionales', async () => {
      await act(async () => {
        render(<ContextualFAQSection context="home" className="custom-class" />)
      })

      const container = document.querySelector('.contextual-faq-section')
      expect(container).toHaveClass('custom-class')
    })
  })

  describe('Indicador de desarrollo', () => {
    const originalEnv = process.env.NODE_ENV

    afterEach(() => {
      process.env.NODE_ENV = originalEnv
    })

    it('debería mostrar indicador de contexto en desarrollo', async () => {
      process.env.NODE_ENV = 'development'

      await act(async () => {
        render(<ContextualFAQSection context="procedures" />)
      })

      await waitFor(() => {
        expect(screen.getByText('FAQ Context: procedures')).toBeInTheDocument()
      })
    })

    it('debería ocultar indicador en producción', async () => {
      process.env.NODE_ENV = 'production'

      await act(async () => {
        render(<ContextualFAQSection context="procedures" />)
      })

      await waitFor(() => {
        expect(screen.getByText('¿Necesitas ayuda con los trámites?')).toBeInTheDocument()
      })

      expect(screen.queryByText('FAQ Context: procedures')).not.toBeInTheDocument()
    })
  })
})

describe('CompactFAQSection', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    mockFaqService.getPopularFAQs.mockResolvedValue(mockFAQs.slice(0, 2))
    mockFaqService.getThemes.mockResolvedValue(mockThemes)
  })

  it('debería renderizar versión compacta', async () => {
    await act(async () => {
      render(<CompactFAQSection context="procedures" />)
    })

    await waitFor(() => {
      expect(screen.getByText('¿Necesitas ayuda con los trámites?')).toBeInTheDocument()
    })

    // No debería mostrar filtros ni estadísticas en modo compacto
    expect(screen.queryByText('Filtrar por categoría:')).not.toBeInTheDocument()
  })
})

describe('FloatingFAQSection', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    mockFaqService.getPopularFAQs.mockResolvedValue(mockFAQs.slice(0, 2))
    mockFaqService.getThemes.mockResolvedValue(mockThemes)
  })

  it('debería renderizar cuando isVisible es true', async () => {
    await act(async () => {
      render(<FloatingFAQSection context="procedures" isVisible={true} />)
    })

    await waitFor(() => {
      expect(screen.getByText('Ayuda Rápida')).toBeInTheDocument()
    })
  })

  it('debería ocultarse cuando isVisible es false', () => {
    render(<FloatingFAQSection context="procedures" isVisible={false} />)
    
    expect(screen.queryByText('Ayuda Rápida')).not.toBeInTheDocument()
  })

  it('debería mostrar botón de cerrar cuando se proporciona onClose', async () => {
    const mockOnClose = jest.fn()

    await act(async () => {
      render(<FloatingFAQSection context="procedures" onClose={mockOnClose} />)
    })

    await waitFor(() => {
      expect(screen.getByLabelText('Cerrar ayuda')).toBeInTheDocument()
    })
  })
})

describe('useFAQContext hook', () => {
  it('debería retornar configuración correcta para cada contexto', () => {
    const contexts: FAQContext[] = ['home', 'procedures', 'management', 'citizen', 'payments', 'certificates', 'licenses']
    
    contexts.forEach(context => {
      const config = useFAQContext(context)
      expect(config).toBeDefined()
      expect(config.title).toBeTruthy()
      expect(config.description).toBeTruthy()
      expect(typeof config.initialLimit).toBe('number')
      expect(typeof config.showSearch).toBe('boolean')
      expect(typeof config.showCategoryFilter).toBe('boolean')
      expect(typeof config.showStats).toBe('boolean')
    })
  })
})
