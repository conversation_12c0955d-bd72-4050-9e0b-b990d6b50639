#!/usr/bin/env python3
"""
Script to split consolidated FAQ file into manageable chunks for database insertion
"""

import re

def split_consolidated_file():
    """Split the consolidated FAQ file into smaller chunks"""
    
    # Read the consolidated file
    with open('consolidated_faq_insertion.sql', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Split by INSERT statements
    insert_statements = re.findall(r'INSERT INTO municipal_faqs.*?;', content, re.DOTALL)
    
    print(f"Found {len(insert_statements)} INSERT statements")
    
    # Create chunks of 20 statements each
    chunk_size = 20
    chunk_num = 1
    
    for i in range(0, len(insert_statements), chunk_size):
        chunk = insert_statements[i:i + chunk_size]
        
        filename = f"faq_chunk_{chunk_num:02d}.sql"
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"-- FAQ Chunk {chunk_num}\n")
            f.write(f"-- Questions {i+1} to {min(i+chunk_size, len(insert_statements))}\n\n")
            f.write("\n".join(chunk))
            f.write("\n")
        
        print(f"Created {filename} with {len(chunk)} questions")
        chunk_num += 1
    
    print(f"\nTotal chunks created: {chunk_num-1}")
    print(f"Total questions: {len(insert_statements)}")
    
    return chunk_num - 1

def main():
    """Main function"""
    print("FAQ Chunk Splitter")
    print("=" * 30)
    
    try:
        total_chunks = split_consolidated_file()
        print(f"\n✅ Successfully created {total_chunks} chunk files")
        print("📁 Files created: faq_chunk_01.sql to faq_chunk_{:02d}.sql".format(total_chunks))
        
        return 0
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
