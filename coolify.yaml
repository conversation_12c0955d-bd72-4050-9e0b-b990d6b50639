# Coolify Configuration for Chía Trámites System
# This file defines the deployment configuration for Coolify

version: "3.8"

services:
  app:
    # Build configuration
    build:
      context: .
      dockerfile: Dockerfile
    
    # Container configuration
    container_name: chia-tramites-app
    restart: unless-stopped
    
    # Port mapping
    ports:
      - "3000:3000"
    
    # Environment variables (will be set in Coolify dashboard)
    environment:
      # Node.js Configuration
      NODE_ENV: production
      NEXT_TELEMETRY_DISABLED: 1
      HOSTNAME: "0.0.0.0"
      PORT: 3000
      
      # Supabase Configuration
      NEXT_PUBLIC_SUPABASE_URL: ${NEXT_PUBLIC_SUPABASE_URL}
      NEXT_PUBLIC_SUPABASE_ANON_KEY: ${NEXT_PUBLIC_SUPABASE_ANON_KEY}
      SUPABASE_SERVICE_ROLE_KEY: ${SUPABASE_SERVICE_ROLE_KEY}
      
      # Authentication
      NEXTAUTH_SECRET: ${NEXTAUTH_SECRET}
      NEXTAUTH_URL: ${NEXTAUTH_URL}
      
      # Application specific
      APP_NAME: "Sistema de Atención Ciudadana - Chía"
      APP_VERSION: "1.0.0"
      
    # Health check
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # Resource limits (adjust based on server capacity)
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
    
    # Labels for Coolify
    labels:
      coolify.managed: "true"
      coolify.name: "chia-tramites"
      coolify.type: "application"
      coolify.port: "3000"
      coolify.domain: "${DOMAIN_NAME}"
      coolify.https: "true"
      coolify.redirect: "true"

# Network configuration
networks:
  default:
    name: chia-network
