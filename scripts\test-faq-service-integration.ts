/**
 * Script de prueba para verificar la integración del FAQ Service con Supabase
 * Ejecutar con: npx tsx scripts/test-faq-service-integration.ts
 */

// Configurar variables de entorno para el test
process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://zeieudvbhlrlnfkwejoh.supabase.co'
process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InplaWV1ZHZiaGxybG5ma3dlam9oIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEzMDk1MDEsImV4cCI6MjA2Njg4NTUwMX0.sOImH-XXxxVjjUZhWwYt6KK6dpfCBK2wvT2rnPmlC50'

// Mock del módulo faqAnalytics para evitar errores
const mockAnalytics = {
  trackSearch: () => {},
  trackFAQView: () => {},
  trackNoResults: () => {}
}

// Reemplazar el import de faqAnalytics
const Module = require('module')
const originalRequire = Module.prototype.require

Module.prototype.require = function(id: string) {
  if (id === './faqAnalytics' || id.endsWith('/faqAnalytics')) {
    return mockAnalytics
  }
  return originalRequire.apply(this, arguments)
}

import { FAQService } from '../lib/services/faqService'

async function testFAQServiceIntegration() {
  console.log('🧪 Iniciando pruebas de integración del FAQ Service...\n')
  
  const faqService = FAQService.getInstance()

  try {
    // Test 1: Obtener categorías
    console.log('📂 Test 1: Obtener categorías')
    const categories = await faqService.getCategories()
    console.log(`✅ Categorías obtenidas: ${categories.length}`)
    categories.forEach(cat => {
      console.log(`   - ${cat.name}: ${cat.count} FAQs (ID: ${cat.id})`)
    })
    console.log()

    // Test 2: Obtener FAQs populares
    console.log('⭐ Test 2: Obtener FAQs populares')
    const popularFAQs = await faqService.getPopularFAQs(3)
    console.log(`✅ FAQs populares obtenidas: ${popularFAQs.length}`)
    popularFAQs.forEach(faq => {
      console.log(`   - ${faq.question} (Popularidad: ${faq.popularity})`)
      console.log(`     Categoría: ${faq.category} | Vistas: ${faq.viewCount}`)
    })
    console.log()

    // Test 3: Buscar FAQs
    console.log('🔍 Test 3: Buscar FAQs')
    const searchResults = await faqService.searchFAQs('impuesto predial')
    console.log(`✅ Resultados de búsqueda: ${searchResults.length}`)
    searchResults.forEach(faq => {
      console.log(`   - ${faq.question}`)
      console.log(`     Categoría: ${faq.category} | Tags: ${faq.tags.join(', ')}`)
    })
    console.log()

    // Test 4: Obtener FAQ por ID
    if (searchResults.length > 0) {
      console.log('🔍 Test 4: Obtener FAQ por ID')
      const faqById = await faqService.getFAQById(searchResults[0].id)
      if (faqById) {
        console.log(`✅ FAQ obtenida por ID: ${faqById.question}`)
        console.log(`   Vistas: ${faqById.viewCount} | Votos útiles: ${faqById.helpfulVotes}`)
        console.log(`   Respuesta: ${faqById.answer.substring(0, 100)}...`)
      } else {
        console.log('❌ No se pudo obtener FAQ por ID')
      }
      console.log()
    }

    // Test 5: Obtener FAQs por categoría
    if (categories.length > 0) {
      console.log('📋 Test 5: Obtener FAQs por categoría')
      const categoryFAQs = await faqService.getFAQsByCategory(categories[0].id, 2)
      console.log(`✅ FAQs de categoría "${categories[0].name}": ${categoryFAQs.length}`)
      categoryFAQs.forEach(faq => {
        console.log(`   - ${faq.question}`)
        console.log(`     Tags: ${faq.tags.join(', ')}`)
      })
      console.log()
    }

    // Test 6: Obtener estadísticas
    console.log('📊 Test 6: Obtener estadísticas')
    const stats = await faqService.getFAQStats()
    console.log('✅ Estadísticas obtenidas:')
    console.log(`   - Total FAQs: ${stats.totalFAQs}`)
    console.log(`   - Total Categorías: ${stats.totalCategories}`)
    console.log(`   - Popularidad promedio: ${stats.averagePopularity}`)
    console.log(`   - Categoría más popular: ${stats.mostPopularCategory}`)
    console.log()

    // Test 7: Búsqueda con filtros
    console.log('🔍 Test 7: Búsqueda con filtros por categoría')
    if (categories.length > 0) {
      const filteredSearch = await faqService.searchFAQs('licencia', {
        category: categories[1].id, // Licencias y Permisos
        limit: 5
      })
      console.log(`✅ Búsqueda filtrada: ${filteredSearch.length} resultados`)
      filteredSearch.forEach(faq => {
        console.log(`   - ${faq.question}`)
      })
    }
    console.log()

    console.log('🎉 Todas las pruebas de integración completadas exitosamente!')

  } catch (error) {
    console.error('❌ Error durante las pruebas:', error)
    console.error('Stack trace:', error instanceof Error ? error.stack : 'No stack trace available')
  }
}

// Ejecutar las pruebas
testFAQServiceIntegration()
