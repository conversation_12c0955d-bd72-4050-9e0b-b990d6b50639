-- Migración para agregar campos de gestión de costos
-- Fecha: 2025-07-03
-- Descripción: Agrega campos has_cost y cost_description para extraer información de costos desde JSON/texto

-- 1. Agregar campos de gestión de costos a la tabla procedures
ALTER TABLE procedures 
ADD COLUMN IF NOT EXISTS has_cost BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS cost_description TEXT;

-- 2. Agregar campos de gestión de costos a la tabla opas
ALTER TABLE opas 
ADD COLUMN IF NOT EXISTS has_cost BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS cost_description TEXT;

-- 3. Función para extraer información de costos desde descripción
CREATE OR REPLACE FUNCTION extract_cost_info_from_description(description_text TEXT)
RETURNS TABLE(has_cost BOOLEAN, cost_description TEXT) AS $$
DECLARE
    cost_info RECORD;
BEGIN
    -- Inicializar valores por defecto
    has_cost := false;
    cost_description := 'Gratuito';
    
    -- Si no hay descripción, retornar valores por defecto
    IF description_text IS NULL OR description_text = '' THEN
        RETURN NEXT;
        RETURN;
    END IF;
    
    -- Convertir a minúsculas para análisis
    description_text := LOWER(description_text);
    
    -- Verificar si explícitamente dice que no tiene pago
    IF description_text ~ '¿tiene pago\?:\s*no' OR 
       description_text ~ 'pago:\s*no' OR
       description_text ~ 'gratuito' OR
       description_text ~ 'sin costo' THEN
        has_cost := false;
        cost_description := 'Gratuito';
        RETURN NEXT;
        RETURN;
    END IF;
    
    -- Verificar si tiene información de costos específicos
    IF description_text ~ 'uvt|smldv|pesos|\$|tarifa|costo|precio' THEN
        has_cost := true;
        
        -- Extraer información específica de costos
        CASE 
            -- UVT (Unidad de Valor Tributario)
            WHEN description_text ~ '[0-9,\.]+\s*uvt' THEN
                cost_description := 'Costo en UVT - Consultar descripción completa';
            
            -- SMLDV (Salario Mínimo Legal Diario Vigente)
            WHEN description_text ~ '[0-9,\.]+\s*smldv' THEN
                cost_description := 'Costo en SMLDV - Consultar descripción completa';
            
            -- Pesos colombianos
            WHEN description_text ~ '\$[0-9,\.]+|[0-9,\.]+\s*pesos' THEN
                cost_description := 'Costo en pesos - Consultar descripción completa';
            
            -- Tarifas variables
            WHEN description_text ~ 'tarifa|estrato' THEN
                cost_description := 'Tarifa variable según estrato - Consultar descripción completa';
            
            -- Costos generales
            ELSE
                cost_description := 'Tiene costo - Consultar descripción completa';
        END CASE;
        
        RETURN NEXT;
        RETURN;
    END IF;
    
    -- Si no se encuentra información específica, asumir gratuito
    has_cost := false;
    cost_description := 'Gratuito';
    RETURN NEXT;
    RETURN;
END;
$$ LANGUAGE plpgsql;

-- 4. Función para actualizar información de costos en procedures
CREATE OR REPLACE FUNCTION update_procedures_cost_info()
RETURNS VOID AS $$
DECLARE
    procedure_record RECORD;
    cost_info RECORD;
BEGIN
    -- Iterar sobre todos los procedimientos
    FOR procedure_record IN 
        SELECT id, name, description
        FROM procedures 
        WHERE description IS NOT NULL
    LOOP
        -- Extraer información de costos
        SELECT * INTO cost_info 
        FROM extract_cost_info_from_description(procedure_record.description);
        
        -- Actualizar el registro
        UPDATE procedures 
        SET 
            has_cost = cost_info.has_cost,
            cost_description = cost_info.cost_description
        WHERE id = procedure_record.id;
        
        RAISE NOTICE 'Actualizado procedimiento: % - Has cost: % - Description: %', 
            procedure_record.name, cost_info.has_cost, cost_info.cost_description;
    END LOOP;
    
    RAISE NOTICE 'Actualización de información de costos en procedures completada';
END;
$$ LANGUAGE plpgsql;

-- 5. Función para actualizar información de costos en opas
CREATE OR REPLACE FUNCTION update_opas_cost_info()
RETURNS VOID AS $$
DECLARE
    opa_record RECORD;
    cost_info RECORD;
BEGIN
    -- Iterar sobre todas las OPAs
    FOR opa_record IN 
        SELECT id, name, description
        FROM opas 
        WHERE description IS NOT NULL
    LOOP
        -- Extraer información de costos
        SELECT * INTO cost_info 
        FROM extract_cost_info_from_description(opa_record.description);
        
        -- Actualizar el registro
        UPDATE opas 
        SET 
            has_cost = cost_info.has_cost,
            cost_description = cost_info.cost_description
        WHERE id = opa_record.id;
        
        RAISE NOTICE 'Actualizada OPA: % - Has cost: % - Description: %', 
            opa_record.name, cost_info.has_cost, cost_info.cost_description;
    END LOOP;
    
    RAISE NOTICE 'Actualización de información de costos en OPAs completada';
END;
$$ LANGUAGE plpgsql;

-- 6. Ejecutar actualización de información de costos
SELECT update_procedures_cost_info();
SELECT update_opas_cost_info();

-- 7. Crear índices para mejorar rendimiento de consultas
CREATE INDEX IF NOT EXISTS idx_procedures_has_cost ON procedures(has_cost);
CREATE INDEX IF NOT EXISTS idx_opas_has_cost ON opas(has_cost);

-- 8. Actualizar vista unificada para incluir información de costos
DROP VIEW IF EXISTS vista_codigos_procedimientos;
CREATE VIEW vista_codigos_procedimientos AS
SELECT 
    p.id,
    p.name,
    p.codigo_tramite as codigo,
    'TRAMITE' as tipo,
    d.code as dep_code,
    d.name as dep_name,
    s.code as subdep_code,
    s.name as subdep_name,
    p.has_cost,
    p.cost_description,
    p.cost as cost_numeric,
    p.response_time
FROM procedures p
LEFT JOIN subdependencies s ON p.subdependency_id = s.id
LEFT JOIN dependencies d ON s.dependency_id = d.id
WHERE p.is_active = true

UNION ALL

SELECT 
    o.id,
    o.name,
    o.code as codigo,
    'OPA' as tipo,
    d.code as dep_code,
    d.name as dep_name,
    s.code as subdep_code,
    s.name as subdep_name,
    o.has_cost,
    o.cost_description,
    NULL as cost_numeric,
    NULL as response_time
FROM opas o
LEFT JOIN subdependencies s ON o.subdependency_id = s.id
LEFT JOIN dependencies d ON s.dependency_id = d.id
WHERE o.is_active = true;

-- 9. Crear trigger para actualizar automáticamente información de costos en nuevos registros
CREATE OR REPLACE FUNCTION trigger_update_cost_info()
RETURNS TRIGGER AS $$
DECLARE
    cost_info RECORD;
BEGIN
    -- Solo procesar si hay descripción
    IF NEW.description IS NOT NULL AND NEW.description != '' THEN
        -- Extraer información de costos
        SELECT * INTO cost_info 
        FROM extract_cost_info_from_description(NEW.description);
        
        -- Actualizar campos de costo
        NEW.has_cost := cost_info.has_cost;
        NEW.cost_description := cost_info.cost_description;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 10. Crear triggers para ambas tablas
DROP TRIGGER IF EXISTS trigger_procedures_cost_info ON procedures;
CREATE TRIGGER trigger_procedures_cost_info
    BEFORE INSERT OR UPDATE OF description ON procedures
    FOR EACH ROW
    EXECUTE FUNCTION trigger_update_cost_info();

DROP TRIGGER IF EXISTS trigger_opas_cost_info ON opas;
CREATE TRIGGER trigger_opas_cost_info
    BEFORE INSERT OR UPDATE OF description ON opas
    FOR EACH ROW
    EXECUTE FUNCTION trigger_update_cost_info();

-- 11. Limpiar funciones auxiliares temporales
DROP FUNCTION IF EXISTS update_procedures_cost_info();
DROP FUNCTION IF EXISTS update_opas_cost_info();

-- 12. Comentarios para documentación
COMMENT ON COLUMN procedures.has_cost IS 
'Indica si el procedimiento tiene costo asociado, extraído automáticamente de la descripción';

COMMENT ON COLUMN procedures.cost_description IS 
'Descripción del costo extraída automáticamente de la descripción del procedimiento';

COMMENT ON COLUMN opas.has_cost IS 
'Indica si la OPA tiene costo asociado, extraído automáticamente de la descripción';

COMMENT ON COLUMN opas.cost_description IS 
'Descripción del costo extraída automáticamente de la descripción de la OPA';

COMMENT ON FUNCTION extract_cost_info_from_description(TEXT) IS 
'Extrae información de costos desde texto de descripción, identificando UVT, SMLDV, pesos y otros patrones';

COMMENT ON VIEW vista_codigos_procedimientos IS 
'Vista unificada de procedimientos con códigos y información de costos extraída automáticamente';
