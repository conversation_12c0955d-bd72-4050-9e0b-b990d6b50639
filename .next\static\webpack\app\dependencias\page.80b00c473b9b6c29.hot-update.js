"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dependencias/page",{

/***/ "(app-pages-browser)/./components/dependencies/DependencyGrid.tsx":
/*!****************************************************!*\
  !*** ./components/dependencies/DependencyGrid.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DependencyGrid: function() { return /* binding */ DependencyGrid; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Banknote_BarChart3_Building_Building2_Car_Crown_FileText_Filter_Folder_GraduationCap_HeartPulse_Map_Network_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Banknote,BarChart3,Building,Building2,Car,Crown,FileText,Filter,Folder,GraduationCap,HeartPulse,Map,Network,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Banknote_BarChart3_Building_Building2_Car_Crown_FileText_Filter_Folder_GraduationCap_HeartPulse_Map_Network_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Banknote,BarChart3,Building,Building2,Car,Crown,FileText,Filter,Folder,GraduationCap,HeartPulse,Map,Network,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Banknote_BarChart3_Building_Building2_Car_Crown_FileText_Filter_Folder_GraduationCap_HeartPulse_Map_Network_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Banknote,BarChart3,Building,Building2,Car,Crown,FileText,Filter,Folder,GraduationCap,HeartPulse,Map,Network,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/banknote.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Banknote_BarChart3_Building_Building2_Car_Crown_FileText_Filter_Folder_GraduationCap_HeartPulse_Map_Network_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Banknote,BarChart3,Building,Building2,Car,Crown,FileText,Filter,Folder,GraduationCap,HeartPulse,Map,Network,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Banknote_BarChart3_Building_Building2_Car_Crown_FileText_Filter_Folder_GraduationCap_HeartPulse_Map_Network_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Banknote,BarChart3,Building,Building2,Car,Crown,FileText,Filter,Folder,GraduationCap,HeartPulse,Map,Network,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Banknote_BarChart3_Building_Building2_Car_Crown_FileText_Filter_Folder_GraduationCap_HeartPulse_Map_Network_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Banknote,BarChart3,Building,Building2,Car,Crown,FileText,Filter,Folder,GraduationCap,HeartPulse,Map,Network,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Banknote_BarChart3_Building_Building2_Car_Crown_FileText_Filter_Folder_GraduationCap_HeartPulse_Map_Network_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Banknote,BarChart3,Building,Building2,Car,Crown,FileText,Filter,Folder,GraduationCap,HeartPulse,Map,Network,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart-pulse.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Banknote_BarChart3_Building_Building2_Car_Crown_FileText_Filter_Folder_GraduationCap_HeartPulse_Map_Network_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Banknote,BarChart3,Building,Building2,Car,Crown,FileText,Filter,Folder,GraduationCap,HeartPulse,Map,Network,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/car.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Banknote_BarChart3_Building_Building2_Car_Crown_FileText_Filter_Folder_GraduationCap_HeartPulse_Map_Network_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Banknote,BarChart3,Building,Building2,Car,Crown,FileText,Filter,Folder,GraduationCap,HeartPulse,Map,Network,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Banknote_BarChart3_Building_Building2_Car_Crown_FileText_Filter_Folder_GraduationCap_HeartPulse_Map_Network_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Banknote,BarChart3,Building,Building2,Car,Crown,FileText,Filter,Folder,GraduationCap,HeartPulse,Map,Network,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Banknote_BarChart3_Building_Building2_Car_Crown_FileText_Filter_Folder_GraduationCap_HeartPulse_Map_Network_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Banknote,BarChart3,Building,Building2,Car,Crown,FileText,Filter,Folder,GraduationCap,HeartPulse,Map,Network,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Banknote_BarChart3_Building_Building2_Car_Crown_FileText_Filter_Folder_GraduationCap_HeartPulse_Map_Network_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Banknote,BarChart3,Building,Building2,Car,Crown,FileText,Filter,Folder,GraduationCap,HeartPulse,Map,Network,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Banknote_BarChart3_Building_Building2_Car_Crown_FileText_Filter_Folder_GraduationCap_HeartPulse_Map_Network_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Banknote,BarChart3,Building,Building2,Car,Crown,FileText,Filter,Folder,GraduationCap,HeartPulse,Map,Network,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Banknote_BarChart3_Building_Building2_Car_Crown_FileText_Filter_Folder_GraduationCap_HeartPulse_Map_Network_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Banknote,BarChart3,Building,Building2,Car,Crown,FileText,Filter,Folder,GraduationCap,HeartPulse,Map,Network,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Banknote_BarChart3_Building_Building2_Car_Crown_FileText_Filter_Folder_GraduationCap_HeartPulse_Map_Network_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Banknote,BarChart3,Building,Building2,Car,Crown,FileText,Filter,Folder,GraduationCap,HeartPulse,Map,Network,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Banknote_BarChart3_Building_Building2_Car_Crown_FileText_Filter_Folder_GraduationCap_HeartPulse_Map_Network_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Banknote,BarChart3,Building,Building2,Car,Crown,FileText,Filter,Folder,GraduationCap,HeartPulse,Map,Network,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/network.js\");\n/* harmony import */ var _lib_services_dependencyService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/services/dependencyService */ \"(app-pages-browser)/./lib/services/dependencyService.ts\");\n/* harmony import */ var _AdvancedDependencySearch__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./AdvancedDependencySearch */ \"(app-pages-browser)/./components/dependencies/AdvancedDependencySearch.tsx\");\n/* harmony import */ var _DependencyDetailModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./DependencyDetailModal */ \"(app-pages-browser)/./components/dependencies/DependencyDetailModal.tsx\");\n/* harmony import */ var _ProcedureDetailModal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ProcedureDetailModal */ \"(app-pages-browser)/./components/dependencies/ProcedureDetailModal.tsx\");\n/* harmony import */ var _components_ui_loading_states__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/loading-states */ \"(app-pages-browser)/./components/ui/loading-states.tsx\");\n/* harmony import */ var _components_ui_error_states__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/error-states */ \"(app-pages-browser)/./components/ui/error-states.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ DependencyGrid auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n// Mapeo de iconos\nconst iconMap = {\n    crown: _barrel_optimize_names_ArrowRight_Banknote_BarChart3_Building_Building2_Car_Crown_FileText_Filter_Folder_GraduationCap_HeartPulse_Map_Network_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n    shield: _barrel_optimize_names_ArrowRight_Banknote_BarChart3_Building_Building2_Car_Crown_FileText_Filter_Folder_GraduationCap_HeartPulse_Map_Network_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n    banknote: _barrel_optimize_names_ArrowRight_Banknote_BarChart3_Building_Building2_Car_Crown_FileText_Filter_Folder_GraduationCap_HeartPulse_Map_Network_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n    map: _barrel_optimize_names_ArrowRight_Banknote_BarChart3_Building_Building2_Car_Crown_FileText_Filter_Folder_GraduationCap_HeartPulse_Map_Network_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n    users: _barrel_optimize_names_ArrowRight_Banknote_BarChart3_Building_Building2_Car_Crown_FileText_Filter_Folder_GraduationCap_HeartPulse_Map_Network_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n    \"graduation-cap\": _barrel_optimize_names_ArrowRight_Banknote_BarChart3_Building_Building2_Car_Crown_FileText_Filter_Folder_GraduationCap_HeartPulse_Map_Network_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n    \"heart-pulse\": _barrel_optimize_names_ArrowRight_Banknote_BarChart3_Building_Building2_Car_Crown_FileText_Filter_Folder_GraduationCap_HeartPulse_Map_Network_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n    car: _barrel_optimize_names_ArrowRight_Banknote_BarChart3_Building_Building2_Car_Crown_FileText_Filter_Folder_GraduationCap_HeartPulse_Map_Network_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n    building: _barrel_optimize_names_ArrowRight_Banknote_BarChart3_Building_Building2_Car_Crown_FileText_Filter_Folder_GraduationCap_HeartPulse_Map_Network_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n    folder: _barrel_optimize_names_ArrowRight_Banknote_BarChart3_Building_Building2_Car_Crown_FileText_Filter_Folder_GraduationCap_HeartPulse_Map_Network_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"]\n};\nfunction DependencyGrid(param) {\n    let { onDependencySelect, showSearch = true, showStats = true, maxItems, className = \"\" } = param;\n    _s();\n    const [dependencies, setDependencies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredDependencies, setFilteredDependencies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [searchResults, setSearchResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Estados para modales\n    const [selectedDependency, setSelectedDependency] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedProcedure, setSelectedProcedure] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isDependencyModalOpen, setIsDependencyModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isProcedureModalOpen, setIsProcedureModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Cargar datos iniciales\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadDependencies();\n    }, []);\n    // Filtrar dependencias cuando cambia la búsqueda\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        filterDependencies();\n    }, [\n        searchQuery,\n        dependencies\n    ]);\n    const loadDependencies = async ()=>{\n        try {\n            setIsLoading(true);\n            setError(null);\n            const [allDependencies, dependencyStats] = await Promise.all([\n                _lib_services_dependencyService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getAllDependencies(),\n                showStats ? _lib_services_dependencyService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getDependencyStats() : Promise.resolve(null)\n            ]);\n            setDependencies(allDependencies);\n            setStats(dependencyStats);\n        } catch (err) {\n            console.error(\"Error cargando dependencias:\", err);\n            setError(\"Error al cargar las dependencias. Por favor, intenta de nuevo.\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const filterDependencies = async ()=>{\n        try {\n            if (!searchQuery.trim()) {\n                let result = dependencies;\n                if (maxItems) {\n                    result = result.slice(0, maxItems);\n                }\n                setFilteredDependencies(result);\n                return;\n            }\n            const searchResults = await _lib_services_dependencyService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].searchDependencies(searchQuery);\n            let result = searchResults;\n            if (maxItems) {\n                result = result.slice(0, maxItems);\n            }\n            setFilteredDependencies(result);\n        } catch (err) {\n            console.error(\"Error filtrando dependencias:\", err);\n        }\n    };\n    const handleDependencyClick = (dependency)=>{\n        if (onDependencySelect) {\n            onDependencySelect(dependency);\n        } else {\n            // Abrir modal de dependencia\n            setSelectedDependency(dependency);\n            setIsDependencyModalOpen(true);\n        }\n    };\n    // Manejar búsqueda avanzada\n    const handleAdvancedSearch = (query, results)=>{\n        setSearchQuery(query);\n        setSearchResults(results);\n    };\n    // Manejar selección de resultado de búsqueda\n    const handleSearchResultSelect = (result)=>{\n        if (result.type === \"DEPENDENCIA\") {\n            // Buscar la dependencia completa\n            const dependency = dependencies.find((dep)=>dep.id === result.id);\n            if (dependency) {\n                setSelectedDependency(dependency);\n                setIsDependencyModalOpen(true);\n            }\n        } else {\n            // Es un trámite o OPA - abrir modal de procedimiento\n            setSelectedProcedure(result);\n            setIsProcedureModalOpen(true);\n        }\n    };\n    // Manejar selección de procedimiento desde modal de dependencia\n    const handleProcedureSelect = (procedure)=>{\n        setIsDependencyModalOpen(false);\n        setSelectedProcedure(procedure);\n        setIsProcedureModalOpen(true);\n    };\n    const getIcon = (iconName)=>{\n        const IconComponent = iconMap[iconName] || _barrel_optimize_names_ArrowRight_Banknote_BarChart3_Building_Building2_Car_Crown_FileText_Filter_Folder_GraduationCap_HeartPulse_Map_Network_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"];\n        return IconComponent;\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: className,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_loading_states__WEBPACK_IMPORTED_MODULE_9__.DependencyGridLoading, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                lineNumber: 179,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n            lineNumber: 178,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: className,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_error_states__WEBPACK_IMPORTED_MODULE_10__.ErrorState, {\n                type: \"server\",\n                title: \"Error al cargar dependencias\",\n                description: error,\n                onRetry: loadDependencies,\n                onGoHome: ()=>window.location.reload(),\n                size: \"lg\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                lineNumber: 187,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n            lineNumber: 186,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 \".concat(className),\n        children: [\n            showStats && stats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"hover:shadow-xl transition-all duration-300 group\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-primary/10 rounded-xl group-hover:bg-primary/20 transition-colors duration-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Banknote_BarChart3_Building_Building2_Car_Crown_FileText_Filter_Folder_GraduationCap_HeartPulse_Map_Network_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"h-6 w-6 text-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-semibold text-gray-600\",\n                                                children: \"Dependencias\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-3xl font-bold text-primary group-hover:scale-105 transition-transform duration-300\",\n                                                children: stats.totalDependencies\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"hover:shadow-glow-green transition-all duration-300 group\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-chia-green-100 rounded-xl group-hover:bg-chia-green-200 transition-colors duration-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Banknote_BarChart3_Building_Building2_Car_Crown_FileText_Filter_Folder_GraduationCap_HeartPulse_Map_Network_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            className: \"h-6 w-6 text-chia-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-semibold text-gray-600\",\n                                                children: \"Tr\\xe1mites\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-3xl font-bold text-chia-green-900 group-hover:scale-105 transition-transform duration-300\",\n                                                children: stats.totalTramites.toLocaleString()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"hover:shadow-xl transition-all duration-300 group\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-chia-blue-100 rounded-xl group-hover:bg-chia-blue-200 transition-colors duration-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Banknote_BarChart3_Building_Building2_Car_Crown_FileText_Filter_Folder_GraduationCap_HeartPulse_Map_Network_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            className: \"h-6 w-6 text-chia-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-semibold text-gray-600\",\n                                                children: \"OPAs\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-3xl font-bold text-chia-blue-900 group-hover:scale-105 transition-transform duration-300\",\n                                                children: stats.totalOPAs.toLocaleString()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"hover:shadow-xl transition-all duration-300 group\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-purple-100 rounded-xl group-hover:bg-purple-200 transition-colors duration-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Banknote_BarChart3_Building_Building2_Car_Crown_FileText_Filter_Folder_GraduationCap_HeartPulse_Map_Network_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                            className: \"h-6 w-6 text-purple-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-semibold text-gray-600\",\n                                                children: \"Total Procedimientos\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-3xl font-bold text-purple-900 group-hover:scale-105 transition-transform duration-300\",\n                                                children: stats.totalProcedures.toLocaleString()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                            lineNumber: 247,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                lineNumber: 203,\n                columnNumber: 9\n            }, this),\n            showSearch && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedDependencySearch__WEBPACK_IMPORTED_MODULE_6__.AdvancedDependencySearch, {\n                onSearch: handleAdvancedSearch,\n                onResultSelect: handleSearchResultSelect,\n                placeholder: \"Buscar dependencias, tr\\xe1mites o servicios...\",\n                className: \"max-w-2xl\",\n                maxResults: 15\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                lineNumber: 264,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                children: filteredDependencies.map((dependency)=>{\n                    const IconComponent = getIcon(dependency.icon || \"folder\");\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"cursor-pointer transition-all duration-300 hover:shadow-xl hover:scale-105 border-2 group \".concat(dependency.color, \" hover:border-chia-blue-400/60\"),\n                        onClick: ()=>handleDependencyClick(dependency),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                className: \"pb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 rounded-2xl bg-white/80 backdrop-blur-sm shadow-lg group-hover:shadow-xl group-hover:scale-110 transition-all duration-300\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                            className: \"h-7 w-7 text-chia-blue-700\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                                            lineNumber: 288,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                                        lineNumber: 287,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                className: \"text-xl font-bold text-chia-blue-900 leading-tight group-hover:text-chia-blue-700 transition-colors\",\n                                                                children: dependency.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                                                lineNumber: 291,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                variant: \"secondary\",\n                                                                className: \"mt-2 text-xs font-semibold\",\n                                                                children: dependency.sigla\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                                                lineNumber: 294,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Banknote_BarChart3_Building_Building2_Car_Crown_FileText_Filter_Folder_GraduationCap_HeartPulse_Map_Network_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                className: \"h-5 w-5 text-gray-400 group-hover:text-chia-blue-600 group-hover:translate-x-1 transition-all duration-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 17\n                                    }, this),\n                                    dependency.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        className: \"text-sm text-gray-700 mt-3 leading-relaxed\",\n                                        children: dependency.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                lineNumber: 284,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"pt-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gradient-to-br from-chia-green-50 to-chia-green-100 rounded-xl p-4 border border-chia-green-200/50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"p-2 bg-chia-green-200 rounded-lg\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Banknote_BarChart3_Building_Building2_Car_Crown_FileText_Filter_Folder_GraduationCap_HeartPulse_Map_Network_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-chia-green-700\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                                                        lineNumber: 317,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                                                    lineNumber: 316,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                    variant: \"secondary\",\n                                                                    className: \"bg-chia-green-200 text-chia-green-800 text-xs\",\n                                                                    children: \"Tr\\xe1mites\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                                                    lineNumber: 319,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                                            lineNumber: 315,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-2xl font-bold text-chia-green-800 group-hover:scale-110 transition-transform duration-300\",\n                                                                    children: dependency.tramitesCount\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                                                    lineNumber: 324,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs font-medium text-chia-green-600\",\n                                                                    children: \"disponibles\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                                                    lineNumber: 327,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                                            lineNumber: 323,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-4 border border-blue-200/50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"p-2 bg-blue-200 rounded-lg\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Banknote_BarChart3_Building_Building2_Car_Crown_FileText_Filter_Folder_GraduationCap_HeartPulse_Map_Network_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-blue-700\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                                                        lineNumber: 335,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                                                    lineNumber: 334,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                    variant: \"secondary\",\n                                                                    className: \"bg-blue-200 text-blue-800 text-xs\",\n                                                                    children: \"OPAs\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                                                    lineNumber: 337,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                                            lineNumber: 333,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-2xl font-bold text-blue-800 group-hover:scale-110 transition-transform duration-300\",\n                                                                    children: dependency.opasCount\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                                                    lineNumber: 342,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs font-medium text-blue-600\",\n                                                                    children: \"disponibles\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                                                    lineNumber: 345,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                                            lineNumber: 341,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between pt-3 border-t border-gray-200/60\",\n                                            children: [\n                                                dependency.subdependenciasCount > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-1.5 bg-purple-100 rounded-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Banknote_BarChart3_Building_Building2_Car_Crown_FileText_Filter_Folder_GraduationCap_HeartPulse_Map_Network_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                className: \"h-4 w-4 text-purple-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                                                lineNumber: 356,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                                            lineNumber: 355,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold text-purple-700 text-sm\",\n                                                            children: [\n                                                                dependency.subdependenciasCount,\n                                                                \" subdependencias\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                                            lineNumber: 358,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                                    lineNumber: 354,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-1.5 bg-gray-100 rounded-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Banknote_BarChart3_Building_Building2_Car_Crown_FileText_Filter_Folder_GraduationCap_HeartPulse_Map_Network_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                className: \"h-4 w-4 text-gray-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                                                lineNumber: 365,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                                            lineNumber: 364,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-500 text-sm\",\n                                                            children: \"Dependencia \\xfanica\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                                    lineNumber: 363,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-bold text-chia-blue-900 group-hover:scale-110 transition-transform duration-300\",\n                                                            children: dependency.totalProcedures\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                                            lineNumber: 375,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs font-medium text-gray-500\",\n                                                            children: \"total servicios\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                                            lineNumber: 378,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                                    lineNumber: 374,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center pt-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                variant: \"outline\",\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"text-xs font-semibold transition-all duration-300\", dependency.totalProcedures > 10 ? \"text-chia-green-600 border-chia-green-300 bg-chia-green-50 group-hover:bg-chia-green-100\" : dependency.totalProcedures > 5 ? \"text-orange-600 border-orange-300 bg-orange-50 group-hover:bg-orange-100\" : \"text-gray-600 border-gray-300 bg-gray-50 group-hover:bg-gray-100\"),\n                                                children: dependency.totalProcedures > 10 ? \"Alta disponibilidad\" : dependency.totalProcedures > 5 ? \"Disponibilidad media\" : \"Disponibilidad b\\xe1sica\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                            lineNumber: 383,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, dependency.id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                lineNumber: 274,\n                columnNumber: 7\n            }, this),\n            filteredDependencies.length === 0 && !isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Banknote_BarChart3_Building_Building2_Car_Crown_FileText_Filter_Folder_GraduationCap_HeartPulse_Map_Network_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                        className: \"h-12 w-12 mx-auto mb-4 text-gray-400\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                        lineNumber: 409,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                        children: \"No se encontraron dependencias\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                        lineNumber: 410,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-4\",\n                        children: searchQuery ? 'No hay dependencias que coincidan con \"'.concat(searchQuery, '\"') : \"No hay dependencias disponibles en este momento\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                        lineNumber: 413,\n                        columnNumber: 11\n                    }, this),\n                    searchQuery && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>setSearchQuery(\"\"),\n                        children: \"Limpiar b\\xfasqueda\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                        lineNumber: 420,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                lineNumber: 408,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DependencyDetailModal__WEBPACK_IMPORTED_MODULE_7__.DependencyDetailModal, {\n                dependency: selectedDependency,\n                isOpen: isDependencyModalOpen,\n                onClose: ()=>{\n                    setIsDependencyModalOpen(false);\n                    setSelectedDependency(null);\n                },\n                onProcedureSelect: handleProcedureSelect\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                lineNumber: 431,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProcedureDetailModal__WEBPACK_IMPORTED_MODULE_8__.ProcedureDetailModal, {\n                procedure: selectedProcedure,\n                isOpen: isProcedureModalOpen,\n                onClose: ()=>{\n                    setIsProcedureModalOpen(false);\n                    setSelectedProcedure(null);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n                lineNumber: 441,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyGrid.tsx\",\n        lineNumber: 200,\n        columnNumber: 5\n    }, this);\n}\n_s(DependencyGrid, \"+ZuLDHi7aZrk01pWFB63kSCTnug=\");\n_c = DependencyGrid;\nvar _c;\n$RefreshReg$(_c, \"DependencyGrid\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvZGVwZW5kZW5jaWVzL0RlcGVuZGVuY3lHcmlkLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUVrRDtBQUM4QztBQUNqRDtBQUVGO0FBbUJ4QjtBQUNzRjtBQUN0QztBQUNOO0FBQ0Y7QUFFUztBQUNiO0FBRXpCO0FBRWhDLGtCQUFrQjtBQUNsQixNQUFNaUMsVUFBVTtJQUNkQyxPQUFPdEIsZ05BQUtBO0lBQ1p1QixRQUFRdEIsZ05BQU1BO0lBQ2R1QixVQUFVdEIsZ05BQVFBO0lBQ2xCdUIsS0FBS3RCLGdOQUFHQTtJQUNSdUIsT0FBT3RCLGdOQUFLQTtJQUNaLGtCQUFrQkMsZ05BQWFBO0lBQy9CLGVBQWVDLGdOQUFVQTtJQUN6QnFCLEtBQUtwQixnTkFBR0E7SUFDUnFCLFVBQVVwQixnTkFBUUE7SUFDbEJxQixRQUFRcEIsZ05BQU1BO0FBQ2hCO0FBVU8sU0FBU3FCLGVBQWUsS0FNVDtRQU5TLEVBQzdCQyxrQkFBa0IsRUFDbEJDLGFBQWEsSUFBSSxFQUNqQkMsWUFBWSxJQUFJLEVBQ2hCQyxRQUFRLEVBQ1JDLFlBQVksRUFBRSxFQUNNLEdBTlM7O0lBTzdCLE1BQU0sQ0FBQ0MsY0FBY0MsZ0JBQWdCLEdBQUdoRCwrQ0FBUUEsQ0FBZSxFQUFFO0lBQ2pFLE1BQU0sQ0FBQ2lELHNCQUFzQkMsd0JBQXdCLEdBQUdsRCwrQ0FBUUEsQ0FBZSxFQUFFO0lBQ2pGLE1BQU0sQ0FBQ21ELE9BQU9DLFNBQVMsR0FBR3BELCtDQUFRQSxDQUF5QjtJQUMzRCxNQUFNLENBQUNxRCxhQUFhQyxlQUFlLEdBQUd0RCwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUN1RCxlQUFlQyxpQkFBaUIsR0FBR3hELCtDQUFRQSxDQUFpQixFQUFFO0lBQ3JFLE1BQU0sQ0FBQ3lELFdBQVdDLGFBQWEsR0FBRzFELCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQzJELE9BQU9DLFNBQVMsR0FBRzVELCtDQUFRQSxDQUFnQjtJQUVsRCx1QkFBdUI7SUFDdkIsTUFBTSxDQUFDNkQsb0JBQW9CQyxzQkFBc0IsR0FBRzlELCtDQUFRQSxDQUFvQjtJQUNoRixNQUFNLENBQUMrRCxtQkFBbUJDLHFCQUFxQixHQUFHaEUsK0NBQVFBLENBQWE7SUFDdkUsTUFBTSxDQUFDaUUsdUJBQXVCQyx5QkFBeUIsR0FBR2xFLCtDQUFRQSxDQUFDO0lBQ25FLE1BQU0sQ0FBQ21FLHNCQUFzQkMsd0JBQXdCLEdBQUdwRSwrQ0FBUUEsQ0FBQztJQUVqRSx5QkFBeUI7SUFDekJDLGdEQUFTQSxDQUFDO1FBQ1JvRTtJQUNGLEdBQUcsRUFBRTtJQUVMLGlEQUFpRDtJQUNqRHBFLGdEQUFTQSxDQUFDO1FBQ1JxRTtJQUNGLEdBQUc7UUFBQ2pCO1FBQWFOO0tBQWE7SUFFOUIsTUFBTXNCLG1CQUFtQjtRQUN2QixJQUFJO1lBQ0ZYLGFBQWE7WUFDYkUsU0FBUztZQUVULE1BQU0sQ0FBQ1csaUJBQWlCQyxnQkFBZ0IsR0FBRyxNQUFNQyxRQUFRQyxHQUFHLENBQUM7Z0JBQzNEakQsdUVBQWlCQSxDQUFDa0Qsa0JBQWtCO2dCQUNwQy9CLFlBQVluQix1RUFBaUJBLENBQUNtRCxrQkFBa0IsS0FBS0gsUUFBUUksT0FBTyxDQUFDO2FBQ3RFO1lBRUQ3QixnQkFBZ0J1QjtZQUNoQm5CLFNBQVNvQjtRQUNYLEVBQUUsT0FBT00sS0FBSztZQUNaQyxRQUFRcEIsS0FBSyxDQUFDLGdDQUFnQ21CO1lBQzlDbEIsU0FBUztRQUNYLFNBQVU7WUFDUkYsYUFBYTtRQUNmO0lBQ0Y7SUFFQSxNQUFNWSxxQkFBcUI7UUFDekIsSUFBSTtZQUNGLElBQUksQ0FBQ2pCLFlBQVkyQixJQUFJLElBQUk7Z0JBQ3ZCLElBQUlDLFNBQVNsQztnQkFDYixJQUFJRixVQUFVO29CQUNab0MsU0FBU0EsT0FBT0MsS0FBSyxDQUFDLEdBQUdyQztnQkFDM0I7Z0JBQ0FLLHdCQUF3QitCO2dCQUN4QjtZQUNGO1lBRUEsTUFBTTFCLGdCQUFnQixNQUFNOUIsdUVBQWlCQSxDQUFDMEQsa0JBQWtCLENBQUM5QjtZQUNqRSxJQUFJNEIsU0FBUzFCO1lBQ2IsSUFBSVYsVUFBVTtnQkFDWm9DLFNBQVNBLE9BQU9DLEtBQUssQ0FBQyxHQUFHckM7WUFDM0I7WUFDQUssd0JBQXdCK0I7UUFDMUIsRUFBRSxPQUFPSCxLQUFLO1lBQ1pDLFFBQVFwQixLQUFLLENBQUMsaUNBQWlDbUI7UUFDakQ7SUFDRjtJQUVBLE1BQU1NLHdCQUF3QixDQUFDQztRQUM3QixJQUFJM0Msb0JBQW9CO1lBQ3RCQSxtQkFBbUIyQztRQUNyQixPQUFPO1lBQ0wsNkJBQTZCO1lBQzdCdkIsc0JBQXNCdUI7WUFDdEJuQix5QkFBeUI7UUFDM0I7SUFDRjtJQUVBLDRCQUE0QjtJQUM1QixNQUFNb0IsdUJBQXVCLENBQUNDLE9BQWVDO1FBQzNDbEMsZUFBZWlDO1FBQ2YvQixpQkFBaUJnQztJQUNuQjtJQUVBLDZDQUE2QztJQUM3QyxNQUFNQywyQkFBMkIsQ0FBQ1I7UUFDaEMsSUFBSUEsT0FBT1MsSUFBSSxLQUFLLGVBQWU7WUFDakMsaUNBQWlDO1lBQ2pDLE1BQU1MLGFBQWF0QyxhQUFhNEMsSUFBSSxDQUFDQyxDQUFBQSxNQUFPQSxJQUFJQyxFQUFFLEtBQUtaLE9BQU9ZLEVBQUU7WUFDaEUsSUFBSVIsWUFBWTtnQkFDZHZCLHNCQUFzQnVCO2dCQUN0Qm5CLHlCQUF5QjtZQUMzQjtRQUNGLE9BQU87WUFDTCxxREFBcUQ7WUFDckRGLHFCQUFxQmlCO1lBQ3JCYix3QkFBd0I7UUFDMUI7SUFDRjtJQUVBLGdFQUFnRTtJQUNoRSxNQUFNMEIsd0JBQXdCLENBQUNDO1FBQzdCN0IseUJBQXlCO1FBQ3pCRixxQkFBcUIrQjtRQUNyQjNCLHdCQUF3QjtJQUMxQjtJQUVBLE1BQU00QixVQUFVLENBQUNDO1FBQ2YsTUFBTUMsZ0JBQWdCbEUsT0FBTyxDQUFDaUUsU0FBaUMsSUFBSTdFLGdOQUFNQTtRQUN6RSxPQUFPOEU7SUFDVDtJQUVBLElBQUl6QyxXQUFXO1FBQ2IscUJBQ0UsOERBQUMwQztZQUFJckQsV0FBV0E7c0JBQ2QsNEVBQUNqQixnRkFBcUJBOzs7Ozs7Ozs7O0lBRzVCO0lBRUEsSUFBSThCLE9BQU87UUFDVCxxQkFDRSw4REFBQ3dDO1lBQUlyRCxXQUFXQTtzQkFDZCw0RUFBQ2hCLG9FQUFVQTtnQkFDVDRELE1BQUs7Z0JBQ0xVLE9BQU07Z0JBQ05DLGFBQWExQztnQkFDYjJDLFNBQVNqQztnQkFDVGtDLFVBQVUsSUFBTUMsT0FBT0MsUUFBUSxDQUFDQyxNQUFNO2dCQUN0Q0MsTUFBSzs7Ozs7Ozs7Ozs7SUFJYjtJQUVBLHFCQUNFLDhEQUFDUjtRQUFJckQsV0FBVyxhQUF1QixPQUFWQTs7WUFFMUJGLGFBQWFPLHVCQUNaLDhEQUFDZ0Q7Z0JBQUlyRCxXQUFVOztrQ0FDYiw4REFBQzVDLHFEQUFJQTt3QkFBQzRDLFdBQVU7a0NBQ2QsNEVBQUMzQyw0REFBV0E7NEJBQUMyQyxXQUFVO3NDQUNyQiw0RUFBQ3FEO2dDQUFJckQsV0FBVTs7a0RBQ2IsOERBQUNxRDt3Q0FBSXJELFdBQVU7a0RBQ2IsNEVBQUNwQyxnTkFBU0E7NENBQUNvQyxXQUFVOzs7Ozs7Ozs7OztrREFFdkIsOERBQUNxRDs7MERBQ0MsOERBQUNTO2dEQUFFOUQsV0FBVTswREFBc0M7Ozs7OzswREFDbkQsOERBQUM4RDtnREFBRTlELFdBQVU7MERBQTJGSyxNQUFNMEQsaUJBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU12SSw4REFBQzNHLHFEQUFJQTt3QkFBQzRDLFdBQVU7a0NBQ2QsNEVBQUMzQyw0REFBV0E7NEJBQUMyQyxXQUFVO3NDQUNyQiw0RUFBQ3FEO2dDQUFJckQsV0FBVTs7a0RBQ2IsOERBQUNxRDt3Q0FBSXJELFdBQVU7a0RBQ2IsNEVBQUNyQyxnTkFBUUE7NENBQUNxQyxXQUFVOzs7Ozs7Ozs7OztrREFFdEIsOERBQUNxRDs7MERBQ0MsOERBQUNTO2dEQUFFOUQsV0FBVTswREFBc0M7Ozs7OzswREFDbkQsOERBQUM4RDtnREFBRTlELFdBQVU7MERBQWtHSyxNQUFNMkQsYUFBYSxDQUFDQyxjQUFjOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU16Siw4REFBQzdHLHFEQUFJQTt3QkFBQzRDLFdBQVU7a0NBQ2QsNEVBQUMzQyw0REFBV0E7NEJBQUMyQyxXQUFVO3NDQUNyQiw0RUFBQ3FEO2dDQUFJckQsV0FBVTs7a0RBQ2IsOERBQUNxRDt3Q0FBSXJELFdBQVU7a0RBQ2IsNEVBQUN2QixnTkFBU0E7NENBQUN1QixXQUFVOzs7Ozs7Ozs7OztrREFFdkIsOERBQUNxRDs7MERBQ0MsOERBQUNTO2dEQUFFOUQsV0FBVTswREFBc0M7Ozs7OzswREFDbkQsOERBQUM4RDtnREFBRTlELFdBQVU7MERBQWlHSyxNQUFNNkQsU0FBUyxDQUFDRCxjQUFjOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU1wSiw4REFBQzdHLHFEQUFJQTt3QkFBQzRDLFdBQVU7a0NBQ2QsNEVBQUMzQyw0REFBV0E7NEJBQUMyQyxXQUFVO3NDQUNyQiw0RUFBQ3FEO2dDQUFJckQsV0FBVTs7a0RBQ2IsOERBQUNxRDt3Q0FBSXJELFdBQVU7a0RBQ2IsNEVBQUN4QixnTkFBTUE7NENBQUN3QixXQUFVOzs7Ozs7Ozs7OztrREFFcEIsOERBQUNxRDs7MERBQ0MsOERBQUNTO2dEQUFFOUQsV0FBVTswREFBc0M7Ozs7OzswREFDbkQsOERBQUM4RDtnREFBRTlELFdBQVU7MERBQThGSyxNQUFNOEQsZUFBZSxDQUFDRixjQUFjOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBUzFKcEUsNEJBQ0MsOERBQUNqQiwrRUFBd0JBO2dCQUN2QndGLFVBQVU1QjtnQkFDVjZCLGdCQUFnQjFCO2dCQUNoQjJCLGFBQVk7Z0JBQ1p0RSxXQUFVO2dCQUNWdUUsWUFBWTs7Ozs7OzBCQUtoQiw4REFBQ2xCO2dCQUFJckQsV0FBVTswQkFDWkcscUJBQXFCYixHQUFHLENBQUMsQ0FBQ2lEO29CQUN6QixNQUFNYSxnQkFBZ0JGLFFBQVFYLFdBQVdpQyxJQUFJLElBQUk7b0JBRWpELHFCQUNFLDhEQUFDcEgscURBQUlBO3dCQUVINEMsV0FBVyw2RkFBOEcsT0FBakJ1QyxXQUFXa0MsS0FBSyxFQUFDO3dCQUN6SEMsU0FBUyxJQUFNcEMsc0JBQXNCQzs7MENBRXJDLDhEQUFDaEYsMkRBQVVBO2dDQUFDeUMsV0FBVTs7a0RBQ3BCLDhEQUFDcUQ7d0NBQUlyRCxXQUFVOzswREFDYiw4REFBQ3FEO2dEQUFJckQsV0FBVTs7a0VBQ2IsOERBQUNxRDt3REFBSXJELFdBQVU7a0VBQ2IsNEVBQUNvRDs0REFBY3BELFdBQVU7Ozs7Ozs7Ozs7O2tFQUUzQiw4REFBQ3FEO3dEQUFJckQsV0FBVTs7MEVBQ2IsOERBQUN4QywwREFBU0E7Z0VBQUN3QyxXQUFVOzBFQUNsQnVDLFdBQVdvQyxJQUFJOzs7Ozs7MEVBRWxCLDhEQUFDakgsdURBQUtBO2dFQUFDa0gsU0FBUTtnRUFBWTVFLFdBQVU7MEVBQ2xDdUMsV0FBV3NDLEtBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswREFJdkIsOERBQUN0RyxnTkFBVUE7Z0RBQUN5QixXQUFVOzs7Ozs7Ozs7Ozs7b0NBR3ZCdUMsV0FBV2dCLFdBQVcsa0JBQ3JCLDhEQUFDakcsZ0VBQWVBO3dDQUFDMEMsV0FBVTtrREFDeEJ1QyxXQUFXZ0IsV0FBVzs7Ozs7Ozs7Ozs7OzBDQUs3Qiw4REFBQ2xHLDREQUFXQTtnQ0FBQzJDLFdBQVU7MENBQ3JCLDRFQUFDcUQ7b0NBQUlyRCxXQUFVOztzREFFYiw4REFBQ3FEOzRDQUFJckQsV0FBVTs7OERBRWIsOERBQUNxRDtvREFBSXJELFdBQVU7O3NFQUNiLDhEQUFDcUQ7NERBQUlyRCxXQUFVOzs4RUFDYiw4REFBQ3FEO29FQUFJckQsV0FBVTs4RUFDYiw0RUFBQ3JDLGdOQUFRQTt3RUFBQ3FDLFdBQVU7Ozs7Ozs7Ozs7OzhFQUV0Qiw4REFBQ3RDLHVEQUFLQTtvRUFBQ2tILFNBQVE7b0VBQVk1RSxXQUFVOzhFQUFnRDs7Ozs7Ozs7Ozs7O3NFQUl2Riw4REFBQ3FEOzREQUFJckQsV0FBVTs7OEVBQ2IsOERBQUM4RDtvRUFBRTlELFdBQVU7OEVBQ1Z1QyxXQUFXdUMsYUFBYTs7Ozs7OzhFQUUzQiw4REFBQ2hCO29FQUFFOUQsV0FBVTs4RUFBMEM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4REFLM0QsOERBQUNxRDtvREFBSXJELFdBQVU7O3NFQUNiLDhEQUFDcUQ7NERBQUlyRCxXQUFVOzs4RUFDYiw4REFBQ3FEO29FQUFJckQsV0FBVTs4RUFDYiw0RUFBQ3ZCLGdOQUFTQTt3RUFBQ3VCLFdBQVU7Ozs7Ozs7Ozs7OzhFQUV2Qiw4REFBQ3RDLHVEQUFLQTtvRUFBQ2tILFNBQVE7b0VBQVk1RSxXQUFVOzhFQUFvQzs7Ozs7Ozs7Ozs7O3NFQUkzRSw4REFBQ3FEOzREQUFJckQsV0FBVTs7OEVBQ2IsOERBQUM4RDtvRUFBRTlELFdBQVU7OEVBQ1Z1QyxXQUFXd0MsU0FBUzs7Ozs7OzhFQUV2Qiw4REFBQ2pCO29FQUFFOUQsV0FBVTs4RUFBb0M7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzREFNdkQsOERBQUNxRDs0Q0FBSXJELFdBQVU7O2dEQUVadUMsV0FBV3lDLG9CQUFvQixHQUFHLGtCQUNqQyw4REFBQzNCO29EQUFJckQsV0FBVTs7c0VBQ2IsOERBQUNxRDs0REFBSXJELFdBQVU7c0VBQ2IsNEVBQUN0QixnTkFBT0E7Z0VBQUNzQixXQUFVOzs7Ozs7Ozs7OztzRUFFckIsOERBQUNpRjs0REFBS2pGLFdBQVU7O2dFQUNidUMsV0FBV3lDLG9CQUFvQjtnRUFBQzs7Ozs7Ozs7Ozs7O3lFQUlyQyw4REFBQzNCO29EQUFJckQsV0FBVTs7c0VBQ2IsOERBQUNxRDs0REFBSXJELFdBQVU7c0VBQ2IsNEVBQUNwQyxnTkFBU0E7Z0VBQUNvQyxXQUFVOzs7Ozs7Ozs7OztzRUFFdkIsOERBQUNpRjs0REFBS2pGLFdBQVU7c0VBQW9DOzs7Ozs7Ozs7Ozs7OERBT3hELDhEQUFDcUQ7b0RBQUlyRCxXQUFVOztzRUFDYiw4REFBQzhEOzREQUFFOUQsV0FBVTtzRUFDVnVDLFdBQVc0QixlQUFlOzs7Ozs7c0VBRTdCLDhEQUFDTDs0REFBRTlELFdBQVU7c0VBQW9DOzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBS3JELDhEQUFDcUQ7NENBQUlyRCxXQUFVO3NEQUNiLDRFQUFDdEMsdURBQUtBO2dEQUNKa0gsU0FBUTtnREFDUjVFLFdBQVdmLCtDQUFFQSxDQUNYLHFEQUNBc0QsV0FBVzRCLGVBQWUsR0FBRyxLQUN6Qiw2RkFDQTVCLFdBQVc0QixlQUFlLEdBQUcsSUFDN0IsNkVBQ0E7MERBR0w1QixXQUFXNEIsZUFBZSxHQUFHLEtBQUssd0JBQ2xDNUIsV0FBVzRCLGVBQWUsR0FBRyxJQUFJLHlCQUF5Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7dUJBcEg5RDVCLFdBQVdRLEVBQUU7Ozs7O2dCQTJIeEI7Ozs7OztZQUlENUMscUJBQXFCK0UsTUFBTSxLQUFLLEtBQUssQ0FBQ3ZFLDJCQUNyQyw4REFBQzBDO2dCQUFJckQsV0FBVTs7a0NBQ2IsOERBQUNwQyxnTkFBU0E7d0JBQUNvQyxXQUFVOzs7Ozs7a0NBQ3JCLDhEQUFDbUY7d0JBQUduRixXQUFVO2tDQUF5Qzs7Ozs7O2tDQUd2RCw4REFBQzhEO3dCQUFFOUQsV0FBVTtrQ0FDVk8sY0FDRywwQ0FBc0QsT0FBWkEsYUFBWSxPQUN0RDs7Ozs7O29CQUdMQSw2QkFDQyw4REFBQzlDLHlEQUFNQTt3QkFDTG1ILFNBQVE7d0JBQ1JGLFNBQVMsSUFBTWxFLGVBQWU7a0NBQy9COzs7Ozs7Ozs7Ozs7MEJBUVAsOERBQUMzQix5RUFBcUJBO2dCQUNwQjBELFlBQVl4QjtnQkFDWnFFLFFBQVFqRTtnQkFDUmtFLFNBQVM7b0JBQ1BqRSx5QkFBeUI7b0JBQ3pCSixzQkFBc0I7Z0JBQ3hCO2dCQUNBc0UsbUJBQW1CdEM7Ozs7OzswQkFHckIsOERBQUNsRSx1RUFBb0JBO2dCQUNuQm1FLFdBQVdoQztnQkFDWG1FLFFBQVEvRDtnQkFDUmdFLFNBQVM7b0JBQ1AvRCx3QkFBd0I7b0JBQ3hCSixxQkFBcUI7Z0JBQ3ZCOzs7Ozs7Ozs7Ozs7QUFJUjtHQXhZZ0J2QjtLQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9jb21wb25lbnRzL2RlcGVuZGVuY2llcy9EZXBlbmRlbmN5R3JpZC50c3g/NGYyNiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IENhcmQsIENhcmRDb250ZW50LCBDYXJkRGVzY3JpcHRpb24sIENhcmRIZWFkZXIsIENhcmRUaXRsZSB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9jYXJkJ1xuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2J1dHRvbidcbmltcG9ydCB7IElucHV0IH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2lucHV0J1xuaW1wb3J0IHsgQmFkZ2UgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYmFkZ2UnXG5pbXBvcnQge1xuICBTZWFyY2gsXG4gIEZpbGVUZXh0LFxuICBCdWlsZGluZzIsXG4gIENyb3duLFxuICBTaGllbGQsXG4gIEJhbmtub3RlLFxuICBNYXAsXG4gIFVzZXJzLFxuICBHcmFkdWF0aW9uQ2FwLFxuICBIZWFydFB1bHNlLFxuICBDYXIsXG4gIEJ1aWxkaW5nLFxuICBGb2xkZXIsXG4gIEFycm93UmlnaHQsXG4gIEZpbHRlcixcbiAgQmFyQ2hhcnQzLFxuICBOZXR3b3JrXG59IGZyb20gJ2x1Y2lkZS1yZWFjdCdcbmltcG9ydCBEZXBlbmRlbmN5U2VydmljZSwgeyB0eXBlIERlcGVuZGVuY3ksIHR5cGUgRGVwZW5kZW5jeVN0YXRzIH0gZnJvbSAnQC9saWIvc2VydmljZXMvZGVwZW5kZW5jeVNlcnZpY2UnXG5pbXBvcnQgeyBBZHZhbmNlZERlcGVuZGVuY3lTZWFyY2ggfSBmcm9tICcuL0FkdmFuY2VkRGVwZW5kZW5jeVNlYXJjaCdcbmltcG9ydCB7IERlcGVuZGVuY3lEZXRhaWxNb2RhbCB9IGZyb20gJy4vRGVwZW5kZW5jeURldGFpbE1vZGFsJ1xuaW1wb3J0IHsgUHJvY2VkdXJlRGV0YWlsTW9kYWwgfSBmcm9tICcuL1Byb2NlZHVyZURldGFpbE1vZGFsJ1xuaW1wb3J0IHsgU2VhcmNoUmVzdWx0IH0gZnJvbSAnQC9saWIvc2VydmljZXMvc2VhcmNoU2VydmljZSdcbmltcG9ydCB7IERlcGVuZGVuY3lHcmlkTG9hZGluZyB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9sb2FkaW5nLXN0YXRlcydcbmltcG9ydCB7IEVycm9yU3RhdGUgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvZXJyb3Itc3RhdGVzJ1xuaW1wb3J0IHsgdXNlRGVwZW5kZW5jeU5hdmlnYXRpb24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYnJlYWRjcnVtYidcbmltcG9ydCB7IGNuIH0gZnJvbSAnQC9saWIvdXRpbHMnXG5cbi8vIE1hcGVvIGRlIGljb25vc1xuY29uc3QgaWNvbk1hcCA9IHtcbiAgY3Jvd246IENyb3duLFxuICBzaGllbGQ6IFNoaWVsZCxcbiAgYmFua25vdGU6IEJhbmtub3RlLFxuICBtYXA6IE1hcCxcbiAgdXNlcnM6IFVzZXJzLFxuICAnZ3JhZHVhdGlvbi1jYXAnOiBHcmFkdWF0aW9uQ2FwLFxuICAnaGVhcnQtcHVsc2UnOiBIZWFydFB1bHNlLFxuICBjYXI6IENhcixcbiAgYnVpbGRpbmc6IEJ1aWxkaW5nLFxuICBmb2xkZXI6IEZvbGRlclxufVxuXG5pbnRlcmZhY2UgRGVwZW5kZW5jeUdyaWRQcm9wcyB7XG4gIG9uRGVwZW5kZW5jeVNlbGVjdD86IChkZXBlbmRlbmN5OiBEZXBlbmRlbmN5KSA9PiB2b2lkXG4gIHNob3dTZWFyY2g/OiBib29sZWFuXG4gIHNob3dTdGF0cz86IGJvb2xlYW5cbiAgbWF4SXRlbXM/OiBudW1iZXJcbiAgY2xhc3NOYW1lPzogc3RyaW5nXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBEZXBlbmRlbmN5R3JpZCh7XG4gIG9uRGVwZW5kZW5jeVNlbGVjdCxcbiAgc2hvd1NlYXJjaCA9IHRydWUsXG4gIHNob3dTdGF0cyA9IHRydWUsXG4gIG1heEl0ZW1zLFxuICBjbGFzc05hbWUgPSAnJ1xufTogRGVwZW5kZW5jeUdyaWRQcm9wcykge1xuICBjb25zdCBbZGVwZW5kZW5jaWVzLCBzZXREZXBlbmRlbmNpZXNdID0gdXNlU3RhdGU8RGVwZW5kZW5jeVtdPihbXSlcbiAgY29uc3QgW2ZpbHRlcmVkRGVwZW5kZW5jaWVzLCBzZXRGaWx0ZXJlZERlcGVuZGVuY2llc10gPSB1c2VTdGF0ZTxEZXBlbmRlbmN5W10+KFtdKVxuICBjb25zdCBbc3RhdHMsIHNldFN0YXRzXSA9IHVzZVN0YXRlPERlcGVuZGVuY3lTdGF0cyB8IG51bGw+KG51bGwpXG4gIGNvbnN0IFtzZWFyY2hRdWVyeSwgc2V0U2VhcmNoUXVlcnldID0gdXNlU3RhdGUoJycpXG4gIGNvbnN0IFtzZWFyY2hSZXN1bHRzLCBzZXRTZWFyY2hSZXN1bHRzXSA9IHVzZVN0YXRlPFNlYXJjaFJlc3VsdFtdPihbXSlcbiAgY29uc3QgW2lzTG9hZGluZywgc2V0SXNMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpXG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbClcblxuICAvLyBFc3RhZG9zIHBhcmEgbW9kYWxlc1xuICBjb25zdCBbc2VsZWN0ZWREZXBlbmRlbmN5LCBzZXRTZWxlY3RlZERlcGVuZGVuY3ldID0gdXNlU3RhdGU8RGVwZW5kZW5jeSB8IG51bGw+KG51bGwpXG4gIGNvbnN0IFtzZWxlY3RlZFByb2NlZHVyZSwgc2V0U2VsZWN0ZWRQcm9jZWR1cmVdID0gdXNlU3RhdGU8YW55IHwgbnVsbD4obnVsbClcbiAgY29uc3QgW2lzRGVwZW5kZW5jeU1vZGFsT3Blbiwgc2V0SXNEZXBlbmRlbmN5TW9kYWxPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbaXNQcm9jZWR1cmVNb2RhbE9wZW4sIHNldElzUHJvY2VkdXJlTW9kYWxPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKVxuXG4gIC8vIENhcmdhciBkYXRvcyBpbmljaWFsZXNcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBsb2FkRGVwZW5kZW5jaWVzKClcbiAgfSwgW10pXG5cbiAgLy8gRmlsdHJhciBkZXBlbmRlbmNpYXMgY3VhbmRvIGNhbWJpYSBsYSBiw7pzcXVlZGFcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBmaWx0ZXJEZXBlbmRlbmNpZXMoKVxuICB9LCBbc2VhcmNoUXVlcnksIGRlcGVuZGVuY2llc10pXG5cbiAgY29uc3QgbG9hZERlcGVuZGVuY2llcyA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgc2V0SXNMb2FkaW5nKHRydWUpXG4gICAgICBzZXRFcnJvcihudWxsKVxuXG4gICAgICBjb25zdCBbYWxsRGVwZW5kZW5jaWVzLCBkZXBlbmRlbmN5U3RhdHNdID0gYXdhaXQgUHJvbWlzZS5hbGwoW1xuICAgICAgICBEZXBlbmRlbmN5U2VydmljZS5nZXRBbGxEZXBlbmRlbmNpZXMoKSxcbiAgICAgICAgc2hvd1N0YXRzID8gRGVwZW5kZW5jeVNlcnZpY2UuZ2V0RGVwZW5kZW5jeVN0YXRzKCkgOiBQcm9taXNlLnJlc29sdmUobnVsbClcbiAgICAgIF0pXG5cbiAgICAgIHNldERlcGVuZGVuY2llcyhhbGxEZXBlbmRlbmNpZXMpXG4gICAgICBzZXRTdGF0cyhkZXBlbmRlbmN5U3RhdHMpXG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjYXJnYW5kbyBkZXBlbmRlbmNpYXM6JywgZXJyKVxuICAgICAgc2V0RXJyb3IoJ0Vycm9yIGFsIGNhcmdhciBsYXMgZGVwZW5kZW5jaWFzLiBQb3IgZmF2b3IsIGludGVudGEgZGUgbnVldm8uJylcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGZpbHRlckRlcGVuZGVuY2llcyA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgaWYgKCFzZWFyY2hRdWVyeS50cmltKCkpIHtcbiAgICAgICAgbGV0IHJlc3VsdCA9IGRlcGVuZGVuY2llc1xuICAgICAgICBpZiAobWF4SXRlbXMpIHtcbiAgICAgICAgICByZXN1bHQgPSByZXN1bHQuc2xpY2UoMCwgbWF4SXRlbXMpXG4gICAgICAgIH1cbiAgICAgICAgc2V0RmlsdGVyZWREZXBlbmRlbmNpZXMocmVzdWx0KVxuICAgICAgICByZXR1cm5cbiAgICAgIH1cblxuICAgICAgY29uc3Qgc2VhcmNoUmVzdWx0cyA9IGF3YWl0IERlcGVuZGVuY3lTZXJ2aWNlLnNlYXJjaERlcGVuZGVuY2llcyhzZWFyY2hRdWVyeSlcbiAgICAgIGxldCByZXN1bHQgPSBzZWFyY2hSZXN1bHRzXG4gICAgICBpZiAobWF4SXRlbXMpIHtcbiAgICAgICAgcmVzdWx0ID0gcmVzdWx0LnNsaWNlKDAsIG1heEl0ZW1zKVxuICAgICAgfVxuICAgICAgc2V0RmlsdGVyZWREZXBlbmRlbmNpZXMocmVzdWx0KVxuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmlsdHJhbmRvIGRlcGVuZGVuY2lhczonLCBlcnIpXG4gICAgfVxuICB9XG5cbiAgY29uc3QgaGFuZGxlRGVwZW5kZW5jeUNsaWNrID0gKGRlcGVuZGVuY3k6IERlcGVuZGVuY3kpID0+IHtcbiAgICBpZiAob25EZXBlbmRlbmN5U2VsZWN0KSB7XG4gICAgICBvbkRlcGVuZGVuY3lTZWxlY3QoZGVwZW5kZW5jeSlcbiAgICB9IGVsc2Uge1xuICAgICAgLy8gQWJyaXIgbW9kYWwgZGUgZGVwZW5kZW5jaWFcbiAgICAgIHNldFNlbGVjdGVkRGVwZW5kZW5jeShkZXBlbmRlbmN5KVxuICAgICAgc2V0SXNEZXBlbmRlbmN5TW9kYWxPcGVuKHRydWUpXG4gICAgfVxuICB9XG5cbiAgLy8gTWFuZWphciBiw7pzcXVlZGEgYXZhbnphZGFcbiAgY29uc3QgaGFuZGxlQWR2YW5jZWRTZWFyY2ggPSAocXVlcnk6IHN0cmluZywgcmVzdWx0czogU2VhcmNoUmVzdWx0W10pID0+IHtcbiAgICBzZXRTZWFyY2hRdWVyeShxdWVyeSlcbiAgICBzZXRTZWFyY2hSZXN1bHRzKHJlc3VsdHMpXG4gIH1cblxuICAvLyBNYW5lamFyIHNlbGVjY2nDs24gZGUgcmVzdWx0YWRvIGRlIGLDunNxdWVkYVxuICBjb25zdCBoYW5kbGVTZWFyY2hSZXN1bHRTZWxlY3QgPSAocmVzdWx0OiBTZWFyY2hSZXN1bHQpID0+IHtcbiAgICBpZiAocmVzdWx0LnR5cGUgPT09ICdERVBFTkRFTkNJQScpIHtcbiAgICAgIC8vIEJ1c2NhciBsYSBkZXBlbmRlbmNpYSBjb21wbGV0YVxuICAgICAgY29uc3QgZGVwZW5kZW5jeSA9IGRlcGVuZGVuY2llcy5maW5kKGRlcCA9PiBkZXAuaWQgPT09IHJlc3VsdC5pZClcbiAgICAgIGlmIChkZXBlbmRlbmN5KSB7XG4gICAgICAgIHNldFNlbGVjdGVkRGVwZW5kZW5jeShkZXBlbmRlbmN5KVxuICAgICAgICBzZXRJc0RlcGVuZGVuY3lNb2RhbE9wZW4odHJ1ZSlcbiAgICAgIH1cbiAgICB9IGVsc2Uge1xuICAgICAgLy8gRXMgdW4gdHLDoW1pdGUgbyBPUEEgLSBhYnJpciBtb2RhbCBkZSBwcm9jZWRpbWllbnRvXG4gICAgICBzZXRTZWxlY3RlZFByb2NlZHVyZShyZXN1bHQpXG4gICAgICBzZXRJc1Byb2NlZHVyZU1vZGFsT3Blbih0cnVlKVxuICAgIH1cbiAgfVxuXG4gIC8vIE1hbmVqYXIgc2VsZWNjacOzbiBkZSBwcm9jZWRpbWllbnRvIGRlc2RlIG1vZGFsIGRlIGRlcGVuZGVuY2lhXG4gIGNvbnN0IGhhbmRsZVByb2NlZHVyZVNlbGVjdCA9IChwcm9jZWR1cmU6IGFueSkgPT4ge1xuICAgIHNldElzRGVwZW5kZW5jeU1vZGFsT3BlbihmYWxzZSlcbiAgICBzZXRTZWxlY3RlZFByb2NlZHVyZShwcm9jZWR1cmUpXG4gICAgc2V0SXNQcm9jZWR1cmVNb2RhbE9wZW4odHJ1ZSlcbiAgfVxuXG4gIGNvbnN0IGdldEljb24gPSAoaWNvbk5hbWU6IHN0cmluZykgPT4ge1xuICAgIGNvbnN0IEljb25Db21wb25lbnQgPSBpY29uTWFwW2ljb25OYW1lIGFzIGtleW9mIHR5cGVvZiBpY29uTWFwXSB8fCBGb2xkZXJcbiAgICByZXR1cm4gSWNvbkNvbXBvbmVudFxuICB9XG5cbiAgaWYgKGlzTG9hZGluZykge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT17Y2xhc3NOYW1lfT5cbiAgICAgICAgPERlcGVuZGVuY3lHcmlkTG9hZGluZyAvPlxuICAgICAgPC9kaXY+XG4gICAgKVxuICB9XG5cbiAgaWYgKGVycm9yKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPXtjbGFzc05hbWV9PlxuICAgICAgICA8RXJyb3JTdGF0ZVxuICAgICAgICAgIHR5cGU9XCJzZXJ2ZXJcIlxuICAgICAgICAgIHRpdGxlPVwiRXJyb3IgYWwgY2FyZ2FyIGRlcGVuZGVuY2lhc1wiXG4gICAgICAgICAgZGVzY3JpcHRpb249e2Vycm9yfVxuICAgICAgICAgIG9uUmV0cnk9e2xvYWREZXBlbmRlbmNpZXN9XG4gICAgICAgICAgb25Hb0hvbWU9eygpID0+IHdpbmRvdy5sb2NhdGlvbi5yZWxvYWQoKX1cbiAgICAgICAgICBzaXplPVwibGdcIlxuICAgICAgICAvPlxuICAgICAgPC9kaXY+XG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT17YHNwYWNlLXktNiAke2NsYXNzTmFtZX1gfT5cbiAgICAgIHsvKiBFc3RhZMOtc3RpY2FzICovfVxuICAgICAge3Nob3dTdGF0cyAmJiBzdGF0cyAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtNCBnYXAtNlwiPlxuICAgICAgICAgIDxDYXJkIGNsYXNzTmFtZT1cImhvdmVyOnNoYWRvdy14bCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgZ3JvdXBcIj5cbiAgICAgICAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJwLTZcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtMyBiZy1wcmltYXJ5LzEwIHJvdW5kZWQteGwgZ3JvdXAtaG92ZXI6YmctcHJpbWFyeS8yMCB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0zMDBcIj5cbiAgICAgICAgICAgICAgICAgIDxCdWlsZGluZzIgY2xhc3NOYW1lPVwiaC02IHctNiB0ZXh0LXByaW1hcnlcIiAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTYwMFwiPkRlcGVuZGVuY2lhczwvcD5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LXByaW1hcnkgZ3JvdXAtaG92ZXI6c2NhbGUtMTA1IHRyYW5zaXRpb24tdHJhbnNmb3JtIGR1cmF0aW9uLTMwMFwiPntzdGF0cy50b3RhbERlcGVuZGVuY2llc308L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICA8L0NhcmQ+XG5cbiAgICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCJob3ZlcjpzaGFkb3ctZ2xvdy1ncmVlbiB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgZ3JvdXBcIj5cbiAgICAgICAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJwLTZcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtMyBiZy1jaGlhLWdyZWVuLTEwMCByb3VuZGVkLXhsIGdyb3VwLWhvdmVyOmJnLWNoaWEtZ3JlZW4tMjAwIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTMwMFwiPlxuICAgICAgICAgICAgICAgICAgPEZpbGVUZXh0IGNsYXNzTmFtZT1cImgtNiB3LTYgdGV4dC1jaGlhLWdyZWVuLTYwMFwiIC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktNjAwXCI+VHLDoW1pdGVzPC9wPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRleHQtY2hpYS1ncmVlbi05MDAgZ3JvdXAtaG92ZXI6c2NhbGUtMTA1IHRyYW5zaXRpb24tdHJhbnNmb3JtIGR1cmF0aW9uLTMwMFwiPntzdGF0cy50b3RhbFRyYW1pdGVzLnRvTG9jYWxlU3RyaW5nKCl9PC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgPC9DYXJkPlxuXG4gICAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwiaG92ZXI6c2hhZG93LXhsIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCBncm91cFwiPlxuICAgICAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInAtNlwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC0zIGJnLWNoaWEtYmx1ZS0xMDAgcm91bmRlZC14bCBncm91cC1ob3ZlcjpiZy1jaGlhLWJsdWUtMjAwIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTMwMFwiPlxuICAgICAgICAgICAgICAgICAgPEJhckNoYXJ0MyBjbGFzc05hbWU9XCJoLTYgdy02IHRleHQtY2hpYS1ibHVlLTYwMFwiIC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktNjAwXCI+T1BBczwvcD5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LWNoaWEtYmx1ZS05MDAgZ3JvdXAtaG92ZXI6c2NhbGUtMTA1IHRyYW5zaXRpb24tdHJhbnNmb3JtIGR1cmF0aW9uLTMwMFwiPntzdGF0cy50b3RhbE9QQXMudG9Mb2NhbGVTdHJpbmcoKX08L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICA8L0NhcmQ+XG5cbiAgICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCJob3ZlcjpzaGFkb3cteGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIGdyb3VwXCI+XG4gICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwicC02XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTMgYmctcHVycGxlLTEwMCByb3VuZGVkLXhsIGdyb3VwLWhvdmVyOmJnLXB1cnBsZS0yMDAgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMzAwXCI+XG4gICAgICAgICAgICAgICAgICA8RmlsdGVyIGNsYXNzTmFtZT1cImgtNiB3LTYgdGV4dC1wdXJwbGUtNjAwXCIgLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LXNlbWlib2xkIHRleHQtZ3JheS02MDBcIj5Ub3RhbCBQcm9jZWRpbWllbnRvczwvcD5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LXB1cnBsZS05MDAgZ3JvdXAtaG92ZXI6c2NhbGUtMTA1IHRyYW5zaXRpb24tdHJhbnNmb3JtIGR1cmF0aW9uLTMwMFwiPntzdGF0cy50b3RhbFByb2NlZHVyZXMudG9Mb2NhbGVTdHJpbmcoKX08L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cblxuICAgICAgey8qIELDunNxdWVkYSBhdmFuemFkYSAqL31cbiAgICAgIHtzaG93U2VhcmNoICYmIChcbiAgICAgICAgPEFkdmFuY2VkRGVwZW5kZW5jeVNlYXJjaFxuICAgICAgICAgIG9uU2VhcmNoPXtoYW5kbGVBZHZhbmNlZFNlYXJjaH1cbiAgICAgICAgICBvblJlc3VsdFNlbGVjdD17aGFuZGxlU2VhcmNoUmVzdWx0U2VsZWN0fVxuICAgICAgICAgIHBsYWNlaG9sZGVyPVwiQnVzY2FyIGRlcGVuZGVuY2lhcywgdHLDoW1pdGVzIG8gc2VydmljaW9zLi4uXCJcbiAgICAgICAgICBjbGFzc05hbWU9XCJtYXgtdy0yeGxcIlxuICAgICAgICAgIG1heFJlc3VsdHM9ezE1fVxuICAgICAgICAvPlxuICAgICAgKX1cblxuICAgICAgey8qIEdyaWQgZGUgZGVwZW5kZW5jaWFzICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy0zIGdhcC04XCI+XG4gICAgICAgIHtmaWx0ZXJlZERlcGVuZGVuY2llcy5tYXAoKGRlcGVuZGVuY3kpID0+IHtcbiAgICAgICAgICBjb25zdCBJY29uQ29tcG9uZW50ID0gZ2V0SWNvbihkZXBlbmRlbmN5Lmljb24gfHwgJ2ZvbGRlcicpXG5cbiAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgPENhcmRcbiAgICAgICAgICAgICAga2V5PXtkZXBlbmRlbmN5LmlkfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2BjdXJzb3ItcG9pbnRlciB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgaG92ZXI6c2hhZG93LXhsIGhvdmVyOnNjYWxlLTEwNSBib3JkZXItMiBncm91cCAke2RlcGVuZGVuY3kuY29sb3J9IGhvdmVyOmJvcmRlci1jaGlhLWJsdWUtNDAwLzYwYH1cbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlRGVwZW5kZW5jeUNsaWNrKGRlcGVuZGVuY3kpfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8Q2FyZEhlYWRlciBjbGFzc05hbWU9XCJwYi00XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTMgcm91bmRlZC0yeGwgYmctd2hpdGUvODAgYmFja2Ryb3AtYmx1ci1zbSBzaGFkb3ctbGcgZ3JvdXAtaG92ZXI6c2hhZG93LXhsIGdyb3VwLWhvdmVyOnNjYWxlLTExMCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8SWNvbkNvbXBvbmVudCBjbGFzc05hbWU9XCJoLTcgdy03IHRleHQtY2hpYS1ibHVlLTcwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LWJvbGQgdGV4dC1jaGlhLWJsdWUtOTAwIGxlYWRpbmctdGlnaHQgZ3JvdXAtaG92ZXI6dGV4dC1jaGlhLWJsdWUtNzAwIHRyYW5zaXRpb24tY29sb3JzXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7ZGVwZW5kZW5jeS5uYW1lfVxuICAgICAgICAgICAgICAgICAgICAgIDwvQ2FyZFRpdGxlPlxuICAgICAgICAgICAgICAgICAgICAgIDxCYWRnZSB2YXJpYW50PVwic2Vjb25kYXJ5XCIgY2xhc3NOYW1lPVwibXQtMiB0ZXh0LXhzIGZvbnQtc2VtaWJvbGRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtkZXBlbmRlbmN5LnNpZ2xhfVxuICAgICAgICAgICAgICAgICAgICAgIDwvQmFkZ2U+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8QXJyb3dSaWdodCBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtZ3JheS00MDAgZ3JvdXAtaG92ZXI6dGV4dC1jaGlhLWJsdWUtNjAwIGdyb3VwLWhvdmVyOnRyYW5zbGF0ZS14LTEgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwXCIgLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIHtkZXBlbmRlbmN5LmRlc2NyaXB0aW9uICYmIChcbiAgICAgICAgICAgICAgICAgIDxDYXJkRGVzY3JpcHRpb24gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNzAwIG10LTMgbGVhZGluZy1yZWxheGVkXCI+XG4gICAgICAgICAgICAgICAgICAgIHtkZXBlbmRlbmN5LmRlc2NyaXB0aW9ufVxuICAgICAgICAgICAgICAgICAgPC9DYXJkRGVzY3JpcHRpb24+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInB0LTJcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgICAgey8qIEluZm9ybWFjacOzbiBjb25zb2xpZGFkYSBlbiBncmlkICovfVxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0yIGdhcC00XCI+XG4gICAgICAgICAgICAgICAgICAgIHsvKiBUcsOhbWl0ZXMgKi99XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1jaGlhLWdyZWVuLTUwIHRvLWNoaWEtZ3JlZW4tMTAwIHJvdW5kZWQteGwgcC00IGJvcmRlciBib3JkZXItY2hpYS1ncmVlbi0yMDAvNTBcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtMiBiZy1jaGlhLWdyZWVuLTIwMCByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxGaWxlVGV4dCBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtY2hpYS1ncmVlbi03MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8QmFkZ2UgdmFyaWFudD1cInNlY29uZGFyeVwiIGNsYXNzTmFtZT1cImJnLWNoaWEtZ3JlZW4tMjAwIHRleHQtY2hpYS1ncmVlbi04MDAgdGV4dC14c1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICBUcsOhbWl0ZXNcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvQmFkZ2U+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtY2hpYS1ncmVlbi04MDAgZ3JvdXAtaG92ZXI6c2NhbGUtMTEwIHRyYW5zaXRpb24tdHJhbnNmb3JtIGR1cmF0aW9uLTMwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7ZGVwZW5kZW5jeS50cmFtaXRlc0NvdW50fVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LWNoaWEtZ3JlZW4tNjAwXCI+ZGlzcG9uaWJsZXM8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgIHsvKiBPUEFzICovfVxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYWRpZW50LXRvLWJyIGZyb20tYmx1ZS01MCB0by1ibHVlLTEwMCByb3VuZGVkLXhsIHAtNCBib3JkZXIgYm9yZGVyLWJsdWUtMjAwLzUwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTIgYmctYmx1ZS0yMDAgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8QmFyQ2hhcnQzIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1ibHVlLTcwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxCYWRnZSB2YXJpYW50PVwic2Vjb25kYXJ5XCIgY2xhc3NOYW1lPVwiYmctYmx1ZS0yMDAgdGV4dC1ibHVlLTgwMCB0ZXh0LXhzXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIE9QQXNcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvQmFkZ2U+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtYmx1ZS04MDAgZ3JvdXAtaG92ZXI6c2NhbGUtMTEwIHRyYW5zaXRpb24tdHJhbnNmb3JtIGR1cmF0aW9uLTMwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7ZGVwZW5kZW5jeS5vcGFzQ291bnR9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtYmx1ZS02MDBcIj5kaXNwb25pYmxlczwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgey8qIEluZm9ybWFjacOzbiBhZGljaW9uYWwgKi99XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBwdC0zIGJvcmRlci10IGJvcmRlci1ncmF5LTIwMC82MFwiPlxuICAgICAgICAgICAgICAgICAgICB7LyogU3ViZGVwZW5kZW5jaWFzICovfVxuICAgICAgICAgICAgICAgICAgICB7ZGVwZW5kZW5jeS5zdWJkZXBlbmRlbmNpYXNDb3VudCA+IDAgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC0xLjUgYmctcHVycGxlLTEwMCByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxOZXR3b3JrIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1wdXJwbGUtNjAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LXB1cnBsZS03MDAgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7ZGVwZW5kZW5jeS5zdWJkZXBlbmRlbmNpYXNDb3VudH0gc3ViZGVwZW5kZW5jaWFzXG4gICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC0xLjUgYmctZ3JheS0xMDAgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8QnVpbGRpbmcyIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1ncmF5LTUwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICBEZXBlbmRlbmNpYSDDum5pY2FcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgICAgICAgICB7LyogVG90YWwgY29uc29saWRhZG8gKi99XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1yaWdodFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1ib2xkIHRleHQtY2hpYS1ibHVlLTkwMCBncm91cC1ob3ZlcjpzY2FsZS0xMTAgdHJhbnNpdGlvbi10cmFuc2Zvcm0gZHVyYXRpb24tMzAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7ZGVwZW5kZW5jeS50b3RhbFByb2NlZHVyZXN9XG4gICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTUwMFwiPnRvdGFsIHNlcnZpY2lvczwvcD5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgey8qIEluZGljYWRvciBkZSBlc3RhZG8gKi99XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHB0LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgPEJhZGdlXG4gICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgICAgICAgICAgICAgICBcInRleHQteHMgZm9udC1zZW1pYm9sZCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDBcIixcbiAgICAgICAgICAgICAgICAgICAgICAgIGRlcGVuZGVuY3kudG90YWxQcm9jZWR1cmVzID4gMTBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPyBcInRleHQtY2hpYS1ncmVlbi02MDAgYm9yZGVyLWNoaWEtZ3JlZW4tMzAwIGJnLWNoaWEtZ3JlZW4tNTAgZ3JvdXAtaG92ZXI6YmctY2hpYS1ncmVlbi0xMDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICA6IGRlcGVuZGVuY3kudG90YWxQcm9jZWR1cmVzID4gNVxuICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwidGV4dC1vcmFuZ2UtNjAwIGJvcmRlci1vcmFuZ2UtMzAwIGJnLW9yYW5nZS01MCBncm91cC1ob3ZlcjpiZy1vcmFuZ2UtMTAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgOiBcInRleHQtZ3JheS02MDAgYm9yZGVyLWdyYXktMzAwIGJnLWdyYXktNTAgZ3JvdXAtaG92ZXI6YmctZ3JheS0xMDBcIlxuICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICB7ZGVwZW5kZW5jeS50b3RhbFByb2NlZHVyZXMgPiAxMCA/IFwiQWx0YSBkaXNwb25pYmlsaWRhZFwiIDpcbiAgICAgICAgICAgICAgICAgICAgICAgZGVwZW5kZW5jeS50b3RhbFByb2NlZHVyZXMgPiA1ID8gXCJEaXNwb25pYmlsaWRhZCBtZWRpYVwiIDogXCJEaXNwb25pYmlsaWRhZCBiw6FzaWNhXCJ9XG4gICAgICAgICAgICAgICAgICAgIDwvQmFkZ2U+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgICApXG4gICAgICAgIH0pfVxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBNZW5zYWplIGN1YW5kbyBubyBoYXkgcmVzdWx0YWRvcyAqL31cbiAgICAgIHtmaWx0ZXJlZERlcGVuZGVuY2llcy5sZW5ndGggPT09IDAgJiYgIWlzTG9hZGluZyAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktMTJcIj5cbiAgICAgICAgICA8QnVpbGRpbmcyIGNsYXNzTmFtZT1cImgtMTIgdy0xMiBteC1hdXRvIG1iLTQgdGV4dC1ncmF5LTQwMFwiIC8+XG4gICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBtYi0yXCI+XG4gICAgICAgICAgICBObyBzZSBlbmNvbnRyYXJvbiBkZXBlbmRlbmNpYXNcbiAgICAgICAgICA8L2gzPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgbWItNFwiPlxuICAgICAgICAgICAge3NlYXJjaFF1ZXJ5IFxuICAgICAgICAgICAgICA/IGBObyBoYXkgZGVwZW5kZW5jaWFzIHF1ZSBjb2luY2lkYW4gY29uIFwiJHtzZWFyY2hRdWVyeX1cImBcbiAgICAgICAgICAgICAgOiAnTm8gaGF5IGRlcGVuZGVuY2lhcyBkaXNwb25pYmxlcyBlbiBlc3RlIG1vbWVudG8nXG4gICAgICAgICAgICB9XG4gICAgICAgICAgPC9wPlxuICAgICAgICAgIHtzZWFyY2hRdWVyeSAmJiAoXG4gICAgICAgICAgICA8QnV0dG9uIFxuICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiIFxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTZWFyY2hRdWVyeSgnJyl9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIExpbXBpYXIgYsO6c3F1ZWRhXG4gICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG5cbiAgICAgIHsvKiBNb2RhbGVzICovfVxuICAgICAgPERlcGVuZGVuY3lEZXRhaWxNb2RhbFxuICAgICAgICBkZXBlbmRlbmN5PXtzZWxlY3RlZERlcGVuZGVuY3l9XG4gICAgICAgIGlzT3Blbj17aXNEZXBlbmRlbmN5TW9kYWxPcGVufVxuICAgICAgICBvbkNsb3NlPXsoKSA9PiB7XG4gICAgICAgICAgc2V0SXNEZXBlbmRlbmN5TW9kYWxPcGVuKGZhbHNlKVxuICAgICAgICAgIHNldFNlbGVjdGVkRGVwZW5kZW5jeShudWxsKVxuICAgICAgICB9fVxuICAgICAgICBvblByb2NlZHVyZVNlbGVjdD17aGFuZGxlUHJvY2VkdXJlU2VsZWN0fVxuICAgICAgLz5cblxuICAgICAgPFByb2NlZHVyZURldGFpbE1vZGFsXG4gICAgICAgIHByb2NlZHVyZT17c2VsZWN0ZWRQcm9jZWR1cmV9XG4gICAgICAgIGlzT3Blbj17aXNQcm9jZWR1cmVNb2RhbE9wZW59XG4gICAgICAgIG9uQ2xvc2U9eygpID0+IHtcbiAgICAgICAgICBzZXRJc1Byb2NlZHVyZU1vZGFsT3BlbihmYWxzZSlcbiAgICAgICAgICBzZXRTZWxlY3RlZFByb2NlZHVyZShudWxsKVxuICAgICAgICB9fVxuICAgICAgLz5cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJDYXJkIiwiQ2FyZENvbnRlbnQiLCJDYXJkRGVzY3JpcHRpb24iLCJDYXJkSGVhZGVyIiwiQ2FyZFRpdGxlIiwiQnV0dG9uIiwiQmFkZ2UiLCJGaWxlVGV4dCIsIkJ1aWxkaW5nMiIsIkNyb3duIiwiU2hpZWxkIiwiQmFua25vdGUiLCJNYXAiLCJVc2VycyIsIkdyYWR1YXRpb25DYXAiLCJIZWFydFB1bHNlIiwiQ2FyIiwiQnVpbGRpbmciLCJGb2xkZXIiLCJBcnJvd1JpZ2h0IiwiRmlsdGVyIiwiQmFyQ2hhcnQzIiwiTmV0d29yayIsIkRlcGVuZGVuY3lTZXJ2aWNlIiwiQWR2YW5jZWREZXBlbmRlbmN5U2VhcmNoIiwiRGVwZW5kZW5jeURldGFpbE1vZGFsIiwiUHJvY2VkdXJlRGV0YWlsTW9kYWwiLCJEZXBlbmRlbmN5R3JpZExvYWRpbmciLCJFcnJvclN0YXRlIiwiY24iLCJpY29uTWFwIiwiY3Jvd24iLCJzaGllbGQiLCJiYW5rbm90ZSIsIm1hcCIsInVzZXJzIiwiY2FyIiwiYnVpbGRpbmciLCJmb2xkZXIiLCJEZXBlbmRlbmN5R3JpZCIsIm9uRGVwZW5kZW5jeVNlbGVjdCIsInNob3dTZWFyY2giLCJzaG93U3RhdHMiLCJtYXhJdGVtcyIsImNsYXNzTmFtZSIsImRlcGVuZGVuY2llcyIsInNldERlcGVuZGVuY2llcyIsImZpbHRlcmVkRGVwZW5kZW5jaWVzIiwic2V0RmlsdGVyZWREZXBlbmRlbmNpZXMiLCJzdGF0cyIsInNldFN0YXRzIiwic2VhcmNoUXVlcnkiLCJzZXRTZWFyY2hRdWVyeSIsInNlYXJjaFJlc3VsdHMiLCJzZXRTZWFyY2hSZXN1bHRzIiwiaXNMb2FkaW5nIiwic2V0SXNMb2FkaW5nIiwiZXJyb3IiLCJzZXRFcnJvciIsInNlbGVjdGVkRGVwZW5kZW5jeSIsInNldFNlbGVjdGVkRGVwZW5kZW5jeSIsInNlbGVjdGVkUHJvY2VkdXJlIiwic2V0U2VsZWN0ZWRQcm9jZWR1cmUiLCJpc0RlcGVuZGVuY3lNb2RhbE9wZW4iLCJzZXRJc0RlcGVuZGVuY3lNb2RhbE9wZW4iLCJpc1Byb2NlZHVyZU1vZGFsT3BlbiIsInNldElzUHJvY2VkdXJlTW9kYWxPcGVuIiwibG9hZERlcGVuZGVuY2llcyIsImZpbHRlckRlcGVuZGVuY2llcyIsImFsbERlcGVuZGVuY2llcyIsImRlcGVuZGVuY3lTdGF0cyIsIlByb21pc2UiLCJhbGwiLCJnZXRBbGxEZXBlbmRlbmNpZXMiLCJnZXREZXBlbmRlbmN5U3RhdHMiLCJyZXNvbHZlIiwiZXJyIiwiY29uc29sZSIsInRyaW0iLCJyZXN1bHQiLCJzbGljZSIsInNlYXJjaERlcGVuZGVuY2llcyIsImhhbmRsZURlcGVuZGVuY3lDbGljayIsImRlcGVuZGVuY3kiLCJoYW5kbGVBZHZhbmNlZFNlYXJjaCIsInF1ZXJ5IiwicmVzdWx0cyIsImhhbmRsZVNlYXJjaFJlc3VsdFNlbGVjdCIsInR5cGUiLCJmaW5kIiwiZGVwIiwiaWQiLCJoYW5kbGVQcm9jZWR1cmVTZWxlY3QiLCJwcm9jZWR1cmUiLCJnZXRJY29uIiwiaWNvbk5hbWUiLCJJY29uQ29tcG9uZW50IiwiZGl2IiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIm9uUmV0cnkiLCJvbkdvSG9tZSIsIndpbmRvdyIsImxvY2F0aW9uIiwicmVsb2FkIiwic2l6ZSIsInAiLCJ0b3RhbERlcGVuZGVuY2llcyIsInRvdGFsVHJhbWl0ZXMiLCJ0b0xvY2FsZVN0cmluZyIsInRvdGFsT1BBcyIsInRvdGFsUHJvY2VkdXJlcyIsIm9uU2VhcmNoIiwib25SZXN1bHRTZWxlY3QiLCJwbGFjZWhvbGRlciIsIm1heFJlc3VsdHMiLCJpY29uIiwiY29sb3IiLCJvbkNsaWNrIiwibmFtZSIsInZhcmlhbnQiLCJzaWdsYSIsInRyYW1pdGVzQ291bnQiLCJvcGFzQ291bnQiLCJzdWJkZXBlbmRlbmNpYXNDb3VudCIsInNwYW4iLCJsZW5ndGgiLCJoMyIsImlzT3BlbiIsIm9uQ2xvc2UiLCJvblByb2NlZHVyZVNlbGVjdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/dependencies/DependencyGrid.tsx\n"));

/***/ })

});