'use client'

import React, { useState, useEffect, useMemo } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  Search, 
  ChevronDown, 
  ChevronUp, 
  HelpCircle,
  Receipt,
  FileCheck,
  Award,
  Zap,
  FileText,
  CreditCard,
  Filter,
  X
} from 'lucide-react'
import faqService, { FAQItem, FAQTheme } from '@/lib/services/faqService'
import faqAnalytics from '@/lib/services/faqAnalytics'

/**
 * Props para el componente FAQSection
 */
export interface FAQSectionProps {
  /** Título de la sección */
  title?: string
  /** Descripción de la sección */
  description?: string
  /** Número máximo de FAQs a mostrar inicialmente */
  initialLimit?: number
  /** Mostrar búsqueda */
  showSearch?: boolean
  /** Mostrar filtros por categoría */
  showCategoryFilter?: boolean
  /** Mostrar estadísticas */
  showStats?: boolean
  /** Clase CSS adicional */
  className?: string
}

/**
 * Mapeo de iconos para temas municipales
 */
const themeIcons = {
  Receipt,
  FileCheck,
  Award,
  Zap,
  FileText,
  CreditCard,
  HelpCircle
}

/**
 * Componente principal de la sección FAQ
 */
export function FAQSection({
  title = "Preguntas Frecuentes",
  description = "Encuentra respuestas rápidas a las consultas más comunes sobre trámites y servicios municipales",
  initialLimit = 10,
  showSearch = true,
  showCategoryFilter = true,
  showStats = true,
  className = ''
}: FAQSectionProps) {
  // Estados
  const [faqs, setFaqs] = useState<FAQItem[]>([])
  const [themes, setThemes] = useState<FAQTheme[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedTheme, setSelectedTheme] = useState<string>('')
  const [expandedFAQs, setExpandedFAQs] = useState<Set<string>>(new Set())
  const [isLoading, setIsLoading] = useState(true)
  const [showAllFAQs, setShowAllFAQs] = useState(false)
  const [stats, setStats] = useState({
    totalFAQs: 0,
    totalThemes: 0,
    averagePopularity: 0,
    mostPopularTheme: ''
  })

  // Cargar datos iniciales
  useEffect(() => {
    loadInitialData()
  }, [])

  // Realizar búsqueda cuando cambie la query o tema
  useEffect(() => {
    if (searchQuery.trim() || selectedTheme) {
      performSearch()
    }
  }, [searchQuery, selectedTheme, showAllFAQs, initialLimit])

  /**
   * Cargar datos iniciales
   */
  const loadInitialData = async () => {
    const startTime = Date.now()
    try {
      setIsLoading(true)
      const [themesData, popularFAQs, statsData] = await Promise.all([
        faqService.getThemes(),
        faqService.getPopularFAQs(initialLimit),
        faqService.getFAQStats()
      ])

      setThemes(themesData)
      setFaqs(popularFAQs)
      setStats(statsData)

      // Registrar carga de sección
      const loadTime = Date.now() - startTime
      faqAnalytics.trackSectionLoad('faq-section', loadTime)
    } catch (error) {
      console.error('Error loading FAQ data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  /**
   * Realizar búsqueda de FAQs
   */
  const performSearch = async () => {
    try {
      if (searchQuery.trim()) {
        const results = await faqService.searchFAQs(searchQuery, {
          theme: selectedTheme || undefined,
          limit: showAllFAQs ? 50 : initialLimit
        })
        setFaqs(results)
      } else if (selectedTheme) {
        const results = await faqService.getFAQsByTheme(
          selectedTheme,
          showAllFAQs ? undefined : initialLimit
        )
        setFaqs(results)
      } else {
        const results = await faqService.getPopularFAQs(
          showAllFAQs ? 50 : initialLimit
        )
        setFaqs(results)
      }
    } catch (error) {
      console.error('Error searching FAQs:', error)
    }
  }

  /**
   * Alternar expansión de FAQ
   */
  const toggleFAQExpansion = (faqId: string) => {
    const newExpanded = new Set(expandedFAQs)
    if (newExpanded.has(faqId)) {
      newExpanded.delete(faqId)
    } else {
      newExpanded.add(faqId)
      // Registrar visualización cuando se expande
      const faq = faqs.find(f => f.id === faqId)
      if (faq) {
        faqAnalytics.trackFAQView(faq.id, faq.question)
      }
    }
    setExpandedFAQs(newExpanded)
  }

  /**
   * Limpiar filtros
   */
  const clearFilters = () => {
    setSearchQuery('')
    setSelectedTheme('')
    setShowAllFAQs(false)
  }

  /**
   * Obtener icono de tema basado en el nombre del tema
   */
  const getThemeIcon = (themeName: string) => {
    // Mapear nombres de temas a iconos apropiados
    const themeIconMap: Record<string, keyof typeof themeIcons> = {
      'CERTIFICADO': 'FileCheck',
      'IMPUESTOS': 'Receipt',
      'LICENCIAS': 'Award',
      'PERMISOS': 'FileText',
      'PAGOS': 'CreditCard',
      'SERVICIOS': 'Zap'
    }

    // Buscar coincidencia parcial en el nombre del tema
    const iconKey = Object.keys(themeIconMap).find(key =>
      themeName.toUpperCase().includes(key)
    )

    const IconComponent = iconKey
      ? themeIcons[themeIconMap[iconKey]]
      : themeIcons.HelpCircle

    return IconComponent
  }

  // FAQs filtrados y limitados
  const displayedFAQs = useMemo(() => {
    return showAllFAQs ? faqs : faqs.slice(0, initialLimit)
  }, [faqs, showAllFAQs, initialLimit])

  if (isLoading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-chia-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Cargando preguntas frecuentes...</p>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center space-x-2">
          <HelpCircle className="h-8 w-8 text-chia-blue-600" />
          <h2 className="text-3xl font-bold text-gray-900">{title}</h2>
        </div>
        <p className="text-lg text-gray-600 max-w-3xl mx-auto">{description}</p>
        
        {/* Estadísticas */}
        {showStats && (
          <div className="flex flex-wrap justify-center gap-4 text-sm text-gray-500">
            <span>{stats.totalFAQs} preguntas</span>
            <span>•</span>
            <span>{stats.totalThemes} temas</span>
            <span>•</span>
            <span>Más consultado: {stats.mostPopularTheme}</span>
          </div>
        )}
      </div>

      {/* Búsqueda y Filtros */}
      {(showSearch || showCategoryFilter) && (
        <Card>
          <CardContent className="p-6">
            <div className="space-y-4">
              {/* Búsqueda */}
              {showSearch && (
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Buscar en preguntas frecuentes..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 pr-10"
                  />
                  {searchQuery && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setSearchQuery('')}
                      className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              )}

              {/* Filtros por categoría */}
              {showCategoryFilter && (
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Filter className="h-4 w-4 text-gray-500" />
                    <span className="text-sm font-medium text-gray-700">Filtrar por tema:</span>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    <Button
                      variant={selectedTheme === '' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setSelectedTheme('')}
                      className="h-8"
                    >
                      Todos
                    </Button>
                    {themes.map((theme) => {
                      const IconComponent = getThemeIcon(theme.name)
                      return (
                        <Button
                          key={theme.id}
                          variant={selectedTheme === theme.id ? 'default' : 'outline'}
                          size="sm"
                          onClick={() => {
                            setSelectedTheme(theme.id)
                            faqAnalytics.trackCategoryFilter(theme.id)
                          }}
                          className="h-8 space-x-1"
                        >
                          <IconComponent className="h-3 w-3" />
                          <span>{theme.name}</span>
                          <Badge variant="secondary" className="ml-1 h-4 text-xs">
                            {theme.count}
                          </Badge>
                        </Button>
                      )
                    })}
                  </div>
                </div>
              )}

              {/* Botón limpiar filtros */}
              {(searchQuery || selectedTheme) && (
                <div className="flex justify-end">
                  <Button variant="ghost" size="sm" onClick={clearFilters}>
                    <X className="h-4 w-4 mr-1" />
                    Limpiar filtros
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Lista de FAQs */}
      <div className="space-y-4">
        {displayedFAQs.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <HelpCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No se encontraron preguntas
              </h3>
              <p className="text-gray-600 mb-4">
                {searchQuery 
                  ? `No hay resultados para "${searchQuery}"`
                  : 'No hay preguntas disponibles en esta categoría'
                }
              </p>
              {(searchQuery || selectedTheme) && (
                <Button variant="outline" onClick={clearFilters}>
                  Ver todas las preguntas
                </Button>
              )}
            </CardContent>
          </Card>
        ) : (
          <>
            {displayedFAQs.map((faq) => {
              const isExpanded = expandedFAQs.has(faq.id)
              const theme = themes.find(t => t.id === faq.themeId)
              const IconComponent = theme ? getThemeIcon(theme.name) : HelpCircle

              return (
                <Card key={faq.id} className="transition-all duration-200 hover:shadow-md">
                  <CardHeader 
                    className="cursor-pointer"
                    onClick={() => toggleFAQExpansion(faq.id)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1 space-y-2">
                        <div className="flex items-center space-x-2">
                          <IconComponent className="h-4 w-4 text-chia-blue-600" />
                          <Badge variant="secondary" className="text-xs">
                            {theme?.name}
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            {faq.popularityScore}% popular
                          </Badge>
                        </div>
                        <CardTitle className="text-left text-lg leading-tight">
                          {faq.question}
                        </CardTitle>
                      </div>
                      <Button variant="ghost" size="sm" className="ml-4 flex-shrink-0">
                        {isExpanded ? (
                          <ChevronUp className="h-4 w-4" />
                        ) : (
                          <ChevronDown className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </CardHeader>
                  
                  {isExpanded && (
                    <CardContent className="pt-0">
                      <div className="space-y-4">
                        <p className="text-gray-700 leading-relaxed">{faq.answer}</p>
                        
                        {/* Keywords */}
                        {faq.keywords.length > 0 && (
                          <div className="flex flex-wrap gap-1">
                            {faq.keywords.map((keyword, index) => (
                              <Badge key={index} variant="outline" className="text-xs">
                                {keyword}
                              </Badge>
                            ))}
                          </div>
                        )}

                        {/* Procedimientos relacionados */}
                        {faq.relatedProcedures && faq.relatedProcedures.length > 0 && (
                          <div className="border-t pt-3">
                            <p className="text-sm font-medium text-gray-700 mb-2">
                              Trámites relacionados:
                            </p>
                            <div className="space-y-1">
                              {faq.relatedProcedures.map((procedure, index) => (
                                <p key={index} className="text-sm text-chia-blue-600">
                                  • {procedure}
                                </p>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  )}
                </Card>
              )
            })}

            {/* Botón ver más */}
            {!showAllFAQs && faqs.length > initialLimit && (
              <div className="text-center">
                <Button 
                  variant="outline" 
                  onClick={() => setShowAllFAQs(true)}
                  className="px-8"
                >
                  Ver más preguntas ({faqs.length - initialLimit} restantes)
                </Button>
              </div>
            )}

            {/* Botón ver menos */}
            {showAllFAQs && faqs.length > initialLimit && (
              <div className="text-center">
                <Button 
                  variant="ghost" 
                  onClick={() => setShowAllFAQs(false)}
                  className="px-8"
                >
                  Ver menos preguntas
                </Button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  )
}
