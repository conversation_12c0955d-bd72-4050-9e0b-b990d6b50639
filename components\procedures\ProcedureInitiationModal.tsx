'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { 
  X,
  Search,
  FileText,
  Clock,
  DollarSign,
  Building,
  AlertCircle,
  CheckCircle,
  Star,
  Users,
  Phone,
  Mail,
  MapPin,
  ExternalLink,
  ArrowRight,
  Info
} from 'lucide-react'

interface ProcedureInitiationModalProps {
  availableProcedures: any[]
  onClose: () => void
  currentUser: any
}

export function ProcedureInitiationModal({
  availableProcedures,
  onClose,
  currentUser
}: ProcedureInitiationModalProps) {
  const [step, setStep] = useState<'select' | 'form' | 'confirm'>('select')
  const [selectedProcedure, setSelectedProcedure] = useState<any>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [categoryFilter, setCategoryFilter] = useState('')
  const [formData, setFormData] = useState<any>({})
  const [submitting, setSubmitting] = useState(false)
  const [error, setError] = useState('')

  const supabase = createClient()

  // Filter procedures based on search and category
  const filteredProcedures = availableProcedures.filter(procedure => {
    const matchesSearch = !searchTerm || 
      procedure.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      procedure.description?.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesCategory = !categoryFilter || procedure.category === categoryFilter
    
    return matchesSearch && matchesCategory
  })

  // Get unique categories
  const categories = [...new Set(availableProcedures.map(p => p.category).filter(Boolean))]

  const handleProcedureSelect = (procedure: any) => {
    setSelectedProcedure(procedure)
    setStep('form')
  }

  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSubmitting(true)
    setError('')

    try {
      // Generate reference number
      const referenceNumber = `TR-${Date.now()}-${Math.random().toString(36).substr(2, 4).toUpperCase()}`

      // Get draft status ID
      const { data: draftStatus } = await supabase
        .from('procedure_statuses')
        .select('id')
        .eq('name', 'draft')
        .single()

      if (!draftStatus) {
        throw new Error('No se pudo obtener el estado de borrador')
      }

      // Create citizen procedure
      const { data: newProcedure, error: insertError } = await supabase
        .from('citizen_procedures')
        .insert({
          citizen_id: currentUser.id,
          procedure_id: selectedProcedure.id,
          reference_number: referenceNumber,
          status_id: draftStatus.id,
          submitted_data: formData,
          priority: 2, // Default to medium priority
          notes: formData.notes || null
        })
        .select()
        .single()

      if (insertError) {
        throw insertError
      }

      // Create notification for successful initiation
      await supabase
        .from('notifications')
        .insert({
          user_id: currentUser.id,
          title: 'Trámite iniciado',
          message: `Se ha iniciado el trámite "${selectedProcedure.name}" con referencia ${referenceNumber}`,
          type: 'procedure_initiated',
          related_id: newProcedure.id,
          is_read: false
        })

      setStep('confirm')
    } catch (err: any) {
      setError(err.message || 'Error al iniciar el trámite')
    } finally {
      setSubmitting(false)
    }
  }

  const renderProcedureCard = (procedure: any) => (
    <Card 
      key={procedure.id} 
      className="cursor-pointer hover:shadow-md transition-shadow border-2 hover:border-blue-300"
      onClick={() => handleProcedureSelect(procedure)}
    >
      <CardContent className="p-4">
        <div className="flex justify-between items-start mb-3">
          <div className="flex-1">
            <h3 className="font-semibold text-gray-900 mb-1">
              {procedure.name}
            </h3>
            <p className="text-sm text-gray-600 line-clamp-2">
              {procedure.description}
            </p>
          </div>
          <ArrowRight className="h-5 w-5 text-gray-400 ml-2 flex-shrink-0" />
        </div>

        <div className="grid grid-cols-2 gap-3 text-sm">
          <div className="flex items-center text-gray-600">
            <Building className="h-4 w-4 mr-2 text-gray-400" />
            {procedure.dependency?.name || 'Sin dependencia'}
          </div>
          <div className="flex items-center text-gray-600">
            <Clock className="h-4 w-4 mr-2 text-gray-400" />
            {procedure.response_time || 'Tiempo variable'}
          </div>
          {procedure.cost && (
            <div className="flex items-center text-gray-600">
              <DollarSign className="h-4 w-4 mr-2 text-gray-400" />
              ${procedure.cost.toLocaleString()}
            </div>
          )}
          {procedure.category && (
            <div className="flex items-center">
              <Badge variant="secondary" className="text-xs">
                {procedure.category}
              </Badge>
            </div>
          )}
        </div>

        {procedure.online_available && (
          <div className="mt-2 flex items-center text-green-600 text-sm">
            <CheckCircle className="h-4 w-4 mr-1" />
            Disponible en línea
          </div>
        )}
      </CardContent>
    </Card>
  )

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">
              {step === 'select' && 'Seleccionar Trámite'}
              {step === 'form' && 'Información del Trámite'}
              {step === 'confirm' && 'Trámite Iniciado'}
            </h2>
            <p className="text-sm text-gray-500 mt-1">
              {step === 'select' && 'Elige el trámite que deseas iniciar'}
              {step === 'form' && 'Completa la información requerida'}
              {step === 'confirm' && 'Tu trámite ha sido iniciado exitosamente'}
            </p>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          {step === 'select' && (
            <div className="space-y-6">
              {/* Search and Filters */}
              <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      type="text"
                      placeholder="Buscar trámites..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <div className="sm:w-48">
                  <select
                    value={categoryFilter}
                    onChange={(e) => setCategoryFilter(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    aria-label="Filtrar por categoría"
                  >
                    <option value="">Todas las categorías</option>
                    {categories.map((category) => (
                      <option key={category} value={category}>
                        {category}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Procedures Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {filteredProcedures.map(renderProcedureCard)}
              </div>

              {filteredProcedures.length === 0 && (
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    No se encontraron trámites
                  </h3>
                  <p className="text-gray-500">
                    Intenta ajustar los filtros de búsqueda
                  </p>
                </div>
              )}
            </div>
          )}

          {step === 'form' && selectedProcedure && (
            <div className="space-y-6">
              {/* Procedure Summary */}
              <Card className="bg-blue-50 border-blue-200">
                <CardContent className="p-4">
                  <div className="flex items-start space-x-4">
                    <FileText className="h-8 w-8 text-blue-600 flex-shrink-0 mt-1" />
                    <div className="flex-1">
                      <h3 className="font-semibold text-blue-900 mb-1">
                        {selectedProcedure.name}
                      </h3>
                      <p className="text-sm text-blue-700 mb-3">
                        {selectedProcedure.description}
                      </p>
                      <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 text-sm">
                        <div className="flex items-center text-blue-700">
                          <Building className="h-4 w-4 mr-2" />
                          {selectedProcedure.dependency?.name}
                        </div>
                        <div className="flex items-center text-blue-700">
                          <Clock className="h-4 w-4 mr-2" />
                          {selectedProcedure.response_time}
                        </div>
                        {selectedProcedure.cost && (
                          <div className="flex items-center text-blue-700">
                            <DollarSign className="h-4 w-4 mr-2" />
                            ${selectedProcedure.cost.toLocaleString()}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Requirements */}
              {selectedProcedure.requirements && selectedProcedure.requirements.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center text-lg">
                      <AlertCircle className="h-5 w-5 mr-2 text-orange-600" />
                      Requisitos
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {selectedProcedure.requirements.map((req: string, index: number) => (
                        <li key={index} className="flex items-start">
                          <CheckCircle className="h-4 w-4 text-green-600 mr-2 mt-0.5 flex-shrink-0" />
                          <span className="text-sm text-gray-700">{req}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              )}

              {/* Form */}
              <form onSubmit={handleFormSubmit} className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Información Adicional</CardTitle>
                    <CardDescription>
                      Proporciona cualquier información adicional que pueda ser relevante para tu trámite
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="contact_preference">
                        Preferencia de Contacto
                      </Label>
                      <select
                        id="contact_preference"
                        value={formData.contact_preference || 'email'}
                        onChange={(e) => setFormData({...formData, contact_preference: e.target.value})}
                        className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        aria-label="Preferencia de contacto"
                      >
                        <option value="email">Correo electrónico</option>
                        <option value="phone">Teléfono</option>
                        <option value="both">Ambos</option>
                      </select>
                    </div>

                    <div>
                      <Label htmlFor="urgency">
                        Nivel de Urgencia
                      </Label>
                      <select
                        id="urgency"
                        value={formData.urgency || 'normal'}
                        onChange={(e) => setFormData({...formData, urgency: e.target.value})}
                        className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        aria-label="Nivel de urgencia"
                      >
                        <option value="low">Baja</option>
                        <option value="normal">Normal</option>
                        <option value="high">Alta</option>
                      </select>
                    </div>

                    <div>
                      <Label htmlFor="notes">
                        Notas Adicionales
                      </Label>
                      <Textarea
                        id="notes"
                        placeholder="Describe cualquier información adicional que consideres importante..."
                        value={formData.notes || ''}
                        onChange={(e) => setFormData({...formData, notes: e.target.value})}
                        rows={4}
                        className="mt-1"
                      />
                    </div>
                  </CardContent>
                </Card>

                {error && (
                  <div className="bg-red-50 border border-red-200 rounded-md p-4">
                    <div className="flex">
                      <AlertCircle className="h-5 w-5 text-red-400 mr-2" />
                      <p className="text-sm text-red-700">{error}</p>
                    </div>
                  </div>
                )}

                <div className="flex justify-between">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setStep('select')}
                  >
                    Volver
                  </Button>
                  <Button
                    type="submit"
                    disabled={submitting}
                  >
                    {submitting ? 'Iniciando...' : 'Iniciar Trámite'}
                  </Button>
                </div>
              </form>
            </div>
          )}

          {step === 'confirm' && (
            <div className="text-center space-y-6">
              <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  ¡Trámite Iniciado Exitosamente!
                </h3>
                <p className="text-gray-600">
                  Tu trámite ha sido registrado y pronto recibirás una confirmación por correo electrónico.
                </p>
              </div>

              <div className="bg-gray-50 rounded-lg p-4">
                <div className="text-sm text-gray-600 space-y-2">
                  <p><strong>Trámite:</strong> {selectedProcedure?.name}</p>
                  <p><strong>Estado:</strong> Borrador</p>
                  <p><strong>Próximos pasos:</strong> Completa la documentación requerida</p>
                </div>
              </div>

              <Button onClick={onClose} className="w-full">
                Continuar
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
