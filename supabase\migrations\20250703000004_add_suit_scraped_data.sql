-- Migración: Tabla para almacenar datos extraídos de SUIT
-- Fecha: 2025-01-03
-- Propósito: Almacenar información detallada extraída del visor SUIT

-- Crear tabla para datos SUIT extraídos
CREATE TABLE IF NOT EXISTS suit_scraped_data (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    ficha_id VARCHAR(20) UNIQUE NOT NULL,
    procedure_id UUID REFERENCES procedures(id) ON DELETE CASCADE,
    opa_id UUID REFERENCES opas(id) ON DELETE CASCADE,
    
    -- Información básica extraída
    titulo TEXT,
    descripcion_detallada TEXT,
    
    -- Información estructurada (JSON)
    requisitos JSONB DEFAULT '[]'::jsonb,
    pasos JSONB DEFAULT '[]'::jsonb,
    documentos_necesarios JSONB DEFAULT '[]'::jsonb,
    
    -- Información administrativa
    entidad_responsable TEXT,
    tiempo_respuesta TEXT,
    costo_detallado TEXT,
    base_juridica TEXT,
    
    -- Metadatos de scraping
    scraped_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    scraping_status VARCHAR(20) DEFAULT 'pending' CHECK (scraping_status IN ('pending', 'success', 'failed', 'auth_required')),
    error_message TEXT,
    
    -- Datos raw para análisis
    raw_html TEXT,
    raw_text TEXT,
    
    -- Constraints
    CONSTRAINT suit_scraped_data_procedure_or_opa CHECK (
        (procedure_id IS NOT NULL AND opa_id IS NULL) OR 
        (procedure_id IS NULL AND opa_id IS NOT NULL)
    )
);

-- Índices para rendimiento
CREATE INDEX IF NOT EXISTS idx_suit_scraped_ficha_id ON suit_scraped_data(ficha_id);
CREATE INDEX IF NOT EXISTS idx_suit_scraped_procedure_id ON suit_scraped_data(procedure_id);
CREATE INDEX IF NOT EXISTS idx_suit_scraped_opa_id ON suit_scraped_data(opa_id);
CREATE INDEX IF NOT EXISTS idx_suit_scraped_status ON suit_scraped_data(scraping_status);
CREATE INDEX IF NOT EXISTS idx_suit_scraped_updated_at ON suit_scraped_data(updated_at);

-- Índice de texto completo para búsquedas
CREATE INDEX IF NOT EXISTS idx_suit_scraped_fulltext ON suit_scraped_data 
USING gin(to_tsvector('spanish', COALESCE(titulo, '') || ' ' || COALESCE(descripcion_detallada, '') || ' ' || COALESCE(raw_text, '')));

-- Función para extraer ficha_id de URL SUIT
CREATE OR REPLACE FUNCTION extract_ficha_id_from_suit_url(suit_url TEXT)
RETURNS TEXT AS $$
BEGIN
    -- Extraer el parámetro fi= de la URL
    RETURN (regexp_matches(suit_url, 'fi=(\d+)', 'i'))[1];
EXCEPTION
    WHEN OTHERS THEN
        RETURN NULL;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Función para inicializar registros SUIT desde procedures existentes
CREATE OR REPLACE FUNCTION initialize_suit_scraped_data()
RETURNS INTEGER AS $$
DECLARE
    inserted_count INTEGER := 0;
    proc_record RECORD;
BEGIN
    -- Insertar registros para procedures con suit_link
    FOR proc_record IN 
        SELECT id, suit_link 
        FROM procedures 
        WHERE suit_link IS NOT NULL 
        AND suit_link != ''
        AND suit_link LIKE '%visorsuit.funcionpublica.gov.co%'
    LOOP
        INSERT INTO suit_scraped_data (
            ficha_id,
            procedure_id,
            scraping_status
        )
        SELECT 
            extract_ficha_id_from_suit_url(proc_record.suit_link),
            proc_record.id,
            'pending'
        WHERE extract_ficha_id_from_suit_url(proc_record.suit_link) IS NOT NULL
        ON CONFLICT (ficha_id) DO NOTHING;
        
        IF FOUND THEN
            inserted_count := inserted_count + 1;
        END IF;
    END LOOP;
    
    RETURN inserted_count;
END;
$$ LANGUAGE plpgsql;

-- Función para actualizar timestamp automáticamente
CREATE OR REPLACE FUNCTION update_suit_scraped_data_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger para actualizar updated_at automáticamente
DROP TRIGGER IF EXISTS trigger_update_suit_scraped_data_updated_at ON suit_scraped_data;
CREATE TRIGGER trigger_update_suit_scraped_data_updated_at
    BEFORE UPDATE ON suit_scraped_data
    FOR EACH ROW
    EXECUTE FUNCTION update_suit_scraped_data_updated_at();

-- Vista para procedimientos con datos SUIT enriquecidos
CREATE OR REPLACE VIEW vista_procedures_with_suit AS
SELECT 
    p.*,
    s.ficha_id,
    s.titulo as suit_titulo,
    s.descripcion_detallada as suit_descripcion,
    s.requisitos as suit_requisitos,
    s.pasos as suit_pasos,
    s.documentos_necesarios as suit_documentos,
    s.entidad_responsable as suit_entidad,
    s.tiempo_respuesta as suit_tiempo,
    s.costo_detallado as suit_costo,
    s.base_juridica as suit_base_juridica,
    s.scraping_status,
    s.scraped_at,
    s.updated_at as suit_updated_at,
    CASE 
        WHEN s.scraping_status = 'success' THEN true
        ELSE false
    END as has_suit_data,
    CASE 
        WHEN s.descripcion_detallada IS NOT NULL AND LENGTH(s.descripcion_detallada) > LENGTH(COALESCE(p.description, ''))
        THEN s.descripcion_detallada
        ELSE p.description
    END as best_description
FROM procedures p
LEFT JOIN suit_scraped_data s ON p.id = s.procedure_id;

-- Vista para estadísticas de scraping SUIT
CREATE OR REPLACE VIEW vista_suit_scraping_stats AS
SELECT 
    COUNT(*) as total_procedures_with_suit,
    COUNT(CASE WHEN scraping_status = 'success' THEN 1 END) as successfully_scraped,
    COUNT(CASE WHEN scraping_status = 'failed' THEN 1 END) as failed_scraping,
    COUNT(CASE WHEN scraping_status = 'auth_required' THEN 1 END) as auth_required,
    COUNT(CASE WHEN scraping_status = 'pending' THEN 1 END) as pending_scraping,
    ROUND(
        COUNT(CASE WHEN scraping_status = 'success' THEN 1 END) * 100.0 / 
        NULLIF(COUNT(*), 0), 
        2
    ) as success_rate_percent,
    MAX(updated_at) as last_scraping_attempt,
    AVG(LENGTH(descripcion_detallada)) as avg_description_length
FROM suit_scraped_data;

-- Función para obtener procedimientos que necesitan scraping
CREATE OR REPLACE FUNCTION get_procedures_needing_scraping(limit_count INTEGER DEFAULT 10)
RETURNS TABLE (
    ficha_id TEXT,
    procedure_id UUID,
    procedure_name TEXT,
    suit_link TEXT,
    last_attempt TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        s.ficha_id,
        p.id as procedure_id,
        p.name as procedure_name,
        p.suit_link,
        s.updated_at as last_attempt
    FROM suit_scraped_data s
    JOIN procedures p ON s.procedure_id = p.id
    WHERE s.scraping_status IN ('pending', 'failed')
    OR (s.scraping_status = 'success' AND s.updated_at < NOW() - INTERVAL '30 days')
    ORDER BY 
        CASE s.scraping_status 
            WHEN 'pending' THEN 1
            WHEN 'failed' THEN 2
            ELSE 3
        END,
        s.updated_at ASC
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- Inicializar datos para procedures existentes
SELECT initialize_suit_scraped_data() as procedures_initialized;

-- Comentarios para documentación
COMMENT ON TABLE suit_scraped_data IS 'Almacena información detallada extraída del visor SUIT de Función Pública';
COMMENT ON COLUMN suit_scraped_data.ficha_id IS 'ID de la ficha en el visor SUIT (parámetro fi= de la URL)';
COMMENT ON COLUMN suit_scraped_data.requisitos IS 'Array JSON de requisitos extraídos de SUIT';
COMMENT ON COLUMN suit_scraped_data.pasos IS 'Array JSON de pasos del procedimiento extraídos de SUIT';
COMMENT ON COLUMN suit_scraped_data.documentos_necesarios IS 'Array JSON de documentos necesarios extraídos de SUIT';
COMMENT ON COLUMN suit_scraped_data.scraping_status IS 'Estado del scraping: pending, success, failed, auth_required';
COMMENT ON COLUMN suit_scraped_data.raw_html IS 'HTML completo de la página SUIT para análisis posterior';
COMMENT ON COLUMN suit_scraped_data.raw_text IS 'Texto extraído de la página SUIT para búsquedas';

COMMENT ON FUNCTION extract_ficha_id_from_suit_url(TEXT) IS 'Extrae el ID de ficha del parámetro fi= en URLs de SUIT';
COMMENT ON FUNCTION initialize_suit_scraped_data() IS 'Inicializa registros en suit_scraped_data para procedures con suit_link';
COMMENT ON FUNCTION get_procedures_needing_scraping(INTEGER) IS 'Obtiene procedimientos que necesitan scraping o actualización';

COMMENT ON VIEW vista_procedures_with_suit IS 'Vista que combina procedures con datos SUIT enriquecidos';
COMMENT ON VIEW vista_suit_scraping_stats IS 'Estadísticas del estado de scraping SUIT';

-- Mostrar estadísticas iniciales
SELECT 
    'Registros SUIT inicializados' as descripcion,
    COUNT(*) as cantidad
FROM suit_scraped_data
WHERE scraping_status = 'pending';

SELECT * FROM vista_suit_scraping_stats;
