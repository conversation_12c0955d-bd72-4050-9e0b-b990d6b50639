#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to process FAQ data from JSON and generate SQL insertion statements
for the Chía municipal FAQ system.
"""

import json
import re

def clean_text(text):
    """Clean text for SQL insertion"""
    if not text:
        return ""
    # Escape single quotes for SQL
    text = text.replace("'", "''")
    # Remove extra whitespace
    text = re.sub(r'\s+', ' ', text.strip())
    return text

def generate_faq_insertions():
    """Generate SQL INSERT statements for all FAQ questions"""
    
    # Load the structured FAQ data
    with open('faqs_chia_estructurado.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    sql_statements = []
    question_count = 0
    
    # Process each dependency entry
    for entry in data['faqs']:
        dependencia = entry['dependencia']
        codigo_dependencia = entry['codigo_dependencia']
        subdependencia = entry.get('subdependencia', '')
        codigo_subdependencia = entry.get('codigo_subdependencia', '')
        
        # Process each theme within the dependency
        for tema_data in entry['temas']:
            tema = tema_data['tema']
            preguntas = tema_data.get('preguntas_frecuentes', [])
            
            # Process each question within the theme
            for i, pregunta_data in enumerate(preguntas, 1):
                pregunta = clean_text(pregunta_data.get('pregunta', ''))
                respuesta = clean_text(pregunta_data.get('respuesta', ''))
                palabras_clave = pregunta_data.get('palabras_clave', [])
                
                # Convert keywords to PostgreSQL array format
                keywords_array = "ARRAY[" + ", ".join([f"'{clean_text(kw)}'" for kw in palabras_clave]) + "]"
                
                # Generate SQL INSERT statement
                sql = f"""INSERT INTO municipal_faqs (question, answer, theme_id, keywords, display_order) VALUES
('{pregunta}', '{respuesta}', 
 (SELECT id FROM faq_themes WHERE name = '{clean_text(tema)}'), 
 {keywords_array}, {i});"""
                
                sql_statements.append(sql)
                question_count += 1
    
    return sql_statements, question_count

def main():
    """Main function to generate FAQ insertion SQL"""
    print("Processing FAQ data...")
    
    try:
        sql_statements, total_questions = generate_faq_insertions()
        
        print(f"Generated {len(sql_statements)} SQL statements for {total_questions} questions")
        
        # Write SQL statements to file in batches
        batch_size = 50
        batch_num = 1
        
        for i in range(0, len(sql_statements), batch_size):
            batch = sql_statements[i:i + batch_size]
            filename = f"insert_faq_questions_batch_{batch_num}.sql"
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"-- FAQ Questions Batch {batch_num}\n")
                f.write(f"-- Questions {i+1} to {min(i+batch_size, len(sql_statements))}\n\n")
                f.write("\n\n".join(batch))
                f.write("\n")
            
            print(f"Created {filename} with {len(batch)} questions")
            batch_num += 1
        
        print(f"\nTotal questions to insert: {total_questions}")
        print(f"Created {batch_num-1} batch files")
        
    except Exception as e:
        print(f"Error processing FAQ data: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
