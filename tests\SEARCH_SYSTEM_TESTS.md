# Suite de Pruebas del Sistema de Búsqueda Inteligente

## Resumen

Este documento describe la suite completa de pruebas creada para validar el nuevo sistema de búsqueda inteligente y filtros mejorados del portal municipal de Chía.

## Archivos de Prueba Creados

### 1. Pruebas Unitarias

#### `tests/unit/hooks/useIntelligentSearch.test.ts`
- **Propósito**: Pruebas del hook personalizado de búsqueda inteligente
- **Cobertura**: 
  - Inicialización con valores por defecto
  - Actualización de términos de búsqueda
  - Filtrado por término de búsqueda
  - Filtrado por dependencia, modalidad, costo
  - Combinación de búsqueda y filtros
  - Cálculo de puntuaciones de coincidencia
  - Sugerencias de búsqueda
  - Limpieza de búsqueda y filtros
  - Estadísticas de búsqueda
- **Casos de Prueba**: 12 casos principales

#### `tests/unit/components/search/SmartFilters.test.tsx`
- **Propósito**: Pruebas del componente de filtros inteligentes
- **Cobertura**:
  - Renderizado de todas las secciones de filtros
  - Funcionalidad de colapso/expansión
  - Selección de filtros por dependencia, modalidad, tiempo de respuesta, costo, tipo de procedimiento
  - Visualización de badges de filtros activos
  - Eliminación individual de filtros
  - Limpieza de todos los filtros
  - Conteo de filtros activos
  - Navegación por teclado
  - Accesibilidad con etiquetas ARIA
  - Mantenimiento del estado de filtros
  - Manejo de dependencias vacías
  - Indicadores de divulgación progresiva
- **Casos de Prueba**: 13 casos principales

#### `tests/unit/components/procedures/ProcedureCardEnhanced.test.tsx`
- **Propósito**: Pruebas del componente de tarjeta de procedimiento mejorada
- **Cobertura**:
  - Renderizado correcto de información del procedimiento
  - Visualización de badges correctos
  - Manejo de procedimientos virtuales/presenciales
  - Visualización de costos gratuitos/con costo
  - Indicadores de urgencia
  - Layouts compacto y detallado
  - Visualización de requisitos principales
  - Manejo de clics en "ver detalles"
  - Funcionalidad de favoritos
  - Manejo de enlaces externos
  - Aplicación de clases CSS personalizadas
  - Manejo de procedimientos sin información de dependencia
  - Tipos de procedimiento (TRAMITE/OPA)
  - Accesibilidad
  - Efectos hover
  - Prevención de propagación de eventos
  - Manejo de requisitos faltantes
- **Casos de Prueba**: 18 casos principales

### 2. Pruebas de Integración

#### `tests/integration/components/procedures/PublicProcedureSearchInterface.integration.test.tsx`
- **Propósito**: Pruebas de integración del componente principal de búsqueda
- **Cobertura**:
  - Renderizado de la interfaz de búsqueda mejorada
  - Visualización inicial de todos los procedimientos
  - Filtrado por término de búsqueda
  - Sugerencias de búsqueda
  - Filtrado por dependencia, modalidad, costo
  - Combinación de búsqueda y filtros
  - Visualización de badges de filtros activos
  - Limpieza de todos los filtros
  - Cambio de modo de vista (grid/lista)
  - Apertura de modal de detalles
  - Manejo de resultados vacíos
  - Estado de carga durante búsqueda
  - Navegación por teclado
- **Casos de Prueba**: 13 casos principales

## Configuración de Pruebas

### Mocks Implementados
- **Lodash debounce**: Mockeo para pruebas síncronas
- **Modal de detalles**: Mock del componente modal
- **Window.open**: Mock para pruebas de enlaces externos
- **Router de Next.js**: Configurado en setup.ts
- **Supabase**: Cliente mockeado en setup.ts

### Datos de Prueba
- **Procedimientos mock**: 3 procedimientos de ejemplo (Licencia de Construcción, Certificado de Residencia, Registro de Empresa)
- **Dependencias mock**: 3 dependencias municipales
- **Subdependencias mock**: 3 oficinas correspondientes

## Cobertura de Pruebas

### Funcionalidades Cubiertas
- ✅ Búsqueda inteligente con puntuación de coincidencias
- ✅ Filtros progresivos por dependencia, modalidad, costo, tiempo de respuesta, tipo
- ✅ Sugerencias de búsqueda en tiempo real
- ✅ Visualización de resultados con tarjetas mejoradas
- ✅ Funcionalidad de favoritos
- ✅ Enlaces externos a SUIT y Gov.co
- ✅ Accesibilidad WCAG 2.1 AA
- ✅ Navegación por teclado
- ✅ Estados de carga y error
- ✅ Responsive design
- ✅ Manejo de casos edge

### Métricas Esperadas
- **Cobertura de líneas**: >80%
- **Cobertura de funciones**: >80%
- **Cobertura de branches**: >80%
- **Cobertura de statements**: >80%

## Ejecución de Pruebas

### Comandos Disponibles
```bash
# Ejecutar todas las pruebas
npm test

# Ejecutar pruebas específicas
npm test -- --testPathPattern="useIntelligentSearch"
npm test -- --testPathPattern="SmartFilters"
npm test -- --testPathPattern="ProcedureCardEnhanced"
npm test -- --testPathPattern="PublicProcedureSearchInterface"

# Ejecutar con cobertura
npm run test:coverage

# Ejecutar en modo watch
npm run test:watch
```

### Estructura de Archivos
```
tests/
├── unit/
│   ├── hooks/
│   │   └── useIntelligentSearch.test.ts
│   └── components/
│       ├── search/
│       │   └── SmartFilters.test.tsx
│       └── procedures/
│           └── ProcedureCardEnhanced.test.tsx
├── integration/
│   └── components/
│       └── procedures/
│           └── PublicProcedureSearchInterface.integration.test.tsx
├── setup.ts
├── env.setup.js
└── SEARCH_SYSTEM_TESTS.md
```

## Próximos Pasos

1. **Ejecutar pruebas**: Verificar que todas las pruebas pasan
2. **Validar cobertura**: Confirmar que se alcanza el 80% de cobertura
3. **Pruebas E2E**: Crear pruebas end-to-end con Playwright
4. **Optimización**: Ajustar pruebas según resultados de ejecución
5. **Documentación**: Actualizar documentación técnica

## Notas Técnicas

- Las pruebas están configuradas para usar Jest con React Testing Library
- Se incluyen mocks para todas las dependencias externas
- Los datos de prueba reflejan casos reales del portal municipal
- Se priorizan pruebas de comportamiento sobre implementación
- Todas las pruebas incluyen casos de accesibilidad y usabilidad
