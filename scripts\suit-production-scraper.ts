/**
 * SUIT Production Scraper
 * Sistema de extracción de información del visor SUIT para procedimientos municipales
 */

import { createClient } from '@supabase/supabase-js'
import puppeteer from 'puppeteer'
import * as dotenv from 'dotenv'
import * as fs from 'fs'
import * as path from 'path'

// Cargar variables de entorno desde .env.local
dotenv.config({ path: path.join(__dirname, '..', '.env.local') })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

const supabase = createClient(supabaseUrl, supabaseKey)

interface SuitScrapingConfig {
  rateLimit: number          // ms entre requests
  maxRetries: number         // máximo reintentos
  timeout: number           // timeout por página
  batchSize: number         // procedimientos por lote
  headless: boolean         // modo navegador
  userAgent: string         // identificación
}

interface SuitExtractedData {
  titulo?: string
  descripcionDetallada?: string
  requisitos?: string[]
  pasos?: string[]
  documentosNecesarios?: string[]
  entidadResponsable?: string
  tiempoRespuesta?: string
  costoDetallado?: string
  baseJuridica?: string
  rawText?: string
  rawHtml?: string
}

interface ScrapingResult {
  fichaId: string
  procedureId: string
  success: boolean
  data?: SuitExtractedData
  error?: string
  processingTime: number
  scrapedAt: Date
}

class SuitProductionScraper {
  private config: SuitScrapingConfig
  private browser: puppeteer.Browser | null = null
  private page: puppeteer.Page | null = null
  private logFile: string
  private statsFile: string

  constructor(config?: Partial<SuitScrapingConfig>) {
    this.config = {
      rateLimit: 5000,        // 5 segundos entre requests
      maxRetries: 3,          // máximo 3 reintentos
      timeout: 30000,         // 30 segundos timeout
      batchSize: 10,          // 10 procedimientos por lote
      headless: true,         // modo headless para producción
      userAgent: 'ChiaTramitesBot/1.0 (Municipio de Chía - Mejora de Servicios Ciudadanos)',
      ...config
    }

    // Configurar archivos de log
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    this.logFile = path.join(__dirname, '..', 'logs', `suit-scraping-${timestamp}.log`)
    this.statsFile = path.join(__dirname, '..', 'logs', `suit-stats-${timestamp}.json`)

    // Crear directorio de logs si no existe
    const logsDir = path.dirname(this.logFile)
    if (!fs.existsSync(logsDir)) {
      fs.mkdirSync(logsDir, { recursive: true })
    }
  }

  private log(level: 'INFO' | 'WARN' | 'ERROR', message: string, data?: any): void {
    const timestamp = new Date().toISOString()
    const logEntry = {
      timestamp,
      level,
      message,
      data: data || null
    }

    // Log a consola
    console.log(`[${timestamp}] ${level}: ${message}`)
    if (data) {
      console.log(JSON.stringify(data, null, 2))
    }

    // Log a archivo
    fs.appendFileSync(this.logFile, JSON.stringify(logEntry) + '\n')
  }

  async initialize(): Promise<void> {
    this.log('INFO', 'Inicializando SUIT Production Scraper', this.config)

    try {
      this.browser = await puppeteer.launch({
        headless: this.config.headless,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu',
          '--disable-web-security',
          '--disable-features=VizDisplayCompositor'
        ]
      })

      this.page = await this.browser.newPage()
      
      // Configurar User-Agent
      await this.page.setUserAgent(this.config.userAgent)
      
      // Configurar viewport
      await this.page.setViewport({ width: 1366, height: 768 })

      // Configurar timeouts
      this.page.setDefaultTimeout(this.config.timeout)
      this.page.setDefaultNavigationTimeout(this.config.timeout)

      this.log('INFO', 'Navegador inicializado correctamente')

    } catch (error) {
      this.log('ERROR', 'Error inicializando navegador', { error: error instanceof Error ? error.message : error })
      throw error
    }
  }

  async scrapeSuitPage(fichaId: string, procedureId: string, retryCount = 0): Promise<ScrapingResult> {
    const startTime = Date.now()
    const url = `https://visorsuit.funcionpublica.gov.co/auth/visor?fi=${fichaId}`

    this.log('INFO', `Iniciando scraping de ficha ${fichaId}`, { url, procedureId, retryCount })

    const result: ScrapingResult = {
      fichaId,
      procedureId,
      success: false,
      processingTime: 0,
      scrapedAt: new Date()
    }

    try {
      if (!this.page) {
        throw new Error('Navegador no inicializado')
      }

      // Navegar a la página con estrategia mejorada
      this.log('INFO', `Navegando a ${url}`)

      // Configurar headers adicionales para evitar detección
      await this.page.setExtraHTTPHeaders({
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'es-ES,es;q=0.8,en-US;q=0.5,en;q=0.3',
        'Accept-Encoding': 'gzip, deflate',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
      })

      const response = await this.page.goto(url, {
        waitUntil: 'domcontentloaded',
        timeout: this.config.timeout
      })

      // Verificar si la página cargó correctamente
      if (!response || response.status() !== 200) {
        throw new Error(`HTTP ${response?.status()}: Error cargando la página`)
      }

      // Esperar a que la página cargue completamente y verificar contenido
      await new Promise(resolve => setTimeout(resolve, 3000))

      // Verificar si hay errores de JavaScript o autenticación requerida
      const pageContent = await this.page.content()

      if (pageContent.includes('__name is not defined') ||
          pageContent.includes('login') ||
          pageContent.includes('autenticación') ||
          pageContent.includes('unauthorized')) {
        throw new Error('auth_required')
      }

      // Extraer información de la página
      const extractedData = await this.extractSuitData()

      if (extractedData.titulo || extractedData.descripcionDetallada || extractedData.rawText) {
        result.success = true
        result.data = extractedData
        this.log('INFO', `Extracción exitosa para ficha ${fichaId}`, {
          tituloLength: extractedData.titulo?.length || 0,
          descripcionLength: extractedData.descripcionDetallada?.length || 0,
          requisitosCount: extractedData.requisitos?.length || 0,
          rawTextLength: extractedData.rawText?.length || 0
        })
      } else {
        result.error = 'No se pudo extraer información estructurada'
        this.log('WARN', `No se extrajo información para ficha ${fichaId}`)
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Error desconocido'
      result.error = errorMessage
      this.log('ERROR', `Error scraping ficha ${fichaId}`, { error: errorMessage, retryCount })

      // Si es error de autenticación, no reintentar
      if (errorMessage === 'auth_required') {
        result.error = 'Autenticación requerida - sitio SUIT requiere login'
        this.log('WARN', `Ficha ${fichaId} requiere autenticación`)
        result.processingTime = Date.now() - startTime
        return result
      }

      // Reintentar si no se alcanzó el máximo
      if (retryCount < this.config.maxRetries) {
        this.log('INFO', `Reintentando ficha ${fichaId} (intento ${retryCount + 1}/${this.config.maxRetries})`)
        await this.waitRateLimit()
        return this.scrapeSuitPage(fichaId, procedureId, retryCount + 1)
      }
    }

    result.processingTime = Date.now() - startTime
    return result
  }

  private async extractSuitData(): Promise<SuitExtractedData> {
    if (!this.page) {
      throw new Error('Página no disponible')
    }

    return await this.page.evaluate(() => {
      const data: SuitExtractedData = {}

      // Función auxiliar para limpiar texto
      const cleanText = (text: string | null): string => {
        return text?.trim().replace(/\s+/g, ' ') || ''
      }

      // Extraer título
      const titleSelectors = [
        'h1', 'h2', 'h3',
        '.title', '.titulo', '.nombre-tramite',
        '[class*="title"]', '[class*="titulo"]', '[class*="nombre"]'
      ]
      
      for (const selector of titleSelectors) {
        const element = document.querySelector(selector)
        if (element && cleanText(element.textContent) && cleanText(element.textContent).length > 10) {
          data.titulo = cleanText(element.textContent)
          break
        }
      }

      // Extraer descripción detallada
      const descSelectors = [
        '.descripcion', '.description', '.detalle',
        '[class*="desc"]', '[class*="detalle"]',
        'p', '.content', '.contenido'
      ]
      
      let longestDescription = ''
      for (const selector of descSelectors) {
        const elements = document.querySelectorAll(selector)
        for (const element of elements) {
          const text = cleanText(element.textContent)
          if (text.length > longestDescription.length && text.length > 50) {
            longestDescription = text
          }
        }
      }
      if (longestDescription) {
        data.descripcionDetallada = longestDescription
      }

      // Extraer requisitos
      const requisitos: string[] = []
      const reqSelectors = [
        '.requisitos', '.requirements', '.documentos',
        '[class*="requisito"]', '[class*="requirement"]', '[class*="documento"]'
      ]
      
      for (const selector of reqSelectors) {
        const elements = document.querySelectorAll(selector)
        for (const element of elements) {
          const items = element.querySelectorAll('li, p, div')
          for (const item of items) {
            const text = cleanText(item.textContent)
            if (text.length > 10 && !requisitos.includes(text)) {
              requisitos.push(text)
            }
          }
        }
      }
      if (requisitos.length > 0) {
        data.requisitos = requisitos
      }

      // Extraer pasos
      const pasos: string[] = []
      const stepSelectors = [
        '.pasos', '.steps', '.procedimiento',
        '[class*="paso"]', '[class*="step"]', '[class*="procedimiento"]'
      ]
      
      for (const selector of stepSelectors) {
        const elements = document.querySelectorAll(selector)
        for (const element of elements) {
          const items = element.querySelectorAll('li, p, div')
          for (const item of items) {
            const text = cleanText(item.textContent)
            if (text.length > 10 && !pasos.includes(text)) {
              pasos.push(text)
            }
          }
        }
      }
      if (pasos.length > 0) {
        data.pasos = pasos
      }

      // Extraer entidad responsable
      const entidadSelectors = [
        '.entidad', '.entity', '.responsable',
        '[class*="entidad"]', '[class*="entity"]', '[class*="responsable"]'
      ]
      
      for (const selector of entidadSelectors) {
        const element = document.querySelector(selector)
        if (element && cleanText(element.textContent)) {
          data.entidadResponsable = cleanText(element.textContent)
          break
        }
      }

      // Extraer tiempo de respuesta
      const tiempoSelectors = [
        '.tiempo', '.time', '.plazo', '.duracion',
        '[class*="tiempo"]', '[class*="time"]', '[class*="plazo"]'
      ]
      
      for (const selector of tiempoSelectors) {
        const element = document.querySelector(selector)
        const text = cleanText(element?.textContent || '')
        if (text && (text.includes('día') || text.includes('mes') || text.includes('hora'))) {
          data.tiempoRespuesta = text
          break
        }
      }

      // Extraer información de costo
      const costoSelectors = [
        '.costo', '.cost', '.precio', '.tarifa',
        '[class*="costo"]', '[class*="cost"]', '[class*="precio"]'
      ]
      
      for (const selector of costoSelectors) {
        const element = document.querySelector(selector)
        const text = cleanText(element?.textContent || '')
        if (text && (text.includes('$') || text.includes('UVT') || text.includes('SMLDV') || text.includes('gratuito'))) {
          data.costoDetallado = text
          break
        }
      }

      // Extraer todo el texto para análisis
      data.rawText = cleanText(document.body.innerText)
      data.rawHtml = document.documentElement.outerHTML

      return data
    })
  }

  private async waitRateLimit(): Promise<void> {
    this.log('INFO', `Esperando ${this.config.rateLimit}ms (rate limiting)`)
    await new Promise(resolve => setTimeout(resolve, this.config.rateLimit))
  }

  async saveScrapingResult(result: ScrapingResult): Promise<void> {
    try {
      if (result.success && result.data) {
        // Actualizar registro exitoso
        const { error } = await supabase
          .from('suit_scraped_data')
          .update({
            titulo: result.data.titulo,
            descripcion_detallada: result.data.descripcionDetallada,
            requisitos: result.data.requisitos || [],
            pasos: result.data.pasos || [],
            documentos_necesarios: result.data.documentosNecesarios || [],
            entidad_responsable: result.data.entidadResponsable,
            tiempo_respuesta: result.data.tiempoRespuesta,
            costo_detallado: result.data.costoDetallado,
            base_juridica: result.data.baseJuridica,
            raw_html: result.data.rawHtml,
            raw_text: result.data.rawText,
            scraping_status: 'success',
            error_message: null,
            scraped_at: result.scrapedAt.toISOString()
          })
          .eq('ficha_id', result.fichaId)

        if (error) {
          this.log('ERROR', `Error guardando resultado exitoso para ficha ${result.fichaId}`, { error })
        } else {
          this.log('INFO', `Resultado exitoso guardado para ficha ${result.fichaId}`)
        }
      } else {
        // Determinar el estado según el tipo de error
        let scrapingStatus = 'failed'
        if (result.error?.includes('Autenticación requerida') || result.error?.includes('auth_required')) {
          scrapingStatus = 'auth_required'
        }

        // Actualizar registro fallido
        const { error } = await supabase
          .from('suit_scraped_data')
          .update({
            scraping_status: scrapingStatus,
            error_message: result.error,
            scraped_at: result.scrapedAt.toISOString()
          })
          .eq('ficha_id', result.fichaId)

        if (error) {
          this.log('ERROR', `Error guardando resultado fallido para ficha ${result.fichaId}`, { error })
        } else {
          this.log('INFO', `Resultado ${scrapingStatus} guardado para ficha ${result.fichaId}`)
        }
      }
    } catch (error) {
      this.log('ERROR', `Error guardando resultado para ficha ${result.fichaId}`, { error })
    }
  }

  async getProceduresNeedingScraping(limit: number = 10): Promise<Array<{ficha_id: string, procedure_id: string, procedure_name: string}>> {
    try {
      const { data, error } = await supabase
        .rpc('get_procedures_needing_scraping', { limit_count: limit })

      if (error) {
        this.log('ERROR', 'Error obteniendo procedimientos para scraping', { error })
        return []
      }

      return data || []
    } catch (error) {
      this.log('ERROR', 'Error en getProceduresNeedingScraping', { error })
      return []
    }
  }

  async processBatch(procedures: Array<{ficha_id: string, procedure_id: string, procedure_name: string}>): Promise<{successful: number, failed: number}> {
    let successful = 0
    let failed = 0

    this.log('INFO', `Procesando lote de ${procedures.length} procedimientos`)

    for (const procedure of procedures) {
      try {
        this.log('INFO', `Procesando: ${procedure.procedure_name} (Ficha: ${procedure.ficha_id})`)

        const result = await this.scrapeSuitPage(procedure.ficha_id, procedure.procedure_id)
        await this.saveScrapingResult(result)

        if (result.success) {
          successful++
          this.log('INFO', `✅ Éxito: ${procedure.procedure_name}`)
        } else {
          failed++
          this.log('WARN', `❌ Fallo: ${procedure.procedure_name} - ${result.error}`)
        }

        // Rate limiting entre procedimientos
        if (procedures.indexOf(procedure) < procedures.length - 1) {
          await this.waitRateLimit()
        }

      } catch (error) {
        failed++
        this.log('ERROR', `Error procesando ${procedure.procedure_name}`, { error })
      }
    }

    this.log('INFO', `Lote completado: ${successful} éxitos, ${failed} fallos`)
    return { successful, failed }
  }

  async runFullScraping(): Promise<void> {
    this.log('INFO', '🚀 INICIANDO SCRAPING COMPLETO DE SUIT')

    const startTime = Date.now()
    let totalSuccessful = 0
    let totalFailed = 0
    let batchNumber = 1

    try {
      await this.initialize()

      while (true) {
        // Obtener siguiente lote
        const procedures = await this.getProceduresNeedingScraping(this.config.batchSize)

        if (procedures.length === 0) {
          this.log('INFO', 'No hay más procedimientos para procesar')
          break
        }

        this.log('INFO', `📦 Procesando lote ${batchNumber} (${procedures.length} procedimientos)`)

        const batchResult = await this.processBatch(procedures)
        totalSuccessful += batchResult.successful
        totalFailed += batchResult.failed

        // Estadísticas del lote
        this.log('INFO', `Lote ${batchNumber} completado`, {
          successful: batchResult.successful,
          failed: batchResult.failed,
          totalSuccessful,
          totalFailed
        })

        // Pausa entre lotes
        if (procedures.length === this.config.batchSize) {
          this.log('INFO', 'Pausa entre lotes (10 segundos)')
          await new Promise(resolve => setTimeout(resolve, 10000))
        }

        batchNumber++
      }

      // Estadísticas finales
      const totalTime = Date.now() - startTime
      const stats = {
        totalProcessed: totalSuccessful + totalFailed,
        successful: totalSuccessful,
        failed: totalFailed,
        successRate: totalSuccessful / (totalSuccessful + totalFailed) * 100,
        totalTimeMs: totalTime,
        totalTimeMinutes: Math.round(totalTime / 60000),
        averageTimePerProcedure: Math.round(totalTime / (totalSuccessful + totalFailed))
      }

      this.log('INFO', '🎉 SCRAPING COMPLETO FINALIZADO', stats)

      // Guardar estadísticas finales
      fs.writeFileSync(this.statsFile, JSON.stringify(stats, null, 2))

      // Obtener estadísticas de la base de datos
      await this.generateFinalReport()

    } catch (error) {
      this.log('ERROR', 'Error durante el scraping completo', { error })
      throw error
    } finally {
      await this.cleanup()
    }
  }

  async generateFinalReport(): Promise<void> {
    try {
      const { data: stats, error } = await supabase
        .from('vista_suit_scraping_stats')
        .select('*')
        .single()

      if (error) {
        this.log('ERROR', 'Error obteniendo estadísticas finales', { error })
        return
      }

      this.log('INFO', '📊 REPORTE FINAL DE SCRAPING', stats)

      // Obtener algunos ejemplos de datos extraídos
      const { data: examples, error: examplesError } = await supabase
        .from('suit_scraped_data')
        .select('ficha_id, titulo, descripcion_detallada')
        .eq('scraping_status', 'success')
        .not('descripcion_detallada', 'is', null)
        .limit(5)

      if (!examplesError && examples) {
        this.log('INFO', '📝 EJEMPLOS DE DATOS EXTRAÍDOS', {
          count: examples.length,
          examples: examples.map(ex => ({
            ficha_id: ex.ficha_id,
            titulo: ex.titulo?.substring(0, 50) + '...',
            descripcion_length: ex.descripcion_detallada?.length || 0
          }))
        })
      }

    } catch (error) {
      this.log('ERROR', 'Error generando reporte final', { error })
    }
  }

  async cleanup(): Promise<void> {
    if (this.browser) {
      await this.browser.close()
      this.log('INFO', 'Navegador cerrado correctamente')
    }
  }
}

// Función principal para ejecutar el scraping
async function runSuitScraping(config?: Partial<SuitScrapingConfig>): Promise<void> {
  const scraper = new SuitProductionScraper(config)

  try {
    await scraper.runFullScraping()
  } catch (error) {
    console.error('💥 Error fatal en el scraping:', error)
    process.exit(1)
  }
}

// Ejecutar si se llama directamente
if (require.main === module) {
  // Configuración para producción
  const productionConfig: Partial<SuitScrapingConfig> = {
    headless: true,
    rateLimit: 5000,
    batchSize: 10,
    maxRetries: 3
  }

  runSuitScraping(productionConfig)
    .then(() => {
      console.log('🎉 Scraping completado exitosamente')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Error en el scraping:', error)
      process.exit(1)
    })
}

export { SuitProductionScraper, runSuitScraping }
