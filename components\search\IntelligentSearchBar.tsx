'use client'

import React, { useState, useEffect, useRef, useCallback } from 'react'
import { Search, X, Clock, TrendingUp, Filter, Loader2, ChevronDown, Building2, FileText, BarChart3, Tag } from 'lucide-react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'
import { useDebounce } from '@/hooks/useDebounce'
import { searchService, SearchResult } from '@/lib/services/searchService'

interface SearchSuggestion {
  id: string
  text: string
  type: 'recent' | 'popular' | 'suggestion'
  count?: number
}

interface SearchFilters {
  type: 'ALL' | 'TRAMITE' | 'OPA' | 'DEPENDENCIA'
  dependency?: string
  keywords: string[]
}

interface QuickFilter {
  id: string
  label: string
  type: 'type' | 'dependency' | 'keyword'
  value: string
  icon: React.ComponentType<any>
  color: string
}

interface IntelligentSearchBarProps {
  onSearch: (query: string, results: SearchResult[]) => void
  onResultSelect: (result: SearchResult) => void
  placeholder?: string
  className?: string
  showFilters?: boolean
  maxResults?: number
}

export function IntelligentSearchBar({
  onSearch,
  onResultSelect,
  placeholder = "Buscar trámites, OPAs o servicios municipales...",
  className,
  showFilters = true,
  maxResults = 8
}: IntelligentSearchBarProps) {
  // Estados del componente
  const [query, setQuery] = useState('')
  const [isOpen, setIsOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [results, setResults] = useState<SearchResult[]>([])
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([])
  const [selectedIndex, setSelectedIndex] = useState(-1)
  const [recentSearches, setRecentSearches] = useState<string[]>([])

  // Estados para filtros
  const [filters, setFilters] = useState<SearchFilters>({
    type: 'ALL',
    keywords: []
  })
  const [showFiltersPanel, setShowFiltersPanel] = useState(false)
  const [availableDependencies, setAvailableDependencies] = useState<string[]>([])

  // Referencias
  const searchRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  // Debounced query para optimizar las búsquedas
  const debouncedQuery = useDebounce(query, 300)

  // Estado para búsquedas populares
  const [popularSearches, setPopularSearches] = useState<string[]>([])

  // Filtros rápidos predefinidos
  const quickFilters: QuickFilter[] = [
    { id: 'tramites', label: 'Trámites', type: 'type', value: 'TRAMITE', icon: FileText, color: 'bg-blue-100 text-blue-700' },
    { id: 'opas', label: 'OPAs', type: 'type', value: 'OPA', icon: BarChart3, color: 'bg-green-100 text-green-700' },
    { id: 'dependencias', label: 'Dependencias', type: 'type', value: 'DEPENDENCIA', icon: Building2, color: 'bg-purple-100 text-purple-700' },
    { id: 'certificados', label: 'Certificados', type: 'keyword', value: 'certificado', icon: Tag, color: 'bg-yellow-100 text-yellow-700' },
    { id: 'licencias', label: 'Licencias', type: 'keyword', value: 'licencia', icon: Tag, color: 'bg-orange-100 text-orange-700' },
    { id: 'paz-salvo', label: 'Paz y Salvo', type: 'keyword', value: 'paz y salvo', icon: Tag, color: 'bg-teal-100 text-teal-700' }
  ]

  // Cargar búsquedas populares y dependencias al montar el componente
  useEffect(() => {
    const loadData = async () => {
      try {
        const [popular, dependencies] = await Promise.all([
          searchService.getPopularSearches(6),
          searchService.getAvailableDependencies()
        ])
        setPopularSearches(popular)
        setAvailableDependencies(dependencies)
      } catch (error) {
        console.error('Error cargando datos:', error)
      }
    }
    loadData()
  }, [])

  // Funciones para manejar filtros
  const handleQuickFilter = (filter: QuickFilter) => {
    if (filter.type === 'type') {
      setFilters(prev => ({
        ...prev,
        type: filter.value as SearchFilters['type']
      }))
    } else if (filter.type === 'keyword') {
      setFilters(prev => ({
        ...prev,
        keywords: prev.keywords.includes(filter.value)
          ? prev.keywords.filter(k => k !== filter.value)
          : [...prev.keywords, filter.value]
      }))
    }
  }

  const handleDependencyFilter = (dependency: string) => {
    setFilters(prev => ({
      ...prev,
      dependency: prev.dependency === dependency ? undefined : dependency
    }))
  }

  const clearFilters = () => {
    setFilters({
      type: 'ALL',
      keywords: []
    })
  }

  const hasActiveFilters = filters.type !== 'ALL' || filters.keywords.length > 0 || filters.dependency

  // Función de búsqueda inteligente con filtros
  const performSearch = useCallback(async (searchQuery: string) => {
    if (!searchQuery.trim() && filters.type === 'ALL' && filters.keywords.length === 0) {
      setResults([])
      return
    }

    setIsLoading(true)

    try {
      // Construir query con palabras clave
      let enhancedQuery = searchQuery
      if (filters.keywords.length > 0) {
        enhancedQuery = `${searchQuery} ${filters.keywords.join(' ')}`.trim()
      }

      // Usar el servicio de búsqueda real con filtros
      const searchResults = await searchService.search(
        enhancedQuery || '*', // Si no hay query, buscar todo
        {
          type: filters.type !== 'ALL' ? filters.type : undefined,
          dependency: filters.dependency
        },
        {
          limit: maxResults,
          includeHighlight: true,
          sortBy: 'relevance'
        }
      )

      setResults(searchResults)
      onSearch(enhancedQuery, searchResults)

    } catch (error) {
      console.error('Error en búsqueda:', error)
      setResults([])
    } finally {
      setIsLoading(false)
    }
  }, [maxResults, onSearch, filters])

  // Efecto para realizar búsqueda cuando cambia el query debounced o los filtros
  useEffect(() => {
    if (debouncedQuery || hasActiveFilters) {
      performSearch(debouncedQuery)
    } else {
      setResults([])
    }
  }, [debouncedQuery, performSearch, hasActiveFilters])

  // Cargar sugerencias cuando se abre el dropdown
  useEffect(() => {
    if (isOpen && !query) {
      const recentSuggestions: SearchSuggestion[] = recentSearches.map((search, index) => ({
        id: `recent-${index}`,
        text: search,
        type: 'recent'
      }))

      const popularSuggestions: SearchSuggestion[] = popularSearches.map((search, index) => ({
        id: `popular-${index}`,
        text: search,
        type: 'popular',
        count: Math.floor(Math.random() * 100) + 50 // Simulado
      }))

      setSuggestions([...recentSuggestions.slice(0, 3), ...popularSuggestions.slice(0, 5)])
    }
  }, [isOpen, query, recentSearches])

  // Manejar cambios en el input
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setQuery(value)
    setSelectedIndex(-1)

    if (value.trim()) {
      setIsOpen(true)
    }
  }

  // Manejar selección de resultado
  const handleResultSelect = (result: SearchResult) => {
    setQuery(result.name)
    setIsOpen(false)

    // Agregar a búsquedas recientes
    const updatedRecent = [result.name, ...recentSearches.filter(s => s !== result.name)].slice(0, 5)
    setRecentSearches(updatedRecent)
    localStorage.setItem('recentSearches', JSON.stringify(updatedRecent))

    onResultSelect(result)
  }

  // Manejar selección de sugerencia
  const handleSuggestionSelect = (suggestion: SearchSuggestion) => {
    setQuery(suggestion.text)
    setIsOpen(false)
    performSearch(suggestion.text)
  }

  // Manejar navegación con teclado
  const handleKeyDown = (e: React.KeyboardEvent) => {
    const totalItems = results.length + suggestions.length

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault()
        setSelectedIndex(prev => (prev < totalItems - 1 ? prev + 1 : -1))
        break
      case 'ArrowUp':
        e.preventDefault()
        setSelectedIndex(prev => (prev > -1 ? prev - 1 : totalItems - 1))
        break
      case 'Enter':
        e.preventDefault()
        if (selectedIndex >= 0) {
          if (selectedIndex < results.length) {
            handleResultSelect(results[selectedIndex])
          } else {
            const suggestionIndex = selectedIndex - results.length
            handleSuggestionSelect(suggestions[suggestionIndex])
          }
        } else if (query.trim()) {
          // If there are results, keep dropdown open to let user select
          if (results.length > 0) {
            // Don't close dropdown, let user see and select results
            return
          }
          // Only redirect if no results are available
          performSearch(query)
          setIsOpen(false)
          // Redirect to search page as fallback
          window.location.href = `/consulta-tramites?q=${encodeURIComponent(query)}`
        }
        break
      case 'Escape':
        setIsOpen(false)
        setSelectedIndex(-1)
        inputRef.current?.blur()
        break
    }
  }

  // Limpiar búsqueda
  const clearSearch = () => {
    setQuery('')
    setResults([])
    setIsOpen(false)
    setSelectedIndex(-1)
    inputRef.current?.focus()
  }

  // Manejar focus del input
  const handleFocus = () => {
    setIsOpen(true)
    // Cargar búsquedas recientes del localStorage
    const saved = localStorage.getItem('recentSearches')
    if (saved) {
      setRecentSearches(JSON.parse(saved))
    }
  }

  // Cerrar dropdown al hacer clic fuera
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false)
        setSelectedIndex(-1)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  // Renderizar resultado individual
  const renderResult = (result: SearchResult, index: number) => (
    <div
      key={result.id}
      className={cn(
        "flex items-start justify-between p-3 cursor-pointer transition-colors",
        "hover:bg-gray-50 border-b border-gray-100 last:border-b-0",
        selectedIndex === index && "bg-blue-50"
      )}
      onClick={() => handleResultSelect(result)}
    >
      <div className="flex-1 min-w-0">
        <div className="flex items-center space-x-2 mb-1">
          <h4
            className="font-medium text-gray-900 truncate"
            dangerouslySetInnerHTML={{
              __html: result.highlightedName || result.name
            }}
          />
          <Badge
            variant={result.type === 'TRAMITE' ? 'default' : 'secondary'}
            className="text-xs"
          >
            {result.type}
          </Badge>
        </div>
        {result.description && (
          <p className="text-sm text-gray-600 line-clamp-2 mb-2">
            {result.description}
          </p>
        )}
        <div className="flex items-center space-x-4 text-xs text-gray-500">
          <span className="flex items-center">
            <Clock className="w-3 h-3 mr-1" />
            {result.responseTime}
          </span>
          {result.cost && (
            <span className="font-medium text-green-600">
              {result.cost}
            </span>
          )}
        </div>
      </div>
      <div className="text-right ml-3">
        <div className="text-xs text-gray-500 mb-1">
          {result.dependency}
        </div>
        {result.popularity && (
          <div className="flex items-center text-xs text-gray-400">
            <TrendingUp className="w-3 h-3 mr-1" />
            {result.popularity}%
          </div>
        )}
      </div>
    </div>
  )

  // Renderizar sugerencia individual
  const renderSuggestion = (suggestion: SearchSuggestion, index: number) => (
    <div
      key={suggestion.id}
      className={cn(
        "flex items-center justify-between p-3 cursor-pointer transition-colors",
        "hover:bg-gray-50 border-b border-gray-100 last:border-b-0",
        selectedIndex === results.length + index && "bg-blue-50"
      )}
      onClick={() => handleSuggestionSelect(suggestion)}
    >
      <div className="flex items-center space-x-3">
        {suggestion.type === 'recent' ? (
          <Clock className="w-4 h-4 text-gray-400" />
        ) : (
          <TrendingUp className="w-4 h-4 text-gray-400" />
        )}
        <span className="text-gray-700">{suggestion.text}</span>
      </div>
      {suggestion.count && (
        <span className="text-xs text-gray-400">
          {suggestion.count} búsquedas
        </span>
      )}
    </div>
  )

  return (
    <div ref={searchRef} className={cn("relative w-full", className)}>
      {/* Input de búsqueda */}
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
          <div className="p-2 bg-chia-blue-100 rounded-xl">
            <Search className="h-6 w-6 text-chia-blue-600" />
          </div>
        </div>

        <Input
          ref={inputRef}
          type="text"
          value={query}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={handleFocus}
          placeholder={placeholder}
          className={cn(
            "pl-12 pr-12 py-4 text-lg h-16",
            "border-2 border-gray-300/60 focus:border-chia-blue-500",
            "rounded-2xl shadow-lg focus:shadow-xl transition-all duration-300",
            "bg-white/90 backdrop-blur-sm hover:bg-white focus:bg-white",
            "placeholder:text-gray-500 font-medium"
          )}
          aria-label="Búsqueda inteligente de trámites"
          aria-expanded={isOpen}
          aria-haspopup="listbox"
          role="combobox"
        />

        {/* Botones de acción */}
        <div className="absolute inset-y-0 right-0 pr-4 flex items-center space-x-2">
          {/* Botón de filtros */}
          {showFilters && (
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => setShowFiltersPanel(!showFiltersPanel)}
              className={cn(
                "h-10 w-10 p-0 rounded-xl transition-all duration-200 hover:scale-110",
                hasActiveFilters
                  ? "bg-chia-blue-100 text-chia-blue-700 hover:bg-chia-blue-200"
                  : "hover:bg-gray-100"
              )}
              aria-label="Filtros de búsqueda"
            >
              <Filter className="h-5 w-5" />
              {hasActiveFilters && (
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-chia-blue-600 rounded-full" />
              )}
            </Button>
          )}

          {/* Botón de limpiar */}
          {(query || hasActiveFilters) && (
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => {
                clearSearch()
                clearFilters()
              }}
              className="h-10 w-10 p-0 hover:bg-gray-100 rounded-xl transition-all duration-200 hover:scale-110"
              aria-label="Limpiar búsqueda y filtros"
            >
              <X className="h-5 w-5 text-gray-500 hover:text-gray-700" />
            </Button>
          )}

          {/* Indicador de carga */}
          {isLoading && (
            <div className="flex items-center">
              <Loader2 className="h-4 w-4 animate-spin text-chia-blue-500" />
            </div>
          )}
        </div>
      </div>

      {/* Panel de filtros */}
      {showFiltersPanel && (
        <Card className="mt-3 shadow-lg border-0 bg-white/95 backdrop-blur-md rounded-2xl overflow-hidden">
          <CardContent className="p-6">
            {/* Filtros rápidos */}
            <div className="mb-6">
              <h3 className="text-sm font-semibold text-gray-800 mb-3">Filtros rápidos</h3>
              <div className="flex flex-wrap gap-2">
                {quickFilters.map((filter) => {
                  const Icon = filter.icon
                  const isActive =
                    (filter.type === 'type' && filters.type === filter.value) ||
                    (filter.type === 'keyword' && filters.keywords.includes(filter.value))

                  return (
                    <Button
                      key={filter.id}
                      variant="ghost"
                      size="sm"
                      onClick={() => handleQuickFilter(filter)}
                      className={cn(
                        "h-8 px-3 rounded-full text-xs font-medium transition-all duration-200",
                        isActive
                          ? `${filter.color} border border-current`
                          : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                      )}
                    >
                      <Icon className="h-3 w-3 mr-1" />
                      {filter.label}
                    </Button>
                  )
                })}
              </div>
            </div>

            {/* Filtro por dependencia */}
            {availableDependencies.length > 0 && (
              <div className="mb-4">
                <h3 className="text-sm font-semibold text-gray-800 mb-3">Filtrar por dependencia</h3>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2 max-h-32 overflow-y-auto">
                  {availableDependencies.slice(0, 12).map((dep) => (
                    <Button
                      key={dep}
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDependencyFilter(dep)}
                      className={cn(
                        "h-8 px-3 rounded-lg text-xs font-medium transition-all duration-200 justify-start",
                        filters.dependency === dep
                          ? "bg-chia-blue-100 text-chia-blue-700 border border-chia-blue-300"
                          : "bg-gray-50 text-gray-700 hover:bg-gray-100"
                      )}
                    >
                      <Building2 className="h-3 w-3 mr-1" />
                      {dep.length > 20 ? `${dep.substring(0, 20)}...` : dep}
                    </Button>
                  ))}
                </div>
              </div>
            )}

            {/* Acciones del panel */}
            <div className="flex justify-between items-center pt-4 border-t border-gray-200">
              <div className="text-xs text-gray-500">
                {hasActiveFilters && (
                  <span>
                    {filters.type !== 'ALL' && `Tipo: ${filters.type}`}
                    {filters.dependency && ` • Dependencia: ${filters.dependency}`}
                    {filters.keywords.length > 0 && ` • Palabras clave: ${filters.keywords.length}`}
                  </span>
                )}
              </div>
              <div className="flex space-x-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearFilters}
                  disabled={!hasActiveFilters}
                  className="h-8 px-3 text-xs"
                >
                  Limpiar filtros
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowFiltersPanel(false)}
                  className="h-8 px-3 text-xs"
                >
                  Cerrar
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Dropdown de resultados */}
      {isOpen && (
        <Card className="absolute top-full left-0 right-0 mt-3 z-[90] shadow-2xl border-0 bg-white/95 backdrop-blur-md rounded-2xl overflow-hidden">
          <CardContent className="p-0 max-h-96 overflow-y-auto">
            {/* Resultados de búsqueda */}
            {results.length > 0 && (
              <div>
                <div className="px-6 py-4 bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200">
                  <h3 className="text-sm font-semibold text-gray-800">
                    Resultados ({results.length})
                  </h3>
                </div>
                {results.map((result, index) => renderResult(result, index))}
              </div>
            )}

            {/* Sugerencias */}
            {suggestions.length > 0 && !query && (
              <div>
                {results.length > 0 && <div className="border-t border-gray-200" />}
                <div className="px-6 py-4 bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200">
                  <h3 className="text-sm font-semibold text-gray-800">
                    {recentSearches.length > 0 ? 'Búsquedas recientes' : 'Búsquedas populares'}
                  </h3>
                </div>
                {suggestions.map((suggestion, index) => renderSuggestion(suggestion, index))}
              </div>
            )}

            {/* Estado vacío */}
            {query && results.length === 0 && !isLoading && (
              <div className="p-6 text-center">
                <Search className="h-12 w-12 text-gray-300 mx-auto mb-3" />
                <h3 className="text-sm font-medium text-gray-700 mb-1">
                  No se encontraron resultados
                </h3>
                <p className="text-sm text-gray-500">
                  Intenta con otros términos de búsqueda
                </p>
              </div>
            )}

            {/* Mensaje inicial */}
            {!query && suggestions.length === 0 && (
              <div className="p-6 text-center">
                <Search className="h-12 w-12 text-gray-300 mx-auto mb-3" />
                <h3 className="text-sm font-medium text-gray-700 mb-1">
                  Busca trámites y servicios
                </h3>
                <p className="text-sm text-gray-500">
                  Escribe para encontrar el trámite que necesitas
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Filtros adicionales (opcional) */}
      {showFilters && isOpen && results.length > 0 && (
        <div className="absolute top-full left-0 right-0 mt-1 z-40">
          <Card className="bg-white/90 backdrop-blur-sm border shadow-sm">
            <CardContent className="p-3">
              <div className="flex items-center space-x-2 text-xs">
                <Button variant="outline" size="sm" className="h-6 text-xs">
                  <Filter className="h-3 w-3 mr-1" />
                  Filtros
                </Button>
                <Badge variant="outline" className="text-xs">
                  Por dependencia
                </Badge>
                <Badge variant="outline" className="text-xs">
                  Por costo
                </Badge>
                <Badge variant="outline" className="text-xs">
                  Por tiempo
                </Badge>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}