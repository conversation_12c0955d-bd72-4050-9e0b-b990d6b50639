import { renderHook } from '@testing-library/react'
import { useRole } from '@/hooks/useRole'
import { useAuth } from '@/hooks/useAuth'

// Mock useAuth hook
jest.mock('@/hooks/useAuth')

const mockUseAuth = useAuth as jest.MockedFunction<typeof useAuth>

describe('useRole Hook', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('Role Detection', () => {
    it('should return ciudadano role for regular user', () => {
      mockUseAuth.mockReturnValue({
        user: { id: 'user-123', email: '<EMAIL>' },
        profile: { 
          id: 'user-123', 
          role: 'ciudadano',
          full_name: 'Test User'
        },
        loading: false,
        signIn: jest.fn(),
        signUp: jest.fn(),
        signOut: jest.fn(),
        resetPassword: jest.fn(),
        updateProfile: jest.fn(),
      } as any)

      const { result } = renderHook(() => useRole())

      expect(result.current.role).toBe('ciudadano')
      expect(result.current.isCiudadano).toBe(true)
      expect(result.current.isAdmin).toBe(false)
      expect(result.current.isSuperAdmin).toBe(false)
    })

    it('should return admin role for admin user', () => {
      mockUseAuth.mockReturnValue({
        user: { id: 'admin-123', email: '<EMAIL>' },
        profile: { 
          id: 'admin-123', 
          role: 'admin',
          full_name: 'Admin User'
        },
        loading: false,
        signIn: jest.fn(),
        signUp: jest.fn(),
        signOut: jest.fn(),
        resetPassword: jest.fn(),
        updateProfile: jest.fn(),
      } as any)

      const { result } = renderHook(() => useRole())

      expect(result.current.role).toBe('admin')
      expect(result.current.isCiudadano).toBe(false)
      expect(result.current.isAdmin).toBe(true)
      expect(result.current.isSuperAdmin).toBe(false)
    })

    it('should return super_admin role for super admin user', () => {
      mockUseAuth.mockReturnValue({
        user: { id: 'super-123', email: '<EMAIL>' },
        profile: { 
          id: 'super-123', 
          role: 'super_admin',
          full_name: 'Super Admin'
        },
        loading: false,
        signIn: jest.fn(),
        signUp: jest.fn(),
        signOut: jest.fn(),
        resetPassword: jest.fn(),
        updateProfile: jest.fn(),
      } as any)

      const { result } = renderHook(() => useRole())

      expect(result.current.role).toBe('super_admin')
      expect(result.current.isCiudadano).toBe(false)
      expect(result.current.isAdmin).toBe(false)
      expect(result.current.isSuperAdmin).toBe(true)
    })

    it('should return null role when no user is authenticated', () => {
      mockUseAuth.mockReturnValue({
        user: null,
        profile: null,
        loading: false,
        signIn: jest.fn(),
        signUp: jest.fn(),
        signOut: jest.fn(),
        resetPassword: jest.fn(),
        updateProfile: jest.fn(),
      } as any)

      const { result } = renderHook(() => useRole())

      expect(result.current.role).toBe(null)
      expect(result.current.isCiudadano).toBe(false)
      expect(result.current.isAdmin).toBe(false)
      expect(result.current.isSuperAdmin).toBe(false)
    })

    it('should return null role when user is loading', () => {
      mockUseAuth.mockReturnValue({
        user: null,
        profile: null,
        loading: true,
        signIn: jest.fn(),
        signUp: jest.fn(),
        signOut: jest.fn(),
        resetPassword: jest.fn(),
        updateProfile: jest.fn(),
      } as any)

      const { result } = renderHook(() => useRole())

      expect(result.current.role).toBe(null)
      expect(result.current.isCiudadano).toBe(false)
      expect(result.current.isAdmin).toBe(false)
      expect(result.current.isSuperAdmin).toBe(false)
    })
  })

  describe('Permission Checking', () => {
    it('should allow ciudadano to access citizen features', () => {
      mockUseAuth.mockReturnValue({
        user: { id: 'user-123', email: '<EMAIL>' },
        profile: { 
          id: 'user-123', 
          role: 'ciudadano',
          full_name: 'Test User'
        },
        loading: false,
        signIn: jest.fn(),
        signUp: jest.fn(),
        signOut: jest.fn(),
        resetPassword: jest.fn(),
        updateProfile: jest.fn(),
      } as any)

      const { result } = renderHook(() => useRole())

      expect(result.current.hasRole(['ciudadano'])).toBe(true)
      expect(result.current.hasRole(['admin'])).toBe(false)
      expect(result.current.hasRole(['ciudadano', 'admin'])).toBe(true)
    })

    it('should allow admin to access admin and citizen features', () => {
      mockUseAuth.mockReturnValue({
        user: { id: 'admin-123', email: '<EMAIL>' },
        profile: { 
          id: 'admin-123', 
          role: 'admin',
          full_name: 'Admin User'
        },
        loading: false,
        signIn: jest.fn(),
        signUp: jest.fn(),
        signOut: jest.fn(),
        resetPassword: jest.fn(),
        updateProfile: jest.fn(),
      } as any)

      const { result } = renderHook(() => useRole())

      expect(result.current.hasRole(['ciudadano'])).toBe(true)
      expect(result.current.hasRole(['admin'])).toBe(true)
      expect(result.current.hasRole(['super_admin'])).toBe(false)
      expect(result.current.hasRole(['admin', 'super_admin'])).toBe(true)
    })

    it('should allow super_admin to access all features', () => {
      mockUseAuth.mockReturnValue({
        user: { id: 'super-123', email: '<EMAIL>' },
        profile: { 
          id: 'super-123', 
          role: 'super_admin',
          full_name: 'Super Admin'
        },
        loading: false,
        signIn: jest.fn(),
        signUp: jest.fn(),
        signOut: jest.fn(),
        resetPassword: jest.fn(),
        updateProfile: jest.fn(),
      } as any)

      const { result } = renderHook(() => useRole())

      expect(result.current.hasRole(['ciudadano'])).toBe(true)
      expect(result.current.hasRole(['admin'])).toBe(true)
      expect(result.current.hasRole(['super_admin'])).toBe(true)
      expect(result.current.hasRole(['ciudadano', 'admin', 'super_admin'])).toBe(true)
    })

    it('should deny access when no role matches', () => {
      mockUseAuth.mockReturnValue({
        user: { id: 'user-123', email: '<EMAIL>' },
        profile: { 
          id: 'user-123', 
          role: 'ciudadano',
          full_name: 'Test User'
        },
        loading: false,
        signIn: jest.fn(),
        signUp: jest.fn(),
        signOut: jest.fn(),
        resetPassword: jest.fn(),
        updateProfile: jest.fn(),
      } as any)

      const { result } = renderHook(() => useRole())

      expect(result.current.hasRole(['admin', 'super_admin'])).toBe(false)
    })

    it('should deny access when user is not authenticated', () => {
      mockUseAuth.mockReturnValue({
        user: null,
        profile: null,
        loading: false,
        signIn: jest.fn(),
        signUp: jest.fn(),
        signOut: jest.fn(),
        resetPassword: jest.fn(),
        updateProfile: jest.fn(),
      } as any)

      const { result } = renderHook(() => useRole())

      expect(result.current.hasRole(['ciudadano'])).toBe(false)
      expect(result.current.hasRole(['admin'])).toBe(false)
      expect(result.current.hasRole(['super_admin'])).toBe(false)
    })
  })

  describe('Role Hierarchy', () => {
    it('should respect role hierarchy for admin accessing citizen features', () => {
      mockUseAuth.mockReturnValue({
        user: { id: 'admin-123', email: '<EMAIL>' },
        profile: { 
          id: 'admin-123', 
          role: 'admin',
          full_name: 'Admin User'
        },
        loading: false,
        signIn: jest.fn(),
        signUp: jest.fn(),
        signOut: jest.fn(),
        resetPassword: jest.fn(),
        updateProfile: jest.fn(),
      } as any)

      const { result } = renderHook(() => useRole())

      // Admin should be able to access citizen features due to hierarchy
      expect(result.current.hasRole(['ciudadano'])).toBe(true)
    })

    it('should respect role hierarchy for super_admin accessing all features', () => {
      mockUseAuth.mockReturnValue({
        user: { id: 'super-123', email: '<EMAIL>' },
        profile: { 
          id: 'super-123', 
          role: 'super_admin',
          full_name: 'Super Admin'
        },
        loading: false,
        signIn: jest.fn(),
        signUp: jest.fn(),
        signOut: jest.fn(),
        resetPassword: jest.fn(),
        updateProfile: jest.fn(),
      } as any)

      const { result } = renderHook(() => useRole())

      // Super admin should be able to access all features
      expect(result.current.hasRole(['ciudadano'])).toBe(true)
      expect(result.current.hasRole(['admin'])).toBe(true)
      expect(result.current.hasRole(['super_admin'])).toBe(true)
    })
  })
})
