import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { SmartFilters, FilterState } from '../../../../components/search/SmartFilters'

// Mock the Select components from shadcn/ui
jest.mock('@/components/ui/select', () => ({
  Select: ({ children, value, onValueChange }: any) => (
    <div data-testid="select" data-value={value}>
      <button type="button" onClick={() => onValueChange('test-value')}>Select Trigger</button>
      {children}
    </div>
  ),
  SelectContent: ({ children }: any) => <div data-testid="select-content">{children}</div>,
  SelectItem: ({ children, value, ...props }: any) => (
    <button type="button" data-testid={`select-item-${value}`} onClick={() => props.onClick?.(value)} {...props}>
      {children}
    </button>
  ),
  SelectTrigger: ({ children }: any) => <button type="button" data-testid="select-trigger">{children}</button>,
  SelectValue: ({ placeholder }: any) => <span>{placeholder}</span>
}))

const mockDependencies = [
  { id: 'dep1', name: 'Secretaría de Planeación', acronym: 'SEPLAN' },
  { id: 'dep2', name: 'Secretaría de Gobierno', acronym: 'SEGOB' },
  { id: 'dep3', name: 'Secretaría de Desarrollo Económico', acronym: 'SEDECO' }
]

const mockOnFiltersChange = jest.fn()

const defaultProps = {
  dependencies: mockDependencies,
  onFiltersChange: mockOnFiltersChange,
  initialFilters: {} as FilterState
}

describe('SmartFilters', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should render all filter sections', () => {
    render(<SmartFilters {...defaultProps} />)

    expect(screen.getByText('Filtros Inteligentes')).toBeInTheDocument()
    expect(screen.getByText('Dependencia')).toBeInTheDocument()
    expect(screen.getByText('Modalidad')).toBeInTheDocument()
    expect(screen.getByText('Tiempo de Respuesta')).toBeInTheDocument()
    expect(screen.getByText('Costo')).toBeInTheDocument()
    expect(screen.getByText('Tipo de Procedimiento')).toBeInTheDocument()
  })

  it('should be collapsible', async () => {
    const user = userEvent.setup()
    render(<SmartFilters {...defaultProps} />)

    const collapseButton = screen.getByRole('button', { name: /colapsar filtros/i })
    await user.click(collapseButton)

    // Filter content should be hidden
    expect(screen.queryByText('Dependencia')).not.toBeInTheDocument()
  })

  it('should handle dependency filter selection', async () => {
    const user = userEvent.setup()
    render(<SmartFilters {...defaultProps} />)

    // Click on dependency filter
    const dependencyButton = screen.getByRole('button', { name: /todas las dependencias/i })
    await user.click(dependencyButton)

    // Select a dependency
    const option = screen.getByText('Secretaría de Planeación')
    await user.click(option)

    expect(mockOnFiltersChange).toHaveBeenCalledWith({
      dependency: 'dep1'
    })
  })

  it('should handle modality filter selection', async () => {
    const user = userEvent.setup()
    render(<SmartFilters {...defaultProps} />)

    // Click on modality filter
    const modalityButtons = screen.getAllByRole('button')
    const virtualButton = modalityButtons.find(btn => btn.textContent?.includes('Virtual'))
    
    if (virtualButton) {
      await user.click(virtualButton)
      expect(mockOnFiltersChange).toHaveBeenCalledWith({
        modality: 'virtual'
      })
    }
  })

  it('should handle response time filter selection', async () => {
    const user = userEvent.setup()
    render(<SmartFilters {...defaultProps} />)

    // Find and click immediate response button
    const immediateButton = screen.getByRole('button', { name: /inmediato/i })
    await user.click(immediateButton)

    expect(mockOnFiltersChange).toHaveBeenCalledWith({
      responseTime: 'inmediato'
    })
  })

  it('should handle cost filter selection', async () => {
    const user = userEvent.setup()
    render(<SmartFilters {...defaultProps} />)

    // Find and click free cost button
    const freeButton = screen.getByRole('button', { name: /gratuito/i })
    await user.click(freeButton)

    expect(mockOnFiltersChange).toHaveBeenCalledWith({
      cost: 'gratuito'
    })
  })

  it('should handle procedure type filter selection', async () => {
    const user = userEvent.setup()
    render(<SmartFilters {...defaultProps} />)

    // Find and click TRAMITE button
    const tramiteButton = screen.getByRole('button', { name: /trámites/i })
    await user.click(tramiteButton)

    expect(mockOnFiltersChange).toHaveBeenCalledWith({
      procedureType: 'TRAMITE'
    })
  })

  it('should display active filter badges', () => {
    const activeFilters: FilterState = {
      dependency: 'dep1',
      modality: 'virtual',
      cost: 'gratuito'
    }

    render(<SmartFilters {...defaultProps} initialFilters={activeFilters} />)

    expect(screen.getByText('Secretaría de Planeación')).toBeInTheDocument()
    expect(screen.getByText('Virtual')).toBeInTheDocument()
    expect(screen.getByText('Gratuito')).toBeInTheDocument()
  })

  it('should allow removing individual filters', async () => {
    const user = userEvent.setup()
    const activeFilters: FilterState = {
      dependency: 'dep1',
      modality: 'virtual'
    }

    render(<SmartFilters {...defaultProps} initialFilters={activeFilters} />)

    // Find and click the X button on dependency filter badge
    const removeBadges = screen.getAllByRole('button', { name: /×/ })
    await user.click(removeBadges[0])

    expect(mockOnFiltersChange).toHaveBeenCalledWith({
      modality: 'virtual'
    })
  })

  it('should clear all filters', async () => {
    const user = userEvent.setup()
    const activeFilters: FilterState = {
      dependency: 'dep1',
      modality: 'virtual',
      cost: 'gratuito'
    }

    render(<SmartFilters {...defaultProps} initialFilters={activeFilters} />)

    const clearButton = screen.getByRole('button', { name: /limpiar filtros/i })
    await user.click(clearButton)

    expect(mockOnFiltersChange).toHaveBeenCalledWith({})
  })

  it('should show filter count when filters are active', () => {
    const activeFilters: FilterState = {
      dependency: 'dep1',
      modality: 'virtual',
      cost: 'gratuito'
    }

    render(<SmartFilters {...defaultProps} initialFilters={activeFilters} />)

    expect(screen.getByText('3 filtros activos')).toBeInTheDocument()
  })

  it('should handle keyboard navigation', async () => {
    const user = userEvent.setup()
    render(<SmartFilters {...defaultProps} />)

    const dependencyButton = screen.getByRole('button', { name: /todas las dependencias/i })
    
    // Focus the button
    dependencyButton.focus()
    expect(dependencyButton).toHaveFocus()

    // Press Enter to open dropdown
    await user.keyboard('{Enter}')
    
    // Should show dropdown options
    expect(screen.getByText('Secretaría de Planeación')).toBeInTheDocument()
  })

  it('should be accessible with proper ARIA labels', () => {
    render(<SmartFilters {...defaultProps} />)

    // Check for proper ARIA labels
    expect(screen.getByRole('region', { name: /filtros inteligentes/i })).toBeInTheDocument()
    
    // Check filter buttons have proper labels
    const dependencyButton = screen.getByRole('button', { name: /todas las dependencias/i })
    expect(dependencyButton).toHaveAttribute('aria-haspopup', 'listbox')
  })

  it('should maintain filter state correctly', async () => {
    const user = userEvent.setup()
    const { rerender } = render(<SmartFilters {...defaultProps} />)

    // Select a filter
    const virtualButton = screen.getByRole('button', { name: /virtual/i })
    await user.click(virtualButton)

    // Rerender with updated filters
    const updatedFilters: FilterState = { modality: 'virtual' }
    rerender(<SmartFilters {...defaultProps} initialFilters={updatedFilters} />)

    // Virtual button should be selected
    expect(virtualButton).toHaveClass('bg-primary/10')
  })

  it('should handle empty dependencies gracefully', () => {
    render(<SmartFilters {...defaultProps} dependencies={[]} />)

    const dependencyButton = screen.getByRole('button', { name: /todas las dependencias/i })
    expect(dependencyButton).toBeDisabled()
  })

  it('should show progressive disclosure hints', () => {
    render(<SmartFilters {...defaultProps} />)

    expect(screen.getByText(/refina tu búsqueda/i)).toBeInTheDocument()
  })
})
