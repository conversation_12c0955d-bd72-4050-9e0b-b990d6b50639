'use client'

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Clock,
  FileText,
  Upload,
  Download,
  Eye,
  Calendar
} from 'lucide-react'

interface Activity {
  id: string
  type: string
  title: string
  description: string
  date: string
  procedureId?: string
  referenceNumber?: string
}

interface RecentActivityProps {
  activities: Activity[]
}

export function RecentActivity({ activities }: RecentActivityProps) {
  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'document_upload':
        return <Upload className="h-4 w-4 text-primary" />
      case 'document_download':
        return <Download className="h-4 w-4 text-green-600" />
      case 'document_view':
        return <Eye className="h-4 w-4 text-purple-600" />
      default:
        return <FileText className="h-4 w-4 text-gray-600" />
    }
  }

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'document_upload':
        return 'bg-primary/10 text-primary'
      case 'document_download':
        return 'bg-green-100 text-green-800'
      case 'document_view':
        return 'bg-purple-100 text-purple-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 1) {
      return 'Hace unos minutos'
    } else if (diffInHours < 24) {
      return `Hace ${diffInHours} hora${diffInHours > 1 ? 's' : ''}`
    } else {
      const diffInDays = Math.floor(diffInHours / 24)
      if (diffInDays < 7) {
        return `Hace ${diffInDays} día${diffInDays > 1 ? 's' : ''}`
      } else {
        return date.toLocaleDateString('es-CO', {
          month: 'short',
          day: 'numeric'
        })
      }
    }
  }

  if (activities.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center">
            <Clock className="h-5 w-5 mr-2 text-gray-600" />
            Actividad Reciente
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <Clock className="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <p className="text-sm text-gray-500">
              No hay actividad reciente
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg flex items-center">
          <Clock className="h-5 w-5 mr-2 text-gray-600" />
          Actividad Reciente
        </CardTitle>
        <CardDescription>
          Últimas acciones con documentos
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {activities.slice(0, 5).map((activity) => (
            <div key={activity.id} className="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
              <div className="flex-shrink-0 mt-1">
                {getActivityIcon(activity.type)}
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between mb-1">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {activity.title}
                  </p>
                  <Badge className={getActivityColor(activity.type)}>
                    {activity.type === 'document_upload' ? 'Subida' :
                     activity.type === 'document_download' ? 'Descarga' :
                     activity.type === 'document_view' ? 'Vista' : 'Actividad'}
                  </Badge>
                </div>
                <p className="text-xs text-gray-600 mb-2">
                  {activity.description}
                </p>
                <div className="flex items-center text-xs text-gray-500">
                  <Calendar className="h-3 w-3 mr-1" />
                  {formatDate(activity.date)}
                  {activity.referenceNumber && (
                    <>
                      <span className="mx-2">•</span>
                      <span>{activity.referenceNumber}</span>
                    </>
                  )}
                </div>
              </div>
            </div>
          ))}
          
          {activities.length > 5 && (
            <div className="text-center pt-2">
              <p className="text-xs text-gray-500">
                Y {activities.length - 5} actividad{activities.length - 5 > 1 ? 'es' : ''} más...
              </p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
