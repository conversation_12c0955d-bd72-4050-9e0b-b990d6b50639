/**
 * Script para verificar el contraste de colores WCAG 2.1 AA
 * Verifica que el color verde principal (#059669) cumple con los estándares de accesibilidad
 */

// Función para calcular la luminancia relativa de un color
function getLuminance(hex) {
  const rgb = parseInt(hex.slice(1), 16);
  const r = (rgb >> 16) & 0xff;
  const g = (rgb >> 8) & 0xff;
  const b = (rgb >> 0) & 0xff;

  const [rs, gs, bs] = [r, g, b].map(c => {
    c = c / 255;
    return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
  });

  return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
}

// Función para calcular el ratio de contraste entre dos colores
function calculateContrast(color1, color2) {
  const lum1 = getLuminance(color1);
  const lum2 = getLuminance(color2);
  const brightest = Math.max(lum1, lum2);
  const darkest = Math.min(lum1, lum2);

  return (brightest + 0.05) / (darkest + 0.05);
}

// Función para verificar si cumple WCAG 2.1 AA
function meetsWCAG_AA(ratio) {
  return ratio >= 4.5; // WCAG 2.1 AA requiere mínimo 4.5:1
}

// Colores a verificar
const chiaGreen600 = '#059669'; // Color oficial verde de Chía
const chiaGreen700 = '#15803d'; // Verde más oscuro para mejor contraste
const white = '#ffffff';
const gray50 = '#f9fafb';
const gray100 = '#f3f4f6';
const gray900 = '#111827';

// Combinaciones de colores a verificar
const colorCombinations = [
  { fg: chiaGreen600, bg: white, name: 'Verde Chía 600 sobre blanco (original)' },
  { fg: white, bg: chiaGreen600, name: 'Texto blanco sobre verde Chía 600 (original)' },
  { fg: chiaGreen700, bg: white, name: 'Verde Chía 700 sobre blanco (alternativa)' },
  { fg: white, bg: chiaGreen700, name: 'Texto blanco sobre verde Chía 700 (alternativa)' },
  { fg: chiaGreen700, bg: gray50, name: 'Verde Chía 700 sobre gris claro' },
  { fg: chiaGreen700, bg: gray100, name: 'Verde Chía 700 sobre gris 100' },
  { fg: gray900, bg: white, name: 'Texto principal sobre blanco (referencia)' }
];

console.log('🎨 Verificación de Contraste WCAG 2.1 AA');
console.log('==========================================\n');

colorCombinations.forEach(({ fg, bg, name }) => {
  const ratio = calculateContrast(fg, bg);
  const passes = meetsWCAG_AA(ratio);
  const status = passes ? '✅ PASA' : '❌ FALLA';
  
  console.log(`${status} ${name}`);
  console.log(`   Ratio: ${ratio.toFixed(2)}:1 (mínimo requerido: 4.5:1)`);
  console.log(`   Colores: ${fg} sobre ${bg}\n`);
});

console.log('📋 Resumen:');
console.log('- WCAG 2.1 AA requiere un ratio mínimo de 4.5:1 para texto normal');
console.log('- WCAG 2.1 AAA requiere un ratio mínimo de 7:1 para texto normal');
console.log('- ✅ RECOMENDACIÓN: Usar chia-green-700 (#15803d) como color primario');
console.log('- ⚠️  El color oficial chia-green-600 (#059669) no cumple WCAG 2.1 AA');
console.log('- 🎯 Solución: Usar chia-green-700 para texto y chia-green-600 para decoración\n');
