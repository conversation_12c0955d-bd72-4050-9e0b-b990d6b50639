/**
 * SUIT Enhanced Scraper
 * Versión mejorada basada en el diagnóstico del sitio SUIT
 */

import { createClient } from '@supabase/supabase-js'
import puppeteer from 'puppeteer'
import * as dotenv from 'dotenv'
import * as path from 'path'
import * as fs from 'fs'

// Cargar variables de entorno
dotenv.config({ path: path.join(__dirname, '..', '.env.local') })

const supabaseUrl = process.env['NEXT_PUBLIC_SUPABASE_URL']!
const supabaseKey = process.env['SUPABASE_SERVICE_ROLE_KEY']!
const supabase = createClient(supabaseUrl, supabaseKey)

interface SuitData {
  titulo: string
  descripcionDetallada?: string
  entidadResponsable?: string
  tiempoRespuesta?: string
  costoDetallado?: string
  modalidad?: string
  puntoAtencion?: string
  rawText: string
  rawHtml: string
}

interface ScrapingResult {
  fichaId: string
  procedureId: string
  success: boolean
  data?: SuitData
  error?: string
  processingTime: number
  scrapedAt: Date
}

class SuitEnhancedScraper {
  private browser: puppeteer.Browser | null = null
  private page: puppeteer.Page | null = null
  private logFile: string
  private statsFile: string

  constructor() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    this.logFile = path.join(__dirname, '..', 'logs', `suit-enhanced-${timestamp}.log`)
    this.statsFile = path.join(__dirname, '..', 'logs', `suit-enhanced-stats-${timestamp}.json`)
  }

  private log(level: string, message: string, data?: any): void {
    const timestamp = new Date().toISOString()
    const logEntry = `[${timestamp}] ${level}: ${message}`
    
    console.log(logEntry)
    if (data) {
      console.log(JSON.stringify(data, null, 2))
    }

    // Escribir al archivo de log
    const logLine = data ? `${logEntry}\n${JSON.stringify(data, null, 2)}\n` : `${logEntry}\n`
    fs.appendFileSync(this.logFile, logLine)
  }

  async initialize(): Promise<void> {
    this.log('INFO', '🚀 Inicializando SUIT Enhanced Scraper')

    this.browser = await puppeteer.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-web-security'
      ]
    })

    this.page = await this.browser.newPage()
    await this.page.setViewport({ width: 1366, height: 768 })
    
    // User agent realista
    await this.page.setUserAgent(
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    )

    this.log('INFO', '✅ Navegador inicializado')
  }

  async scrapeSuitPage(fichaId: string, procedureId: string): Promise<ScrapingResult> {
    const startTime = Date.now()
    const url = `https://visorsuit.funcionpublica.gov.co/auth/visor?fi=${fichaId}`
    
    this.log('INFO', `🔍 Scraping ficha ${fichaId}`, { url, procedureId })

    const result: ScrapingResult = {
      fichaId,
      procedureId,
      success: false,
      processingTime: 0,
      scrapedAt: new Date()
    }

    try {
      if (!this.page) {
        throw new Error('Navegador no inicializado')
      }

      // Navegar a la página
      const response = await this.page.goto(url, {
        waitUntil: 'domcontentloaded',
        timeout: 60000
      })

      if (!response || response.status() !== 200) {
        throw new Error(`HTTP ${response?.status()}: Error cargando la página`)
      }

      // Esperar carga completa
      await new Promise(resolve => setTimeout(resolve, 5000))

      // Extraer datos usando la estructura conocida del sitio
      const extractedData = await this.page.evaluate(() => {
        const data: SuitData = {
          titulo: '',
          rawText: document.body.innerText,
          rawHtml: document.documentElement.outerHTML
        }

        const lines = document.body.innerText.split('\n')
          .map(line => line.trim())
          .filter(line => line.length > 0)

        // Extraer título - aparece después de "Página web de la entidad"
        let titleFound = false
        for (let i = 0; i < lines.length; i++) {
          if (lines[i].includes('Página web de la entidad') && i + 1 < lines.length) {
            // Buscar el siguiente elemento significativo
            for (let j = i + 1; j < lines.length && j < i + 5; j++) {
              const candidate = lines[j]
              if (candidate && 
                  !candidate.includes('(También se conoce como:') && 
                  candidate !== '¿Cuándo se puede realizar?' && 
                  candidate.length > 5 &&
                  !candidate.includes('http')) {
                data.titulo = candidate
                titleFound = true
                break
              }
            }
            break
          }
        }

        // Extraer entidad responsable
        const entidadIndex = lines.findIndex(line => line === 'Informacion suministrada por')
        if (entidadIndex !== -1 && entidadIndex + 1 < lines.length) {
          data.entidadResponsable = lines[entidadIndex + 1]
        }

        // Extraer información de costo
        const costoIndex = lines.findIndex(line => line === '¿Requiere pago?')
        if (costoIndex !== -1 && costoIndex + 1 < lines.length) {
          data.costoDetallado = lines[costoIndex + 1]
        }

        // Extraer modalidad
        const modalidadIndex = lines.findIndex(line => line === '¿Es totalmente en línea?')
        if (modalidadIndex !== -1 && modalidadIndex + 1 < lines.length) {
          data.modalidad = lines[modalidadIndex + 1]
        }

        // Extraer punto de atención
        const atencionIndex = lines.findIndex(line => line === '¿A dónde ir?')
        if (atencionIndex !== -1 && atencionIndex + 1 < lines.length) {
          data.puntoAtencion = lines[atencionIndex + 1]
        }

        // Extraer tiempo de respuesta
        const tiempoIndex = lines.findIndex(line => line === '¿Cuándo se puede realizar?')
        if (tiempoIndex !== -1 && tiempoIndex + 1 < lines.length) {
          data.tiempoRespuesta = lines[tiempoIndex + 1]
        }

        // Extraer descripción - buscar sección "Descripción"
        const descIndex = lines.findIndex(line => line === 'Descripción')
        if (descIndex !== -1) {
          const descLines = []
          for (let i = descIndex + 1; i < lines.length; i++) {
            const line = lines[i]
            if (line.startsWith('¿') || line === 'Encuesta' || line === 'Requisitos') {
              break
            }
            if (line.length > 10) {
              descLines.push(line)
            }
          }
          if (descLines.length > 0) {
            data.descripcionDetallada = descLines.join(' ')
          }
        }

        return data
      })

      if (extractedData.titulo && extractedData.titulo.length > 5) {
        result.success = true
        result.data = extractedData
        this.log('INFO', `✅ Extracción exitosa para ficha ${fichaId}`, {
          titulo: extractedData.titulo,
          entidad: extractedData.entidadResponsable,
          costo: extractedData.costoDetallado
        })
      } else {
        result.error = 'No se pudo extraer título válido'
        this.log('WARN', `⚠️  No se extrajo título para ficha ${fichaId}`)
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Error desconocido'
      result.error = errorMessage
      this.log('ERROR', `❌ Error scraping ficha ${fichaId}`, { error: errorMessage })
    }

    result.processingTime = Date.now() - startTime
    return result
  }

  async saveResult(result: ScrapingResult): Promise<void> {
    try {
      if (result.success && result.data) {
        const { error } = await supabase
          .from('suit_scraped_data')
          .update({
            titulo: result.data.titulo,
            descripcion_detallada: result.data.descripcionDetallada,
            entidad_responsable: result.data.entidadResponsable,
            tiempo_respuesta: result.data.tiempoRespuesta,
            costo_detallado: result.data.costoDetallado,
            raw_html: result.data.rawHtml,
            raw_text: result.data.rawText,
            scraping_status: 'success',
            error_message: null,
            scraped_at: result.scrapedAt.toISOString()
          })
          .eq('ficha_id', result.fichaId)

        if (error) {
          this.log('ERROR', `Error guardando resultado exitoso para ficha ${result.fichaId}`, { error })
        } else {
          this.log('INFO', `💾 Resultado guardado para ficha ${result.fichaId}`)
        }
      } else {
        const { error } = await supabase
          .from('suit_scraped_data')
          .update({
            scraping_status: 'failed',
            error_message: result.error,
            scraped_at: result.scrapedAt.toISOString()
          })
          .eq('ficha_id', result.fichaId)

        if (error) {
          this.log('ERROR', `Error guardando resultado fallido para ficha ${result.fichaId}`, { error })
        } else {
          this.log('INFO', `💾 Fallo guardado para ficha ${result.fichaId}`)
        }
      }
    } catch (error) {
      this.log('ERROR', `Error guardando resultado para ficha ${result.fichaId}`, { error })
    }
  }

  async cleanup(): Promise<void> {
    if (this.browser) {
      await this.browser.close()
      this.log('INFO', '🧹 Navegador cerrado')
    }
  }
}

// Script principal para ejecutar el scraping
async function runEnhancedScraping(): Promise<void> {
  const scraper = new SuitEnhancedScraper()

  try {
    await scraper.initialize()

    // Obtener procedimientos pendientes
    const { data: procedures, error } = await supabase
      .rpc('get_procedures_needing_scraping')

    if (error) {
      console.error('Error obteniendo procedimientos:', error)
      return
    }

    if (!procedures || procedures.length === 0) {
      console.log('No hay procedimientos pendientes de scraping')
      return
    }

    console.log(`📋 Procesando ${procedures.length} procedimientos...`)

    let successCount = 0
    let failureCount = 0

    for (let i = 0; i < procedures.length; i++) {
      const procedure = procedures[i]
      const fichaId = procedure.suit_link.match(/fi=(\d+)/)?.[1]

      if (!fichaId) {
        console.log(`⚠️  No se pudo extraer ficha ID de: ${procedure.suit_link}`)
        continue
      }

      console.log(`\n📄 [${i + 1}/${procedures.length}] Procesando: ${procedure.name}`)

      const result = await scraper.scrapeSuitPage(fichaId, procedure.id)
      await scraper.saveResult(result)

      if (result.success) {
        successCount++
      } else {
        failureCount++
      }

      // Rate limiting - 5 segundos entre requests
      if (i < procedures.length - 1) {
        console.log('⏳ Esperando 5 segundos...')
        await new Promise(resolve => setTimeout(resolve, 5000))
      }
    }

    // Estadísticas finales
    const successRate = (successCount / (successCount + failureCount) * 100).toFixed(2)
    console.log('\n📊 ESTADÍSTICAS FINALES')
    console.log('=' .repeat(50))
    console.log(`✅ Exitosos: ${successCount}`)
    console.log(`❌ Fallidos: ${failureCount}`)
    console.log(`📈 Tasa de éxito: ${successRate}%`)

  } catch (error) {
    console.error('💥 Error durante el scraping:', error)
  } finally {
    await scraper.cleanup()
  }
}

// Ejecutar si es el módulo principal
if (require.main === module) {
  console.log('🚀 INICIANDO SUIT ENHANCED SCRAPER')
  console.log('=' .repeat(50))

  runEnhancedScraping()
    .then(() => {
      console.log('🎉 Scraping completado')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Error en el scraping:', error)
      process.exit(1)
    })
}

export { SuitEnhancedScraper }
