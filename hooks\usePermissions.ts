'use client'

import { useMemo } from 'react'
import { useAuth } from './useAuth'
import { useRole } from './useRole'

interface PermissionChecks {
  // Route access permissions
  canAccessDashboard: () => boolean
  canAccessAdminPanel: () => boolean
  canAccessSuperAdminPanel: () => boolean
  canAccessProcedures: () => boolean
  canAccessChat: () => boolean
  canAccessProfile: () => boolean
  canAccessNotifications: () => boolean
  
  // Resource permissions
  canViewProcedure: (procedureId: string, dependencyId?: string) => boolean
  canEditProcedure: (procedureId: string, dependencyId?: string) => boolean
  canDeleteProcedure: (procedureId: string, dependencyId?: string) => boolean
  canCreateProcedure: (dependencyId?: string) => boolean
  
  // User management permissions
  canViewUser: (userId: string, userDependencyId?: string) => boolean
  canEditUser: (userId: string, userDependencyId?: string) => boolean
  canDeleteUser: (userId: string, userDependencyId?: string) => boolean
  canCreateUser: (dependencyId?: string) => boolean
  
  // Dependency permissions
  canViewDependency: (dependencyId: string) => boolean
  canEditDependency: (dependencyId: string) => boolean
  canDeleteDependency: (dependencyId: string) => boolean
  canCreateDependency: () => boolean
  
  // System permissions
  canViewSystemLogs: () => boolean
  canManageSystemSettings: () => boolean
  canViewAnalytics: (dependencyId?: string) => boolean
  canExportData: (dependencyId?: string) => boolean
}

export function usePermissions(): PermissionChecks {
  const { user, profile, isAuthenticated } = useAuth()
  const { 
    isCitizen, 
    isAdmin, 
    isSuperAdmin, 
    isDependencyAdmin, 
    permissions 
  } = useRole()

  const checks = useMemo((): PermissionChecks => {
    // Route access permissions
    const canAccessDashboard = () => {
      return isAuthenticated && permissions.canViewDashboard
    }

    const canAccessAdminPanel = () => {
      return isAuthenticated && (isAdmin || isSuperAdmin)
    }

    const canAccessSuperAdminPanel = () => {
      return isAuthenticated && isSuperAdmin
    }

    const canAccessProcedures = () => {
      return isAuthenticated && permissions.canCreateProcedures
    }

    const canAccessChat = () => {
      return isAuthenticated && permissions.canChatWithAI
    }

    const canAccessProfile = () => {
      return isAuthenticated && permissions.canViewProfile
    }

    const canAccessNotifications = () => {
      return isAuthenticated && permissions.canReceiveNotifications
    }

    // Resource permissions - Procedures
    const canViewProcedure = (procedureId: string, dependencyId?: string) => {
      if (!isAuthenticated) return false
      
      // Super admin can view all procedures
      if (isSuperAdmin) return true
      
      // Admin can view procedures in their dependency
      if (isAdmin && dependencyId && isDependencyAdmin(dependencyId)) {
        return true
      }
      
      // Citizens can view their own procedures (this would need additional logic)
      // For now, we'll allow citizens to view procedures they have access to
      if (isCitizen) return true
      
      return false
    }

    const canEditProcedure = (procedureId: string, dependencyId?: string) => {
      if (!isAuthenticated) return false
      
      // Super admin can edit all procedures
      if (isSuperAdmin) return true
      
      // Admin can edit procedures in their dependency
      if (isAdmin && dependencyId && isDependencyAdmin(dependencyId)) {
        return true
      }
      
      return false
    }

    const canDeleteProcedure = (procedureId: string, dependencyId?: string) => {
      if (!isAuthenticated) return false
      
      // Super admin can delete all procedures
      if (isSuperAdmin) return true
      
      // Admin can delete procedures in their dependency
      if (isAdmin && dependencyId && isDependencyAdmin(dependencyId)) {
        return true
      }
      
      return false
    }

    const canCreateProcedure = (dependencyId?: string) => {
      if (!isAuthenticated) return false
      
      // Super admin can create procedures anywhere
      if (isSuperAdmin) return true
      
      // Admin can create procedures in their dependency
      if (isAdmin && dependencyId && isDependencyAdmin(dependencyId)) {
        return true
      }
      
      // Citizens can create citizen procedures
      if (isCitizen) return true
      
      return false
    }

    // User management permissions
    const canViewUser = (userId: string, userDependencyId?: string) => {
      if (!isAuthenticated) return false
      
      // Users can always view their own profile
      if (user?.id === userId) return true
      
      // Super admin can view all users
      if (isSuperAdmin) return true
      
      // Admin can view users in their dependency
      if (isAdmin && userDependencyId && isDependencyAdmin(userDependencyId)) {
        return true
      }
      
      return false
    }

    const canEditUser = (userId: string, userDependencyId?: string) => {
      if (!isAuthenticated) return false
      
      // Users can edit their own profile
      if (user?.id === userId && permissions.canEditProfile) return true
      
      // Super admin can edit all users
      if (isSuperAdmin) return true
      
      // Admin can edit users in their dependency (except other admins)
      if (isAdmin && userDependencyId && isDependencyAdmin(userDependencyId)) {
        return true
      }
      
      return false
    }

    const canDeleteUser = (userId: string, userDependencyId?: string) => {
      if (!isAuthenticated) return false
      
      // Users cannot delete themselves
      if (user?.id === userId) return false
      
      // Super admin can delete users
      if (isSuperAdmin) return true
      
      // Admin can delete non-admin users in their dependency
      if (isAdmin && userDependencyId && isDependencyAdmin(userDependencyId)) {
        return true
      }
      
      return false
    }

    const canCreateUser = (dependencyId?: string) => {
      if (!isAuthenticated) return false
      
      // Super admin can create users anywhere
      if (isSuperAdmin) return true
      
      // Admin can create users in their dependency
      if (isAdmin && dependencyId && isDependencyAdmin(dependencyId)) {
        return true
      }
      
      return false
    }

    // Dependency permissions
    const canViewDependency = (dependencyId: string) => {
      if (!isAuthenticated) return false
      
      // Super admin can view all dependencies
      if (isSuperAdmin) return true
      
      // Admin can view their own dependency
      if (isAdmin && isDependencyAdmin(dependencyId)) return true
      
      // Citizens can view dependencies for procedures
      if (isCitizen) return true
      
      return false
    }

    const canEditDependency = (dependencyId: string) => {
      if (!isAuthenticated) return false
      
      // Only super admin can edit dependencies
      return isSuperAdmin
    }

    const canDeleteDependency = (dependencyId: string) => {
      if (!isAuthenticated) return false
      
      // Only super admin can delete dependencies
      return isSuperAdmin
    }

    const canCreateDependency = () => {
      if (!isAuthenticated) return false
      
      // Only super admin can create dependencies
      return isSuperAdmin
    }

    // System permissions
    const canViewSystemLogs = () => {
      return isAuthenticated && permissions.canViewAuditLogs
    }

    const canManageSystemSettings = () => {
      return isAuthenticated && permissions.canManageSystem
    }

    const canViewAnalytics = (dependencyId?: string) => {
      if (!isAuthenticated) return false
      
      // Super admin can view all analytics
      if (isSuperAdmin) return true
      
      // Admin can view analytics for their dependency
      if (isAdmin && dependencyId && isDependencyAdmin(dependencyId)) {
        return true
      }
      
      return false
    }

    const canExportData = (dependencyId?: string) => {
      if (!isAuthenticated) return false
      
      // Super admin can export all data
      if (isSuperAdmin) return true
      
      // Admin can export data from their dependency
      if (isAdmin && dependencyId && isDependencyAdmin(dependencyId)) {
        return true
      }
      
      return false
    }

    return {
      canAccessDashboard,
      canAccessAdminPanel,
      canAccessSuperAdminPanel,
      canAccessProcedures,
      canAccessChat,
      canAccessProfile,
      canAccessNotifications,
      canViewProcedure,
      canEditProcedure,
      canDeleteProcedure,
      canCreateProcedure,
      canViewUser,
      canEditUser,
      canDeleteUser,
      canCreateUser,
      canViewDependency,
      canEditDependency,
      canDeleteDependency,
      canCreateDependency,
      canViewSystemLogs,
      canManageSystemSettings,
      canViewAnalytics,
      canExportData,
    }
  }, [
    isAuthenticated,
    user,
    profile,
    isCitizen,
    isAdmin,
    isSuperAdmin,
    isDependencyAdmin,
    permissions,
  ])

  return checks
}
