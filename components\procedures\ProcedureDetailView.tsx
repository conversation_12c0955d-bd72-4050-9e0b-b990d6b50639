'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  X,
  FileText,
  Clock,
  User,
  Building,
  Calendar,
  Upload,
  MessageSquare,
  AlertCircle,
  CheckCircle,
  Phone,
  Mail,
  MapPin,
  ExternalLink,
  History,
  Paperclip,
  DollarSign,
  Star
} from 'lucide-react'

interface ProcedureDetailViewProps {
  procedure: any
  onClose: () => void
  onUploadDocuments: () => void
}

interface StatusHistory {
  id: string
  status_name: string
  status_display_name: string
  status_color: string
  changed_at: string
  changed_by: string
  notes?: string
}

export function ProcedureDetailView({
  procedure,
  onClose,
  onUploadDocuments
}: ProcedureDetailViewProps) {
  const [statusHistory, setStatusHistory] = useState<StatusHistory[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('details')

  const supabase = createClient()

  useEffect(() => {
    loadStatusHistory()
  }, [procedure.id])

  const loadStatusHistory = async () => {
    try {
      // For now, we'll create a mock status history since we don't have a status_history table
      // In a real implementation, you would query the status_history table
      const mockHistory: StatusHistory[] = [
        {
          id: '1',
          status_name: 'draft',
          status_display_name: 'Borrador',
          status_color: '#6B7280',
          changed_at: procedure.created_at,
          changed_by: 'Sistema',
          notes: 'Trámite iniciado por el ciudadano'
        }
      ]

      if (procedure.status.name !== 'draft') {
        mockHistory.push({
          id: '2',
          status_name: procedure.status.name,
          status_display_name: procedure.status.display_name,
          status_color: procedure.status.color || '#3B82F6',
          changed_at: procedure.updated_at,
          changed_by: procedure.assigned_to?.full_name || 'Sistema',
          notes: 'Estado actualizado'
        })
      }

      setStatusHistory(mockHistory.reverse()) // Most recent first
    } catch (error) {
      console.error('Error loading status history:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-CO', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getStatusColor = (status: any) => {
    return status.color || '#3B82F6'
  }

  const getPriorityBadge = (priority: number) => {
    const priorities = {
      1: { label: 'Baja', color: 'bg-green-100 text-green-800' },
      2: { label: 'Media', color: 'bg-yellow-100 text-yellow-800' },
      3: { label: 'Alta', color: 'bg-red-100 text-red-800' }
    }
    
    const p = priorities[priority as keyof typeof priorities] || priorities[2]
    return <Badge className={p.color}>{p.label}</Badge>
  }

  const getDaysRemaining = (dateString: string | null) => {
    if (!dateString) return null
    const targetDate = new Date(dateString)
    const today = new Date()
    const diffTime = targetDate.getTime() - today.getTime()
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b bg-gradient-to-r from-blue-600 to-blue-700 text-white">
          <div className="flex-1">
            <div className="flex items-center space-x-4">
              <FileText className="h-8 w-8" />
              <div>
                <h2 className="text-xl font-semibold">
                  {procedure.procedure.name}
                </h2>
                <p className="text-white/80 text-sm">
                  Referencia: {procedure.reference_number}
                </p>
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <Badge 
              style={{ backgroundColor: getStatusColor(procedure.status) }}
              className="text-white px-3 py-1"
            >
              {procedure.status.display_name}
            </Badge>
            <Button variant="ghost" size="sm" onClick={onClose} className="text-white hover:bg-white/20">
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="details">Detalles</TabsTrigger>
              <TabsTrigger value="documents">
                Documentos ({procedure.attachments?.length || 0})
              </TabsTrigger>
              <TabsTrigger value="history">Historial</TabsTrigger>
              <TabsTrigger value="contact">Contacto</TabsTrigger>
            </TabsList>

            <TabsContent value="details" className="mt-6 space-y-6">
              {/* Basic Information */}
              <Card>
                <CardHeader>
                  <CardTitle>Información General</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div>
                        <label className="text-sm font-medium text-gray-500">Trámite</label>
                        <p className="text-gray-900">{procedure.procedure.name}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Dependencia</label>
                        <p className="text-gray-900 flex items-center">
                          <Building className="h-4 w-4 mr-2 text-gray-400" />
                          {procedure.procedure.dependency.name}
                        </p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Estado</label>
                        <div className="flex items-center space-x-2">
                          <Badge 
                            style={{ backgroundColor: getStatusColor(procedure.status) }}
                            className="text-white"
                          >
                            {procedure.status.display_name}
                          </Badge>
                          {getPriorityBadge(procedure.priority)}
                        </div>
                      </div>
                    </div>
                    
                    <div className="space-y-4">
                      <div>
                        <label className="text-sm font-medium text-gray-500">Fecha de Creación</label>
                        <p className="text-gray-900 flex items-center">
                          <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                          {formatDate(procedure.created_at)}
                        </p>
                      </div>
                      {procedure.estimated_completion && (
                        <div>
                          <label className="text-sm font-medium text-gray-500">Fecha Estimada</label>
                          <p className="text-gray-900 flex items-center">
                            <Clock className="h-4 w-4 mr-2 text-gray-400" />
                            {formatDate(procedure.estimated_completion)}
                            {(() => {
                              const days = getDaysRemaining(procedure.estimated_completion)
                              return days !== null && (
                                <span className={`ml-2 text-sm ${
                                  days > 0 ? 'text-green-600' : 
                                  days === 0 ? 'text-yellow-600' : 
                                  'text-red-600'
                                }`}>
                                  ({days > 0 ? `${days} días restantes` : 
                                    days === 0 ? 'Vence hoy' : 
                                    `Vencido hace ${Math.abs(days)} días`})
                                </span>
                              )
                            })()}
                          </p>
                        </div>
                      )}
                      {procedure.assigned_to && (
                        <div>
                          <label className="text-sm font-medium text-gray-500">Asignado a</label>
                          <p className="text-gray-900 flex items-center">
                            <User className="h-4 w-4 mr-2 text-gray-400" />
                            {procedure.assigned_to.full_name}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Procedure Information */}
              <Card>
                <CardHeader>
                  <CardTitle>Información del Trámite</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Descripción</label>
                      <p className="text-gray-900 mt-1">{procedure.procedure.description}</p>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      {procedure.procedure.response_time && (
                        <div>
                          <label className="text-sm font-medium text-gray-500">Tiempo de Respuesta</label>
                          <p className="text-gray-900 flex items-center mt-1">
                            <Clock className="h-4 w-4 mr-2 text-gray-400" />
                            {procedure.procedure.response_time}
                          </p>
                        </div>
                      )}
                      
                      {procedure.procedure.cost && (
                        <div>
                          <label className="text-sm font-medium text-gray-500">Costo</label>
                          <p className="text-gray-900 flex items-center mt-1">
                            <DollarSign className="h-4 w-4 mr-2 text-gray-400" />
                            ${procedure.procedure.cost.toLocaleString()}
                          </p>
                        </div>
                      )}
                      
                      {procedure.procedure.online_available && (
                        <div>
                          <label className="text-sm font-medium text-gray-500">Disponibilidad</label>
                          <p className="text-green-600 flex items-center mt-1">
                            <CheckCircle className="h-4 w-4 mr-2" />
                            Disponible en línea
                          </p>
                        </div>
                      )}
                    </div>

                    {procedure.procedure.requirements && procedure.procedure.requirements.length > 0 && (
                      <div>
                        <label className="text-sm font-medium text-gray-500 mb-2 block">Requisitos</label>
                        <ul className="space-y-1">
                          {procedure.procedure.requirements.map((req: string, index: number) => (
                            <li key={index} className="flex items-start text-sm text-gray-700">
                              <CheckCircle className="h-4 w-4 text-green-600 mr-2 mt-0.5 flex-shrink-0" />
                              {req}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Additional Information */}
              {procedure.submitted_data && Object.keys(procedure.submitted_data).length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle>Información Adicional</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {Object.entries(procedure.submitted_data).map(([key, value]) => (
                        <div key={key}>
                          <label className="text-sm font-medium text-gray-500 capitalize">
                            {key.replace(/_/g, ' ')}
                          </label>
                          <p className="text-gray-900 mt-1">{String(value)}</p>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Notes */}
              {procedure.notes && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <MessageSquare className="h-5 w-5 mr-2 text-gray-600" />
                      Notas
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-700">{procedure.notes}</p>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="documents" className="mt-6 space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">
                  Documentos Adjuntos
                </h3>
                {!procedure.status.is_final && (
                  <Button onClick={onUploadDocuments}>
                    <Upload className="h-4 w-4 mr-2" />
                    Subir Documentos
                  </Button>
                )}
              </div>

              {procedure.attachments && procedure.attachments.length > 0 ? (
                <div className="grid gap-4">
                  {procedure.attachments.map((filePath: string, index: number) => {
                    const fileName = filePath.split('/').pop() || 'Documento'
                    return (
                      <Card key={index}>
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                              <Paperclip className="h-5 w-5 text-gray-400" />
                              <div>
                                <p className="font-medium text-gray-900">{fileName}</p>
                                <p className="text-sm text-gray-500">
                                  Subido: {formatDate(procedure.updated_at)}
                                </p>
                              </div>
                            </div>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                // Open document in new tab
                                const { data } = supabase.storage
                                  .from('documents')
                                  .getPublicUrl(filePath)
                                window.open(data.publicUrl, '_blank')
                              }}
                            >
                              <ExternalLink className="h-4 w-4 mr-2" />
                              Ver
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    )
                  })}
                </div>
              ) : (
                <Card>
                  <CardContent className="p-8 text-center">
                    <Paperclip className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      No hay documentos adjuntos
                    </h3>
                    <p className="text-gray-500 mb-4">
                      Aún no has subido documentos para este trámite
                    </p>
                    {!procedure.status.is_final && (
                      <Button onClick={onUploadDocuments}>
                        <Upload className="h-4 w-4 mr-2" />
                        Subir Primer Documento
                      </Button>
                    )}
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="history" className="mt-6 space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <History className="h-5 w-5 mr-2 text-gray-600" />
                    Historial de Estados
                  </CardTitle>
                  <CardDescription>
                    Seguimiento completo de los cambios de estado del trámite
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {loading ? (
                    <div className="text-center py-4">
                      <p className="text-gray-500">Cargando historial...</p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {statusHistory.map((entry, index) => (
                        <div key={entry.id} className="flex items-start space-x-4">
                          <div className="flex-shrink-0">
                            <div
                              className="w-3 h-3 rounded-full mt-2"
                              style={{ backgroundColor: entry.status_color }}
                            />
                            {index < statusHistory.length - 1 && (
                              <div className="w-0.5 h-8 bg-gray-200 ml-1 mt-1" />
                            )}
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center space-x-2">
                              <Badge
                                style={{ backgroundColor: entry.status_color }}
                                className="text-white"
                              >
                                {entry.status_display_name}
                              </Badge>
                              <span className="text-sm text-gray-500">
                                {formatDate(entry.changed_at)}
                              </span>
                            </div>
                            <p className="text-sm text-gray-600 mt-1">
                              Cambiado por: {entry.changed_by}
                            </p>
                            {entry.notes && (
                              <p className="text-sm text-gray-700 mt-1 bg-gray-50 p-2 rounded">
                                {entry.notes}
                              </p>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="contact" className="mt-6 space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Información de Contacto</CardTitle>
                  <CardDescription>
                    Datos de contacto de la dependencia responsable
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Dependencia</label>
                      <p className="text-gray-900 flex items-center mt-1">
                        <Building className="h-4 w-4 mr-2 text-gray-400" />
                        {procedure.procedure.dependency.name}
                      </p>
                    </div>

                    {procedure.procedure.dependency.contact_email && (
                      <div>
                        <label className="text-sm font-medium text-gray-500">Correo Electrónico</label>
                        <p className="text-gray-900 flex items-center mt-1">
                          <Mail className="h-4 w-4 mr-2 text-gray-400" />
                          <a
                            href={`mailto:${procedure.procedure.dependency.contact_email}`}
                            className="text-primary hover:text-primary/80"
                          >
                            {procedure.procedure.dependency.contact_email}
                          </a>
                        </p>
                      </div>
                    )}

                    {procedure.procedure.dependency.contact_phone && (
                      <div>
                        <label className="text-sm font-medium text-gray-500">Teléfono</label>
                        <p className="text-gray-900 flex items-center mt-1">
                          <Phone className="h-4 w-4 mr-2 text-gray-400" />
                          <a
                            href={`tel:${procedure.procedure.dependency.contact_phone}`}
                            className="text-primary hover:text-primary/80"
                          >
                            {procedure.procedure.dependency.contact_phone}
                          </a>
                        </p>
                      </div>
                    )}

                    {procedure.procedure.dependency.address && (
                      <div>
                        <label className="text-sm font-medium text-gray-500">Dirección</label>
                        <p className="text-gray-900 flex items-start mt-1">
                          <MapPin className="h-4 w-4 mr-2 text-gray-400 mt-0.5" />
                          {procedure.procedure.dependency.address}
                        </p>
                      </div>
                    )}

                    {procedure.assigned_to && (
                      <div className="border-t pt-4 mt-6">
                        <label className="text-sm font-medium text-gray-500">Funcionario Asignado</label>
                        <div className="mt-2 p-3 bg-gray-50 rounded-lg">
                          <p className="font-medium text-gray-900 flex items-center">
                            <User className="h-4 w-4 mr-2 text-gray-400" />
                            {procedure.assigned_to.full_name}
                          </p>
                          {procedure.assigned_to.email && (
                            <p className="text-sm text-gray-600 flex items-center mt-1">
                              <Mail className="h-4 w-4 mr-2 text-gray-400" />
                              <a
                                href={`mailto:${procedure.assigned_to.email}`}
                                className="text-primary hover:text-primary/80"
                              >
                                {procedure.assigned_to.email}
                              </a>
                            </p>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Footer */}
        <div className="flex justify-between items-center p-6 border-t bg-gray-50">
          <div className="text-sm text-gray-600">
            Última actualización: {formatDate(procedure.updated_at)}
          </div>
          <div className="flex space-x-3">
            {!procedure.status.is_final && (
              <Button variant="outline" onClick={onUploadDocuments}>
                <Upload className="h-4 w-4 mr-2" />
                Gestionar Documentos
              </Button>
            )}
            <Button onClick={onClose}>
              Cerrar
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
