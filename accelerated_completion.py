#!/usr/bin/env python3
"""
Accelerated FAQ Completion - Create large efficient batches
"""

import re

def create_large_completion_batches():
    """Create large batches for rapid completion"""
    
    # Load the corrected SQL file
    with open('corrected_faq_insertion.sql', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Extract all INSERT statements
    insert_statements = re.findall(r'INSERT INTO municipal_faqs.*?;', content, re.DOTALL)
    
    print(f"Found {len(insert_statements)} total INSERT statements")
    
    # We have 63 questions, so skip the first 63
    remaining_statements = insert_statements[63:]
    
    print(f"Need to apply {len(remaining_statements)} remaining questions")
    
    # Create 4 large batches of ~80 questions each
    batch_size = 80
    
    for i in range(0, len(remaining_statements), batch_size):
        batch = remaining_statements[i:i+batch_size]
        batch_num = i // batch_size + 1
        
        filename = f'accelerated_batch_{batch_num:02d}.sql'
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"-- Accelerated FAQ Completion Batch {batch_num}\n")
            f.write(f"-- Questions {i+64} to {min(i+63+batch_size, 383)}\n")
            f.write(f"-- Total questions in batch: {len(batch)}\n\n")
            f.write("\n".join(batch))
            f.write("\n")
        
        print(f"Created {filename} with {len(batch)} questions")
    
    return len(remaining_statements)

def main():
    """Main function"""
    print("Accelerated FAQ Completion")
    print("=" * 25)
    
    try:
        remaining_count = create_large_completion_batches()
        
        print(f"\n✅ Successfully created accelerated batches")
        print(f"📊 Current questions in database: 63")
        print(f"📊 Questions to be applied: {remaining_count}")
        print(f"📊 Total target: 383")
        print(f"📊 Progress: {round((63/383)*100, 1)}%")
        
        print(f"\n🚀 Ready for accelerated completion!")
        
        return 0
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
