# Migración de Formato de Códigos a XXX-XXX-XXX

## Resumen

Se ha completado exitosamente la migración del formato de códigos de procedimientos de XXX-XXX a XXX-XXX-XXX para lograr consistencia total entre trámites y OPAs.

## Cambios Implementados

### 1. Formato de Códigos Unificado

**Antes:**
- Trámites: `XXX-XXX` (ej: `001-001`)
- OPAs: `XXX-XXX` (ej: `000-001`)

**Después:**
- Trámites: `XXX-XXX-XXX` (ej: `000-001-001`)
- OPAs: `XXX-XXX-XXX` (ej: `000-000-001`)

**Estructura del código:**
- **XXX**: Código de dependencia (3 dígitos)
- **XXX**: Código de subdependencia (3 dígitos)  
- **XXX**: Consecutivo por subdependencia (3 dígitos)

### 2. Migraciones de Base de Datos

#### Migración 1: `update_codigo_format_to_full_standard_v3`
- Eliminó constraints existentes que interferían
- Actualizó función `generate_consecutive_code()` para formato XXX-XXX-XXX
- Migró códigos de trámites existentes al nuevo formato
- Agregó constraint de validación para trámites

#### Migración 2: `extend_opa_code_length_and_normalize_v3`
- Amplió campo `code` en tabla `opas` de VARCHAR(10) a VARCHAR(15)
- Migró códigos de OPAs al formato completo
- Agregó constraint de validación para OPAs
- Recreó vista unificada `vista_codigos_procedimientos`

### 3. Funciones de Base de Datos Actualizadas

#### `generate_consecutive_code(p_subdependency_id UUID, p_table_name TEXT)`
- Genera códigos en formato XXX-XXX-XXX
- Obtiene código de dependencia automáticamente
- Calcula consecutivo por subdependencia
- Soporta tanto trámites como OPAs

#### `get_dependency_code_from_subdependency(p_subdependency_id UUID)`
- Función auxiliar para obtener código de dependencia
- Retorna '000' como valor por defecto

### 4. Constraints y Validaciones

#### Trámites (tabla `procedures`)
```sql
ALTER TABLE procedures ADD CONSTRAINT procedures_codigo_tramite_format_check 
    CHECK (codigo_tramite ~ '^[0-9]{3}-[0-9]{3}-[0-9]{3}$');
```

#### OPAs (tabla `opas`)
```sql
ALTER TABLE opas ADD CONSTRAINT opas_code_format_check 
    CHECK (code ~ '^[0-9]{3}-[0-9]{3}-[0-9]{3}$');
```

### 5. Vista Unificada

La vista `vista_codigos_procedimientos` ahora muestra todos los códigos en formato XXX-XXX-XXX:

```sql
CREATE VIEW vista_codigos_procedimientos AS
SELECT 
    p.id,
    p.name,
    p.codigo_tramite as codigo,
    'TRAMITE' as tipo,
    d.code as dep_code,
    d.name as dep_name,
    s.code as subdep_code,
    s.name as subdep_name
FROM procedures p
LEFT JOIN subdependencies s ON p.subdependency_id = s.id
LEFT JOIN dependencies d ON s.dependency_id = d.id
WHERE p.is_active = true

UNION ALL

SELECT 
    o.id,
    o.name,
    o.code as codigo,
    'OPA' as tipo,
    d.code as dep_code,
    d.name as dep_name,
    s.code as subdep_code,
    s.name as subdep_name
FROM opas o
LEFT JOIN subdependencies s ON o.subdependency_id = s.id
LEFT JOIN dependencies d ON s.dependency_id = d.id
WHERE o.is_active = true;
```

## Resultados de la Migración

### Estadísticas Finales
- **Total de procedimientos migrados**: 826
- **Trámites migrados**: 108 (100% éxito)
- **OPAs migradas**: 718 (100% éxito)
- **Códigos inválidos**: 0
- **Porcentaje de éxito**: 100%

### Ejemplos de Códigos Migrados

#### Trámites
- `001-001` → `000-001-001` (Certificado de residencia)
- `040-041` → `040-041-001` (Impuesto de industria y comercio)

#### OPAs
- `000-001` → `000-000-001` (Radicación directa al Alcalde)
- `001-001` → `000-001-001` (Certificado de residencia)

## Pruebas y Validación

### Script de Verificación
Se creó `scripts/test-codigo-format.ts` que verifica:
- Formato correcto de todos los códigos
- Integridad de la vista unificada
- Estadísticas de migración

### Pruebas Unitarias Actualizadas
- `tests/components/procedures/ProcedureCodeDisplay.test.tsx`
- Actualizados todos los casos de prueba para formato XXX-XXX-XXX
- Validación de regex actualizada: `/^\d{3}-\d{3}-\d{3}$/`

## Impacto en Interfaces de Usuario

### Componentes Afectados
- `ProcedureCardEnhanced`: Muestra códigos en formato XXX-XXX-XXX
- `PublicProcedureDetailModal`: Headers con códigos actualizados
- Todas las interfaces mantienen compatibilidad automática

### Sin Cambios Requeridos en Frontend
- Los componentes ya estaban preparados para mostrar códigos dinámicamente
- No se requieren cambios en la lógica de presentación
- Las interfaces se adaptan automáticamente al nuevo formato

## Beneficios Logrados

1. **Consistencia Total**: Todos los procedimientos usan el mismo formato
2. **Trazabilidad Mejorada**: Códigos incluyen información de dependencia
3. **Escalabilidad**: Soporte para hasta 999 procedimientos por subdependencia
4. **Integridad Referencial**: Códigos reflejan la estructura organizacional
5. **Facilidad de Búsqueda**: Formato uniforme facilita filtros y búsquedas

## Mantenimiento Futuro

### Generación Automática
- Nuevos procedimientos reciben códigos automáticamente
- Triggers configurados para ambas tablas
- Consecutivos se calculan por subdependencia

### Monitoreo
- Usar `scripts/test-codigo-format.ts` para verificaciones periódicas
- Constraints de base de datos previenen códigos inválidos
- Vista unificada facilita auditorías

## Archivos Modificados

### Migraciones
- `supabase/migrations/20250703000002_update_codigo_format_to_full_standard.sql`
- Migraciones aplicadas: `update_codigo_format_to_full_standard_v3`, `extend_opa_code_length_and_normalize_v3`

### Scripts
- `scripts/test-codigo-format.ts` (nuevo)

### Pruebas
- `tests/components/procedures/ProcedureCodeDisplay.test.tsx` (actualizado)

### Documentación
- `docs/CODIGO_FORMAT_MIGRATION.md` (este archivo)

---

**Fecha de migración**: 2025-07-03  
**Estado**: ✅ Completado exitosamente  
**Validación**: ✅ 100% de códigos en formato correcto
