'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { 
  BarChart3, 
  TrendingUp, 
  Download, 
  Calendar,
  Users,
  FileText,
  Clock,
  CheckCircle,
  Building,
  DollarSign
} from 'lucide-react'
import { createClient } from '@/lib/supabase/client'
import { useAuth, useRole } from '@/hooks'
import { ConditionalContent } from '@/components/auth'

interface ReportData {
  totalProcedures: number
  completedProcedures: number
  pendingProcedures: number
  averageProcessingTime: number
  totalRevenue: number
  proceduresByMonth: Array<{
    month: string
    count: number
  }>
  proceduresByType: Array<{
    name: string
    count: number
  }>
  proceduresByStatus: Array<{
    status: string
    count: number
  }>
}

export function AdminReports() {
  const [reportData, setReportData] = useState<ReportData | null>(null)
  const [loading, setLoading] = useState(true)
  const [dateRange, setDateRange] = useState('last_30_days')
  const [selectedDependency, setSelectedDependency] = useState('all')
  const [dependencies, setDependencies] = useState<any[]>([])

  const { profile } = useAuth()
  const { isSuperAdmin, isAdmin } = useRole()
  const supabase = createClient()

  useEffect(() => {
    fetchDependencies()
    fetchReportData()
  }, [dateRange, selectedDependency])

  const fetchDependencies = async () => {
    if (!isSuperAdmin) return

    try {
      const { data, error } = await supabase
        .from('dependencies')
        .select('id, name')
        .order('name')

      if (error) throw error
      setDependencies(data || [])
    } catch (error) {
      console.error('Error fetching dependencies:', error)
    }
  }

  const fetchReportData = async () => {
    try {
      setLoading(true)
      
      // Calculate date range
      const endDate = new Date()
      const startDate = new Date()
      
      switch (dateRange) {
        case 'last_7_days':
          startDate.setDate(endDate.getDate() - 7)
          break
        case 'last_30_days':
          startDate.setDate(endDate.getDate() - 30)
          break
        case 'last_90_days':
          startDate.setDate(endDate.getDate() - 90)
          break
        case 'last_year':
          startDate.setFullYear(endDate.getFullYear() - 1)
          break
      }

      let baseQuery = supabase
        .from('citizen_procedures')
        .select(`
          id,
          created_at,
          status_id,
          procedure:procedures(
            name,
            cost,
            dependency_id,
            dependency:dependencies(name)
          )
        `)
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString())

      // Filter by dependency if admin or specific dependency selected
      if (isAdmin && profile?.dependency_id) {
        baseQuery = baseQuery.in('procedure.dependency_id', [profile.dependency_id])
      } else if (isSuperAdmin && selectedDependency !== 'all') {
        baseQuery = baseQuery.in('procedure.dependency_id', [selectedDependency])
      }

      const { data: procedures, error } = await baseQuery

      if (error) throw error

      // Process data for reports
      const totalProcedures = procedures?.length || 0
      const completedProcedures = procedures?.filter(p => p.status_id === 'completed').length || 0
      const pendingProcedures = procedures?.filter(p => p.status_id === 'pending').length || 0
      
      // Calculate total revenue
      const totalRevenue = procedures?.reduce((sum, p) => {
        return sum + (p.procedure?.cost || 0)
      }, 0) || 0

      // Group by month
      const proceduresByMonth = procedures?.reduce((acc: any, p) => {
        const month = new Date(p.created_at).toLocaleDateString('es-CO', { 
          year: 'numeric', 
          month: 'short' 
        })
        acc[month] = (acc[month] || 0) + 1
        return acc
      }, {})

      // Group by procedure type
      const proceduresByType = procedures?.reduce((acc: any, p) => {
        const name = p.procedure?.name || 'Sin especificar'
        acc[name] = (acc[name] || 0) + 1
        return acc
      }, {})

      // Group by status
      const proceduresByStatus = procedures?.reduce((acc: any, p) => {
        acc[p.status_id] = (acc[p.status_id] || 0) + 1
        return acc
      }, {})

      setReportData({
        totalProcedures,
        completedProcedures,
        pendingProcedures,
        averageProcessingTime: 0, // TODO: Calculate based on actual processing times
        totalRevenue,
        proceduresByMonth: Object.entries(proceduresByMonth || {}).map(([month, count]) => ({
          month,
          count: count as number
        })),
        proceduresByType: Object.entries(proceduresByType || {}).map(([name, count]) => ({
          name,
          count: count as number
        })),
        proceduresByStatus: Object.entries(proceduresByStatus || {}).map(([status, count]) => ({
          status,
          count: count as number
        }))
      })
    } catch (error) {
      console.error('Error fetching report data:', error)
    } finally {
      setLoading(false)
    }
  }

  const exportReport = () => {
    // TODO: Implement CSV/PDF export functionality
    console.log('Exporting report...')
  }

  const getStatusDisplayName = (status: string) => {
    const statusMap: { [key: string]: string } = {
      'pending': 'Pendiente',
      'in_progress': 'En Proceso',
      'completed': 'Completado',
      'rejected': 'Rechazado',
      'cancelled': 'Cancelado'
    }
    return statusMap[status] || status
  }

  const getStatusColor = (status: string) => {
    const colorMap: { [key: string]: string } = {
      'pending': 'bg-yellow-500',
      'in_progress': 'bg-blue-500',
      'completed': 'bg-green-500',
      'rejected': 'bg-red-500',
      'cancelled': 'bg-gray-500'
    }
    return colorMap[status] || 'bg-gray-500'
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center">
              <BarChart3 className="h-12 w-12 mx-auto text-gray-400 mb-4" />
              <p className="text-gray-500">Cargando reportes...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header with Filters */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Reportes y Estadísticas</CardTitle>
              <CardDescription>
                <ConditionalContent
                  adminContent={`Análisis de datos de ${profile?.dependency?.name || 'tu dependencia'}`}
                  superAdminContent="Análisis completo del sistema municipal"
                >
                  Análisis de datos del sistema
                </ConditionalContent>
              </CardDescription>
            </div>
            <Button onClick={exportReport} variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Exportar
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="w-full sm:w-48">
              <Select value={dateRange} onValueChange={setDateRange}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="last_7_days">Últimos 7 días</SelectItem>
                  <SelectItem value="last_30_days">Últimos 30 días</SelectItem>
                  <SelectItem value="last_90_days">Últimos 90 días</SelectItem>
                  <SelectItem value="last_year">Último año</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            {isSuperAdmin && (
              <div className="w-full sm:w-48">
                <Select value={selectedDependency} onValueChange={setSelectedDependency}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Todas las dependencias</SelectItem>
                    {dependencies.map((dep) => (
                      <SelectItem key={dep.id} value={dep.id}>
                        {dep.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Trámites</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{reportData?.totalProcedures || 0}</div>
            <p className="text-xs text-muted-foreground">
              En el período seleccionado
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completados</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {reportData?.completedProcedures || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              {reportData?.totalProcedures ? 
                `${Math.round((reportData.completedProcedures / reportData.totalProcedures) * 100)}% del total` :
                '0% del total'
              }
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pendientes</CardTitle>
            <Clock className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">
              {reportData?.pendingProcedures || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              Esperando atención
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Ingresos</CardTitle>
            <DollarSign className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${(reportData?.totalRevenue || 0).toLocaleString('es-CO')}
            </div>
            <p className="text-xs text-muted-foreground">
              Total recaudado
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts and Detailed Reports */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Procedures by Type */}
        <Card>
          <CardHeader>
            <CardTitle>Trámites por Tipo</CardTitle>
            <CardDescription>
              Distribución de trámites más solicitados
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {reportData?.proceduresByType.slice(0, 5).map((item, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span className="text-sm font-medium">{item.name}</span>
                  </div>
                  <Badge variant="outline">{item.count}</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Procedures by Status */}
        <Card>
          <CardHeader>
            <CardTitle>Trámites por Estado</CardTitle>
            <CardDescription>
              Estado actual de los trámites
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {reportData?.proceduresByStatus.map((item, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div className={`w-2 h-2 rounded-full ${getStatusColor(item.status)}`}></div>
                    <span className="text-sm font-medium">
                      {getStatusDisplayName(item.status)}
                    </span>
                  </div>
                  <Badge variant="outline">{item.count}</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Monthly Trend */}
      <Card>
        <CardHeader>
          <CardTitle>Tendencia Mensual</CardTitle>
          <CardDescription>
            Evolución de trámites en el tiempo
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {reportData?.proceduresByMonth.map((item, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4 text-gray-400" />
                  <span className="text-sm font-medium">{item.month}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-24 bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-500 h-2 rounded-full" 
                      style={{ 
                        width: `${Math.min((item.count / Math.max(...(reportData?.proceduresByMonth.map(m => m.count) || [1]))) * 100, 100)}%` 
                      }}
                    ></div>
                  </div>
                  <Badge variant="outline">{item.count}</Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
