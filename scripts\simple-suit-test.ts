/**
 * Prueba simple del scraper SUIT sin dependencias de .env
 */

import { createClient } from '@supabase/supabase-js'
import puppeteer from 'puppeteer'

// Credenciales directas (solo para pruebas)
const supabaseUrl = 'https://zeieudvbhlrlnfkwejoh.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InplaWV1ZHZiaGxybG5ma3dlam9oIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTMwOTUwMSwiZXhwIjoyMDY2ODg1NTAxfQ.V0x7kNUg1ZJ_4lP0Nx3_k2UbdTqfIwvnMd4Y2CQaaAg'

const supabase = createClient(supabaseUrl, supabaseKey)

async function testSuitConnection() {
  console.log('🧪 PRUEBA SIMPLE DEL SCRAPER SUIT')
  console.log('=' .repeat(40))

  try {
    // 1. Probar conexión a Supabase
    console.log('📊 Probando conexión a Supabase...')
    const { data: stats, error: statsError } = await supabase
      .from('vista_suit_scraping_stats')
      .select('*')
      .single()

    if (statsError) {
      console.error('❌ Error conectando a Supabase:', statsError)
      return
    }

    console.log('✅ Conexión a Supabase exitosa')
    console.log(`   Total procedimientos: ${stats.total_procedures_with_suit}`)
    console.log(`   Pendientes: ${stats.pending_scraping}`)

    // 2. Obtener procedimientos para prueba
    console.log('\n📋 Obteniendo procedimientos de prueba...')
    const { data: procedures, error: procError } = await supabase
      .rpc('get_procedures_needing_scraping', { limit_count: 2 })

    if (procError) {
      console.error('❌ Error obteniendo procedimientos:', procError)
      return
    }

    if (!procedures || procedures.length === 0) {
      console.log('⚠️ No hay procedimientos pendientes')
      return
    }

    console.log(`✅ Obtenidos ${procedures.length} procedimientos:`)
    procedures.forEach((proc, index) => {
      console.log(`   ${index + 1}. ${proc.procedure_name} (Ficha: ${proc.ficha_id})`)
    })

    // 3. Probar scraping de una página
    console.log('\n🌐 Probando scraping de una página SUIT...')
    const testProcedure = procedures[0]
    const url = `https://visorsuit.funcionpublica.gov.co/auth/visor?fi=${testProcedure.ficha_id}`
    
    console.log(`   URL: ${url}`)
    console.log(`   Procedimiento: ${testProcedure.procedure_name}`)

    // Inicializar navegador
    console.log('   🚀 Inicializando navegador...')
    const browser = await puppeteer.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage'
      ]
    })

    const page = await browser.newPage()
    await page.setUserAgent('ChiaTramitesBot/1.0 (Municipio de Chía - Prueba)')
    await page.setViewport({ width: 1366, height: 768 })

    try {
      console.log('   📄 Navegando a la página...')

      // Primero intentar cargar la página principal de SUIT
      console.log('   🔗 Probando acceso a SUIT...')
      await page.goto('https://visorsuit.funcionpublica.gov.co/', {
        waitUntil: 'domcontentloaded',
        timeout: 15000
      })

      console.log('   ✅ Acceso a SUIT exitoso, navegando a ficha específica...')
      await page.goto(url, {
        waitUntil: 'domcontentloaded',
        timeout: 15000
      })

      // Esperar un poco para que cargue
      await new Promise(resolve => setTimeout(resolve, 3000))

      console.log('   🔍 Extrayendo información...')
      
      // Extraer información básica
      const pageInfo = await page.evaluate(() => {
        const title = document.title
        const bodyText = document.body.innerText
        const headings = Array.from(document.querySelectorAll('h1, h2, h3')).map(h => h.textContent?.trim()).filter(Boolean)
        
        return {
          title,
          bodyLength: bodyText.length,
          headings: headings.slice(0, 5), // Primeros 5 headings
          hasContent: bodyText.length > 100
        }
      })

      console.log('   📊 Información extraída:')
      console.log(`      Título: ${pageInfo.title}`)
      console.log(`      Longitud del contenido: ${pageInfo.bodyLength} caracteres`)
      console.log(`      Tiene contenido útil: ${pageInfo.hasContent ? '✅ Sí' : '❌ No'}`)
      
      if (pageInfo.headings.length > 0) {
        console.log(`      Encabezados encontrados:`)
        pageInfo.headings.forEach((heading, index) => {
          console.log(`         ${index + 1}. ${heading.substring(0, 60)}...`)
        })
      }

      // Verificar si requiere autenticación
      const requiresAuth = await page.evaluate(() => {
        const bodyText = document.body.innerText.toLowerCase()
        return bodyText.includes('login') || 
               bodyText.includes('autenticación') || 
               bodyText.includes('iniciar sesión') ||
               bodyText.includes('acceso denegado')
      })

      console.log(`      Requiere autenticación: ${requiresAuth ? '⚠️ Sí' : '✅ No'}`)

      if (pageInfo.hasContent && !requiresAuth) {
        console.log('   🎉 ¡Scraping exitoso! La página es accesible y tiene contenido')
        
        // Guardar resultado de prueba
        const { error: saveError } = await supabase
          .from('suit_scraped_data')
          .update({
            titulo: pageInfo.title,
            descripcion_detallada: `Prueba exitosa - ${pageInfo.bodyLength} caracteres extraídos`,
            scraping_status: 'success',
            scraped_at: new Date().toISOString()
          })
          .eq('ficha_id', testProcedure.ficha_id)

        if (saveError) {
          console.log(`   ⚠️ Error guardando resultado: ${saveError.message}`)
        } else {
          console.log('   💾 Resultado guardado en base de datos')
        }
      } else {
        console.log('   ❌ Scraping fallido - página no accesible o sin contenido')
      }

    } catch (error) {
      console.log(`   💥 Error durante el scraping: ${error}`)
    } finally {
      await browser.close()
      console.log('   🧹 Navegador cerrado')
    }

    // 4. Verificar estadísticas actualizadas
    console.log('\n📈 Verificando estadísticas actualizadas...')
    const { data: updatedStats, error: updatedStatsError } = await supabase
      .from('vista_suit_scraping_stats')
      .select('*')
      .single()

    if (!updatedStatsError && updatedStats) {
      console.log(`   Exitosos: ${updatedStats.successfully_scraped}`)
      console.log(`   Fallidos: ${updatedStats.failed_scraping}`)
      console.log(`   Pendientes: ${updatedStats.pending_scraping}`)
      console.log(`   Tasa de éxito: ${updatedStats.success_rate_percent}%`)
    }

    console.log('\n🎉 PRUEBA COMPLETADA')
    console.log('✅ El sistema está listo para el scraping completo')

  } catch (error) {
    console.error('\n💥 ERROR EN LA PRUEBA:', error)
  }
}

// Ejecutar prueba
if (require.main === module) {
  testSuitConnection()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('💥 Error fatal:', error)
      process.exit(1)
    })
}

export { testSuitConnection }
