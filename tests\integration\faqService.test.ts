import { describe, it, expect, beforeAll, afterAll } from '@jest/globals'
import FAQService from '@/lib/services/faqService'
import { supabase } from '@/lib/supabase/client'

describe('FAQService - Municipal FAQ Integration', () => {
  beforeAll(async () => {
    // Verificar conexión a Supabase
    try {
      const { data, error } = await supabase.from('municipal_faqs').select('id').limit(1)
      if (error) {
        console.warn('Supabase connection issue:', error.message)
      }
    } catch (error) {
      console.warn('Supabase setup error:', error)
    }
  })

  afterAll(async () => {
    // Cleanup si es necesario
  })

  describe('getThemes', () => {
    it('should fetch FAQ themes from database', async () => {
      const themes = await FAQService.getThemes()
      
      expect(Array.isArray(themes)).toBe(true)
      if (themes.length > 0) {
        const theme = themes[0]
        expect(theme).toHaveProperty('id')
        expect(theme).toHaveProperty('name')
        expect(theme).toHaveProperty('count')
        expect(typeof theme.name).toBe('string')
        expect(typeof theme.count).toBe('number')
      }
    })

    it('should return themes sorted by display order', async () => {
      const themes = await FAQService.getThemes()
      
      if (themes.length > 1) {
        for (let i = 1; i < themes.length; i++) {
          const prevOrder = themes[i - 1].displayOrder || 0
          const currentOrder = themes[i].displayOrder || 0
          expect(currentOrder).toBeGreaterThanOrEqual(prevOrder)
        }
      }
    })
  })

  describe('searchFAQs', () => {
    it('should perform full-text search in Spanish', async () => {
      const results = await FAQService.searchFAQs('certificado')
      
      expect(Array.isArray(results)).toBe(true)
      if (results.length > 0) {
        const faq = results[0]
        expect(faq).toHaveProperty('id')
        expect(faq).toHaveProperty('question')
        expect(faq).toHaveProperty('answer')
        expect(faq).toHaveProperty('theme')
        expect(faq).toHaveProperty('keywords')
        expect(Array.isArray(faq.keywords)).toBe(true)
      }
    })

    it('should return empty array for empty search query', async () => {
      const results = await FAQService.searchFAQs('')
      expect(results).toEqual([])
    })

    it('should respect limit parameter', async () => {
      const limit = 3
      const results = await FAQService.searchFAQs('certificado', { limit })
      
      expect(results.length).toBeLessThanOrEqual(limit)
    })

    it('should filter by theme when specified', async () => {
      const themes = await FAQService.getThemes()
      if (themes.length > 0) {
        const themeId = themes[0].id
        const results = await FAQService.searchFAQs('certificado', { theme: themeId })
        
        results.forEach(faq => {
          expect(faq.themeId).toBe(themeId)
        })
      }
    })
  })

  describe('getFAQsByTheme', () => {
    it('should fetch FAQs by theme ID', async () => {
      const themes = await FAQService.getThemes()
      if (themes.length > 0) {
        const themeId = themes[0].id
        const faqs = await FAQService.getFAQsByTheme(themeId)
        
        expect(Array.isArray(faqs)).toBe(true)
        faqs.forEach(faq => {
          expect(faq.themeId).toBe(themeId)
        })
      }
    })

    it('should respect limit parameter', async () => {
      const themes = await FAQService.getThemes()
      if (themes.length > 0) {
        const themeId = themes[0].id
        const limit = 2
        const faqs = await FAQService.getFAQsByTheme(themeId, limit)
        
        expect(faqs.length).toBeLessThanOrEqual(limit)
      }
    })
  })

  describe('getPopularFAQs', () => {
    it('should fetch popular FAQs sorted by popularity score', async () => {
      const faqs = await FAQService.getPopularFAQs(5)
      
      expect(Array.isArray(faqs)).toBe(true)
      expect(faqs.length).toBeLessThanOrEqual(5)
      
      if (faqs.length > 1) {
        for (let i = 1; i < faqs.length; i++) {
          expect(faqs[i].popularityScore).toBeLessThanOrEqual(faqs[i - 1].popularityScore)
        }
      }
    })
  })

  describe('getFAQById', () => {
    it('should fetch FAQ by ID and increment view count', async () => {
      // Primero obtener un FAQ existente
      const popularFAQs = await FAQService.getPopularFAQs(1)
      if (popularFAQs.length > 0) {
        const faqId = popularFAQs[0].id
        const initialViewCount = popularFAQs[0].viewCount
        
        const faq = await FAQService.getFAQById(faqId)
        
        expect(faq).not.toBeNull()
        if (faq) {
          expect(faq.id).toBe(faqId)
          expect(faq).toHaveProperty('question')
          expect(faq).toHaveProperty('answer')
          expect(faq).toHaveProperty('theme')
        }
      }
    })

    it('should return null for non-existent FAQ ID', async () => {
      const faq = await FAQService.getFAQById('non-existent-id')
      expect(faq).toBeNull()
    })
  })

  describe('getFAQStats', () => {
    it('should return FAQ statistics', async () => {
      const stats = await FAQService.getFAQStats()
      
      expect(stats).toHaveProperty('totalFAQs')
      expect(stats).toHaveProperty('totalThemes')
      expect(stats).toHaveProperty('averagePopularity')
      expect(stats).toHaveProperty('mostPopularTheme')
      
      expect(typeof stats.totalFAQs).toBe('number')
      expect(typeof stats.totalThemes).toBe('number')
      expect(typeof stats.averagePopularity).toBe('number')
      expect(typeof stats.mostPopularTheme).toBe('string')
      
      expect(stats.totalFAQs).toBeGreaterThanOrEqual(0)
      expect(stats.totalThemes).toBeGreaterThanOrEqual(0)
    })
  })

  describe('Compatibility methods', () => {
    it('getCategories should work as alias for getThemes', async () => {
      const themes = await FAQService.getThemes()
      const categories = await FAQService.getCategories()
      
      expect(categories).toEqual(themes)
    })

    it('getFAQsByCategory should work as alias for getFAQsByTheme', async () => {
      const themes = await FAQService.getThemes()
      if (themes.length > 0) {
        const themeId = themes[0].id
        const faqsByTheme = await FAQService.getFAQsByTheme(themeId)
        const faqsByCategory = await FAQService.getFAQsByCategory(themeId)
        
        expect(faqsByCategory).toEqual(faqsByTheme)
      }
    })
  })

  describe('Error handling', () => {
    it('should handle search errors gracefully', async () => {
      // Test con caracteres especiales que podrían causar errores
      const results = await FAQService.searchFAQs("'; DROP TABLE municipal_faqs; --")
      
      expect(Array.isArray(results)).toBe(true)
      // No debería causar errores, solo retornar array vacío o resultados válidos
    })

    it('should handle network errors gracefully', async () => {
      // Simular error de red desconectando temporalmente
      // En un entorno real, esto requeriría mocking de Supabase
      const results = await FAQService.searchFAQs('test')
      expect(Array.isArray(results)).toBe(true)
    })
  })
})
