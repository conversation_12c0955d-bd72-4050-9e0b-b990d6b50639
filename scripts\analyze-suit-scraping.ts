/**
 * Análisis de viabilidad de web scraping para enlaces SUIT
 * Investigación técnica, legal y propuesta de implementación
 */

import { createClient } from '@supabase/supabase-js'
import * as dotenv from 'dotenv'
import fetch from 'node-fetch'

// Cargar variables de entorno
dotenv.config()

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

const supabase = createClient(supabaseUrl, supabaseKey)

interface SuitAnalysisResult {
  url: string
  status: number
  contentType?: string
  hasAuth: boolean
  hasRobotsTxt: boolean
  scrapingViability: 'VIABLE' | 'REQUIRES_AUTH' | 'BLOCKED' | 'UNKNOWN'
  errorMessage?: string
}

interface ProcedureWithSuit {
  id: string
  name: string
  suit_link: string
  description: string
}

async function analyzeSuitScraping() {
  console.log('🔍 Iniciando análisis de viabilidad de web scraping SUIT...\n')

  try {
    // 1. Obtener muestra de enlaces SUIT
    console.log('📋 Obteniendo muestra de enlaces SUIT...')
    
    const { data: procedures, error } = await supabase
      .from('procedures')
      .select('id, name, suit_link, description')
      .not('suit_link', 'is', null)
      .neq('suit_link', '')
      .limit(5)

    if (error) {
      console.error('❌ Error obteniendo procedures:', error)
      return
    }

    console.log(`✅ Obtenidos ${procedures.length} procedimientos con enlaces SUIT\n`)

    // 2. Analizar robots.txt del dominio SUIT
    console.log('🤖 Analizando robots.txt...')
    await analyzeRobotsTxt()

    // 3. Probar acceso a enlaces SUIT
    console.log('\n🌐 Probando acceso a enlaces SUIT...')
    const analysisResults: SuitAnalysisResult[] = []

    for (const procedure of procedures) {
      console.log(`\n📄 Analizando: ${procedure.name}`)
      console.log(`🔗 URL: ${procedure.suit_link}`)
      
      const result = await testSuitUrl(procedure.suit_link)
      analysisResults.push(result)
      
      console.log(`📊 Estado: ${result.status}`)
      console.log(`🔒 Viabilidad: ${result.scrapingViability}`)
      
      if (result.errorMessage) {
        console.log(`⚠️  Error: ${result.errorMessage}`)
      }

      // Pausa entre requests para ser respetuosos
      await new Promise(resolve => setTimeout(resolve, 2000))
    }

    // 4. Generar reporte de análisis
    console.log('\n📊 REPORTE DE ANÁLISIS')
    console.log('=' .repeat(50))
    
    const viable = analysisResults.filter(r => r.scrapingViability === 'VIABLE').length
    const requiresAuth = analysisResults.filter(r => r.scrapingViability === 'REQUIRES_AUTH').length
    const blocked = analysisResults.filter(r => r.scrapingViability === 'BLOCKED').length
    const unknown = analysisResults.filter(r => r.scrapingViability === 'UNKNOWN').length

    console.log(`📈 Total de URLs analizadas: ${analysisResults.length}`)
    console.log(`✅ Viables para scraping: ${viable}`)
    console.log(`🔐 Requieren autenticación: ${requiresAuth}`)
    console.log(`🚫 Bloqueadas: ${blocked}`)
    console.log(`❓ Estado desconocido: ${unknown}`)

    // 5. Análisis de estructura de datos actual
    console.log('\n📋 ANÁLISIS DE DATOS ACTUALES')
    console.log('=' .repeat(50))
    
    await analyzeCurrentData()

    // 6. Recomendaciones técnicas
    console.log('\n💡 RECOMENDACIONES TÉCNICAS')
    console.log('=' .repeat(50))
    generateRecommendations(analysisResults)

    // 7. Consideraciones legales
    console.log('\n⚖️  CONSIDERACIONES LEGALES')
    console.log('=' .repeat(50))
    generateLegalConsiderations()

    // 8. Propuesta de implementación
    console.log('\n🛠️  PROPUESTA DE IMPLEMENTACIÓN')
    console.log('=' .repeat(50))
    generateImplementationProposal(analysisResults)

  } catch (error) {
    console.error('❌ Error durante el análisis:', error)
  }
}

async function analyzeRobotsTxt(): Promise<void> {
  try {
    const robotsUrl = 'https://visorsuit.funcionpublica.gov.co/robots.txt'
    const response = await fetch(robotsUrl, {
      headers: {
        'User-Agent': 'ChiaTramitesBot/1.0 (Municipal Services Analysis)'
      }
    })

    if (response.ok) {
      const robotsContent = await response.text()
      console.log('✅ robots.txt encontrado:')
      console.log(robotsContent.substring(0, 500) + (robotsContent.length > 500 ? '...' : ''))
      
      // Analizar si permite scraping
      const disallowAll = robotsContent.includes('Disallow: /')
      const hasSpecificRules = robotsContent.includes('Disallow: /auth') || robotsContent.includes('Disallow: /visor')
      
      if (disallowAll) {
        console.log('⚠️  robots.txt prohíbe todo el scraping')
      } else if (hasSpecificRules) {
        console.log('⚠️  robots.txt tiene reglas específicas para ciertas rutas')
      } else {
        console.log('✅ robots.txt permite scraping general')
      }
    } else {
      console.log('❌ No se pudo acceder a robots.txt')
    }
  } catch (error) {
    console.log('❌ Error analizando robots.txt:', error)
  }
}

async function testSuitUrl(url: string): Promise<SuitAnalysisResult> {
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'es-ES,es;q=0.8,en-US;q=0.5,en;q=0.3',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
      },
      timeout: 10000
    })

    const contentType = response.headers.get('content-type') || ''
    const content = await response.text()

    // Analizar el contenido para determinar viabilidad
    let scrapingViability: SuitAnalysisResult['scrapingViability'] = 'UNKNOWN'
    let hasAuth = false

    if (response.status === 200) {
      if (content.includes('login') || content.includes('auth') || content.includes('AuthApp')) {
        scrapingViability = 'REQUIRES_AUTH'
        hasAuth = true
      } else if (content.includes('blocked') || content.includes('forbidden')) {
        scrapingViability = 'BLOCKED'
      } else if (content.length > 1000 && contentType.includes('text/html')) {
        scrapingViability = 'VIABLE'
      }
    } else if (response.status === 401 || response.status === 403) {
      scrapingViability = 'REQUIRES_AUTH'
      hasAuth = true
    } else if (response.status === 429) {
      scrapingViability = 'BLOCKED'
    }

    return {
      url,
      status: response.status,
      contentType,
      hasAuth,
      hasRobotsTxt: false, // Se analiza por separado
      scrapingViability
    }

  } catch (error) {
    return {
      url,
      status: 0,
      hasAuth: false,
      hasRobotsTxt: false,
      scrapingViability: 'UNKNOWN',
      errorMessage: error instanceof Error ? error.message : 'Error desconocido'
    }
  }
}

async function analyzeCurrentData(): Promise<void> {
  try {
    const { data: stats, error } = await supabase
      .from('procedures')
      .select('suit_link, description')
      .not('suit_link', 'is', null)
      .neq('suit_link', '')

    if (error) {
      console.error('❌ Error obteniendo estadísticas:', error)
      return
    }

    const totalWithSuit = stats.length
    const avgDescLength = stats.reduce((sum, p) => sum + (p.description?.length || 0), 0) / totalWithSuit
    const shortDescriptions = stats.filter(p => (p.description?.length || 0) < 100).length

    console.log(`📊 Procedimientos con enlaces SUIT: ${totalWithSuit}`)
    console.log(`📏 Longitud promedio de descripción: ${Math.round(avgDescLength)} caracteres`)
    console.log(`📝 Descripciones cortas (<100 chars): ${shortDescriptions} (${Math.round((shortDescriptions/totalWithSuit)*100)}%)`)
    
    if (shortDescriptions > totalWithSuit * 0.3) {
      console.log('⚠️  Muchas descripciones son cortas - el scraping podría ser beneficioso')
    }
  } catch (error) {
    console.error('❌ Error analizando datos actuales:', error)
  }
}

function generateRecommendations(results: SuitAnalysisResult[]): void {
  const requiresAuth = results.filter(r => r.scrapingViability === 'REQUIRES_AUTH').length
  const total = results.length

  if (requiresAuth === total) {
    console.log('🔐 RECOMENDACIÓN: Implementar autenticación automatizada')
    console.log('   - Investigar API oficial de SUIT')
    console.log('   - Considerar selenium/puppeteer para autenticación')
    console.log('   - Implementar rate limiting estricto')
  } else {
    console.log('✅ RECOMENDACIÓN: Scraping directo viable para algunos casos')
    console.log('   - Implementar scraping selectivo')
    console.log('   - Cache de resultados para evitar requests repetidos')
  }

  console.log('\n🛡️  MEDIDAS DE SEGURIDAD RECOMENDADAS:')
  console.log('   - Rate limiting: máximo 1 request por 5 segundos')
  console.log('   - User-Agent identificativo del municipio')
  console.log('   - Manejo robusto de errores y timeouts')
  console.log('   - Logs detallados para auditoría')
  console.log('   - Respeto a robots.txt y términos de servicio')
}

function generateLegalConsiderations(): void {
  console.log('📋 ASPECTOS LEGALES A CONSIDERAR:')
  console.log('   ✅ Datos públicos gubernamentales')
  console.log('   ✅ Propósito de servicio público municipal')
  console.log('   ⚠️  Verificar términos de uso de SUIT')
  console.log('   ⚠️  Respetar robots.txt y rate limits')
  console.log('   ⚠️  No sobrecargar servidores gubernamentales')
  console.log('   ✅ Transparencia en el uso de datos')
  console.log('   ✅ Beneficio directo para ciudadanos')
}

function generateImplementationProposal(results: SuitAnalysisResult[]): void {
  console.log('🏗️  FASES DE IMPLEMENTACIÓN PROPUESTAS:')
  console.log('\n📋 FASE 1: Investigación y Preparación')
  console.log('   - Contactar a Función Pública para API oficial')
  console.log('   - Análisis detallado de estructura HTML')
  console.log('   - Definir campos a extraer (descripción, requisitos, etc.)')
  
  console.log('\n🛠️  FASE 2: Desarrollo de Prototipo')
  console.log('   - Scraper básico con rate limiting')
  console.log('   - Parseo de HTML y extracción de datos')
  console.log('   - Sistema de cache y almacenamiento')
  
  console.log('\n🧪 FASE 3: Pruebas y Validación')
  console.log('   - Pruebas con muestra pequeña')
  console.log('   - Validación de calidad de datos extraídos')
  console.log('   - Monitoreo de rendimiento y errores')
  
  console.log('\n🚀 FASE 4: Implementación Gradual')
  console.log('   - Despliegue en ambiente de pruebas')
  console.log('   - Scraping programado (ej: semanal)')
  console.log('   - Integración con sistema de procedimientos')
  
  console.log('\n📊 MÉTRICAS DE ÉXITO:')
  console.log('   - % de procedimientos con descripciones enriquecidas')
  console.log('   - Tiempo de respuesta del scraper')
  console.log('   - Tasa de errores < 5%')
  console.log('   - Satisfacción de usuarios con información mejorada')
}

// Ejecutar análisis
if (require.main === module) {
  analyzeSuitScraping()
    .then(() => {
      console.log('\n🎉 Análisis completado')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Error fatal:', error)
      process.exit(1)
    })
}

export { analyzeSuitScraping }
