'use client'

/**
 * Mobile optimization utilities
 * Includes responsive design helpers, touch interactions, and mobile-specific optimizations
 */

import React, { useEffect, useState, useCallback } from 'react'

// Breakpoint definitions
export const BREAKPOINTS = {
  xs: 0,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536
} as const

type Breakpoint = keyof typeof BREAKPOINTS

// Device detection
export function useDeviceDetection() {
  const [deviceInfo, setDeviceInfo] = useState({
    isMobile: false,
    isTablet: false,
    isDesktop: false,
    isTouchDevice: false,
    orientation: 'portrait' as 'portrait' | 'landscape',
    screenSize: 'md' as Breakpoint
  })

  useEffect(() => {
    const updateDeviceInfo = () => {
      const width = window.innerWidth
      const height = window.innerHeight
      const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0

      // Determine screen size
      let screenSize: Breakpoint = 'xs'
      for (const [breakpoint, minWidth] of Object.entries(BREAKPOINTS).reverse()) {
        if (width >= minWidth) {
          screenSize = breakpoint as Breakpoint
          break
        }
      }

      // Device type detection
      const isMobile = width < BREAKPOINTS.md
      const isTablet = width >= BREAKPOINTS.md && width < BREAKPOINTS.lg && isTouchDevice
      const isDesktop = width >= BREAKPOINTS.lg

      // Orientation
      const orientation = width > height ? 'landscape' : 'portrait'

      setDeviceInfo({
        isMobile,
        isTablet,
        isDesktop,
        isTouchDevice,
        orientation,
        screenSize
      })
    }

    updateDeviceInfo()
    window.addEventListener('resize', updateDeviceInfo)
    window.addEventListener('orientationchange', updateDeviceInfo)

    return () => {
      window.removeEventListener('resize', updateDeviceInfo)
      window.removeEventListener('orientationchange', updateDeviceInfo)
    }
  }, [])

  return deviceInfo
}

// Responsive value hook
export function useResponsiveValue<T>(values: Partial<Record<Breakpoint, T>>, defaultValue: T): T {
  const { screenSize } = useDeviceDetection()
  
  // Find the appropriate value for current screen size
  const breakpointOrder: Breakpoint[] = ['2xl', 'xl', 'lg', 'md', 'sm', 'xs']
  const currentIndex = breakpointOrder.indexOf(screenSize)
  
  for (let i = currentIndex; i < breakpointOrder.length; i++) {
    const breakpoint = breakpointOrder[i]
    if (values[breakpoint] !== undefined) {
      return values[breakpoint]!
    }
  }
  
  return defaultValue
}

// Touch gesture detection
export function useTouchGestures(elementRef: React.RefObject<HTMLElement>) {
  const [gestures, setGestures] = useState({
    isSwipeLeft: false,
    isSwipeRight: false,
    isSwipeUp: false,
    isSwipeDown: false,
    isPinching: false,
    scale: 1
  })

  useEffect(() => {
    const element = elementRef.current
    if (!element) return

    let startX = 0
    let startY = 0
    let startDistance = 0

    const handleTouchStart = (e: TouchEvent) => {
      if (e.touches.length === 1) {
        startX = e.touches[0].clientX
        startY = e.touches[0].clientY
      } else if (e.touches.length === 2) {
        const touch1 = e.touches[0]
        const touch2 = e.touches[1]
        startDistance = Math.sqrt(
          Math.pow(touch2.clientX - touch1.clientX, 2) +
          Math.pow(touch2.clientY - touch1.clientY, 2)
        )
      }
    }

    const handleTouchMove = (e: TouchEvent) => {
      if (e.touches.length === 2) {
        const touch1 = e.touches[0]
        const touch2 = e.touches[1]
        const currentDistance = Math.sqrt(
          Math.pow(touch2.clientX - touch1.clientX, 2) +
          Math.pow(touch2.clientY - touch1.clientY, 2)
        )
        
        const scale = currentDistance / startDistance
        setGestures(prev => ({ ...prev, isPinching: true, scale }))
      }
    }

    const handleTouchEnd = (e: TouchEvent) => {
      if (e.changedTouches.length === 1) {
        const endX = e.changedTouches[0].clientX
        const endY = e.changedTouches[0].clientY
        const deltaX = endX - startX
        const deltaY = endY - startY
        const minSwipeDistance = 50

        if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > minSwipeDistance) {
          if (deltaX > 0) {
            setGestures(prev => ({ ...prev, isSwipeRight: true }))
          } else {
            setGestures(prev => ({ ...prev, isSwipeLeft: true }))
          }
        } else if (Math.abs(deltaY) > minSwipeDistance) {
          if (deltaY > 0) {
            setGestures(prev => ({ ...prev, isSwipeDown: true }))
          } else {
            setGestures(prev => ({ ...prev, isSwipeUp: true }))
          }
        }

        // Reset gestures after a short delay
        setTimeout(() => {
          setGestures({
            isSwipeLeft: false,
            isSwipeRight: false,
            isSwipeUp: false,
            isSwipeDown: false,
            isPinching: false,
            scale: 1
          })
        }, 100)
      }
    }

    element.addEventListener('touchstart', handleTouchStart, { passive: true })
    element.addEventListener('touchmove', handleTouchMove, { passive: true })
    element.addEventListener('touchend', handleTouchEnd, { passive: true })

    return () => {
      element.removeEventListener('touchstart', handleTouchStart)
      element.removeEventListener('touchmove', handleTouchMove)
      element.removeEventListener('touchend', handleTouchEnd)
    }
  }, [elementRef])

  return gestures
}

// Viewport height fix for mobile browsers
export function useViewportHeight() {
  const [vh, setVh] = useState(0)

  useEffect(() => {
    const updateVh = () => {
      const vh = window.innerHeight * 0.01
      setVh(vh)
      document.documentElement.style.setProperty('--vh', `${vh}px`)
    }

    updateVh()
    window.addEventListener('resize', updateVh)
    window.addEventListener('orientationchange', updateVh)

    return () => {
      window.removeEventListener('resize', updateVh)
      window.removeEventListener('orientationchange', updateVh)
    }
  }, [])

  return vh
}

// Safe area insets for devices with notches
export function useSafeAreaInsets() {
  const [insets, setInsets] = useState({
    top: 0,
    right: 0,
    bottom: 0,
    left: 0
  })

  useEffect(() => {
    const updateInsets = () => {
      const computedStyle = getComputedStyle(document.documentElement)
      
      setInsets({
        top: parseInt(computedStyle.getPropertyValue('env(safe-area-inset-top)') || '0'),
        right: parseInt(computedStyle.getPropertyValue('env(safe-area-inset-right)') || '0'),
        bottom: parseInt(computedStyle.getPropertyValue('env(safe-area-inset-bottom)') || '0'),
        left: parseInt(computedStyle.getPropertyValue('env(safe-area-inset-left)') || '0')
      })
    }

    updateInsets()
    window.addEventListener('orientationchange', updateInsets)

    return () => {
      window.removeEventListener('orientationchange', updateInsets)
    }
  }, [])

  return insets
}

// Mobile-optimized scroll behavior
export function useMobileScroll(options: {
  threshold?: number
  onScrollUp?: () => void
  onScrollDown?: () => void
} = {}) {
  const { threshold = 10, onScrollUp, onScrollDown } = options
  const [scrollDirection, setScrollDirection] = useState<'up' | 'down' | null>(null)
  const [scrollY, setScrollY] = useState(0)

  useEffect(() => {
    let lastScrollY = window.scrollY
    let ticking = false

    const updateScrollDirection = () => {
      const currentScrollY = window.scrollY
      const difference = Math.abs(currentScrollY - lastScrollY)

      if (difference > threshold) {
        const direction = currentScrollY > lastScrollY ? 'down' : 'up'
        setScrollDirection(direction)
        setScrollY(currentScrollY)

        if (direction === 'up' && onScrollUp) {
          onScrollUp()
        } else if (direction === 'down' && onScrollDown) {
          onScrollDown()
        }

        lastScrollY = currentScrollY
      }
      ticking = false
    }

    const onScroll = () => {
      if (!ticking) {
        requestAnimationFrame(updateScrollDirection)
        ticking = true
      }
    }

    window.addEventListener('scroll', onScroll, { passive: true })
    return () => window.removeEventListener('scroll', onScroll)
  }, [threshold, onScrollUp, onScrollDown])

  return { scrollDirection, scrollY }
}

// Performance optimization for mobile
export function useMobilePerformance() {
  const [isLowEndDevice, setIsLowEndDevice] = useState(false)

  useEffect(() => {
    // Detect low-end devices
    const checkDeviceCapabilities = () => {
      const memory = (navigator as any).deviceMemory
      const cores = navigator.hardwareConcurrency
      const connection = (navigator as any).connection

      let isLowEnd = false

      // Memory check (less than 4GB)
      if (memory && memory < 4) {
        isLowEnd = true
      }

      // CPU cores check (less than 4 cores)
      if (cores && cores < 4) {
        isLowEnd = true
      }

      // Network check (slow connection)
      if (connection && (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g')) {
        isLowEnd = true
      }

      setIsLowEndDevice(isLowEnd)
    }

    checkDeviceCapabilities()
  }, [])

  return { isLowEndDevice }
}

// Mobile-friendly component wrapper
export function MobileOptimized({ 
  children, 
  fallback,
  enableTouchOptimizations = true 
}: {
  children: React.ReactNode
  fallback?: React.ReactNode
  enableTouchOptimizations?: boolean
}) {
  const { isMobile, isTouchDevice } = useDeviceDetection()
  const { isLowEndDevice } = useMobilePerformance()

  useViewportHeight()

  // Apply mobile-specific optimizations
  useEffect(() => {
    if (isMobile && enableTouchOptimizations) {
      // Disable hover effects on touch devices
      document.body.classList.add('touch-device')
      
      // Optimize scrolling
      document.body.style.webkitOverflowScrolling = 'touch'
      
      // Prevent zoom on input focus
      const viewport = document.querySelector('meta[name=viewport]')
      if (viewport) {
        viewport.setAttribute('content', 
          'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no'
        )
      }
    }

    return () => {
      if (isMobile) {
        document.body.classList.remove('touch-device')
      }
    }
  }, [isMobile, enableTouchOptimizations])

  // Show fallback for low-end devices if provided
  if (isLowEndDevice && fallback) {
    return <>{fallback}</>
  }

  return <>{children}</>
}

// Responsive grid utility
export function getResponsiveGridCols(screenSize: Breakpoint): number {
  const gridCols = {
    xs: 1,
    sm: 2,
    md: 2,
    lg: 3,
    xl: 4,
    '2xl': 4
  }
  
  return gridCols[screenSize] || 1
}

// Mobile-optimized image loading
export function getMobileImageSizes(screenSize: Breakpoint): string {
  const sizes = {
    xs: '100vw',
    sm: '50vw',
    md: '33vw',
    lg: '25vw',
    xl: '20vw',
    '2xl': '16vw'
  }
  
  return sizes[screenSize] || '100vw'
}
