'use client'

import React, { Component, ErrorInfo, ReactNode } from 'react'
import { <PERSON>ert<PERSON><PERSON>gle, RefreshCw, Home, MessageCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { PerformanceMonitor } from '@/lib/performance'
import { announceToScreenReader } from '@/lib/accessibility'

interface Props {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: ErrorInfo) => void
  showDetails?: boolean
  level?: 'page' | 'component' | 'critical'
}

interface State {
  hasError: boolean
  error: Error | null
  errorInfo: ErrorInfo | null
  errorId: string
}

export class ErrorBoundary extends Component<Props, State> {
  private retryCount = 0
  private maxRetries = 3

  constructor(props: Props) {
    super(props)

    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: ''
    }
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
      errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log error for monitoring
    this.logError(error, errorInfo)

    // Update state with error info
    this.setState({
      error,
      errorInfo
    })

    // Call custom error handler
    this.props.onError?.(error, errorInfo)

    // Announce error to screen readers
    announceToScreenReader(
      'Ha ocurrido un error en la aplicación. Por favor, intenta recargar la página.',
      'assertive'
    )

    // Track performance impact
    PerformanceMonitor.mark('error-boundary-triggered')
  }

  private logError = (error: Error, errorInfo: ErrorInfo) => {
    const errorData = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      errorId: this.state.errorId,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      level: this.props.level || 'component'
    }

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Error Boundary caught an error:', errorData)
    }

    // In production, send to monitoring service
    if (process.env.NODE_ENV === 'production') {
      // Send to error monitoring service (Sentry, LogRocket, etc.)
      this.sendErrorToMonitoring(errorData)
    }
  }

  private sendErrorToMonitoring = async (errorData: any) => {
    try {
      // Example: Send to monitoring endpoint
      await fetch('/api/errors', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(errorData),
      })
    } catch (err) {
      console.error('Failed to send error to monitoring:', err)
    }
  }

  private handleRetry = () => {
    if (this.retryCount < this.maxRetries) {
      this.retryCount++
      this.setState({
        hasError: false,
        error: null,
        errorInfo: null,
        errorId: ''
      })

      // Track retry attempt
      PerformanceMonitor.mark('error-boundary-retry')
      
      // Announce retry to screen readers
      announceToScreenReader('Reintentando cargar el contenido...')
    } else {
      // Max retries reached, suggest page reload
      announceToScreenReader(
        'Se ha alcanzado el máximo de reintentos. Por favor, recarga la página.',
        'assertive'
      )
    }
  }

  private handleReload = () => {
    window.location.reload()
  }

  private handleGoHome = () => {
    window.location.href = '/'
  }

  private handleContactSupport = () => {
    // Open support chat or contact form
    window.location.href = '/chat'
  }

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback
      }

      // Default error UI based on level
      return this.renderErrorUI()
    }

    return this.props.children
  }

  private renderErrorUI() {
    const { level = 'component', showDetails = false } = this.props
    const { error, errorInfo, errorId } = this.state

    // Critical errors get full page treatment
    if (level === 'critical' || level === 'page') {
      return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
          <Card className="w-full max-w-lg">
            <CardHeader className="text-center">
              <div className="mx-auto w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-4">
                <AlertTriangle className="w-6 h-6 text-red-600" />
              </div>
              <CardTitle className="text-xl text-gray-900">
                Algo salió mal
              </CardTitle>
              <CardDescription>
                Ha ocurrido un error inesperado. Nuestro equipo ha sido notificado automáticamente.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-sm text-gray-600 bg-gray-100 p-3 rounded-md">
                <strong>ID del Error:</strong> {errorId}
              </div>

              <div className="flex flex-col sm:flex-row gap-2">
                {this.retryCount < this.maxRetries ? (
                  <Button
                    onClick={this.handleRetry}
                    className="flex-1"
                    variant="default"
                  >
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Reintentar ({this.maxRetries - this.retryCount} intentos restantes)
                  </Button>
                ) : (
                  <Button
                    onClick={this.handleReload}
                    className="flex-1"
                    variant="default"
                  >
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Recargar página
                  </Button>
                )}
                
                <Button
                  onClick={this.handleGoHome}
                  variant="outline"
                  className="flex-1"
                >
                  <Home className="w-4 h-4 mr-2" />
                  Ir al inicio
                </Button>
              </div>

              <Button
                onClick={this.handleContactSupport}
                variant="ghost"
                className="w-full"
              >
                <MessageCircle className="w-4 h-4 mr-2" />
                Contactar soporte
              </Button>

              {showDetails && process.env.NODE_ENV === 'development' && (
                <details className="mt-4">
                  <summary className="cursor-pointer text-sm font-medium text-gray-700 mb-2">
                    Detalles técnicos
                  </summary>
                  <div className="text-xs bg-gray-100 p-3 rounded-md overflow-auto max-h-40">
                    <div className="mb-2">
                      <strong>Error:</strong> {error?.message}
                    </div>
                    <div className="mb-2">
                      <strong>Stack:</strong>
                      <pre className="whitespace-pre-wrap mt-1">{error?.stack}</pre>
                    </div>
                    {errorInfo && (
                      <div>
                        <strong>Component Stack:</strong>
                        <pre className="whitespace-pre-wrap mt-1">{errorInfo.componentStack}</pre>
                      </div>
                    )}
                  </div>
                </details>
              )}
            </CardContent>
          </Card>
        </div>
      )
    }

    // Component-level errors get inline treatment
    return (
      <div className="border border-red-200 bg-red-50 rounded-lg p-4 my-4">
        <div className="flex items-start">
          <AlertTriangle className="w-5 h-5 text-red-600 mt-0.5 mr-3 flex-shrink-0" />
          <div className="flex-1">
            <h3 className="text-sm font-medium text-red-800 mb-1">
              Error en el componente
            </h3>
            <p className="text-sm text-red-700 mb-3">
              Este componente no se pudo cargar correctamente.
            </p>
            <div className="flex gap-2">
              {this.retryCount < this.maxRetries ? (
                <Button
                  onClick={this.handleRetry}
                  size="sm"
                  variant="outline"
                  className="border-red-300 text-red-700 hover:bg-red-100"
                >
                  <RefreshCw className="w-3 h-3 mr-1" />
                  Reintentar
                </Button>
              ) : (
                <Button
                  onClick={this.handleReload}
                  size="sm"
                  variant="outline"
                  className="border-red-300 text-red-700 hover:bg-red-100"
                >
                  <RefreshCw className="w-3 h-3 mr-1" />
                  Recargar
                </Button>
              )}
            </div>
            
            {showDetails && process.env.NODE_ENV === 'development' && (
              <details className="mt-3">
                <summary className="cursor-pointer text-xs text-red-600">
                  Ver detalles
                </summary>
                <div className="text-xs text-red-600 mt-1 font-mono">
                  {error?.message}
                </div>
              </details>
            )}
          </div>
        </div>
      </div>
    )
  }
}

// HOC for wrapping components with error boundary
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<Props, 'children'>
) {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  )

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`
  
  return WrappedComponent
}

// Hook for error reporting
export function useErrorHandler() {
  const reportError = (error: Error, context?: string) => {
    // Create a synthetic error boundary trigger
    throw new Error(`${context ? `[${context}] ` : ''}${error.message}`)
  }

  return { reportError }
}
