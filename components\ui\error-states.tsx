"use client"

import * as React from "react"
import { 
  AlertCircle, 
  RefreshCw, 
  Search, 
  Building2, 
  FileText, 
  Wifi,
  Server,
  Clock,
  Shield,
  Home
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { cn } from "@/lib/utils"

// Tipos de errores
export type ErrorType = 
  | "network" 
  | "server" 
  | "timeout" 
  | "permission" 
  | "not-found" 
  | "search" 
  | "validation" 
  | "general"

// Configuración de errores
const errorConfig: Record<ErrorType, {
  icon: React.ComponentType<{ className?: string }>
  title: string
  description: string
  color: string
  bgColor: string
  suggestions: string[]
}> = {
  network: {
    icon: Wifi,
    title: "Error de Conexión",
    description: "No se pudo conectar con el servidor. Verifica tu conexión a internet.",
    color: "text-red-600",
    bgColor: "bg-red-50",
    suggestions: [
      "Verifica tu conexión a internet",
      "Intenta recargar la página",
      "Contacta al soporte técnico si el problema persiste"
    ]
  },
  server: {
    icon: Server,
    title: "Error del Servidor",
    description: "Ocurrió un problema en nuestros servidores. Estamos trabajando para solucionarlo.",
    color: "text-orange-600",
    bgColor: "bg-orange-50",
    suggestions: [
      "Intenta nuevamente en unos minutos",
      "El problema es temporal y se resolverá pronto",
      "Contacta al soporte si el error persiste"
    ]
  },
  timeout: {
    icon: Clock,
    title: "Tiempo de Espera Agotado",
    description: "La operación tardó más de lo esperado en completarse.",
    color: "text-yellow-600",
    bgColor: "bg-yellow-50",
    suggestions: [
      "Intenta nuevamente",
      "Verifica tu conexión a internet",
      "El servidor puede estar experimentando alta demanda"
    ]
  },
  permission: {
    icon: Shield,
    title: "Acceso Denegado",
    description: "No tienes permisos para acceder a esta información.",
    color: "text-purple-600",
    bgColor: "bg-purple-50",
    suggestions: [
      "Verifica que hayas iniciado sesión",
      "Contacta al administrador para obtener permisos",
      "Algunos contenidos requieren autorización especial"
    ]
  },
  "not-found": {
    icon: Search,
    title: "Contenido No Encontrado",
    description: "La información solicitada no existe o ha sido movida.",
    color: "text-primary",
    bgColor: "bg-primary/5",
    suggestions: [
      "Verifica que la información sea correcta",
      "Usa el buscador para encontrar contenido similar",
      "Regresa a la página principal"
    ]
  },
  search: {
    icon: Search,
    title: "Sin Resultados",
    description: "No se encontraron resultados para tu búsqueda.",
    color: "text-gray-600",
    bgColor: "bg-gray-50",
    suggestions: [
      "Intenta con términos más generales",
      "Verifica la ortografía",
      "Usa filtros diferentes"
    ]
  },
  validation: {
    icon: AlertCircle,
    title: "Datos Inválidos",
    description: "Los datos proporcionados no son válidos.",
    color: "text-red-600",
    bgColor: "bg-red-50",
    suggestions: [
      "Verifica que todos los campos estén completos",
      "Revisa el formato de los datos ingresados",
      "Consulta los requisitos específicos"
    ]
  },
  general: {
    icon: AlertCircle,
    title: "Error Inesperado",
    description: "Ocurrió un error inesperado. Por favor intenta nuevamente.",
    color: "text-gray-600",
    bgColor: "bg-gray-50",
    suggestions: [
      "Intenta recargar la página",
      "Verifica tu conexión",
      "Contacta al soporte técnico"
    ]
  }
}

// Componente principal de error
interface ErrorStateProps {
  type?: ErrorType
  title?: string
  description?: string
  error?: Error | string
  onRetry?: () => void
  onGoHome?: () => void
  showDetails?: boolean
  className?: string
  size?: "sm" | "md" | "lg"
}

export function ErrorState({
  type = "general",
  title,
  description,
  error,
  onRetry,
  onGoHome,
  showDetails = false,
  className,
  size = "md"
}: ErrorStateProps) {
  const config = errorConfig[type]
  const IconComponent = config.icon

  const sizeClasses = {
    sm: "p-4",
    md: "p-6",
    lg: "p-8"
  }

  const iconSizes = {
    sm: "h-8 w-8",
    md: "h-12 w-12",
    lg: "h-16 w-16"
  }

  return (
    <Card className={cn("border-0 shadow-none", config.bgColor, className)}>
      <CardContent className={sizeClasses[size]}>
        <div className="flex flex-col items-center text-center space-y-4">
          <div className={cn(
            "rounded-full p-3",
            config.bgColor,
            "ring-8 ring-white"
          )}>
            <IconComponent className={cn(iconSizes[size], config.color)} />
          </div>
          
          <div className="space-y-2">
            <CardTitle className={cn("text-lg font-semibold", config.color)}>
              {title || config.title}
            </CardTitle>
            <CardDescription className="text-gray-600 max-w-md">
              {description || config.description}
            </CardDescription>
          </div>

          {/* Sugerencias */}
          <div className="space-y-2 text-sm text-gray-600">
            <p className="font-medium">Sugerencias:</p>
            <ul className="space-y-1 text-left">
              {config.suggestions.map((suggestion, index) => (
                <li key={index} className="flex items-start space-x-2">
                  <span className="text-primary mt-1">•</span>
                  <span>{suggestion}</span>
                </li>
              ))}
            </ul>
          </div>

          {/* Botones de acción */}
          <div className="flex flex-col sm:flex-row gap-3 pt-2">
            {onRetry && (
              <Button 
                onClick={onRetry}
                variant="default"
                className="bg-primary hover:bg-primary/90"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Intentar Nuevamente
              </Button>
            )}
            {onGoHome && (
              <Button 
                onClick={onGoHome}
                variant="outline"
                className="border-primary text-primary hover:bg-primary/5"
              >
                <Home className="h-4 w-4 mr-2" />
                Ir al Inicio
              </Button>
            )}
          </div>

          {/* Detalles técnicos del error */}
          {showDetails && error && (
            <details className="w-full mt-4">
              <summary className="cursor-pointer text-sm text-gray-500 hover:text-gray-700">
                Detalles técnicos
              </summary>
              <div className="mt-2 p-3 bg-gray-100 rounded-lg text-xs text-gray-700 font-mono text-left overflow-auto">
                {typeof error === 'string' ? error : error.message}
                {typeof error !== 'string' && error.stack && (
                  <pre className="mt-2 text-xs">{error.stack}</pre>
                )}
              </div>
            </details>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

// Error específico para búsqueda sin resultados
interface SearchEmptyStateProps {
  query: string
  onClearSearch?: () => void
  onGoHome?: () => void
  suggestions?: string[]
  className?: string
}

export function SearchEmptyState({
  query,
  onClearSearch,
  onGoHome,
  suggestions = [
    "Intenta con términos más generales",
    "Verifica la ortografía",
    "Usa palabras clave diferentes"
  ],
  className
}: SearchEmptyStateProps) {
  return (
    <div className={cn("text-center py-12", className)}>
      <Search className="h-16 w-16 text-gray-400 mx-auto mb-4" />
      <h3 className="text-lg font-semibold text-gray-900 mb-2">
        Sin resultados para "{query}"
      </h3>
      <p className="text-gray-600 mb-6 max-w-md mx-auto">
        No encontramos dependencias, trámites o servicios que coincidan con tu búsqueda.
      </p>
      
      <div className="space-y-4">
        <div className="text-sm text-gray-600">
          <p className="font-medium mb-2">Sugerencias:</p>
          <ul className="space-y-1">
            {suggestions.map((suggestion, index) => (
              <li key={index}>• {suggestion}</li>
            ))}
          </ul>
        </div>

        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          {onClearSearch && (
            <Button 
              onClick={onClearSearch}
              variant="default"
              className="bg-primary hover:bg-primary/90"
            >
              Limpiar Búsqueda
            </Button>
          )}
          {onGoHome && (
            <Button 
              onClick={onGoHome}
              variant="outline"
              className="border-primary text-primary hover:bg-primary/5"
            >
              <Home className="h-4 w-4 mr-2" />
              Explorar Dependencias
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}

// Error inline para componentes pequeños
interface InlineErrorProps {
  message: string
  onRetry?: () => void
  className?: string
}

export function InlineError({ message, onRetry, className }: InlineErrorProps) {
  return (
    <div className={cn(
      "flex items-center space-x-2 text-sm text-red-600 bg-red-50 p-2 rounded-lg",
      className
    )}>
      <AlertCircle className="h-4 w-4 flex-shrink-0" />
      <span className="flex-1">{message}</span>
      {onRetry && (
        <Button 
          onClick={onRetry}
          variant="ghost"
          size="sm"
          className="h-6 px-2 text-red-600 hover:text-red-700 hover:bg-red-100"
        >
          <RefreshCw className="h-3 w-3" />
        </Button>
      )}
    </div>
  )
}
