import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { ProtectedNavigation } from '@/components/navigation/ProtectedNavigation'
import { useAuth } from '@/hooks/useAuth'
import { useRole } from '@/hooks/useRole'
import { useRouter } from 'next/navigation'

// Mock dependencies
jest.mock('@/hooks/useAuth')
jest.mock('@/hooks/useRole')
jest.mock('next/navigation')

const mockUseAuth = useAuth as jest.MockedFunction<typeof useAuth>
const mockUseRole = useRole as jest.MockedFunction<typeof useRole>
const mockUseRouter = useRouter as jest.MockedFunction<typeof useRouter>

const mockRouter = {
  push: jest.fn(),
  replace: jest.fn(),
  refresh: jest.fn(),
}

describe('ProtectedNavigation Component', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    mockUseRouter.mockReturnValue(mockRouter as any)
  })

  describe('Rendering for Different Roles', () => {
    it('should render citizen navigation items for ciudadano role', () => {
      mockUseAuth.mockReturnValue({
        user: { id: 'user-123', email: '<EMAIL>' },
        profile: { 
          id: 'user-123', 
          role: 'ciudadano',
          full_name: 'Test User'
        },
        loading: false,
        signOut: jest.fn(),
      } as any)

      mockUseRole.mockReturnValue({
        role: 'ciudadano',
        isCiudadano: true,
        isAdmin: false,
        isSuperAdmin: false,
        hasRole: jest.fn((roles) => roles.includes('ciudadano')),
      })

      render(<ProtectedNavigation />)

      // Should show citizen-specific items
      expect(screen.getByText('Dashboard')).toBeInTheDocument()
      expect(screen.getByText('Mis Trámites')).toBeInTheDocument()
      expect(screen.getByText('Chat IA')).toBeInTheDocument()
      
      // Should not show admin items
      expect(screen.queryByText('Administración')).not.toBeInTheDocument()
    })

    it('should render admin navigation items for admin role', () => {
      mockUseAuth.mockReturnValue({
        user: { id: 'admin-123', email: '<EMAIL>' },
        profile: { 
          id: 'admin-123', 
          role: 'admin',
          full_name: 'Admin User'
        },
        loading: false,
        signOut: jest.fn(),
      } as any)

      mockUseRole.mockReturnValue({
        role: 'admin',
        isCiudadano: false,
        isAdmin: true,
        isSuperAdmin: false,
        hasRole: jest.fn((roles) => roles.includes('admin') || roles.includes('ciudadano')),
      })

      render(<ProtectedNavigation />)

      // Should show admin items
      expect(screen.getByText('Dashboard')).toBeInTheDocument()
      expect(screen.getByText('Administración')).toBeInTheDocument()
      expect(screen.getByText('Chat IA')).toBeInTheDocument()
      
      // Should not show citizen-specific items
      expect(screen.queryByText('Mis Trámites')).not.toBeInTheDocument()
    })

    it('should render all navigation items for super_admin role', () => {
      mockUseAuth.mockReturnValue({
        user: { id: 'super-123', email: '<EMAIL>' },
        profile: { 
          id: 'super-123', 
          role: 'super_admin',
          full_name: 'Super Admin'
        },
        loading: false,
        signOut: jest.fn(),
      } as any)

      mockUseRole.mockReturnValue({
        role: 'super_admin',
        isCiudadano: false,
        isAdmin: false,
        isSuperAdmin: true,
        hasRole: jest.fn(() => true), // Super admin has access to everything
      })

      render(<ProtectedNavigation />)

      // Should show all items
      expect(screen.getByText('Dashboard')).toBeInTheDocument()
      expect(screen.getByText('Administración')).toBeInTheDocument()
      expect(screen.getByText('Chat IA')).toBeInTheDocument()
    })
  })

  describe('User Profile Display', () => {
    it('should display user profile information', () => {
      mockUseAuth.mockReturnValue({
        user: { id: 'user-123', email: '<EMAIL>' },
        profile: { 
          id: 'user-123', 
          role: 'ciudadano',
          full_name: 'Test User'
        },
        loading: false,
        signOut: jest.fn(),
      } as any)

      mockUseRole.mockReturnValue({
        role: 'ciudadano',
        isCiudadano: true,
        isAdmin: false,
        isSuperAdmin: false,
        hasRole: jest.fn((roles) => roles.includes('ciudadano')),
      })

      render(<ProtectedNavigation />)

      // Should display user name
      expect(screen.getByText('Test User')).toBeInTheDocument()
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
    })

    it('should display user avatar with first letter of name', () => {
      mockUseAuth.mockReturnValue({
        user: { id: 'user-123', email: '<EMAIL>' },
        profile: { 
          id: 'user-123', 
          role: 'ciudadano',
          full_name: 'Test User'
        },
        loading: false,
        signOut: jest.fn(),
      } as any)

      mockUseRole.mockReturnValue({
        role: 'ciudadano',
        isCiudadano: true,
        isAdmin: false,
        isSuperAdmin: false,
        hasRole: jest.fn((roles) => roles.includes('ciudadano')),
      })

      render(<ProtectedNavigation />)

      // Should display first letter of name in avatar
      expect(screen.getByText('T')).toBeInTheDocument()
    })
  })

  describe('User Actions', () => {
    it('should handle logout when signout is clicked', async () => {
      const mockSignOut = jest.fn()
      
      mockUseAuth.mockReturnValue({
        user: { id: 'user-123', email: '<EMAIL>' },
        profile: { 
          id: 'user-123', 
          role: 'ciudadano',
          full_name: 'Test User'
        },
        loading: false,
        signOut: mockSignOut,
      } as any)

      mockUseRole.mockReturnValue({
        role: 'ciudadano',
        isCiudadano: true,
        isAdmin: false,
        isSuperAdmin: false,
        hasRole: jest.fn((roles) => roles.includes('ciudadano')),
      })

      render(<ProtectedNavigation />)

      // Click on user profile to open dropdown
      const userButton = screen.getByRole('button', { name: /test user/i })
      fireEvent.click(userButton)

      // Click logout
      const logoutButton = screen.getByText('Cerrar Sesión')
      fireEvent.click(logoutButton)

      await waitFor(() => {
        expect(mockSignOut).toHaveBeenCalled()
      })
    })

    it('should navigate to profile page when profile link is clicked', async () => {
      mockUseAuth.mockReturnValue({
        user: { id: 'user-123', email: '<EMAIL>' },
        profile: { 
          id: 'user-123', 
          role: 'ciudadano',
          full_name: 'Test User'
        },
        loading: false,
        signOut: jest.fn(),
      } as any)

      mockUseRole.mockReturnValue({
        role: 'ciudadano',
        isCiudadano: true,
        isAdmin: false,
        isSuperAdmin: false,
        hasRole: jest.fn((roles) => roles.includes('ciudadano')),
      })

      render(<ProtectedNavigation />)

      // Click on user profile to open dropdown
      const userButton = screen.getByRole('button', { name: /test user/i })
      fireEvent.click(userButton)

      // Click profile link
      const profileLink = screen.getByText('Mi Perfil')
      fireEvent.click(profileLink)

      expect(mockRouter.push).toHaveBeenCalledWith('/perfil')
    })
  })

  describe('Mobile Navigation', () => {
    it('should toggle mobile menu when hamburger is clicked', () => {
      mockUseAuth.mockReturnValue({
        user: { id: 'user-123', email: '<EMAIL>' },
        profile: { 
          id: 'user-123', 
          role: 'ciudadano',
          full_name: 'Test User'
        },
        loading: false,
        signOut: jest.fn(),
      } as any)

      mockUseRole.mockReturnValue({
        role: 'ciudadano',
        isCiudadano: true,
        isAdmin: false,
        isSuperAdmin: false,
        hasRole: jest.fn((roles) => roles.includes('ciudadano')),
      })

      render(<ProtectedNavigation />)

      // Find and click mobile menu button
      const mobileMenuButton = screen.getByRole('button', { name: /toggle menu/i })
      fireEvent.click(mobileMenuButton)

      // Mobile menu should be visible
      expect(screen.getByTestId('mobile-menu')).toBeInTheDocument()
    })
  })

  describe('Loading State', () => {
    it('should show loading state when auth is loading', () => {
      mockUseAuth.mockReturnValue({
        user: null,
        profile: null,
        loading: true,
        signOut: jest.fn(),
      } as any)

      mockUseRole.mockReturnValue({
        role: null,
        isCiudadano: false,
        isAdmin: false,
        isSuperAdmin: false,
        hasRole: jest.fn(() => false),
      })

      render(<ProtectedNavigation />)

      // Should show loading indicator
      expect(screen.getByTestId('navigation-loading')).toBeInTheDocument()
    })
  })

  describe('Notifications', () => {
    it('should display notification badge when there are unread notifications', () => {
      mockUseAuth.mockReturnValue({
        user: { id: 'user-123', email: '<EMAIL>' },
        profile: { 
          id: 'user-123', 
          role: 'ciudadano',
          full_name: 'Test User',
          unread_notifications: 3
        },
        loading: false,
        signOut: jest.fn(),
      } as any)

      mockUseRole.mockReturnValue({
        role: 'ciudadano',
        isCiudadano: true,
        isAdmin: false,
        isSuperAdmin: false,
        hasRole: jest.fn((roles) => roles.includes('ciudadano')),
      })

      render(<ProtectedNavigation />)

      // Should show notification badge
      expect(screen.getByText('3')).toBeInTheDocument()
    })
  })
})
