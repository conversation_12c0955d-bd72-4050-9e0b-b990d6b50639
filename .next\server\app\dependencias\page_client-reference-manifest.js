globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/dependencias/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./app/page.tsx":{"*":{"id":"(ssr)/./app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/error/ErrorBoundary.tsx":{"*":{"id":"(ssr)/./components/error/ErrorBoundary.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./lib/accessibility.tsx":{"*":{"id":"(ssr)/./lib/accessibility.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./lib/mobile.tsx":{"*":{"id":"(ssr)/./lib/mobile.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/faq/ContextualFAQSection.tsx":{"*":{"id":"(ssr)/./components/faq/ContextualFAQSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/layout/PublicLayout.tsx":{"*":{"id":"(ssr)/./components/layout/PublicLayout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/procedures/PublicProcedureSearchInterface.tsx":{"*":{"id":"(ssr)/./components/procedures/PublicProcedureSearchInterface.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/dependencias/page.tsx":{"*":{"id":"(ssr)/./app/dependencias/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-tramites\\app\\page.tsx":{"id":"(app-pages-browser)/./app/page.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-tramites\\components\\error\\ErrorBoundary.tsx":{"id":"(app-pages-browser)/./components/error/ErrorBoundary.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-tramites\\lib\\accessibility.tsx":{"id":"(app-pages-browser)/./lib/accessibility.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-tramites\\lib\\mobile.tsx":{"id":"(app-pages-browser)/./lib/mobile.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-tramites\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"preload\":true,\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"preload\":true,\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-tramites\\app\\globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-tramites\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-tramites\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-tramites\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-tramites\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-tramites\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-tramites\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-tramites\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-tramites\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-tramites\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-tramites\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-tramites\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-tramites\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-tramites\\components\\faq\\ContextualFAQSection.tsx":{"id":"(app-pages-browser)/./components/faq/ContextualFAQSection.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-tramites\\components\\layout\\PublicLayout.tsx":{"id":"(app-pages-browser)/./components/layout/PublicLayout.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-tramites\\components\\procedures\\PublicProcedureSearchInterface.tsx":{"id":"(app-pages-browser)/./components/procedures/PublicProcedureSearchInterface.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-tramites\\app\\dependencias\\page.tsx":{"id":"(app-pages-browser)/./app/dependencias/page.tsx","name":"*","chunks":["app/dependencias/page","static/chunks/app/dependencias/page.js"],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-tramites\\":[],"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-tramites\\app\\page":[],"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-tramites\\app\\layout":["static/css/app/layout.css"],"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-tramites\\app\\dependencias\\page":[]}}