import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { ProcedureCardEnhanced } from '../../../../components/procedures/ProcedureCardEnhanced'
import { SuitEnhancedProcedure, enhanceProcedureWithSuit } from '../../../../types/suit-enhanced-procedure'

const baseMockProcedure = {
  id: '1',
  name: 'Licencia de Construcción',
  description: 'Permiso para construir edificaciones residenciales y comerciales',
  cost: 150000,
  response_time: '15 días hábiles',
  procedure_type: 'TRAMITE' as const,
  has_cost: true,
  cost_description: '$150,000 COP',
  online_available: false,
  dependency: { id: 'dep1', name: 'Secretaría de Planeación', acronym: 'SEPLAN' },
  subdependency: { id: 'sub1', name: 'Oficina de Licencias' },
  requirements: [
    'Planos arquitectónicos aprobados',
    'Certificado de tradición y libertad',
    'Estudio de suelos',
    'Licencia ambiental (si aplica)',
    'Pago de derechos'
  ],
  documents_required: ['Cédula', 'Formulario'],
  suit_url: 'https://suit.gov.co/licencia-construccion',
  gov_url: 'https://gov.co/licencia-construccion'
}

// Convert to SuitEnhancedProcedure without SUIT data
const mockProcedure: SuitEnhancedProcedure = enhanceProcedureWithSuit(baseMockProcedure)

const mockOnViewDetails = jest.fn()
const mockOnToggleFavorite = jest.fn()

const defaultProps = {
  procedure: mockProcedure,
  onViewDetails: mockOnViewDetails,
  onToggleFavorite: mockOnToggleFavorite,
  isFavorite: false
}

describe('ProcedureCardEnhanced', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should render procedure information correctly', () => {
    render(<ProcedureCardEnhanced {...defaultProps} />)

    expect(screen.getByText('Licencia de Construcción')).toBeInTheDocument()
    expect(screen.getByText('Secretaría de Planeación')).toBeInTheDocument()
    expect(screen.getByText('• Oficina de Licencias')).toBeInTheDocument()
    expect(screen.getByText('$150,000 COP')).toBeInTheDocument()
    expect(screen.getByText('15 días hábiles')).toBeInTheDocument()
  })

  it('should display correct badges', () => {
    render(<ProcedureCardEnhanced {...defaultProps} />)

    expect(screen.getByText('Trámite')).toBeInTheDocument()
    expect(screen.getByText('Presencial')).toBeInTheDocument()
  })

  it('should show virtual badge for online procedures', () => {
    const onlineProcedure = {
      ...mockProcedure,
      online_available: true
    }

    render(<ProcedureCardEnhanced {...defaultProps} procedure={onlineProcedure} />)

    expect(screen.getByText('Virtual')).toBeInTheDocument()
  })

  it('should show free cost for procedures without cost', () => {
    const freeProcedure = {
      ...mockProcedure,
      has_cost: false,
      cost: 0
    }

    render(<ProcedureCardEnhanced {...defaultProps} procedure={freeProcedure} />)

    expect(screen.getByText('Gratuito')).toBeInTheDocument()
  })

  it('should display urgency indicator for immediate procedures', () => {
    const urgentProcedure = {
      ...mockProcedure,
      response_time: 'Inmediato'
    }

    render(<ProcedureCardEnhanced {...defaultProps} procedure={urgentProcedure} />)

    expect(screen.getByText('Urgente')).toBeInTheDocument()
  })

  it('should show description in detailed layout', () => {
    render(<ProcedureCardEnhanced {...defaultProps} layout="detailed" />)

    expect(screen.getByText(/Permiso para construir edificaciones/)).toBeInTheDocument()
  })

  it('should show top requirements in detailed layout', () => {
    render(<ProcedureCardEnhanced {...defaultProps} layout="detailed" />)

    expect(screen.getByText('Requisitos principales:')).toBeInTheDocument()
    expect(screen.getByText('Planos arquitectónicos aprobados')).toBeInTheDocument()
    expect(screen.getByText('Certificado de tradición y libertad')).toBeInTheDocument()
    expect(screen.getByText('Estudio de suelos')).toBeInTheDocument()
    expect(screen.getByText('+2 requisitos más')).toBeInTheDocument()
  })

  it('should handle view details click', async () => {
    const user = userEvent.setup()
    render(<ProcedureCardEnhanced {...defaultProps} />)

    const viewButton = screen.getByRole('button', { name: /ver detalles/i })
    await user.click(viewButton)

    expect(mockOnViewDetails).toHaveBeenCalledWith(mockProcedure)
  })

  it('should handle favorite toggle', async () => {
    const user = userEvent.setup()
    render(<ProcedureCardEnhanced {...defaultProps} />)

    // Hover to show favorite button
    const card = screen.getByText('Licencia de Construcción').closest('div')
    if (card) {
      fireEvent.mouseEnter(card)
    }

    // Find the favorite button by looking for the heart icon
    const favoriteButton = screen.getByRole('button', { name: '' })
    await user.click(favoriteButton)

    expect(mockOnToggleFavorite).toHaveBeenCalledWith('1')
  })

  it('should show filled heart for favorite procedures', () => {
    render(<ProcedureCardEnhanced {...defaultProps} isFavorite={true} />)

    const card = screen.getByText('Licencia de Construcción').closest('div')
    if (card) {
      fireEvent.mouseEnter(card)
    }

    // Find the favorite button (it has no accessible name)
    const favoriteButton = screen.getByRole('button', { name: '' })
    const heartIcon = favoriteButton.querySelector('svg')
    expect(heartIcon).toHaveClass('fill-red-500')
  })

  it('should handle external link clicks', async () => {
    const user = userEvent.setup()
    // Mock window.open
    const mockOpen = jest.fn()
    Object.defineProperty(window, 'open', { value: mockOpen })

    render(<ProcedureCardEnhanced {...defaultProps} />)

    const suitButton = screen.getByRole('button', { name: /suit/i })
    await user.click(suitButton)

    expect(mockOpen).toHaveBeenCalledWith('https://suit.gov.co/licencia-construccion', '_blank')

    const govButton = screen.getByRole('button', { name: /gov\.co/i })
    await user.click(govButton)

    expect(mockOpen).toHaveBeenCalledWith('https://gov.co/licencia-construccion', '_blank')
  })

  it('should not show external links if URLs are not provided', () => {
    const procedureWithoutLinks = {
      ...mockProcedure,
      suit_url: undefined,
      gov_url: undefined
    }

    render(<ProcedureCardEnhanced {...defaultProps} procedure={procedureWithoutLinks} />)

    expect(screen.queryByRole('button', { name: /suit/i })).not.toBeInTheDocument()
    expect(screen.queryByRole('button', { name: /gov\.co/i })).not.toBeInTheDocument()
  })

  it('should apply custom className', () => {
    const { container } = render(
      <ProcedureCardEnhanced {...defaultProps} className="custom-class" />
    )

    expect(container.firstChild).toHaveClass('custom-class')
  })

  it('should handle compact layout correctly', () => {
    render(<ProcedureCardEnhanced {...defaultProps} layout="compact" />)

    // Should not show description in compact mode
    expect(screen.queryByText(/Permiso para construir edificaciones/)).not.toBeInTheDocument()
    // Should not show requirements in compact mode
    expect(screen.queryByText('Requisitos principales:')).not.toBeInTheDocument()
  })

  it('should handle procedures without dependency information', () => {
    const procedureWithoutDep = {
      ...mockProcedure,
      dependency: undefined,
      subdependency: undefined
    }

    render(<ProcedureCardEnhanced {...defaultProps} procedure={procedureWithoutDep} />)

    expect(screen.queryByText('Secretaría de Planeación')).not.toBeInTheDocument()
  })

  it('should handle OPA procedure type correctly', () => {
    const opaProcedure = {
      ...mockProcedure,
      procedure_type: 'OPA' as const
    }

    render(<ProcedureCardEnhanced {...defaultProps} procedure={opaProcedure} />)

    expect(screen.getByText('OPA')).toBeInTheDocument()
  })

  it('should be accessible with proper ARIA labels', () => {
    render(<ProcedureCardEnhanced {...defaultProps} />)

    const viewButton = screen.getByRole('button', { name: /ver detalles/i })
    expect(viewButton).toBeInTheDocument()

    // Check that the card is properly structured for screen readers
    expect(screen.getByText('Licencia de Construcción')).toBeInTheDocument()
  })

  it('should handle hover effects', () => {
    const { container } = render(<ProcedureCardEnhanced {...defaultProps} />)

    const card = container.firstChild as HTMLElement
    fireEvent.mouseEnter(card)

    expect(card).toHaveClass('hover:shadow-lg')
  })

  it('should prevent event bubbling on button clicks', async () => {
    const user = userEvent.setup()
    const cardClickHandler = jest.fn()

    const { container } = render(
      <div onClick={cardClickHandler}>
        <ProcedureCardEnhanced {...defaultProps} />
      </div>
    )

    const viewButton = screen.getByRole('button', { name: /ver detalles/i })
    await user.click(viewButton)

    expect(mockOnViewDetails).toHaveBeenCalled()
    expect(cardClickHandler).not.toHaveBeenCalled()
  })

  it('should handle missing requirements gracefully', () => {
    const procedureWithoutReqs: SuitEnhancedProcedure = {
      ...mockProcedure,
      requirements: undefined,
      best_requirements: []
    }

    render(<ProcedureCardEnhanced {...defaultProps} procedure={procedureWithoutReqs} layout="detailed" showPreview={true} />)

    expect(screen.queryByText('Requisitos principales:')).not.toBeInTheDocument()
  })

  describe('SUIT Enhancement Integration', () => {
    const suitEnhancedProcedure: SuitEnhancedProcedure = {
      ...mockProcedure,
      suit_data: {
        descripcion_detallada: 'Enhanced SUIT description for construction license',
        requisitos: ['SUIT req 1', 'SUIT req 2', 'SUIT req 3'],
        pasos: ['SUIT step 1', 'SUIT step 2'],
        tiempo_respuesta: '10 días hábiles',
        costo_detallado: 'Gratuito según SUIT'
      },
      best_description: 'Enhanced SUIT description for construction license',
      best_requirements: ['SUIT req 1', 'SUIT req 2', 'SUIT req 3'],
      best_response_time: '10 días hábiles',
      best_cost_info: 'Gratuito según SUIT',
      has_suit_enhancement: true
    }

    it('should display SUIT enhancement indicators', () => {
      render(<ProcedureCardEnhanced {...defaultProps} procedure={suitEnhancedProcedure} showPreview={true} layout="detailed" />)

      // Should show enhanced description
      expect(screen.getByText('Enhanced SUIT description for construction license')).toBeInTheDocument()

      // Should show enhanced requirements
      expect(screen.getByText('SUIT req 1')).toBeInTheDocument()
    })

    it('should use enhanced information when SUIT data is available', () => {
      render(<ProcedureCardEnhanced {...defaultProps} procedure={suitEnhancedProcedure} showPreview={true} layout="detailed" />)

      // Should display SUIT enhanced description
      expect(screen.getByText('Enhanced SUIT description for construction license')).toBeInTheDocument()

      // Should display enhanced time and cost
      expect(screen.getByText('10 días hábiles')).toBeInTheDocument()
      expect(screen.getByText('Gratuito según SUIT')).toBeInTheDocument()
    })

    it('should show enhanced requirements with SUIT indicator', () => {
      render(<ProcedureCardEnhanced {...defaultProps} procedure={suitEnhancedProcedure} showPreview={true} layout="detailed" />)

      // Should show SUIT enhanced requirements
      expect(screen.getByText('SUIT req 1')).toBeInTheDocument()
      expect(screen.getByText('SUIT req 2')).toBeInTheDocument()

      // Should show requirements section
      expect(screen.getByText('Requisitos principales:')).toBeInTheDocument()
    })

    it('should fallback to original data when SUIT data is not available', () => {
      const nonEnhancedProcedure: SuitEnhancedProcedure = {
        ...mockProcedure,
        best_description: mockProcedure.description,
        best_requirements: mockProcedure.requirements,
        best_response_time: mockProcedure.response_time,
        best_cost_info: mockProcedure.cost_description,
        has_suit_enhancement: false
      }

      render(<ProcedureCardEnhanced {...defaultProps} procedure={nonEnhancedProcedure} showPreview={true} layout="detailed" />)

      // Should display original description
      expect(screen.getByText(mockProcedure.description!)).toBeInTheDocument()

      // Should not show enhanced requirements section when no requirements
      // (This test validates fallback behavior)
    })
  })
})
