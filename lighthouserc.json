{"ci": {"collect": {"url": ["http://localhost:3000", "http://localhost:3000/dashboard", "http://localhost:3000/tramites", "http://localhost:3000/consulta-tramites", "http://localhost:3000/documentos", "http://localhost:3000/chat", "http://localhost:3000/admin"], "numberOfRuns": 3, "settings": {"chromeFlags": "--no-sandbox --disable-dev-shm-usage", "preset": "desktop", "throttling": {"rttMs": 40, "throughputKbps": 10240, "cpuSlowdownMultiplier": 1, "requestLatencyMs": 0, "downloadThroughputKbps": 0, "uploadThroughputKbps": 0}, "emulatedFormFactor": "desktop", "locale": "es-ES"}}, "assert": {"assertions": {"categories:performance": ["error", {"minScore": 0.8}], "categories:accessibility": ["error", {"minScore": 0.95}], "categories:best-practices": ["error", {"minScore": 0.9}], "categories:seo": ["error", {"minScore": 0.9}], "first-contentful-paint": ["error", {"maxNumericValue": 2000}], "largest-contentful-paint": ["error", {"maxNumericValue": 3000}], "cumulative-layout-shift": ["error", {"maxNumericValue": 0.1}], "total-blocking-time": ["error", {"maxNumericValue": 300}], "speed-index": ["error", {"maxNumericValue": 3000}], "interactive": ["error", {"maxNumericValue": 4000}]}}, "upload": {"target": "filesystem", "outputDir": "./reports/lighthouse"}, "server": {"command": "npm run start", "port": 3000, "timeout": 60000}}}