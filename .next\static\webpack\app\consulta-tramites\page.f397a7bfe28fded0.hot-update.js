"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/consulta-tramites/page",{

/***/ "(app-pages-browser)/./components/procedures/ProcedureCardEnhanced.tsx":
/*!*********************************************************!*\
  !*** ./components/procedures/ProcedureCardEnhanced.tsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProcedureCardEnhanced: function() { return /* binding */ ProcedureCardEnhanced; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building2_CheckCircle_Clock_DollarSign_ExternalLink_Eye_FileText_Heart_Info_Monitor_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building2,CheckCircle,Clock,DollarSign,ExternalLink,Eye,FileText,Heart,Info,Monitor,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/monitor.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building2_CheckCircle_Clock_DollarSign_ExternalLink_Eye_FileText_Heart_Info_Monitor_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building2,CheckCircle,Clock,DollarSign,ExternalLink,Eye,FileText,Heart,Info,Monitor,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building2_CheckCircle_Clock_DollarSign_ExternalLink_Eye_FileText_Heart_Info_Monitor_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building2,CheckCircle,Clock,DollarSign,ExternalLink,Eye,FileText,Heart,Info,Monitor,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building2_CheckCircle_Clock_DollarSign_ExternalLink_Eye_FileText_Heart_Info_Monitor_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building2,CheckCircle,Clock,DollarSign,ExternalLink,Eye,FileText,Heart,Info,Monitor,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building2_CheckCircle_Clock_DollarSign_ExternalLink_Eye_FileText_Heart_Info_Monitor_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building2,CheckCircle,Clock,DollarSign,ExternalLink,Eye,FileText,Heart,Info,Monitor,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building2_CheckCircle_Clock_DollarSign_ExternalLink_Eye_FileText_Heart_Info_Monitor_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building2,CheckCircle,Clock,DollarSign,ExternalLink,Eye,FileText,Heart,Info,Monitor,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building2_CheckCircle_Clock_DollarSign_ExternalLink_Eye_FileText_Heart_Info_Monitor_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building2,CheckCircle,Clock,DollarSign,ExternalLink,Eye,FileText,Heart,Info,Monitor,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building2_CheckCircle_Clock_DollarSign_ExternalLink_Eye_FileText_Heart_Info_Monitor_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building2,CheckCircle,Clock,DollarSign,ExternalLink,Eye,FileText,Heart,Info,Monitor,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building2_CheckCircle_Clock_DollarSign_ExternalLink_Eye_FileText_Heart_Info_Monitor_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building2,CheckCircle,Clock,DollarSign,ExternalLink,Eye,FileText,Heart,Info,Monitor,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building2_CheckCircle_Clock_DollarSign_ExternalLink_Eye_FileText_Heart_Info_Monitor_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building2,CheckCircle,Clock,DollarSign,ExternalLink,Eye,FileText,Heart,Info,Monitor,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building2_CheckCircle_Clock_DollarSign_ExternalLink_Eye_FileText_Heart_Info_Monitor_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building2,CheckCircle,Clock,DollarSign,ExternalLink,Eye,FileText,Heart,Info,Monitor,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building2_CheckCircle_Clock_DollarSign_ExternalLink_Eye_FileText_Heart_Info_Monitor_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building2,CheckCircle,Clock,DollarSign,ExternalLink,Eye,FileText,Heart,Info,Monitor,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* harmony import */ var _types_suit_enhanced_procedure__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/types/suit-enhanced-procedure */ \"(app-pages-browser)/./types/suit-enhanced-procedure.ts\");\n/* __next_internal_client_entry_do_not_use__ ProcedureCardEnhanced auto */ \n\n\n\n\n\n\n\nfunction ProcedureCardEnhanced(param) {\n    let { procedure, onViewDetails, onToggleFavorite, isFavorite = false, layout = \"compact\", className, showPreview = true } = param;\n    var _procedure_best_requirements, _procedure_suit_data, _procedure_suit_data1, _procedure_best_requirements1, _procedure_best_requirements2;\n    // Obtener estadísticas de mejora SUIT\n    const suitStats = (0,_types_suit_enhanced_procedure__WEBPACK_IMPORTED_MODULE_6__.getSuitEnhancementStats)(procedure);\n    const getModalityInfo = (bestModality)=>{\n        if (bestModality === \"Virtual\") return {\n            label: \"Virtual\",\n            color: \"bg-green-100 text-green-800\",\n            icon: _barrel_optimize_names_AlertCircle_Building2_CheckCircle_Clock_DollarSign_ExternalLink_Eye_FileText_Heart_Info_Monitor_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        };\n        if (bestModality === \"Presencial\") return {\n            label: \"Presencial\",\n            color: \"bg-chia-blue-100 text-chia-blue-800\",\n            icon: _barrel_optimize_names_AlertCircle_Building2_CheckCircle_Clock_DollarSign_ExternalLink_Eye_FileText_Heart_Info_Monitor_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        };\n        return {\n            label: \"Mixta\",\n            color: \"bg-purple-100 text-purple-800\",\n            icon: _barrel_optimize_names_AlertCircle_Building2_CheckCircle_Clock_DollarSign_ExternalLink_Eye_FileText_Heart_Info_Monitor_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n        };\n    };\n    const getProcedureTypeInfo = (type)=>{\n        if (type === \"TRAMITE\") return {\n            label: \"Tr\\xe1mite\",\n            color: \"bg-primary/10 text-primary\"\n        };\n        if (type === \"OPA\") return {\n            label: \"OPA\",\n            color: \"bg-chia-green-100 text-chia-green-800\"\n        };\n        return {\n            label: \"Procedimiento\",\n            color: \"bg-gray-100 text-gray-800\"\n        };\n    };\n    const getUrgencyLevel = (responseTime)=>{\n        if (!responseTime) return null;\n        const time = responseTime.toLowerCase();\n        if (time.includes(\"inmediato\") || time.includes(\"mismo d\\xeda\")) {\n            return {\n                level: \"high\",\n                color: \"text-red-600\",\n                icon: _barrel_optimize_names_AlertCircle_Building2_CheckCircle_Clock_DollarSign_ExternalLink_Eye_FileText_Heart_Info_Monitor_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n            };\n        }\n        if (time.includes(\"1\") || time.includes(\"2\") || time.includes(\"3\")) {\n            return {\n                level: \"medium\",\n                color: \"text-yellow-600\",\n                icon: _barrel_optimize_names_AlertCircle_Building2_CheckCircle_Clock_DollarSign_ExternalLink_Eye_FileText_Heart_Info_Monitor_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n            };\n        }\n        return {\n            level: \"low\",\n            color: \"text-green-600\",\n            icon: _barrel_optimize_names_AlertCircle_Building2_CheckCircle_Clock_DollarSign_ExternalLink_Eye_FileText_Heart_Info_Monitor_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n        };\n    };\n    const modalityInfo = getModalityInfo(procedure.best_modality);\n    const typeInfo = getProcedureTypeInfo(procedure.procedure_type);\n    const urgencyInfo = getUrgencyLevel(procedure.best_response_time);\n    const ModalityIcon = modalityInfo.icon;\n    // Usar los mejores requisitos disponibles (SUIT o originales)\n    const topRequirements = ((_procedure_best_requirements = procedure.best_requirements) === null || _procedure_best_requirements === void 0 ? void 0 : _procedure_best_requirements.slice(0, 3)) || [];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"hover:shadow-lg transition-all duration-300 border-l-4 border-l-transparent hover:border-l-primary\", \"group cursor-pointer\", className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n            className: \"p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-start\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start justify-between mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                (procedure.codigo_tramite || procedure.code) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                        variant: \"outline\",\n                                                        className: \"text-xs font-mono bg-chia-blue-50 text-chia-blue-700 border-chia-blue-200\",\n                                                        children: procedure.codigo_tramite || procedure.code\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                                                        lineNumber: 112,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-gray-900 line-clamp-2 group-hover:text-chia-blue-700 transition-colors flex-1\",\n                                                            children: procedure.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                                                            lineNumber: 118,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        suitStats.hasEnhancement && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-1 mt-0.5\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building2_CheckCircle_Clock_DollarSign_ExternalLink_Eye_FileText_Heart_Info_Monitor_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-3 w-3 text-amber-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                                                                    lineNumber: 124,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-amber-600 font-medium\",\n                                                                    children: [\n                                                                        \"+\",\n                                                                        suitStats.enhancementCount\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                                                                    lineNumber: 125,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                                                            lineNumber: 123,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 17\n                                        }, this),\n                                        onToggleFavorite && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            className: \"ml-2 p-1 h-8 w-8 opacity-0 group-hover:opacity-100 transition-opacity\",\n                                            onClick: (e)=>{\n                                                e.stopPropagation();\n                                                onToggleFavorite(procedure.id);\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building2_CheckCircle_Clock_DollarSign_ExternalLink_Eye_FileText_Heart_Info_Monitor_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"h-4 w-4\", isFavorite ? \"fill-red-500 text-red-500\" : \"text-gray-400 hover:text-red-500\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 15\n                                }, this),\n                                procedure.dependency && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center text-sm text-gray-600 mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building2_CheckCircle_Clock_DollarSign_ExternalLink_Eye_FileText_Heart_Info_Monitor_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"truncate\",\n                                            children: procedure.dependency.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 19\n                                        }, this),\n                                        procedure.subdependency && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2 text-gray-500 truncate\",\n                                            children: [\n                                                \"• \",\n                                                procedure.subdependency.name\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                className: typeInfo.color,\n                                children: typeInfo.label\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                className: modalityInfo.color,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ModalityIcon, {\n                                        className: \"h-3 w-3 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 15\n                                    }, this),\n                                    modalityInfo.label\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, this),\n                            urgencyInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                variant: \"outline\",\n                                className: urgencyInfo.color,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(urgencyInfo.icon, {\n                                        className: \"h-3 w-3 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Urgente\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-4 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building2_CheckCircle_Clock_DollarSign_ExternalLink_Eye_FileText_Heart_Info_Monitor_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: \"Tiempo\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium\",\n                                                children: (0,_types_suit_enhanced_procedure__WEBPACK_IMPORTED_MODULE_6__.formatEnhancedResponseTime)(procedure)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building2_CheckCircle_Clock_DollarSign_ExternalLink_Eye_FileText_Heart_Info_Monitor_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: \"Costo\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium\",\n                                                children: (0,_types_suit_enhanced_procedure__WEBPACK_IMPORTED_MODULE_6__.formatEnhancedCost)(procedure)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 11\n                    }, this),\n                    showPreview && procedure.best_description && layout === \"detailed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 line-clamp-2\",\n                                children: procedure.best_description\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 15\n                            }, this),\n                            ((_procedure_suit_data = procedure.suit_data) === null || _procedure_suit_data === void 0 ? void 0 : _procedure_suit_data.descripcion_detallada) && procedure.best_description === procedure.suit_data.descripcion_detallada && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-0 right-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building2_CheckCircle_Clock_DollarSign_ExternalLink_Eye_FileText_Heart_Info_Monitor_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"h-3 w-3 text-amber-500\",\n                                    title: \"Informaci\\xf3n mejorada con SUIT\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 13\n                    }, this),\n                    showPreview && topRequirements.length > 0 && layout === \"detailed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1 text-xs font-medium text-gray-700 mb-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Requisitos principales:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 17\n                                    }, this),\n                                    ((_procedure_suit_data1 = procedure.suit_data) === null || _procedure_suit_data1 === void 0 ? void 0 : _procedure_suit_data1.requisitos) && procedure.best_requirements === procedure.suit_data.requisitos && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building2_CheckCircle_Clock_DollarSign_ExternalLink_Eye_FileText_Heart_Info_Monitor_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-3 w-3 text-amber-500\",\n                                        title: \"Requisitos mejorados con SUIT\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"text-xs text-gray-600 space-y-1\",\n                                children: [\n                                    topRequirements.map((req, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-block w-1 h-1 bg-gray-400 rounded-full mt-2 mr-2 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"line-clamp-1\",\n                                                    children: req\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 19\n                                        }, this)),\n                                    (((_procedure_best_requirements1 = procedure.best_requirements) === null || _procedure_best_requirements1 === void 0 ? void 0 : _procedure_best_requirements1.length) || 0) > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"text-chia-blue-600 font-medium\",\n                                        children: [\n                                            \"+\",\n                                            (((_procedure_best_requirements2 = procedure.best_requirements) === null || _procedure_best_requirements2 === void 0 ? void 0 : _procedure_best_requirements2.length) || 0) - 3,\n                                            \" requisitos m\\xe1s\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between pt-2 border-t border-gray-100\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2\",\n                                children: [\n                                    procedure.suit_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"text-xs p-2 h-8\",\n                                        onClick: (e)=>{\n                                            e.stopPropagation();\n                                            window.open(procedure.suit_url, \"_blank\");\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building2_CheckCircle_Clock_DollarSign_ExternalLink_Eye_FileText_Heart_Info_Monitor_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-3 w-3 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"SUIT\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 17\n                                    }, this),\n                                    procedure.gov_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"text-xs p-2 h-8\",\n                                        onClick: (e)=>{\n                                            e.stopPropagation();\n                                            window.open(procedure.gov_url, \"_blank\");\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building2_CheckCircle_Clock_DollarSign_ExternalLink_Eye_FileText_Heart_Info_Monitor_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-3 w-3 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Gov.co\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                size: \"sm\",\n                                variant: \"outline\",\n                                className: \"hover:bg-chia-blue-50 hover:border-chia-blue-300\",\n                                onClick: (e)=>{\n                                    e.stopPropagation();\n                                    onViewDetails(procedure);\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building2_CheckCircle_Clock_DollarSign_ExternalLink_Eye_FileText_Heart_Info_Monitor_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Ver detalles\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n                lineNumber: 103,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n            lineNumber: 102,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\procedures\\\\ProcedureCardEnhanced.tsx\",\n        lineNumber: 97,\n        columnNumber: 5\n    }, this);\n}\n_c = ProcedureCardEnhanced;\nvar _c;\n$RefreshReg$(_c, \"ProcedureCardEnhanced\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/procedures/ProcedureCardEnhanced.tsx\n"));

/***/ })

});