@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* <PERSON><PERSON> Colors */
    --chia-blue-50: 240 249 255;
    --chia-blue-100: 219 234 254;
    --chia-blue-200: 191 219 254;
    --chia-blue-300: 147 197 253;
    --chia-blue-400: 96 165 250;
    --chia-blue-500: 59 130 246;
    --chia-blue-600: 37 99 235;
    --chia-blue-700: 29 78 216;
    --chia-blue-800: 30 64 175;
    --chia-blue-900: 30 58 138;

    --chia-green-50: 240 253 244;
    --chia-green-100: 220 252 231;
    --chia-green-200: 187 247 208;
    --chia-green-300: 134 239 172;
    --chia-green-400: 74 222 128;
    --chia-green-500: 34 197 94;
    --chia-green-600: 5 150 105; /* #059669 - Color oficial de Chía */
    --chia-green-700: 21 128 61;
    --chia-green-800: 22 101 52;
    --chia-green-900: 20 83 45;

    /* Shadcn/ui Variables - Actualizadas para usar chia-green como primario */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 150 69% 24%; /* Chia Green 700 en HSL - Cumple WCAG 2.1 AA */
    --primary-foreground: 0 0% 100%; /* Blanco para contraste */
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 150 69% 24%; /* Chia Green 700 para focus ring */
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 150 84% 50%; /* Chia Green más claro para modo oscuro */
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 150 84% 50%; /* Chia Green para focus ring en modo oscuro */
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground font-medium;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* Enhanced typography */
  h1, h2, h3, h4, h5, h6 {
    @apply font-bold tracking-tight;
    line-height: 1.2;
  }

  h1 {
    @apply text-4xl md:text-5xl lg:text-6xl;
  }

  h2 {
    @apply text-3xl md:text-4xl lg:text-5xl;
  }

  h3 {
    @apply text-2xl md:text-3xl;
  }

  h4 {
    @apply text-xl md:text-2xl;
  }

  p {
    @apply leading-relaxed;
  }

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }

  /* Focus styles */
  *:focus-visible {
    @apply outline-none ring-2 ring-primary/20 ring-offset-2;
  }
}

@layer utilities {
  /* Modern glass effect */
  .glass {
    @apply bg-white/80 backdrop-blur-sm border border-white/20;
  }

  .glass-dark {
    @apply bg-gray-900/80 backdrop-blur-sm border border-gray-700/20;
  }

  /* Enhanced shadows */
  .shadow-soft {
    box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
  }

  .shadow-glow-blue {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.15);
  }

  .shadow-glow-green {
    box-shadow: 0 0 20px rgba(34, 197, 94, 0.15);
  }

  /* Text gradients */
  .text-gradient-blue {
    @apply bg-gradient-to-r from-chia-blue-600 to-chia-blue-800 bg-clip-text text-transparent;
  }

  .text-gradient-green {
    @apply bg-gradient-to-r from-chia-green-600 to-chia-green-800 bg-clip-text text-transparent;
  }

  /* Improved line clamping */
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

/* Custom Chía Styles */
.chia-gradient {
  background: linear-gradient(135deg, rgb(var(--chia-blue-600)) 0%, rgb(var(--chia-green-600)) 100%);
}

.chia-gradient-light {
  background: linear-gradient(135deg, rgb(var(--chia-blue-50)) 0%, rgb(var(--chia-green-50)) 100%);
}

/* Navigation Animations */
.nav-item {
  @apply transition-all duration-200 ease-in-out;
}

.nav-item:hover {
  @apply transform scale-105;
}

/* Loading Animations */
@keyframes pulse-slow {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse-slow {
  animation: pulse-slow 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus styles for better accessibility */
.focus-visible:focus {
  @apply outline-none ring-2 ring-ring ring-offset-2;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}
