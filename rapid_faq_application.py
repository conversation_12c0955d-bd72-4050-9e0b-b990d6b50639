#!/usr/bin/env python3
"""
Rapid FAQ application - Apply remaining chunks efficiently
"""

import os
import re

def combine_multiple_chunks(start_chunk, end_chunk):
    """Combine multiple chunks into a single large batch"""
    
    combined_sql = []
    total_questions = 0
    
    for chunk_num in range(start_chunk, end_chunk + 1):
        filename = f"corrected_faq_chunk_{chunk_num:02d}.sql"
        
        if not os.path.exists(filename):
            print(f"Warning: {filename} not found, skipping...")
            continue
        
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Extract INSERT statements
        insert_statements = re.findall(r'INSERT INTO municipal_faqs.*?;', content, re.DOTALL)
        combined_sql.extend(insert_statements)
        total_questions += len(insert_statements)
        
        print(f"Added {len(insert_statements)} questions from {filename}")
    
    # Write combined file
    output_filename = f"rapid_batch_{start_chunk:02d}_to_{end_chunk:02d}.sql"
    
    with open(output_filename, 'w', encoding='utf-8') as f:
        f.write(f"-- Rapid FAQ Batch: Chunks {start_chunk} to {end_chunk}\n")
        f.write(f"-- Total questions: {total_questions}\n\n")
        f.write("\n".join(combined_sql))
        f.write("\n")
    
    print(f"Created {output_filename} with {total_questions} questions")
    return output_filename, total_questions

def create_rapid_batches():
    """Create large batches for rapid application"""
    
    print("Creating rapid batches for efficient FAQ application...")
    
    # Create large batches (we already have chunks 1-6 applied, so start from 7)
    # Current status: 43 questions applied, need to apply chunks 7-26 (remaining 340 questions)
    
    batches = [
        (7, 11),   # Chunks 7-11 (75 questions)
        (12, 16),  # Chunks 12-16 (75 questions) 
        (17, 21),  # Chunks 17-21 (75 questions)
        (22, 26)   # Chunks 22-26 (remaining questions)
    ]
    
    batch_files = []
    
    for i, (start, end) in enumerate(batches, 1):
        print(f"\nCreating rapid batch {i}: chunks {start}-{end}")
        filename, count = combine_multiple_chunks(start, end)
        batch_files.append((filename, count))
    
    return batch_files

def main():
    """Main function"""
    print("Rapid FAQ Batch Creator")
    print("=" * 25)
    
    try:
        batch_files = create_rapid_batches()
        
        print(f"\n✅ Successfully created {len(batch_files)} rapid batch files:")
        total_questions = 0
        
        for i, (filename, count) in enumerate(batch_files, 1):
            print(f"   {i}. {filename} ({count} questions)")
            total_questions += count
        
        print(f"\n📊 Total questions in rapid batches: {total_questions}")
        print(f"📊 Current database: 43 questions")
        print(f"📊 After application: {43 + total_questions} questions")
        print(f"📋 Target: 383 questions")
        
        return 0
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
