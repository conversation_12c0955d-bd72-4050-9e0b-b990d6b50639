import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { DocumentPortal } from '@/components/documents/DocumentPortal'
import { mockUsers, setupMockAuthSuccess, cleanupMocks } from '@/tests/utils/test-helpers'

// Mock documents data
const mockDocuments = [
  {
    id: '1',
    name: 'cedula.pdf',
    type: 'application/pdf',
    size: 1024000,
    procedure_id: 'proc-1',
    procedure_name: 'Certificado de Residencia',
    uploaded_at: '2024-01-01T00:00:00Z',
    url: 'https://example.com/cedula.pdf',
  },
  {
    id: '2',
    name: 'recibo_servicios.jpg',
    type: 'image/jpeg',
    size: 512000,
    procedure_id: 'proc-1',
    procedure_name: 'Certificado de Residencia',
    uploaded_at: '2024-01-02T00:00:00Z',
    url: 'https://example.com/recibo.jpg',
  },
  {
    id: '3',
    name: 'planos.pdf',
    type: 'application/pdf',
    size: 2048000,
    procedure_id: 'proc-2',
    procedure_name: 'Licencia de Construcción',
    uploaded_at: '2024-01-03T00:00:00Z',
    url: 'https://example.com/planos.pdf',
  },
]

// Mock Supabase storage
const mockStorage = {
  from: jest.fn(() => ({
    list: jest.fn(() => Promise.resolve({
      data: mockDocuments,
      error: null,
    })),
    upload: jest.fn(() => Promise.resolve({
      data: { path: 'test-path' },
      error: null,
    })),
    download: jest.fn(() => Promise.resolve({
      data: new Blob(['test content']),
      error: null,
    })),
    remove: jest.fn(() => Promise.resolve({
      data: null,
      error: null,
    })),
  })),
}

jest.mock('@/lib/supabase/client', () => ({
  supabase: {
    storage: mockStorage,
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => Promise.resolve({
          data: mockDocuments,
          error: null,
        })),
      })),
    })),
  },
}))

// Mock hooks
jest.mock('@/hooks/useAuth', () => ({
  useAuth: () => ({
    user: mockUsers.ciudadano,
    profile: mockUsers.ciudadano.profile,
    loading: false,
  }),
}))

describe('DocumentPortal', () => {
  const user = userEvent.setup()

  beforeEach(() => {
    setupMockAuthSuccess('ciudadano')
    jest.clearAllMocks()
  })

  afterEach(() => {
    cleanupMocks()
  })

  it('renders document portal correctly', async () => {
    render(<DocumentPortal />)

    // Check for main heading
    expect(screen.getByText('Portal de Documentos')).toBeInTheDocument()

    // Check for upload button
    expect(screen.getByText('Subir Documento')).toBeInTheDocument()

    // Check for view toggle buttons
    expect(screen.getByText('Cuadrícula')).toBeInTheDocument()
    expect(screen.getByText('Lista')).toBeInTheDocument()
  })

  it('displays document statistics correctly', async () => {
    render(<DocumentPortal />)

    await waitFor(() => {
      // Check for statistics cards
      expect(screen.getByText('Total Documentos')).toBeInTheDocument()
      expect(screen.getByText('3')).toBeInTheDocument()
      expect(screen.getByText('Espacio Usado')).toBeInTheDocument()
    })
  })

  it('displays documents in grid view', async () => {
    render(<DocumentPortal />)

    await waitFor(() => {
      // Check that documents are displayed
      expect(screen.getByText('cedula.pdf')).toBeInTheDocument()
      expect(screen.getByText('recibo_servicios.jpg')).toBeInTheDocument()
      expect(screen.getByText('planos.pdf')).toBeInTheDocument()
    })

    // Check for procedure names
    expect(screen.getByText('Certificado de Residencia')).toBeInTheDocument()
    expect(screen.getByText('Licencia de Construcción')).toBeInTheDocument()
  })

  it('switches between grid and list views', async () => {
    render(<DocumentPortal />)

    // Initially in grid view
    expect(screen.getByTestId('document-grid')).toBeInTheDocument()

    // Switch to list view
    const listViewButton = screen.getByText('Lista')
    await user.click(listViewButton)

    await waitFor(() => {
      expect(screen.getByTestId('document-list')).toBeInTheDocument()
    })

    // Switch back to grid view
    const gridViewButton = screen.getByText('Cuadrícula')
    await user.click(gridViewButton)

    await waitFor(() => {
      expect(screen.getByTestId('document-grid')).toBeInTheDocument()
    })
  })

  it('filters documents by search query', async () => {
    render(<DocumentPortal />)

    // Wait for documents to load
    await waitFor(() => {
      expect(screen.getByText('cedula.pdf')).toBeInTheDocument()
    })

    // Search for specific document
    const searchInput = screen.getByPlaceholderText('Buscar documentos...')
    await user.type(searchInput, 'cedula')

    await waitFor(() => {
      // Should show only matching documents
      expect(screen.getByText('cedula.pdf')).toBeInTheDocument()
      expect(screen.queryByText('planos.pdf')).not.toBeInTheDocument()
    })
  })

  it('filters documents by procedure', async () => {
    render(<DocumentPortal />)

    await waitFor(() => {
      expect(screen.getByText('cedula.pdf')).toBeInTheDocument()
    })

    // Open procedure filter
    const procedureFilter = screen.getByText('Filtrar por trámite')
    await user.click(procedureFilter)

    // Select specific procedure
    const certificadoOption = screen.getByText('Certificado de Residencia')
    await user.click(certificadoOption)

    await waitFor(() => {
      // Should show only documents for that procedure
      expect(screen.getByText('cedula.pdf')).toBeInTheDocument()
      expect(screen.getByText('recibo_servicios.jpg')).toBeInTheDocument()
      expect(screen.queryByText('planos.pdf')).not.toBeInTheDocument()
    })
  })

  it('filters documents by file type', async () => {
    render(<DocumentPortal />)

    await waitFor(() => {
      expect(screen.getByText('cedula.pdf')).toBeInTheDocument()
    })

    // Open type filter
    const typeFilter = screen.getByText('Filtrar por tipo')
    await user.click(typeFilter)

    // Select PDF type
    const pdfOption = screen.getByText('PDF')
    await user.click(pdfOption)

    await waitFor(() => {
      // Should show only PDF documents
      expect(screen.getByText('cedula.pdf')).toBeInTheDocument()
      expect(screen.getByText('planos.pdf')).toBeInTheDocument()
      expect(screen.queryByText('recibo_servicios.jpg')).not.toBeInTheDocument()
    })
  })

  it('opens document upload modal', async () => {
    render(<DocumentPortal />)

    const uploadButton = screen.getByText('Subir Documento')
    await user.click(uploadButton)

    await waitFor(() => {
      expect(screen.getByText('Subir Nuevo Documento')).toBeInTheDocument()
      expect(screen.getByText('Arrastra archivos aquí o haz clic para seleccionar')).toBeInTheDocument()
    })
  })

  it('handles file upload correctly', async () => {
    render(<DocumentPortal />)

    // Open upload modal
    const uploadButton = screen.getByText('Subir Documento')
    await user.click(uploadButton)

    await waitFor(() => {
      expect(screen.getByText('Subir Nuevo Documento')).toBeInTheDocument()
    })

    // Mock file input
    const fileInput = screen.getByLabelText('Seleccionar archivos')
    const file = new File(['test content'], 'test.pdf', { type: 'application/pdf' })

    await user.upload(fileInput, file)

    await waitFor(() => {
      expect(screen.getByText('test.pdf')).toBeInTheDocument()
    })

    // Submit upload
    const submitButton = screen.getByText('Subir Archivo')
    await user.click(submitButton)

    await waitFor(() => {
      expect(mockStorage.from().upload).toHaveBeenCalled()
    })
  })

  it('validates file types and sizes', async () => {
    render(<DocumentPortal />)

    // Open upload modal
    const uploadButton = screen.getByText('Subir Documento')
    await user.click(uploadButton)

    // Try to upload invalid file type
    const fileInput = screen.getByLabelText('Seleccionar archivos')
    const invalidFile = new File(['test'], 'test.exe', { type: 'application/exe' })

    await user.upload(fileInput, invalidFile)

    await waitFor(() => {
      expect(screen.getByText('Tipo de archivo no permitido')).toBeInTheDocument()
    })

    // Try to upload oversized file
    const oversizedFile = new File(['x'.repeat(11 * 1024 * 1024)], 'large.pdf', { 
      type: 'application/pdf' 
    })

    await user.upload(fileInput, oversizedFile)

    await waitFor(() => {
      expect(screen.getByText('El archivo es demasiado grande')).toBeInTheDocument()
    })
  })

  it('handles document download', async () => {
    render(<DocumentPortal />)

    await waitFor(() => {
      expect(screen.getByText('cedula.pdf')).toBeInTheDocument()
    })

    // Click download button
    const downloadButton = screen.getByLabelText('Descargar cedula.pdf')
    await user.click(downloadButton)

    await waitFor(() => {
      expect(mockStorage.from().download).toHaveBeenCalled()
    })
  })

  it('handles document deletion', async () => {
    render(<DocumentPortal />)

    await waitFor(() => {
      expect(screen.getByText('cedula.pdf')).toBeInTheDocument()
    })

    // Click delete button
    const deleteButton = screen.getByLabelText('Eliminar cedula.pdf')
    await user.click(deleteButton)

    // Confirm deletion
    await waitFor(() => {
      expect(screen.getByText('¿Estás seguro?')).toBeInTheDocument()
    })

    const confirmButton = screen.getByText('Eliminar')
    await user.click(confirmButton)

    await waitFor(() => {
      expect(mockStorage.from().remove).toHaveBeenCalled()
    })
  })

  it('displays loading state correctly', () => {
    // Mock loading state
    jest.doMock('@/lib/supabase/client', () => ({
      supabase: {
        storage: {
          from: jest.fn(() => ({
            list: jest.fn(() => new Promise(() => {})), // Never resolves
          })),
        },
      },
    }))

    render(<DocumentPortal />)

    expect(screen.getByTestId('documents-loading')).toBeInTheDocument()
  })

  it('displays error state correctly', async () => {
    // Mock error state
    mockStorage.from.mockImplementation(() => ({
      list: jest.fn(() => Promise.resolve({
        data: null,
        error: { message: 'Storage error' },
      })),
    }))

    render(<DocumentPortal />)

    await waitFor(() => {
      expect(screen.getByText('Error al cargar documentos')).toBeInTheDocument()
    })
  })

  it('displays empty state correctly', async () => {
    // Mock empty state
    mockStorage.from.mockImplementation(() => ({
      list: jest.fn(() => Promise.resolve({
        data: [],
        error: null,
      })),
    }))

    render(<DocumentPortal />)

    await waitFor(() => {
      expect(screen.getByText('No tienes documentos subidos')).toBeInTheDocument()
      expect(screen.getByText('Sube tu primer documento')).toBeInTheDocument()
    })
  })

  it('is accessible', () => {
    const { container } = render(<DocumentPortal />)

    // Check for proper heading structure
    const mainHeading = screen.getByRole('heading', { level: 1 })
    expect(mainHeading).toBeInTheDocument()

    // Check for proper button roles and labels
    const buttons = screen.getAllByRole('button')
    buttons.forEach(button => {
      expect(
        button.textContent || 
        button.getAttribute('aria-label') || 
        button.getAttribute('title')
      ).toBeTruthy()
    })

    // Check for proper form controls
    const searchInput = screen.getByPlaceholderText('Buscar documentos...')
    expect(searchInput).toHaveAttribute('aria-label')
  })

  it('supports keyboard navigation', async () => {
    render(<DocumentPortal />)

    await waitFor(() => {
      expect(screen.getByText('cedula.pdf')).toBeInTheDocument()
    })

    // Tab through interactive elements
    const uploadButton = screen.getByText('Subir Documento')
    uploadButton.focus()
    expect(uploadButton).toHaveFocus()

    await user.tab()
    const searchInput = screen.getByPlaceholderText('Buscar documentos...')
    expect(searchInput).toHaveFocus()
  })

  it('handles drag and drop upload', async () => {
    render(<DocumentPortal />)

    // Open upload modal
    const uploadButton = screen.getByText('Subir Documento')
    await user.click(uploadButton)

    await waitFor(() => {
      expect(screen.getByText('Arrastra archivos aquí')).toBeInTheDocument()
    })

    // Simulate drag and drop
    const dropZone = screen.getByTestId('upload-dropzone')
    const file = new File(['test content'], 'dropped.pdf', { type: 'application/pdf' })

    fireEvent.dragOver(dropZone)
    fireEvent.drop(dropZone, {
      dataTransfer: {
        files: [file],
      },
    })

    await waitFor(() => {
      expect(screen.getByText('dropped.pdf')).toBeInTheDocument()
    })
  })
})
