#!/usr/bin/env python3
"""
Script para procesar trámites del archivo JSON y generar migraciones SQL
para el portal municipal de Chía.
"""

import json
import re

def clean_text(text):
    """Limpia el texto para SQL, escapando comillas simples."""
    if not text:
        return ""
    # Escapar comillas simples para SQL
    return text.replace("'", "''")

def generate_tramite_sql(tramite_data, tramite_number):
    """Genera SQL para un trámite individual."""
    nombre = clean_text(tramite_data.get("Nombre", ""))
    formulario = clean_text(tramite_data.get("Formulario", ""))
    tiempo_respuesta = clean_text(tramite_data.get("Tiempo de respuesta", ""))
    tiene_pago = clean_text(tramite_data.get("¿Tiene pago?", ""))
    suit_link = tramite_data.get("Visualización trámite en el SUIT", "")
    govco_link = tramite_data.get("Visualización trámite en GOV.CO", "")
    codigo_dep = tramite_data.get("codigo_dependencia", "")
    codigo_sub = tramite_data.get("codigo_subdependencia", "")
    
    # Crear descripción combinada
    descripcion_parts = []
    if formulario and formulario != "No":
        descripcion_parts.append(f"Formulario: {formulario}")
    if tiempo_respuesta:
        descripcion_parts.append(f"Tiempo de respuesta: {tiempo_respuesta}")
    if tiene_pago:
        descripcion_parts.append(f"¿Tiene pago?: {tiene_pago}")
    
    descripcion = ". ".join(descripcion_parts)
    descripcion = clean_text(descripcion)
    
    sql = f"""-- Trámite {tramite_number}: {nombre}
SELECT 
    '{nombre}',
    '{descripcion}',
    dsm.dep_id,
    dsm.sub_id,
    '{tiempo_respuesta}',
    '{suit_link}',
    '{govco_link}',
    'tramite',
    true
FROM dep_sub_mapping dsm
WHERE dsm.dep_code = '{codigo_dep}' AND dsm.sub_code = '{codigo_sub}'"""
    
    return sql

def process_tramites_file(filename, start_index=45, batch_size=25):
    """Procesa el archivo de trámites y genera SQL por lotes."""
    with open(filename, 'r', encoding='utf-8') as f:
        tramites = json.load(f)
    
    total_tramites = len(tramites)
    print(f"Total trámites en archivo: {total_tramites}")
    print(f"Procesando desde índice {start_index} en lotes de {batch_size}")
    
    batch_number = 1
    current_index = start_index
    
    while current_index < total_tramites:
        end_index = min(current_index + batch_size, total_tramites)
        batch_tramites = tramites[current_index:end_index]
        
        print(f"\n=== LOTE {batch_number} (Trámites {current_index + 1}-{end_index}) ===")
        
        # Generar SQL para el lote
        sql_parts = []
        sql_parts.append(f"-- Insert lote {batch_number} de trámites ({current_index + 1}-{end_index}) con preservación de enlaces gubernamentales")
        sql_parts.append("WITH dep_sub_mapping AS (")
        sql_parts.append("  SELECT ")
        sql_parts.append("    d.id as dep_id, ")
        sql_parts.append("    d.code as dep_code,")
        sql_parts.append("    s.id as sub_id,")
        sql_parts.append("    s.code as sub_code")
        sql_parts.append("  FROM dependencies d")
        sql_parts.append("  LEFT JOIN subdependencies s ON d.id = s.dependency_id")
        sql_parts.append(")")
        sql_parts.append("")
        sql_parts.append("INSERT INTO procedures (")
        sql_parts.append("    name, description, dependency_id, subdependency_id, response_time, ")
        sql_parts.append("    suit_link, govco_link, procedure_type, is_active")
        sql_parts.append(")")
        
        # Generar SQL para cada trámite en el lote
        tramite_sqls = []
        for i, tramite in enumerate(batch_tramites):
            tramite_number = current_index + i + 1
            tramite_sql = generate_tramite_sql(tramite, tramite_number)
            tramite_sqls.append(tramite_sql)
        
        # Unir con UNION ALL
        sql_parts.append("\nUNION ALL\n\n".join(tramite_sqls) + ";")
        
        full_sql = "\n".join(sql_parts)
        
        # Guardar SQL en archivo
        filename_sql = f"tramites_lote_{batch_number}.sql"
        with open(filename_sql, 'w', encoding='utf-8') as f:
            f.write(full_sql)
        
        print(f"SQL generado en: {filename_sql}")
        print(f"Trámites en este lote: {len(batch_tramites)}")
        
        # Mostrar algunos ejemplos
        for i, tramite in enumerate(batch_tramites[:3]):  # Mostrar solo los primeros 3
            print(f"  - {tramite.get('Nombre', 'Sin nombre')}")
        
        if len(batch_tramites) > 3:
            print(f"  ... y {len(batch_tramites) - 3} más")
        
        current_index = end_index
        batch_number += 1
    
    print(f"\n✅ Procesamiento completado. Generados {batch_number - 1} lotes SQL.")

if __name__ == "__main__":
    process_tramites_file("tramites_chia_optimo.json", start_index=45, batch_size=25)
